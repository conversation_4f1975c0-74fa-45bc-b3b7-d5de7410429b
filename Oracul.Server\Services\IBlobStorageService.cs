using Oracul.Server.Models;

namespace Oracul.Server.Services
{
    /// <summary>
    /// Interface for Azure Blob Storage operations
    /// </summary>
    public interface IBlobStorageService
    {
        /// <summary>
        /// Upload a file to blob storage
        /// </summary>
        /// <param name="fileStream">The file stream to upload</param>
        /// <param name="fileName">The original file name</param>
        /// <param name="contentType">The MIME content type</param>
        /// <param name="containerPath">The path within the container (e.g., "profiles/123")</param>
        /// <returns>Upload result with blob URL and metadata</returns>
        Task<BlobUploadResult> UploadFileAsync(Stream fileStream, string fileName, string contentType, string containerPath);

        /// <summary>
        /// Get the public URL for a blob
        /// </summary>
        /// <param name="blobPath">The full blob path</param>
        /// <returns>The public URL to access the blob</returns>
        Task<string> GetFileUrlAsync(string blobPath);

        /// <summary>
        /// Delete a file from blob storage
        /// </summary>
        /// <param name="blobPath">The full blob path</param>
        /// <returns>True if deleted successfully, false if not found</returns>
        Task<bool> DeleteFileAsync(string blobPath);

        /// <summary>
        /// Download a file from blob storage
        /// </summary>
        /// <param name="blobPath">The full blob path</param>
        /// <returns>Stream of the file content</returns>
        Task<Stream> DownloadFileAsync(string blobPath);

        /// <summary>
        /// Check if a file exists in blob storage
        /// </summary>
        /// <param name="blobPath">The full blob path</param>
        /// <returns>True if the file exists</returns>
        Task<bool> FileExistsAsync(string blobPath);

        /// <summary>
        /// Generate a unique file name with timestamp and GUID
        /// </summary>
        /// <param name="originalFileName">The original file name</param>
        /// <param name="prefix">Optional prefix for the file name</param>
        /// <returns>A unique file name</returns>
        string GenerateUniqueFileName(string originalFileName, string? prefix = null);

        /// <summary>
        /// Validate file type and size
        /// </summary>
        /// <param name="fileName">The file name</param>
        /// <param name="contentType">The MIME content type</param>
        /// <param name="fileSize">The file size in bytes</param>
        /// <param name="allowedTypes">Allowed MIME types</param>
        /// <param name="maxSizeBytes">Maximum file size in bytes</param>
        /// <returns>Validation result</returns>
        FileValidationResult ValidateFile(string fileName, string contentType, long fileSize, string[] allowedTypes, long maxSizeBytes);
    }
}
