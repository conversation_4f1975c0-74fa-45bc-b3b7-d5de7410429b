{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { FormBuilder } from '@angular/forms';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { Router } from '@angular/router';\nimport { of, throwError } from 'rxjs';\nimport { ProfileEditComponent } from './profile-edit.component';\nimport { ProfileService } from '../../services/profile.service';\nimport { AuthService } from '../../../auth/services/auth.service';\ndescribe('ProfileEditComponent', () => {\n  let component;\n  let mockProfileService;\n  let mockAuthService;\n  let mockSnackBar;\n  let mockRouter;\n  let formBuilder;\n  const mockProfile = {\n    id: 1,\n    userId: 1,\n    username: 'testuser',\n    slug: 'testuser',\n    firstName: 'Test',\n    lastName: 'User',\n    profilePhotoUrl: 'https://example.com/photo.jpg',\n    coverPhotoUrl: 'https://example.com/cover.jpg',\n    professionalTitle: 'Astrologer',\n    headline: 'Professional Astrologer',\n    summary: 'Experienced astrologer with 10 years of practice',\n    isPublic: true,\n    profileCompletionPercentage: 85,\n    location: {\n      city: 'Sofia',\n      state: 'Sofia',\n      country: 'Bulgaria',\n      displayLocation: 'Sofia, Bulgaria'\n    },\n    contactInfo: {\n      email: '<EMAIL>',\n      isEmailPublic: true,\n      website: 'https://example.com',\n      portfolioUrl: 'https://portfolio.example.com',\n      phoneNumbers: [],\n      businessAddress: {\n        street: '123 Test St',\n        city: 'Sofia',\n        state: 'Sofia',\n        postalCode: '1000',\n        country: 'Bulgaria',\n        isPublic: true\n      }\n    },\n    skills: [{\n      id: 1,\n      name: 'Tarot Reading',\n      category: 'Astrology',\n      proficiencyLevel: 'expert',\n      endorsements: 5\n    }],\n    blogPosts: [],\n    achievements: [],\n    certifications: [],\n    experiences: [],\n    portfolioItems: [],\n    consultationRates: {\n      hourlyRate: 50,\n      sessionRate: 80,\n      currency: 'BGN'\n    },\n    serviceOfferings: [{\n      id: 1,\n      name: 'Personal Reading',\n      description: 'Comprehensive personal astrology reading',\n      price: 100,\n      currency: 'BGN',\n      duration: 60,\n      category: 'Reading',\n      isActive: true\n    }],\n    socialLinks: [],\n    profileViews: 0,\n    createdAt: new Date(),\n    updatedAt: new Date()\n  };\n  beforeEach(() => {\n    const profileServiceSpy = jasmine.createSpyObj('ProfileService', ['getCurrentUserProfile', 'updateProfile', 'uploadProfilePhoto', 'uploadCoverPhoto']);\n    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    const snackBarSpy = jasmine.createSpyObj('MatSnackBar', ['open']);\n    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);\n    TestBed.configureTestingModule({\n      providers: [FormBuilder, {\n        provide: ProfileService,\n        useValue: profileServiceSpy\n      }, {\n        provide: AuthService,\n        useValue: authServiceSpy\n      }, {\n        provide: MatSnackBar,\n        useValue: snackBarSpy\n      }, {\n        provide: Router,\n        useValue: routerSpy\n      }]\n    });\n    formBuilder = TestBed.inject(FormBuilder);\n    mockProfileService = TestBed.inject(ProfileService);\n    mockAuthService = TestBed.inject(AuthService);\n    mockSnackBar = TestBed.inject(MatSnackBar);\n    mockRouter = TestBed.inject(Router);\n    // Create component manually\n    component = new ProfileEditComponent(formBuilder, mockProfileService, mockAuthService, mockRouter, mockSnackBar);\n    // Setup default mock responses\n    mockProfileService.getCurrentUserProfile.and.returnValue(of(mockProfile));\n    mockProfileService.updateProfile.and.returnValue(of(mockProfile));\n    mockProfileService.uploadProfilePhoto.and.returnValue(of({\n      url: 'https://example.com/new-photo.jpg'\n    }));\n    mockProfileService.uploadCoverPhoto.and.returnValue(of({\n      url: 'https://example.com/new-cover.jpg'\n    }));\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should load profile on init', () => {\n    component.ngOnInit();\n    expect(mockProfileService.getCurrentUserProfile).toHaveBeenCalled();\n    expect(component.profile).toEqual(mockProfile);\n    expect(component.isLoading).toBeFalse();\n  });\n  it('should populate form with profile data', () => {\n    component.ngOnInit();\n    expect(component.profileForm.get('firstName')?.value).toBe('Test');\n    expect(component.profileForm.get('lastName')?.value).toBe('User');\n    expect(component.profileForm.get('professionalTitle')?.value).toBe('Astrologer');\n    expect(component.profileForm.get('headline')?.value).toBe('Professional Astrologer');\n    expect(component.profileForm.get('summary')?.value).toBe('Experienced astrologer with 10 years of practice');\n    expect(component.profileForm.get('isPublic')?.value).toBe(true);\n  });\n  it('should populate consultation rates', () => {\n    component.ngOnInit();\n    const consultationRates = component.profileForm.get('consultationRates');\n    expect(consultationRates?.get('hourlyRate')?.value).toBe(50);\n    expect(consultationRates?.get('sessionRate')?.value).toBe(80);\n    expect(consultationRates?.get('currency')?.value).toBe('BGN');\n  });\n  it('should populate skills array', () => {\n    component.ngOnInit();\n    expect(component.skills.length).toBe(1);\n    const firstSkill = component.skills.at(0);\n    expect(firstSkill?.get('name')?.value).toBe('Tarot Reading');\n    expect(firstSkill?.get('category')?.value).toBe('Astrology');\n    expect(firstSkill?.get('proficiencyLevel')?.value).toBe('expert');\n  });\n  it('should populate service offerings array', () => {\n    component.ngOnInit();\n    expect(component.serviceOfferings.length).toBe(1);\n    const firstService = component.serviceOfferings.at(0);\n    expect(firstService?.get('name')?.value).toBe('Personal Reading');\n    expect(firstService?.get('description')?.value).toBe('Comprehensive personal astrology reading');\n    expect(firstService?.get('price')?.value).toBe(100);\n    expect(firstService?.get('currency')?.value).toBe('BGN');\n    expect(firstService?.get('duration')?.value).toBe(60);\n    expect(firstService?.get('category')?.value).toBe('Reading');\n    expect(firstService?.get('isActive')?.value).toBe(true);\n  });\n  it('should add new skill', () => {\n    component.ngOnInit();\n    const initialSkillsCount = component.skills.length;\n    component.addSkill();\n    expect(component.skills.length).toBe(initialSkillsCount + 1);\n    const newSkill = component.skills.at(component.skills.length - 1);\n    expect(newSkill?.get('name')?.value).toBe('');\n    expect(newSkill?.get('proficiencyLevel')?.value).toBe('intermediate');\n  });\n  it('should remove skill', () => {\n    component.ngOnInit();\n    component.addSkill(); // Add a skill first\n    const skillsCount = component.skills.length;\n    component.removeSkill(0);\n    expect(component.skills.length).toBe(skillsCount - 1);\n  });\n  it('should add new service offering', () => {\n    component.ngOnInit();\n    const initialServicesCount = component.serviceOfferings.length;\n    component.addServiceOffering();\n    expect(component.serviceOfferings.length).toBe(initialServicesCount + 1);\n    const newService = component.serviceOfferings.at(component.serviceOfferings.length - 1);\n    expect(newService?.get('name')?.value).toBe('');\n    expect(newService?.get('price')?.value).toBe(0);\n    expect(newService?.get('currency')?.value).toBe('BGN');\n    expect(newService?.get('duration')?.value).toBe(60);\n    expect(newService?.get('isActive')?.value).toBe(true);\n  });\n  it('should remove service offering', () => {\n    component.ngOnInit();\n    component.addServiceOffering(); // Add a service first\n    const servicesCount = component.serviceOfferings.length;\n    component.removeServiceOffering(0);\n    expect(component.serviceOfferings.length).toBe(servicesCount - 1);\n  });\n  it('should validate form before submission', () => {\n    component.ngOnInit();\n    // Make form invalid\n    component.profileForm.get('firstName')?.setValue('');\n    spyOn(component, 'validateForm').and.returnValue(false);\n    component.onSubmit();\n    expect(component.validateForm).toHaveBeenCalled();\n    expect(mockProfileService.updateProfile).not.toHaveBeenCalled();\n  });\n  it('should submit valid form', () => {\n    component.ngOnInit();\n    spyOn(component, 'validateForm').and.returnValue(true);\n    component.onSubmit();\n    expect(component.validateForm).toHaveBeenCalled();\n    expect(mockProfileService.updateProfile).toHaveBeenCalled();\n    expect(component.isSaving).toBeFalse();\n  });\n  it('should handle form submission error', () => {\n    component.ngOnInit();\n    mockProfileService.updateProfile.and.returnValue(throwError(() => new Error('Update failed')));\n    spyOn(component, 'validateForm').and.returnValue(true);\n    component.onSubmit();\n    expect(component.isSaving).toBeFalse();\n  });\n  it('should validate image file correctly', () => {\n    const validFile = new File([''], 'test.jpg', {\n      type: 'image/jpeg'\n    });\n    Object.defineProperty(validFile, 'size', {\n      value: 1024 * 1024\n    }); // 1MB\n    const result = component['validateImageFile'](validFile, 'profile');\n    expect(result).toBe(true);\n  });\n  it('should reject invalid image file type', () => {\n    const invalidFile = new File([''], 'test.txt', {\n      type: 'text/plain'\n    });\n    Object.defineProperty(invalidFile, 'size', {\n      value: 1024\n    }); // 1KB\n    const result = component['validateImageFile'](invalidFile, 'profile');\n    expect(result).toBe(false);\n  });\n  it('should reject oversized image file', () => {\n    const oversizedFile = new File([''], 'test.jpg', {\n      type: 'image/jpeg'\n    });\n    Object.defineProperty(oversizedFile, 'size', {\n      value: 10 * 1024 * 1024\n    }); // 10MB\n    const result = component['validateImageFile'](oversizedFile, 'profile');\n    expect(result).toBe(false);\n  });\n  it('should handle profile photo upload', () => {\n    const file = new File([''], 'test.jpg', {\n      type: 'image/jpeg'\n    });\n    Object.defineProperty(file, 'size', {\n      value: 1024 * 1024\n    }); // 1MB\n    component.ngOnInit();\n    spyOn(component, 'validateImageFile').and.returnValue(true);\n    spyOn(component, 'createImagePreview');\n    const event = {\n      target: {\n        files: [file],\n        value: ''\n      }\n    };\n    component.onProfilePhotoSelected(event);\n    expect(component['validateImageFile']).toHaveBeenCalledWith(file, 'profile');\n    expect(component['createImagePreview']).toHaveBeenCalledWith(file, 'profile');\n    expect(mockProfileService.uploadProfilePhoto).toHaveBeenCalledWith(file);\n  });\n  it('should handle cover photo upload', () => {\n    const file = new File([''], 'test.jpg', {\n      type: 'image/jpeg'\n    });\n    Object.defineProperty(file, 'size', {\n      value: 2 * 1024 * 1024\n    }); // 2MB\n    component.ngOnInit();\n    spyOn(component, 'validateImageFile').and.returnValue(true);\n    spyOn(component, 'createImagePreview');\n    const event = {\n      target: {\n        files: [file],\n        value: ''\n      }\n    };\n    component.onCoverPhotoSelected(event);\n    expect(component['validateImageFile']).toHaveBeenCalledWith(file, 'cover');\n    expect(component['createImagePreview']).toHaveBeenCalledWith(file, 'cover');\n    expect(mockProfileService.uploadCoverPhoto).toHaveBeenCalledWith(file);\n  });\n  it('should provide skill category options', () => {\n    const options = component.getSkillCategoryOptions();\n    expect(options.length).toBeGreaterThan(0);\n    expect(options.some(opt => opt.value === 'Astrology')).toBe(true);\n    expect(options.some(opt => opt.value === 'Tarot Reading')).toBe(true);\n  });\n  it('should provide proficiency level options', () => {\n    const options = component.getProficiencyLevelOptions();\n    expect(options.length).toBe(4);\n    expect(options.some(opt => opt.value === 'beginner')).toBe(true);\n    expect(options.some(opt => opt.value === 'expert')).toBe(true);\n  });\n  it('should provide service category options', () => {\n    const options = component.getServiceCategoryOptions();\n    expect(options.length).toBeGreaterThan(0);\n    expect(options.some(opt => opt.value === 'Reading')).toBe(true);\n    expect(options.some(opt => opt.value === 'Consultation')).toBe(true);\n  });\n  it('should provide currency options', () => {\n    const options = component.getCurrencyOptions();\n    expect(options.length).toBeGreaterThan(0);\n    expect(options.some(opt => opt.value === 'BGN')).toBe(true);\n    expect(options.some(opt => opt.value === 'EUR')).toBe(true);\n  });\n  it('should handle loading error', () => {\n    mockProfileService.getCurrentUserProfile.and.returnValue(throwError(() => new Error('Load failed')));\n    component.ngOnInit();\n    expect(component.isLoading).toBeFalse();\n  });\n  // Backend Integration Tests\n  describe('Backend Integration Verification', () => {\n    it('should include skills, consultation rates, and service offerings in update request', () => {\n      component.ngOnInit();\n      // Setup form with all data including new fields\n      component.profileForm.patchValue({\n        firstName: 'John',\n        lastName: 'Doe',\n        professionalTitle: 'Senior Astrologer',\n        consultationRates: {\n          hourlyRate: 75,\n          sessionRate: 120,\n          currency: 'BGN'\n        }\n      });\n      // Add skills\n      component.addSkill();\n      const skillsArray = component.skills;\n      skillsArray.at(skillsArray.length - 1)?.patchValue({\n        name: 'Crystal Healing',\n        category: 'Energy Work',\n        proficiencyLevel: 'advanced'\n      });\n      // Add service offerings\n      component.addServiceOffering();\n      const servicesArray = component.serviceOfferings;\n      servicesArray.at(servicesArray.length - 1)?.patchValue({\n        name: 'Relationship Reading',\n        description: 'Detailed relationship compatibility analysis',\n        price: 150,\n        currency: 'BGN',\n        duration: 90,\n        category: 'Consultation',\n        isActive: true\n      });\n      // Mock successful update\n      mockProfileService.updateProfile.and.returnValue(of(mockProfile));\n      // Submit form\n      spyOn(component, 'validateForm').and.returnValue(true);\n      component.onSubmit();\n      // Verify the update request includes all new fields\n      expect(mockProfileService.updateProfile).toHaveBeenCalled();\n      const updateCall = mockProfileService.updateProfile.calls.mostRecent();\n      const updateRequest = updateCall.args[0];\n      // Verify skills are included\n      expect(updateRequest.skills).toBeDefined();\n      expect(updateRequest.skills?.length).toBeGreaterThan(0);\n      expect(updateRequest.skills?.some(skill => skill.name === 'Crystal Healing')).toBe(true);\n      // Verify consultation rates are included\n      expect(updateRequest.consultationRates).toBeDefined();\n      expect(updateRequest.consultationRates?.hourlyRate).toBe(75);\n      expect(updateRequest.consultationRates?.sessionRate).toBe(120);\n      expect(updateRequest.consultationRates?.currency).toBe('BGN');\n      // Verify service offerings are included\n      expect(updateRequest.serviceOfferings).toBeDefined();\n      expect(updateRequest.serviceOfferings?.length).toBeGreaterThan(0);\n      expect(updateRequest.serviceOfferings?.some(service => service.name === 'Relationship Reading')).toBe(true);\n    });\n    it('should verify that backend UpdateProfileRequest model needs to be updated', () => {\n      // This test documents the current backend limitation\n      // The backend UpdateProfileRequest model in ProfileModels.cs is missing:\n      // - skills: List<ProfileSkillDto>\n      // - consultationRates: ConsultationRatesDto\n      // - serviceOfferings: List<ServiceOfferingDto>\n      // The frontend is sending these fields but the backend cannot receive them\n      // This test serves as documentation of the integration gap\n      expect(true).toBe(true); // Placeholder - this documents the issue\n    });\n  });\n});", "map": {"version": 3, "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AAErC,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,WAAW,QAAQ,qCAAqC;AAGjEC,QAAQ,CAAC,sBAAsB,EAAE,MAAK;EACpC,IAAIC,SAA+B;EACnC,IAAIC,kBAAkD;EACtD,IAAIC,eAA4C;EAChD,IAAIC,YAAyC;EAC7C,IAAIC,UAAkC;EACtC,IAAIC,WAAwB;EAE5B,MAAMC,WAAW,GAAgB;IAC/BC,EAAE,EAAE,CAAC;IACLC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,MAAM;IAChBC,eAAe,EAAE,+BAA+B;IAChDC,aAAa,EAAE,+BAA+B;IAC9CC,iBAAiB,EAAE,YAAY;IAC/BC,QAAQ,EAAE,yBAAyB;IACnCC,OAAO,EAAE,kDAAkD;IAC3DC,QAAQ,EAAE,IAAI;IACdC,2BAA2B,EAAE,EAAE;IAC/BC,QAAQ,EAAE;MACRC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,UAAU;MACnBC,eAAe,EAAE;KAClB;IACDC,WAAW,EAAE;MACXC,KAAK,EAAE,kBAAkB;MACzBC,aAAa,EAAE,IAAI;MACnBC,OAAO,EAAE,qBAAqB;MAC9BC,YAAY,EAAE,+BAA+B;MAC7CC,YAAY,EAAE,EAAE;MAChBC,eAAe,EAAE;QACfC,MAAM,EAAE,aAAa;QACrBX,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,OAAO;QACdW,UAAU,EAAE,MAAM;QAClBV,OAAO,EAAE,UAAU;QACnBL,QAAQ,EAAE;;KAEb;IACDgB,MAAM,EAAE,CACN;MACE3B,EAAE,EAAE,CAAC;MACL4B,IAAI,EAAE,eAAe;MACrBC,QAAQ,EAAE,WAAW;MACrBC,gBAAgB,EAAE,QAAQ;MAC1BC,YAAY,EAAE;KACf,CACF;IACDC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE;MACjBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE;KACX;IACDC,gBAAgB,EAAE,CAChB;MACEzC,EAAE,EAAE,CAAC;MACL4B,IAAI,EAAE,kBAAkB;MACxBc,WAAW,EAAE,0CAA0C;MACvDC,KAAK,EAAE,GAAG;MACVH,QAAQ,EAAE,KAAK;MACfI,QAAQ,EAAE,EAAE;MACZf,QAAQ,EAAE,SAAS;MACnBgB,QAAQ,EAAE;KACX,CACF;IACDC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,IAAIC,IAAI,EAAE;IACrBC,SAAS,EAAE,IAAID,IAAI;GACpB;EAEDE,UAAU,CAAC,MAAK;IACd,MAAMC,iBAAiB,GAAGC,OAAO,CAACC,YAAY,CAAC,gBAAgB,EAAE,CAC/D,uBAAuB,EACvB,eAAe,EACf,oBAAoB,EACpB,kBAAkB,CACnB,CAAC;IACF,MAAMC,cAAc,GAAGF,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAC9E,MAAME,WAAW,GAAGH,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,CAAC;IACjE,MAAMG,SAAS,GAAGJ,OAAO,CAACC,YAAY,CAAC,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC;IAE9DvE,OAAO,CAAC2E,sBAAsB,CAAC;MAC7BC,SAAS,EAAE,CACT3E,WAAW,EACX;QAAE4E,OAAO,EAAEtE,cAAc;QAAEuE,QAAQ,EAAET;MAAiB,CAAE,EACxD;QAAEQ,OAAO,EAAErE,WAAW;QAAEsE,QAAQ,EAAEN;MAAc,CAAE,EAClD;QAAEK,OAAO,EAAE3E,WAAW;QAAE4E,QAAQ,EAAEL;MAAW,CAAE,EAC/C;QAAEI,OAAO,EAAE1E,MAAM;QAAE2E,QAAQ,EAAEJ;MAAS,CAAE;KAE3C,CAAC;IAEF3D,WAAW,GAAGf,OAAO,CAAC+E,MAAM,CAAC9E,WAAW,CAAC;IACzCU,kBAAkB,GAAGX,OAAO,CAAC+E,MAAM,CAACxE,cAAc,CAAmC;IACrFK,eAAe,GAAGZ,OAAO,CAAC+E,MAAM,CAACvE,WAAW,CAAgC;IAC5EK,YAAY,GAAGb,OAAO,CAAC+E,MAAM,CAAC7E,WAAW,CAAgC;IACzEY,UAAU,GAAGd,OAAO,CAAC+E,MAAM,CAAC5E,MAAM,CAA2B;IAE7D;IACAO,SAAS,GAAG,IAAIJ,oBAAoB,CAClCS,WAAW,EACXJ,kBAAkB,EAClBC,eAAe,EACfE,UAAU,EACVD,YAAY,CACb;IAED;IACAF,kBAAkB,CAACqE,qBAAqB,CAACC,GAAG,CAACC,WAAW,CAAC9E,EAAE,CAACY,WAAW,CAAC,CAAC;IACzEL,kBAAkB,CAACwE,aAAa,CAACF,GAAG,CAACC,WAAW,CAAC9E,EAAE,CAACY,WAAW,CAAC,CAAC;IACjEL,kBAAkB,CAACyE,kBAAkB,CAACH,GAAG,CAACC,WAAW,CAAC9E,EAAE,CAAC;MAAEiF,GAAG,EAAE;IAAmC,CAAE,CAAC,CAAC;IACvG1E,kBAAkB,CAAC2E,gBAAgB,CAACL,GAAG,CAACC,WAAW,CAAC9E,EAAE,CAAC;MAAEiF,GAAG,EAAE;IAAmC,CAAE,CAAC,CAAC;EACvG,CAAC,CAAC;EAEFE,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAC9E,SAAS,CAAC,CAAC+E,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,6BAA6B,EAAE,MAAK;IACrC7E,SAAS,CAACgF,QAAQ,EAAE;IACpBF,MAAM,CAAC7E,kBAAkB,CAACqE,qBAAqB,CAAC,CAACW,gBAAgB,EAAE;IACnEH,MAAM,CAAC9E,SAAS,CAACkF,OAAO,CAAC,CAACC,OAAO,CAAC7E,WAAW,CAAC;IAC9CwE,MAAM,CAAC9E,SAAS,CAACoF,SAAS,CAAC,CAACC,SAAS,EAAE;EACzC,CAAC,CAAC;EAEFR,EAAE,CAAC,wCAAwC,EAAE,MAAK;IAChD7E,SAAS,CAACgF,QAAQ,EAAE;IAEpBF,MAAM,CAAC9E,SAAS,CAACsF,WAAW,CAACC,GAAG,CAAC,WAAW,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;IAClEX,MAAM,CAAC9E,SAAS,CAACsF,WAAW,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;IACjEX,MAAM,CAAC9E,SAAS,CAACsF,WAAW,CAACC,GAAG,CAAC,mBAAmB,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,YAAY,CAAC;IAChFX,MAAM,CAAC9E,SAAS,CAACsF,WAAW,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,yBAAyB,CAAC;IACpFX,MAAM,CAAC9E,SAAS,CAACsF,WAAW,CAACC,GAAG,CAAC,SAAS,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,kDAAkD,CAAC;IAC5GX,MAAM,CAAC9E,SAAS,CAACsF,WAAW,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACjE,CAAC,CAAC;EAEFZ,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5C7E,SAAS,CAACgF,QAAQ,EAAE;IAEpB,MAAMpC,iBAAiB,GAAG5C,SAAS,CAACsF,WAAW,CAACC,GAAG,CAAC,mBAAmB,CAAC;IACxET,MAAM,CAAClC,iBAAiB,EAAE2C,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;IAC5DX,MAAM,CAAClC,iBAAiB,EAAE2C,GAAG,CAAC,aAAa,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;IAC7DX,MAAM,CAAClC,iBAAiB,EAAE2C,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;EAC/D,CAAC,CAAC;EAEFZ,EAAE,CAAC,8BAA8B,EAAE,MAAK;IACtC7E,SAAS,CAACgF,QAAQ,EAAE;IAEpBF,MAAM,CAAC9E,SAAS,CAACkC,MAAM,CAACwD,MAAM,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC;IACvC,MAAME,UAAU,GAAG3F,SAAS,CAACkC,MAAM,CAAC0D,EAAE,CAAC,CAAC,CAAC;IACzCd,MAAM,CAACa,UAAU,EAAEJ,GAAG,CAAC,MAAM,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,eAAe,CAAC;IAC5DX,MAAM,CAACa,UAAU,EAAEJ,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,WAAW,CAAC;IAC5DX,MAAM,CAACa,UAAU,EAAEJ,GAAG,CAAC,kBAAkB,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;EACnE,CAAC,CAAC;EAEFZ,EAAE,CAAC,yCAAyC,EAAE,MAAK;IACjD7E,SAAS,CAACgF,QAAQ,EAAE;IAEpBF,MAAM,CAAC9E,SAAS,CAACgD,gBAAgB,CAAC0C,MAAM,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC;IACjD,MAAMI,YAAY,GAAG7F,SAAS,CAACgD,gBAAgB,CAAC4C,EAAE,CAAC,CAAC,CAAC;IACrDd,MAAM,CAACe,YAAY,EAAEN,GAAG,CAAC,MAAM,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,kBAAkB,CAAC;IACjEX,MAAM,CAACe,YAAY,EAAEN,GAAG,CAAC,aAAa,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,0CAA0C,CAAC;IAChGX,MAAM,CAACe,YAAY,EAAEN,GAAG,CAAC,OAAO,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACnDX,MAAM,CAACe,YAAY,EAAEN,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IACxDX,MAAM,CAACe,YAAY,EAAEN,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;IACrDX,MAAM,CAACe,YAAY,EAAEN,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,SAAS,CAAC;IAC5DX,MAAM,CAACe,YAAY,EAAEN,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACzD,CAAC,CAAC;EAEFZ,EAAE,CAAC,sBAAsB,EAAE,MAAK;IAC9B7E,SAAS,CAACgF,QAAQ,EAAE;IAEpB,MAAMc,kBAAkB,GAAG9F,SAAS,CAACkC,MAAM,CAACwD,MAAM;IAClD1F,SAAS,CAAC+F,QAAQ,EAAE;IAEpBjB,MAAM,CAAC9E,SAAS,CAACkC,MAAM,CAACwD,MAAM,CAAC,CAACD,IAAI,CAACK,kBAAkB,GAAG,CAAC,CAAC;IAC5D,MAAME,QAAQ,GAAGhG,SAAS,CAACkC,MAAM,CAAC0D,EAAE,CAAC5F,SAAS,CAACkC,MAAM,CAACwD,MAAM,GAAG,CAAC,CAAC;IACjEZ,MAAM,CAACkB,QAAQ,EAAET,GAAG,CAAC,MAAM,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;IAC7CX,MAAM,CAACkB,QAAQ,EAAET,GAAG,CAAC,kBAAkB,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,cAAc,CAAC;EACvE,CAAC,CAAC;EAEFZ,EAAE,CAAC,qBAAqB,EAAE,MAAK;IAC7B7E,SAAS,CAACgF,QAAQ,EAAE;IAEpBhF,SAAS,CAAC+F,QAAQ,EAAE,CAAC,CAAC;IACtB,MAAME,WAAW,GAAGjG,SAAS,CAACkC,MAAM,CAACwD,MAAM;IAE3C1F,SAAS,CAACkG,WAAW,CAAC,CAAC,CAAC;IACxBpB,MAAM,CAAC9E,SAAS,CAACkC,MAAM,CAACwD,MAAM,CAAC,CAACD,IAAI,CAACQ,WAAW,GAAG,CAAC,CAAC;EACvD,CAAC,CAAC;EAEFpB,EAAE,CAAC,iCAAiC,EAAE,MAAK;IACzC7E,SAAS,CAACgF,QAAQ,EAAE;IAEpB,MAAMmB,oBAAoB,GAAGnG,SAAS,CAACgD,gBAAgB,CAAC0C,MAAM;IAC9D1F,SAAS,CAACoG,kBAAkB,EAAE;IAE9BtB,MAAM,CAAC9E,SAAS,CAACgD,gBAAgB,CAAC0C,MAAM,CAAC,CAACD,IAAI,CAACU,oBAAoB,GAAG,CAAC,CAAC;IACxE,MAAME,UAAU,GAAGrG,SAAS,CAACgD,gBAAgB,CAAC4C,EAAE,CAAC5F,SAAS,CAACgD,gBAAgB,CAAC0C,MAAM,GAAG,CAAC,CAAC;IACvFZ,MAAM,CAACuB,UAAU,EAAEd,GAAG,CAAC,MAAM,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;IAC/CX,MAAM,CAACuB,UAAU,EAAEd,GAAG,CAAC,OAAO,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;IAC/CX,MAAM,CAACuB,UAAU,EAAEd,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IACtDX,MAAM,CAACuB,UAAU,EAAEd,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;IACnDX,MAAM,CAACuB,UAAU,EAAEd,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACvD,CAAC,CAAC;EAEFZ,EAAE,CAAC,gCAAgC,EAAE,MAAK;IACxC7E,SAAS,CAACgF,QAAQ,EAAE;IAEpBhF,SAAS,CAACoG,kBAAkB,EAAE,CAAC,CAAC;IAChC,MAAME,aAAa,GAAGtG,SAAS,CAACgD,gBAAgB,CAAC0C,MAAM;IAEvD1F,SAAS,CAACuG,qBAAqB,CAAC,CAAC,CAAC;IAClCzB,MAAM,CAAC9E,SAAS,CAACgD,gBAAgB,CAAC0C,MAAM,CAAC,CAACD,IAAI,CAACa,aAAa,GAAG,CAAC,CAAC;EACnE,CAAC,CAAC;EAEFzB,EAAE,CAAC,wCAAwC,EAAE,MAAK;IAChD7E,SAAS,CAACgF,QAAQ,EAAE;IAEpB;IACAhF,SAAS,CAACsF,WAAW,CAACC,GAAG,CAAC,WAAW,CAAC,EAAEiB,QAAQ,CAAC,EAAE,CAAC;IAEpDC,KAAK,CAACzG,SAAS,EAAE,cAAc,CAAC,CAACuE,GAAG,CAACC,WAAW,CAAC,KAAK,CAAC;IACvDxE,SAAS,CAAC0G,QAAQ,EAAE;IAEpB5B,MAAM,CAAC9E,SAAS,CAAC2G,YAAY,CAAC,CAAC1B,gBAAgB,EAAE;IACjDH,MAAM,CAAC7E,kBAAkB,CAACwE,aAAa,CAAC,CAACmC,GAAG,CAAC3B,gBAAgB,EAAE;EACjE,CAAC,CAAC;EAEFJ,EAAE,CAAC,0BAA0B,EAAE,MAAK;IAClC7E,SAAS,CAACgF,QAAQ,EAAE;IAEpByB,KAAK,CAACzG,SAAS,EAAE,cAAc,CAAC,CAACuE,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;IACtDxE,SAAS,CAAC0G,QAAQ,EAAE;IAEpB5B,MAAM,CAAC9E,SAAS,CAAC2G,YAAY,CAAC,CAAC1B,gBAAgB,EAAE;IACjDH,MAAM,CAAC7E,kBAAkB,CAACwE,aAAa,CAAC,CAACQ,gBAAgB,EAAE;IAC3DH,MAAM,CAAC9E,SAAS,CAAC6G,QAAQ,CAAC,CAACxB,SAAS,EAAE;EACxC,CAAC,CAAC;EAEFR,EAAE,CAAC,qCAAqC,EAAE,MAAK;IAC7C7E,SAAS,CAACgF,QAAQ,EAAE;IAEpB/E,kBAAkB,CAACwE,aAAa,CAACF,GAAG,CAACC,WAAW,CAAC7E,UAAU,CAAC,MAAM,IAAImH,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;IAC9FL,KAAK,CAACzG,SAAS,EAAE,cAAc,CAAC,CAACuE,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;IAEtDxE,SAAS,CAAC0G,QAAQ,EAAE;IAEpB5B,MAAM,CAAC9E,SAAS,CAAC6G,QAAQ,CAAC,CAACxB,SAAS,EAAE;EACxC,CAAC,CAAC;EAEFR,EAAE,CAAC,sCAAsC,EAAE,MAAK;IAC9C,MAAMkC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;MAAEC,IAAI,EAAE;IAAY,CAAE,CAAC;IACpEC,MAAM,CAACC,cAAc,CAACJ,SAAS,EAAE,MAAM,EAAE;MAAEvB,KAAK,EAAE,IAAI,GAAG;IAAI,CAAE,CAAC,CAAC,CAAC;IAElE,MAAM4B,MAAM,GAAGpH,SAAS,CAAC,mBAAmB,CAAC,CAAC+G,SAAS,EAAE,SAAS,CAAC;IACnEjC,MAAM,CAACsC,MAAM,CAAC,CAAC3B,IAAI,CAAC,IAAI,CAAC;EAC3B,CAAC,CAAC;EAEFZ,EAAE,CAAC,uCAAuC,EAAE,MAAK;IAC/C,MAAMwC,WAAW,GAAG,IAAIL,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;MAAEC,IAAI,EAAE;IAAY,CAAE,CAAC;IACtEC,MAAM,CAACC,cAAc,CAACE,WAAW,EAAE,MAAM,EAAE;MAAE7B,KAAK,EAAE;IAAI,CAAE,CAAC,CAAC,CAAC;IAE7D,MAAM4B,MAAM,GAAGpH,SAAS,CAAC,mBAAmB,CAAC,CAACqH,WAAW,EAAE,SAAS,CAAC;IACrEvC,MAAM,CAACsC,MAAM,CAAC,CAAC3B,IAAI,CAAC,KAAK,CAAC;EAC5B,CAAC,CAAC;EAEFZ,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5C,MAAMyC,aAAa,GAAG,IAAIN,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;MAAEC,IAAI,EAAE;IAAY,CAAE,CAAC;IACxEC,MAAM,CAACC,cAAc,CAACG,aAAa,EAAE,MAAM,EAAE;MAAE9B,KAAK,EAAE,EAAE,GAAG,IAAI,GAAG;IAAI,CAAE,CAAC,CAAC,CAAC;IAE3E,MAAM4B,MAAM,GAAGpH,SAAS,CAAC,mBAAmB,CAAC,CAACsH,aAAa,EAAE,SAAS,CAAC;IACvExC,MAAM,CAACsC,MAAM,CAAC,CAAC3B,IAAI,CAAC,KAAK,CAAC;EAC5B,CAAC,CAAC;EAEFZ,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5C,MAAM0C,IAAI,GAAG,IAAIP,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;MAAEC,IAAI,EAAE;IAAY,CAAE,CAAC;IAC/DC,MAAM,CAACC,cAAc,CAACI,IAAI,EAAE,MAAM,EAAE;MAAE/B,KAAK,EAAE,IAAI,GAAG;IAAI,CAAE,CAAC,CAAC,CAAC;IAE7DxF,SAAS,CAACgF,QAAQ,EAAE;IAEpByB,KAAK,CAACzG,SAAgB,EAAE,mBAAmB,CAAC,CAACuE,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;IAClEiC,KAAK,CAACzG,SAAgB,EAAE,oBAAoB,CAAC;IAE7C,MAAMwH,KAAK,GAAG;MAAEC,MAAM,EAAE;QAAEC,KAAK,EAAE,CAACH,IAAI,CAAC;QAAE/B,KAAK,EAAE;MAAE;IAAE,CAAE;IACtDxF,SAAS,CAAC2H,sBAAsB,CAACH,KAAK,CAAC;IAEvC1C,MAAM,CAAC9E,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAAC4H,oBAAoB,CAACL,IAAI,EAAE,SAAS,CAAC;IAC5EzC,MAAM,CAAC9E,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC4H,oBAAoB,CAACL,IAAI,EAAE,SAAS,CAAC;IAC7EzC,MAAM,CAAC7E,kBAAkB,CAACyE,kBAAkB,CAAC,CAACkD,oBAAoB,CAACL,IAAI,CAAC;EAC1E,CAAC,CAAC;EAEF1C,EAAE,CAAC,kCAAkC,EAAE,MAAK;IAC1C,MAAM0C,IAAI,GAAG,IAAIP,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;MAAEC,IAAI,EAAE;IAAY,CAAE,CAAC;IAC/DC,MAAM,CAACC,cAAc,CAACI,IAAI,EAAE,MAAM,EAAE;MAAE/B,KAAK,EAAE,CAAC,GAAG,IAAI,GAAG;IAAI,CAAE,CAAC,CAAC,CAAC;IAEjExF,SAAS,CAACgF,QAAQ,EAAE;IAEpByB,KAAK,CAACzG,SAAgB,EAAE,mBAAmB,CAAC,CAACuE,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;IAClEiC,KAAK,CAACzG,SAAgB,EAAE,oBAAoB,CAAC;IAE7C,MAAMwH,KAAK,GAAG;MAAEC,MAAM,EAAE;QAAEC,KAAK,EAAE,CAACH,IAAI,CAAC;QAAE/B,KAAK,EAAE;MAAE;IAAE,CAAE;IACtDxF,SAAS,CAAC6H,oBAAoB,CAACL,KAAK,CAAC;IAErC1C,MAAM,CAAC9E,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAAC4H,oBAAoB,CAACL,IAAI,EAAE,OAAO,CAAC;IAC1EzC,MAAM,CAAC9E,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC4H,oBAAoB,CAACL,IAAI,EAAE,OAAO,CAAC;IAC3EzC,MAAM,CAAC7E,kBAAkB,CAAC2E,gBAAgB,CAAC,CAACgD,oBAAoB,CAACL,IAAI,CAAC;EACxE,CAAC,CAAC;EAEF1C,EAAE,CAAC,uCAAuC,EAAE,MAAK;IAC/C,MAAMiD,OAAO,GAAG9H,SAAS,CAAC+H,uBAAuB,EAAE;IACnDjD,MAAM,CAACgD,OAAO,CAACpC,MAAM,CAAC,CAACsC,eAAe,CAAC,CAAC,CAAC;IACzClD,MAAM,CAACgD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACjEX,MAAM,CAACgD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,eAAe,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACvE,CAAC,CAAC;EAEFZ,EAAE,CAAC,0CAA0C,EAAE,MAAK;IAClD,MAAMiD,OAAO,GAAG9H,SAAS,CAACmI,0BAA0B,EAAE;IACtDrD,MAAM,CAACgD,OAAO,CAACpC,MAAM,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC;IAC9BX,MAAM,CAACgD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,UAAU,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IAChEX,MAAM,CAACgD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EAChE,CAAC,CAAC;EAEFZ,EAAE,CAAC,yCAAyC,EAAE,MAAK;IACjD,MAAMiD,OAAO,GAAG9H,SAAS,CAACoI,yBAAyB,EAAE;IACrDtD,MAAM,CAACgD,OAAO,CAACpC,MAAM,CAAC,CAACsC,eAAe,CAAC,CAAC,CAAC;IACzClD,MAAM,CAACgD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,SAAS,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IAC/DX,MAAM,CAACgD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,cAAc,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACtE,CAAC,CAAC;EAEFZ,EAAE,CAAC,iCAAiC,EAAE,MAAK;IACzC,MAAMiD,OAAO,GAAG9H,SAAS,CAACqI,kBAAkB,EAAE;IAC9CvD,MAAM,CAACgD,OAAO,CAACpC,MAAM,CAAC,CAACsC,eAAe,CAAC,CAAC,CAAC;IACzClD,MAAM,CAACgD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IAC3DX,MAAM,CAACgD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EAC7D,CAAC,CAAC;EAEFZ,EAAE,CAAC,6BAA6B,EAAE,MAAK;IACrC5E,kBAAkB,CAACqE,qBAAqB,CAACC,GAAG,CAACC,WAAW,CAAC7E,UAAU,CAAC,MAAM,IAAImH,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;IAEpG9G,SAAS,CAACgF,QAAQ,EAAE;IAEpBF,MAAM,CAAC9E,SAAS,CAACoF,SAAS,CAAC,CAACC,SAAS,EAAE;EACzC,CAAC,CAAC;EAEF;EACAtF,QAAQ,CAAC,kCAAkC,EAAE,MAAK;IAChD8E,EAAE,CAAC,oFAAoF,EAAE,MAAK;MAC5F7E,SAAS,CAACgF,QAAQ,EAAE;MAEpB;MACAhF,SAAS,CAACsF,WAAW,CAACgD,UAAU,CAAC;QAC/B3H,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,KAAK;QACfG,iBAAiB,EAAE,mBAAmB;QACtC6B,iBAAiB,EAAE;UACjBC,UAAU,EAAE,EAAE;UACdC,WAAW,EAAE,GAAG;UAChBC,QAAQ,EAAE;;OAEb,CAAC;MAEF;MACA/C,SAAS,CAAC+F,QAAQ,EAAE;MACpB,MAAMwC,WAAW,GAAGvI,SAAS,CAACkC,MAAM;MACpCqG,WAAW,CAAC3C,EAAE,CAAC2C,WAAW,CAAC7C,MAAM,GAAG,CAAC,CAAC,EAAE4C,UAAU,CAAC;QACjDnG,IAAI,EAAE,iBAAiB;QACvBC,QAAQ,EAAE,aAAa;QACvBC,gBAAgB,EAAE;OACnB,CAAC;MAEF;MACArC,SAAS,CAACoG,kBAAkB,EAAE;MAC9B,MAAMoC,aAAa,GAAGxI,SAAS,CAACgD,gBAAgB;MAChDwF,aAAa,CAAC5C,EAAE,CAAC4C,aAAa,CAAC9C,MAAM,GAAG,CAAC,CAAC,EAAE4C,UAAU,CAAC;QACrDnG,IAAI,EAAE,sBAAsB;QAC5Bc,WAAW,EAAE,8CAA8C;QAC3DC,KAAK,EAAE,GAAG;QACVH,QAAQ,EAAE,KAAK;QACfI,QAAQ,EAAE,EAAE;QACZf,QAAQ,EAAE,cAAc;QACxBgB,QAAQ,EAAE;OACX,CAAC;MAEF;MACAnD,kBAAkB,CAACwE,aAAa,CAACF,GAAG,CAACC,WAAW,CAAC9E,EAAE,CAACY,WAAW,CAAC,CAAC;MAEjE;MACAmG,KAAK,CAACzG,SAAS,EAAE,cAAc,CAAC,CAACuE,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;MACtDxE,SAAS,CAAC0G,QAAQ,EAAE;MAEpB;MACA5B,MAAM,CAAC7E,kBAAkB,CAACwE,aAAa,CAAC,CAACQ,gBAAgB,EAAE;MAC3D,MAAMwD,UAAU,GAAGxI,kBAAkB,CAACwE,aAAa,CAACiE,KAAK,CAACC,UAAU,EAAE;MACtE,MAAMC,aAAa,GAAGH,UAAU,CAACI,IAAI,CAAC,CAAC,CAAC;MAExC;MACA/D,MAAM,CAAC8D,aAAa,CAAC1G,MAAM,CAAC,CAAC4G,WAAW,EAAE;MAC1ChE,MAAM,CAAC8D,aAAa,CAAC1G,MAAM,EAAEwD,MAAM,CAAC,CAACsC,eAAe,CAAC,CAAC,CAAC;MACvDlD,MAAM,CAAC8D,aAAa,CAAC1G,MAAM,EAAE+F,IAAI,CAAEc,KAAU,IAAKA,KAAK,CAAC5G,IAAI,KAAK,iBAAiB,CAAC,CAAC,CAACsD,IAAI,CAAC,IAAI,CAAC;MAE/F;MACAX,MAAM,CAAC8D,aAAa,CAAChG,iBAAiB,CAAC,CAACkG,WAAW,EAAE;MACrDhE,MAAM,CAAC8D,aAAa,CAAChG,iBAAiB,EAAEC,UAAU,CAAC,CAAC4C,IAAI,CAAC,EAAE,CAAC;MAC5DX,MAAM,CAAC8D,aAAa,CAAChG,iBAAiB,EAAEE,WAAW,CAAC,CAAC2C,IAAI,CAAC,GAAG,CAAC;MAC9DX,MAAM,CAAC8D,aAAa,CAAChG,iBAAiB,EAAEG,QAAQ,CAAC,CAAC0C,IAAI,CAAC,KAAK,CAAC;MAE7D;MACAX,MAAM,CAAC8D,aAAa,CAAC5F,gBAAgB,CAAC,CAAC8F,WAAW,EAAE;MACpDhE,MAAM,CAAC8D,aAAa,CAAC5F,gBAAgB,EAAE0C,MAAM,CAAC,CAACsC,eAAe,CAAC,CAAC,CAAC;MACjElD,MAAM,CAAC8D,aAAa,CAAC5F,gBAAgB,EAAEiF,IAAI,CAAEe,OAAY,IAAKA,OAAO,CAAC7G,IAAI,KAAK,sBAAsB,CAAC,CAAC,CAACsD,IAAI,CAAC,IAAI,CAAC;IACpH,CAAC,CAAC;IAEFZ,EAAE,CAAC,2EAA2E,EAAE,MAAK;MACnF;MACA;MACA;MACA;MACA;MAEA;MACA;MAEAC,MAAM,CAAC,IAAI,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;AAGJ,CAAC,CAAC", "names": ["TestBed", "FormBuilder", "MatSnackBar", "Router", "of", "throwError", "ProfileEditComponent", "ProfileService", "AuthService", "describe", "component", "mockProfileService", "mockAuthService", "mockSnackBar", "mockRouter", "formBuilder", "mockProfile", "id", "userId", "username", "slug", "firstName", "lastName", "profilePhotoUrl", "coverPhotoUrl", "professional<PERSON>itle", "headline", "summary", "isPublic", "profileCompletionPercentage", "location", "city", "state", "country", "displayLocation", "contactInfo", "email", "isEmailPublic", "website", "portfolioUrl", "phoneNumbers", "businessAddress", "street", "postalCode", "skills", "name", "category", "proficiencyLevel", "endorsements", "blogPosts", "achievements", "certifications", "experiences", "portfolioItems", "consultationRates", "hourlyRate", "sessionRate", "currency", "serviceOfferings", "description", "price", "duration", "isActive", "socialLinks", "profileViews", "createdAt", "Date", "updatedAt", "beforeEach", "profileServiceSpy", "jasmine", "createSpyObj", "authServiceSpy", "snackBarSpy", "routerSpy", "configureTestingModule", "providers", "provide", "useValue", "inject", "getCurrentUserProfile", "and", "returnValue", "updateProfile", "uploadProfilePhoto", "url", "uploadCoverPhoto", "it", "expect", "toBeTruthy", "ngOnInit", "toHaveBeenCalled", "profile", "toEqual", "isLoading", "toBeFalse", "profileForm", "get", "value", "toBe", "length", "firstSkill", "at", "firstService", "initialSkillsCount", "addSkill", "newSkill", "skillsCount", "removeSkill", "initialServicesCount", "addServiceOffering", "newService", "servicesCount", "removeServiceOffering", "setValue", "spyOn", "onSubmit", "validateForm", "not", "isSaving", "Error", "validFile", "File", "type", "Object", "defineProperty", "result", "invalidFile", "oversizedFile", "file", "event", "target", "files", "onProfilePhotoSelected", "toHaveBeenCalledWith", "onCoverPhotoSelected", "options", "getSkillCategoryOptions", "toBeGreaterThan", "some", "opt", "getProficiencyLevelOptions", "getServiceCategoryOptions", "getCurrencyOptions", "patchValue", "skillsArray", "servicesArray", "updateCall", "calls", "mostRecent", "updateRequest", "args", "toBeDefined", "skill", "service"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\profile-edit\\profile-edit.component.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\nimport { FormBuilder } from '@angular/forms';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { Router } from '@angular/router';\nimport { of, throwError } from 'rxjs';\n\nimport { ProfileEditComponent } from './profile-edit.component';\nimport { ProfileService } from '../../services/profile.service';\nimport { AuthService } from '../../../auth/services/auth.service';\nimport { UserProfile, ProfileSkill, ConsultationRates, ServiceOffering } from '../../models/profile.models';\n\ndescribe('ProfileEditComponent', () => {\n  let component: ProfileEditComponent;\n  let mockProfileService: jasmine.SpyObj<ProfileService>;\n  let mockAuthService: jasmine.SpyObj<AuthService>;\n  let mockSnackBar: jasmine.SpyObj<MatSnackBar>;\n  let mockRouter: jasmine.SpyObj<Router>;\n  let formBuilder: FormBuilder;\n\n  const mockProfile: UserProfile = {\n    id: 1,\n    userId: 1,\n    username: 'testuser',\n    slug: 'testuser',\n    firstName: 'Test',\n    lastName: 'User',\n    profilePhotoUrl: 'https://example.com/photo.jpg',\n    coverPhotoUrl: 'https://example.com/cover.jpg',\n    professionalTitle: 'Astrologer',\n    headline: 'Professional Astrologer',\n    summary: 'Experienced astrologer with 10 years of practice',\n    isPublic: true,\n    profileCompletionPercentage: 85,\n    location: {\n      city: 'Sofia',\n      state: 'Sofia',\n      country: 'Bulgaria',\n      displayLocation: 'Sofia, Bulgaria'\n    },\n    contactInfo: {\n      email: '<EMAIL>',\n      isEmailPublic: true,\n      website: 'https://example.com',\n      portfolioUrl: 'https://portfolio.example.com',\n      phoneNumbers: [],\n      businessAddress: {\n        street: '123 Test St',\n        city: 'Sofia',\n        state: 'Sofia',\n        postalCode: '1000',\n        country: 'Bulgaria',\n        isPublic: true\n      }\n    },\n    skills: [\n      {\n        id: 1,\n        name: 'Tarot Reading',\n        category: 'Astrology',\n        proficiencyLevel: 'expert',\n        endorsements: 5\n      }\n    ],\n    blogPosts: [],\n    achievements: [],\n    certifications: [],\n    experiences: [],\n    portfolioItems: [],\n    consultationRates: {\n      hourlyRate: 50,\n      sessionRate: 80,\n      currency: 'BGN'\n    },\n    serviceOfferings: [\n      {\n        id: 1,\n        name: 'Personal Reading',\n        description: 'Comprehensive personal astrology reading',\n        price: 100,\n        currency: 'BGN',\n        duration: 60,\n        category: 'Reading',\n        isActive: true\n      }\n    ],\n    socialLinks: [],\n    profileViews: 0,\n    createdAt: new Date(),\n    updatedAt: new Date()\n  };\n\n  beforeEach(() => {\n    const profileServiceSpy = jasmine.createSpyObj('ProfileService', [\n      'getCurrentUserProfile',\n      'updateProfile',\n      'uploadProfilePhoto',\n      'uploadCoverPhoto'\n    ]);\n    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    const snackBarSpy = jasmine.createSpyObj('MatSnackBar', ['open']);\n    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);\n\n    TestBed.configureTestingModule({\n      providers: [\n        FormBuilder,\n        { provide: ProfileService, useValue: profileServiceSpy },\n        { provide: AuthService, useValue: authServiceSpy },\n        { provide: MatSnackBar, useValue: snackBarSpy },\n        { provide: Router, useValue: routerSpy }\n      ]\n    });\n\n    formBuilder = TestBed.inject(FormBuilder);\n    mockProfileService = TestBed.inject(ProfileService) as jasmine.SpyObj<ProfileService>;\n    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;\n    mockSnackBar = TestBed.inject(MatSnackBar) as jasmine.SpyObj<MatSnackBar>;\n    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;\n\n    // Create component manually\n    component = new ProfileEditComponent(\n      formBuilder,\n      mockProfileService,\n      mockAuthService,\n      mockRouter,\n      mockSnackBar\n    );\n\n    // Setup default mock responses\n    mockProfileService.getCurrentUserProfile.and.returnValue(of(mockProfile));\n    mockProfileService.updateProfile.and.returnValue(of(mockProfile));\n    mockProfileService.uploadProfilePhoto.and.returnValue(of({ url: 'https://example.com/new-photo.jpg' }));\n    mockProfileService.uploadCoverPhoto.and.returnValue(of({ url: 'https://example.com/new-cover.jpg' }));\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should load profile on init', () => {\n    component.ngOnInit();\n    expect(mockProfileService.getCurrentUserProfile).toHaveBeenCalled();\n    expect(component.profile).toEqual(mockProfile);\n    expect(component.isLoading).toBeFalse();\n  });\n\n  it('should populate form with profile data', () => {\n    component.ngOnInit();\n\n    expect(component.profileForm.get('firstName')?.value).toBe('Test');\n    expect(component.profileForm.get('lastName')?.value).toBe('User');\n    expect(component.profileForm.get('professionalTitle')?.value).toBe('Astrologer');\n    expect(component.profileForm.get('headline')?.value).toBe('Professional Astrologer');\n    expect(component.profileForm.get('summary')?.value).toBe('Experienced astrologer with 10 years of practice');\n    expect(component.profileForm.get('isPublic')?.value).toBe(true);\n  });\n\n  it('should populate consultation rates', () => {\n    component.ngOnInit();\n\n    const consultationRates = component.profileForm.get('consultationRates');\n    expect(consultationRates?.get('hourlyRate')?.value).toBe(50);\n    expect(consultationRates?.get('sessionRate')?.value).toBe(80);\n    expect(consultationRates?.get('currency')?.value).toBe('BGN');\n  });\n\n  it('should populate skills array', () => {\n    component.ngOnInit();\n\n    expect(component.skills.length).toBe(1);\n    const firstSkill = component.skills.at(0);\n    expect(firstSkill?.get('name')?.value).toBe('Tarot Reading');\n    expect(firstSkill?.get('category')?.value).toBe('Astrology');\n    expect(firstSkill?.get('proficiencyLevel')?.value).toBe('expert');\n  });\n\n  it('should populate service offerings array', () => {\n    component.ngOnInit();\n\n    expect(component.serviceOfferings.length).toBe(1);\n    const firstService = component.serviceOfferings.at(0);\n    expect(firstService?.get('name')?.value).toBe('Personal Reading');\n    expect(firstService?.get('description')?.value).toBe('Comprehensive personal astrology reading');\n    expect(firstService?.get('price')?.value).toBe(100);\n    expect(firstService?.get('currency')?.value).toBe('BGN');\n    expect(firstService?.get('duration')?.value).toBe(60);\n    expect(firstService?.get('category')?.value).toBe('Reading');\n    expect(firstService?.get('isActive')?.value).toBe(true);\n  });\n\n  it('should add new skill', () => {\n    component.ngOnInit();\n\n    const initialSkillsCount = component.skills.length;\n    component.addSkill();\n\n    expect(component.skills.length).toBe(initialSkillsCount + 1);\n    const newSkill = component.skills.at(component.skills.length - 1);\n    expect(newSkill?.get('name')?.value).toBe('');\n    expect(newSkill?.get('proficiencyLevel')?.value).toBe('intermediate');\n  });\n\n  it('should remove skill', () => {\n    component.ngOnInit();\n\n    component.addSkill(); // Add a skill first\n    const skillsCount = component.skills.length;\n\n    component.removeSkill(0);\n    expect(component.skills.length).toBe(skillsCount - 1);\n  });\n\n  it('should add new service offering', () => {\n    component.ngOnInit();\n\n    const initialServicesCount = component.serviceOfferings.length;\n    component.addServiceOffering();\n\n    expect(component.serviceOfferings.length).toBe(initialServicesCount + 1);\n    const newService = component.serviceOfferings.at(component.serviceOfferings.length - 1);\n    expect(newService?.get('name')?.value).toBe('');\n    expect(newService?.get('price')?.value).toBe(0);\n    expect(newService?.get('currency')?.value).toBe('BGN');\n    expect(newService?.get('duration')?.value).toBe(60);\n    expect(newService?.get('isActive')?.value).toBe(true);\n  });\n\n  it('should remove service offering', () => {\n    component.ngOnInit();\n\n    component.addServiceOffering(); // Add a service first\n    const servicesCount = component.serviceOfferings.length;\n\n    component.removeServiceOffering(0);\n    expect(component.serviceOfferings.length).toBe(servicesCount - 1);\n  });\n\n  it('should validate form before submission', () => {\n    component.ngOnInit();\n\n    // Make form invalid\n    component.profileForm.get('firstName')?.setValue('');\n\n    spyOn(component, 'validateForm').and.returnValue(false);\n    component.onSubmit();\n\n    expect(component.validateForm).toHaveBeenCalled();\n    expect(mockProfileService.updateProfile).not.toHaveBeenCalled();\n  });\n\n  it('should submit valid form', () => {\n    component.ngOnInit();\n\n    spyOn(component, 'validateForm').and.returnValue(true);\n    component.onSubmit();\n\n    expect(component.validateForm).toHaveBeenCalled();\n    expect(mockProfileService.updateProfile).toHaveBeenCalled();\n    expect(component.isSaving).toBeFalse();\n  });\n\n  it('should handle form submission error', () => {\n    component.ngOnInit();\n\n    mockProfileService.updateProfile.and.returnValue(throwError(() => new Error('Update failed')));\n    spyOn(component, 'validateForm').and.returnValue(true);\n\n    component.onSubmit();\n\n    expect(component.isSaving).toBeFalse();\n  });\n\n  it('should validate image file correctly', () => {\n    const validFile = new File([''], 'test.jpg', { type: 'image/jpeg' });\n    Object.defineProperty(validFile, 'size', { value: 1024 * 1024 }); // 1MB\n\n    const result = component['validateImageFile'](validFile, 'profile');\n    expect(result).toBe(true);\n  });\n\n  it('should reject invalid image file type', () => {\n    const invalidFile = new File([''], 'test.txt', { type: 'text/plain' });\n    Object.defineProperty(invalidFile, 'size', { value: 1024 }); // 1KB\n\n    const result = component['validateImageFile'](invalidFile, 'profile');\n    expect(result).toBe(false);\n  });\n\n  it('should reject oversized image file', () => {\n    const oversizedFile = new File([''], 'test.jpg', { type: 'image/jpeg' });\n    Object.defineProperty(oversizedFile, 'size', { value: 10 * 1024 * 1024 }); // 10MB\n\n    const result = component['validateImageFile'](oversizedFile, 'profile');\n    expect(result).toBe(false);\n  });\n\n  it('should handle profile photo upload', () => {\n    const file = new File([''], 'test.jpg', { type: 'image/jpeg' });\n    Object.defineProperty(file, 'size', { value: 1024 * 1024 }); // 1MB\n\n    component.ngOnInit();\n\n    spyOn(component as any, 'validateImageFile').and.returnValue(true);\n    spyOn(component as any, 'createImagePreview');\n\n    const event = { target: { files: [file], value: '' } };\n    component.onProfilePhotoSelected(event);\n\n    expect(component['validateImageFile']).toHaveBeenCalledWith(file, 'profile');\n    expect(component['createImagePreview']).toHaveBeenCalledWith(file, 'profile');\n    expect(mockProfileService.uploadProfilePhoto).toHaveBeenCalledWith(file);\n  });\n\n  it('should handle cover photo upload', () => {\n    const file = new File([''], 'test.jpg', { type: 'image/jpeg' });\n    Object.defineProperty(file, 'size', { value: 2 * 1024 * 1024 }); // 2MB\n\n    component.ngOnInit();\n\n    spyOn(component as any, 'validateImageFile').and.returnValue(true);\n    spyOn(component as any, 'createImagePreview');\n\n    const event = { target: { files: [file], value: '' } };\n    component.onCoverPhotoSelected(event);\n\n    expect(component['validateImageFile']).toHaveBeenCalledWith(file, 'cover');\n    expect(component['createImagePreview']).toHaveBeenCalledWith(file, 'cover');\n    expect(mockProfileService.uploadCoverPhoto).toHaveBeenCalledWith(file);\n  });\n\n  it('should provide skill category options', () => {\n    const options = component.getSkillCategoryOptions();\n    expect(options.length).toBeGreaterThan(0);\n    expect(options.some(opt => opt.value === 'Astrology')).toBe(true);\n    expect(options.some(opt => opt.value === 'Tarot Reading')).toBe(true);\n  });\n\n  it('should provide proficiency level options', () => {\n    const options = component.getProficiencyLevelOptions();\n    expect(options.length).toBe(4);\n    expect(options.some(opt => opt.value === 'beginner')).toBe(true);\n    expect(options.some(opt => opt.value === 'expert')).toBe(true);\n  });\n\n  it('should provide service category options', () => {\n    const options = component.getServiceCategoryOptions();\n    expect(options.length).toBeGreaterThan(0);\n    expect(options.some(opt => opt.value === 'Reading')).toBe(true);\n    expect(options.some(opt => opt.value === 'Consultation')).toBe(true);\n  });\n\n  it('should provide currency options', () => {\n    const options = component.getCurrencyOptions();\n    expect(options.length).toBeGreaterThan(0);\n    expect(options.some(opt => opt.value === 'BGN')).toBe(true);\n    expect(options.some(opt => opt.value === 'EUR')).toBe(true);\n  });\n\n  it('should handle loading error', () => {\n    mockProfileService.getCurrentUserProfile.and.returnValue(throwError(() => new Error('Load failed')));\n\n    component.ngOnInit();\n\n    expect(component.isLoading).toBeFalse();\n  });\n\n  // Backend Integration Tests\n  describe('Backend Integration Verification', () => {\n    it('should include skills, consultation rates, and service offerings in update request', () => {\n      component.ngOnInit();\n\n      // Setup form with all data including new fields\n      component.profileForm.patchValue({\n        firstName: 'John',\n        lastName: 'Doe',\n        professionalTitle: 'Senior Astrologer',\n        consultationRates: {\n          hourlyRate: 75,\n          sessionRate: 120,\n          currency: 'BGN'\n        }\n      });\n\n      // Add skills\n      component.addSkill();\n      const skillsArray = component.skills;\n      skillsArray.at(skillsArray.length - 1)?.patchValue({\n        name: 'Crystal Healing',\n        category: 'Energy Work',\n        proficiencyLevel: 'advanced'\n      });\n\n      // Add service offerings\n      component.addServiceOffering();\n      const servicesArray = component.serviceOfferings;\n      servicesArray.at(servicesArray.length - 1)?.patchValue({\n        name: 'Relationship Reading',\n        description: 'Detailed relationship compatibility analysis',\n        price: 150,\n        currency: 'BGN',\n        duration: 90,\n        category: 'Consultation',\n        isActive: true\n      });\n\n      // Mock successful update\n      mockProfileService.updateProfile.and.returnValue(of(mockProfile));\n\n      // Submit form\n      spyOn(component, 'validateForm').and.returnValue(true);\n      component.onSubmit();\n\n      // Verify the update request includes all new fields\n      expect(mockProfileService.updateProfile).toHaveBeenCalled();\n      const updateCall = mockProfileService.updateProfile.calls.mostRecent();\n      const updateRequest = updateCall.args[0];\n\n      // Verify skills are included\n      expect(updateRequest.skills).toBeDefined();\n      expect(updateRequest.skills?.length).toBeGreaterThan(0);\n      expect(updateRequest.skills?.some((skill: any) => skill.name === 'Crystal Healing')).toBe(true);\n\n      // Verify consultation rates are included\n      expect(updateRequest.consultationRates).toBeDefined();\n      expect(updateRequest.consultationRates?.hourlyRate).toBe(75);\n      expect(updateRequest.consultationRates?.sessionRate).toBe(120);\n      expect(updateRequest.consultationRates?.currency).toBe('BGN');\n\n      // Verify service offerings are included\n      expect(updateRequest.serviceOfferings).toBeDefined();\n      expect(updateRequest.serviceOfferings?.length).toBeGreaterThan(0);\n      expect(updateRequest.serviceOfferings?.some((service: any) => service.name === 'Relationship Reading')).toBe(true);\n    });\n\n    it('should verify that backend UpdateProfileRequest model needs to be updated', () => {\n      // This test documents the current backend limitation\n      // The backend UpdateProfileRequest model in ProfileModels.cs is missing:\n      // - skills: List<ProfileSkillDto>\n      // - consultationRates: ConsultationRatesDto\n      // - serviceOfferings: List<ServiceOfferingDto>\n\n      // The frontend is sending these fields but the backend cannot receive them\n      // This test serves as documentation of the integration gap\n\n      expect(true).toBe(true); // Placeholder - this documents the issue\n    });\n  });\n\n\n});\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}