<div class="profile-edit-container" *ngIf="!isLoading">
  <div class="edit-header">
    <h1>
      <mat-icon>edit</mat-icon>
      Edit Profile
    </h1>
    <div class="header-actions">
      <button mat-stroked-button (click)="onCancel()" [disabled]="isSaving">
        Cancel
      </button>
      <button
        mat-raised-button
        color="primary"
        (click)="onSubmit()"
        [disabled]="isSaving || profileForm.invalid">
        <mat-icon *ngIf="isSaving">
          <mat-spinner diameter="20"></mat-spinner>
        </mat-icon>
        <mat-icon *ngIf="!isSaving">save</mat-icon>
        {{ isSaving ? 'Saving...' : 'Save Changes' }}
      </button>
    </div>
  </div>

  <form [formGroup]="profileForm" class="profile-form">
    <!-- Photo Upload Section -->
    <mat-card class="photo-section">
      <mat-card-header>
        <mat-card-title>Profile Photos</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="photo-uploads">
          <div class="profile-photo-upload">
            <div class="photo-preview">
              <img [src]="profilePhotoPreview || profile?.profilePhotoUrl || '/assets/images/default-avatar.png'"
                   alt="Profile Photo" class="profile-photo">
              <div class="photo-overlay" [class.uploading]="isUploadingProfilePhoto">
                <mat-spinner *ngIf="isUploadingProfilePhoto" diameter="24"></mat-spinner>
                <mat-icon *ngIf="!isUploadingProfilePhoto">camera_alt</mat-icon>
              </div>
            </div>
            <input type="file" #profilePhotoInput (change)="onProfilePhotoSelected($event)"
                   accept="image/jpeg,image/jpg,image/png,image/gif,image/webp" style="display: none;">
            <button mat-stroked-button
                    (click)="profilePhotoInput.click()"
                    [disabled]="isUploadingProfilePhoto">
              <mat-icon *ngIf="isUploadingProfilePhoto">
                <mat-spinner diameter="16"></mat-spinner>
              </mat-icon>
              <mat-icon *ngIf="!isUploadingProfilePhoto">photo_camera</mat-icon>
              {{ isUploadingProfilePhoto ? 'Uploading...' : 'Change Profile Photo' }}
            </button>
            <p class="upload-hint">Max 5MB • JPEG, PNG, GIF, WebP</p>
          </div>

          <div class="cover-photo-upload">
            <div class="cover-preview">
              <div class="cover-photo"
                   [style.background-image]="coverPhotoPreview ? ('url(' + coverPhotoPreview + ')') :
                   (profile && profile.coverPhotoUrl) ? ('url(' + profile.coverPhotoUrl + ')') : 'var(--theme-gradient-primary)'">
                <div class="photo-overlay" [class.uploading]="isUploadingCoverPhoto">
                  <mat-spinner *ngIf="isUploadingCoverPhoto" diameter="24"></mat-spinner>
                  <mat-icon *ngIf="!isUploadingCoverPhoto">camera_alt</mat-icon>
                </div>
              </div>
            </div>
            <input type="file" #coverPhotoInput (change)="onCoverPhotoSelected($event)"
                   accept="image/jpeg,image/jpg,image/png,image/gif,image/webp" style="display: none;">
            <button mat-stroked-button
                    (click)="coverPhotoInput.click()"
                    [disabled]="isUploadingCoverPhoto">
              <mat-icon *ngIf="isUploadingCoverPhoto">
                <mat-spinner diameter="16"></mat-spinner>
              </mat-icon>
              <mat-icon *ngIf="!isUploadingCoverPhoto">photo_camera</mat-icon>
              {{ isUploadingCoverPhoto ? 'Uploading...' : 'Change Cover Photo' }}
            </button>
            <p class="upload-hint">Max 10MB • JPEG, PNG, GIF, WebP</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Basic Information -->
    <mat-card class="form-section">
      <mat-card-header>
        <mat-card-title>Basic Information</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>First Name</mat-label>
            <input matInput formControlName="firstName" required>
            <mat-error *ngIf="profileForm.get('firstName')?.invalid && profileForm.get('firstName')?.touched">
              {{ getErrorMessage('firstName') }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Last Name</mat-label>
            <input matInput formControlName="lastName" required>
            <mat-error *ngIf="profileForm.get('lastName')?.invalid && profileForm.get('lastName')?.touched">
              {{ getErrorMessage('lastName') }}
            </mat-error>
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Professional Title</mat-label>
          <input matInput formControlName="professionalTitle"
                 placeholder="e.g., Senior Software Engineer, UX Designer">
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Professional Headline</mat-label>
          <textarea matInput formControlName="headline" rows="2"
                    placeholder="A brief, compelling description of what you do"
                    maxlength="220"></textarea>
          <mat-hint align="end">{{ profileForm.get('headline')?.value?.length || 0 }}/220</mat-hint>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Professional Summary</mat-label>
          <textarea matInput formControlName="summary" rows="6"
                    placeholder="Describe your expertise, experience, and what makes you unique"
                    maxlength="2000"></textarea>
          <mat-hint align="end">{{ profileForm.get('summary')?.value?.length || 0 }}/2000</mat-hint>
        </mat-form-field>
      </mat-card-content>
    </mat-card>

    <!-- Location Information -->
    <mat-card class="form-section" formGroupName="location">
      <mat-card-header>
        <mat-card-title>Location</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>City</mat-label>
            <input matInput formControlName="city">
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>State/Province</mat-label>
            <input matInput formControlName="state">
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Country</mat-label>
            <input matInput formControlName="country">
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Display Location</mat-label>
            <input matInput formControlName="displayLocation"
                   placeholder="e.g., San Francisco, CA">
          </mat-form-field>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Contact Information -->
    <mat-card class="form-section" formGroupName="contactInfo">
      <mat-card-header>
        <mat-card-title>Contact Information</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Email</mat-label>
            <input matInput formControlName="email" type="email">
            <mat-error *ngIf="profileForm.get('contactInfo.email')?.invalid && profileForm.get('contactInfo.email')?.touched">
              {{ getErrorMessage('contactInfo.email') }}
            </mat-error>
          </mat-form-field>

          <div class="checkbox-field">
            <mat-checkbox formControlName="isEmailPublic">
              Make email public
            </mat-checkbox>
          </div>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Website</mat-label>
            <input matInput formControlName="website" placeholder="https://yourwebsite.com">
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Portfolio URL</mat-label>
            <input matInput formControlName="portfolioUrl" placeholder="https://portfolio.com">
          </mat-form-field>
        </div>

        <!-- Phone Numbers -->
        <div class="phone-numbers-section">
          <h4>Phone Numbers</h4>
          <div formArrayName="phoneNumbers">
            <div *ngFor="let phone of phoneNumbers.controls; let i = index"
                 [formGroupName]="i" class="phone-number-item">
              <div class="form-row">
                <mat-form-field appearance="outline" class="phone-input">
                  <mat-label>Phone Number</mat-label>
                  <input matInput formControlName="number" placeholder="+****************">
                </mat-form-field>

                <mat-form-field appearance="outline" class="phone-type">
                  <mat-label>Type</mat-label>
                  <mat-select formControlName="type">
                    <mat-option *ngFor="let type of getPhoneTypeOptions()" [value]="type.value">
                      {{ type.label }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>

                <div class="phone-controls">
                  <mat-checkbox formControlName="isPublic">Public</mat-checkbox>
                  <mat-checkbox formControlName="isPrimary">Primary</mat-checkbox>
                  <button mat-icon-button color="warn" (click)="removePhoneNumber(i)" type="button">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <button mat-stroked-button (click)="addPhoneNumber()" type="button">
            <mat-icon>add</mat-icon>
            Add Phone Number
          </button>
        </div>

        <!-- Business Address -->
        <div class="business-address-section" formGroupName="businessAddress">
          <h4>Business Address</h4>
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Street Address</mat-label>
            <input matInput formControlName="street">
          </mat-form-field>

          <div class="form-row">
            <mat-form-field appearance="outline" class="third-width">
              <mat-label>City</mat-label>
              <input matInput formControlName="city">
            </mat-form-field>

            <mat-form-field appearance="outline" class="third-width">
              <mat-label>State</mat-label>
              <input matInput formControlName="state">
            </mat-form-field>

            <mat-form-field appearance="outline" class="third-width">
              <mat-label>Postal Code</mat-label>
              <input matInput formControlName="postalCode">
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Country</mat-label>
              <input matInput formControlName="country">
            </mat-form-field>

            <div class="checkbox-field">
              <mat-checkbox formControlName="isPublic">
                Make business address public
              </mat-checkbox>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Social Links -->
    <mat-card class="form-section">
      <mat-card-header>
        <mat-card-title>Social Links</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div formArrayName="socialLinks">
          <div *ngFor="let link of socialLinks.controls; let i = index"
               [formGroupName]="i" class="social-link-item">
            <div class="form-row">
              <mat-form-field appearance="outline" class="platform-select">
                <mat-label>Platform</mat-label>
                <mat-select formControlName="platform">
                  <mat-option *ngFor="let platform of getPlatformOptions()" [value]="platform.value">
                    {{ platform.label }}
                  </mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline" class="url-input">
                <mat-label>URL</mat-label>
                <input matInput formControlName="url" placeholder="https://linkedin.com/in/yourname">
              </mat-form-field>

              <div class="link-controls">
                <mat-checkbox formControlName="isPublic">Public</mat-checkbox>
                <button mat-icon-button color="warn" (click)="removeSocialLink(i)" type="button">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </div>
          </div>
        </div>
        <button mat-stroked-button (click)="addSocialLink()" type="button">
          <mat-icon>add</mat-icon>
          Add Social Link
        </button>
      </mat-card-content>
    </mat-card>

    <!-- Skills Management -->
    <mat-card class="form-section">
      <mat-card-header>
        <mat-card-title>Skills & Expertise</mat-card-title>
        <mat-card-subtitle>Add your skills and areas of expertise</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div formArrayName="skills">
          <div *ngFor="let skill of skills.controls; let i = index" [formGroupName]="i" class="skill-item">
            <div class="skill-fields">
              <mat-form-field appearance="outline" class="skill-name">
                <mat-label>Skill Name</mat-label>
                <input matInput formControlName="name" placeholder="e.g., Tarot Reading">
                <mat-error *ngIf="skill.get('name')?.hasError('required')">
                  Skill name is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="skill-category">
                <mat-label>Category</mat-label>
                <mat-select formControlName="category">
                  <mat-option value="">Select Category</mat-option>
                  <mat-option *ngFor="let category of getSkillCategoryOptions()" [value]="category.value">
                    {{ category.label }}
                  </mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline" class="skill-proficiency">
                <mat-label>Proficiency Level</mat-label>
                <mat-select formControlName="proficiencyLevel">
                  <mat-option *ngFor="let level of getProficiencyLevelOptions()" [value]="level.value">
                    {{ level.label }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="skill.get('proficiencyLevel')?.hasError('required')">
                  Proficiency level is required
                </mat-error>
              </mat-form-field>

              <button mat-icon-button color="warn" (click)="removeSkill(i)" type="button" class="remove-skill">
                <mat-icon>delete</mat-icon>
              </button>
            </div>
          </div>
        </div>
        <button mat-stroked-button (click)="addSkill()" type="button" class="add-skill-btn">
          <mat-icon>add</mat-icon>
          Add Skill
        </button>
      </mat-card-content>
    </mat-card>

    <!-- Consultation Rates -->
    <mat-card class="form-section">
      <mat-card-header>
        <mat-card-title>Consultation Rates</mat-card-title>
        <mat-card-subtitle>Set your pricing for consultations</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div formGroupName="consultationRates" class="rates-section">
          <div class="rate-fields">
            <mat-form-field appearance="outline" class="rate-field">
              <mat-label>Hourly Rate</mat-label>
              <input matInput type="number" formControlName="hourlyRate" placeholder="0.00" min="0" max="10000">
              <span matSuffix>{{ profileForm.get('consultationRates.currency')?.value || 'BGN' }}</span>
              <mat-error *ngIf="profileForm.get('consultationRates.hourlyRate')?.hasError('min')">
                Rate must be positive
              </mat-error>
              <mat-error *ngIf="profileForm.get('consultationRates.hourlyRate')?.hasError('max')">
                Rate cannot exceed 10,000
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="rate-field">
              <mat-label>Session Rate</mat-label>
              <input matInput type="number" formControlName="sessionRate" placeholder="0.00" min="0" max="10000">
              <span matSuffix>{{ profileForm.get('consultationRates.currency')?.value || 'BGN' }}</span>
              <mat-error *ngIf="profileForm.get('consultationRates.sessionRate')?.hasError('min')">
                Rate must be positive
              </mat-error>
              <mat-error *ngIf="profileForm.get('consultationRates.sessionRate')?.hasError('max')">
                Rate cannot exceed 10,000
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="currency-field">
              <mat-label>Currency</mat-label>
              <mat-select formControlName="currency">
                <mat-option *ngFor="let currency of getCurrencyOptions()" [value]="currency.value">
                  {{ currency.label }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="profileForm.get('consultationRates.currency')?.hasError('required')">
                Currency is required
              </mat-error>
            </mat-form-field>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Service Offerings -->
    <mat-card class="form-section">
      <mat-card-header>
        <mat-card-title>Service Offerings</mat-card-title>
        <mat-card-subtitle>Define your specific services and packages</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div formArrayName="serviceOfferings">
          <div *ngFor="let service of serviceOfferings.controls; let i = index" [formGroupName]="i" class="service-item">
            <div class="service-header">
              <h4>Service {{ i + 1 }}</h4>
              <mat-slide-toggle formControlName="isActive" color="primary">
                Active
              </mat-slide-toggle>
            </div>

            <div class="service-fields">
              <mat-form-field appearance="outline" class="service-name">
                <mat-label>Service Name</mat-label>
                <input matInput formControlName="name" placeholder="e.g., Personal Tarot Reading">
                <mat-error *ngIf="service.get('name')?.hasError('required')">
                  Service name is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="service-category">
                <mat-label>Category</mat-label>
                <mat-select formControlName="category">
                  <mat-option value="">Select Category</mat-option>
                  <mat-option *ngFor="let category of getServiceCategoryOptions()" [value]="category.value">
                    {{ category.label }}
                  </mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline" class="service-description">
                <mat-label>Description</mat-label>
                <textarea matInput formControlName="description" rows="3"
                          placeholder="Describe what this service includes..."></textarea>
                <mat-error *ngIf="service.get('description')?.hasError('required')">
                  Description is required
                </mat-error>
              </mat-form-field>

              <div class="service-pricing">
                <mat-form-field appearance="outline" class="price-field">
                  <mat-label>Price</mat-label>
                  <input matInput type="number" formControlName="price" placeholder="0.00" min="0">
                  <span matSuffix>{{ service.get('currency')?.value || 'BGN' }}</span>
                  <mat-error *ngIf="service.get('price')?.hasError('required')">
                    Price is required
                  </mat-error>
                  <mat-error *ngIf="service.get('price')?.hasError('min')">
                    Price must be positive
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="currency-field">
                  <mat-label>Currency</mat-label>
                  <mat-select formControlName="currency">
                    <mat-option *ngFor="let currency of getCurrencyOptions()" [value]="currency.value">
                      {{ currency.label }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="duration-field">
                  <mat-label>Duration (minutes)</mat-label>
                  <input matInput type="number" formControlName="duration" placeholder="60" min="1">
                  <mat-hint>Optional - leave blank if not applicable</mat-hint>
                </mat-form-field>
              </div>

              <button mat-icon-button color="warn" (click)="removeServiceOffering(i)" type="button" class="remove-service">
                <mat-icon>delete</mat-icon>
              </button>
            </div>
          </div>
        </div>
        <button mat-stroked-button (click)="addServiceOffering()" type="button" class="add-service-btn">
          <mat-icon>add</mat-icon>
          Add Service
        </button>
      </mat-card-content>
    </mat-card>

    <!-- Privacy Settings -->
    <mat-card class="form-section">
      <mat-card-header>
        <mat-card-title>Privacy Settings</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="privacy-controls">
          <mat-slide-toggle formControlName="isPublic" color="primary">
            <span class="toggle-label">Make profile public</span>
            <p class="toggle-description">
              When enabled, your profile will be visible to everyone and searchable.
              When disabled, only you can see your profile.
            </p>
          </mat-slide-toggle>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Form Actions -->
    <div class="form-actions">
      <button mat-raised-button color="primary" type="submit" [disabled]="isSaving">
        <mat-icon *ngIf="isSaving">
          <mat-spinner diameter="16"></mat-spinner>
        </mat-icon>
        <mat-icon *ngIf="!isSaving">save</mat-icon>
        {{ isSaving ? 'Saving...' : 'Save Changes' }}
      </button>
      <button mat-stroked-button type="button" routerLink="/profile">
        Cancel
      </button>
    </div>
  </form>
</div>

<!-- Loading State -->
<div class="loading-container" *ngIf="isLoading">
  <mat-spinner diameter="50"></mat-spinner>
  <p>Loading profile...</p>
</div>
