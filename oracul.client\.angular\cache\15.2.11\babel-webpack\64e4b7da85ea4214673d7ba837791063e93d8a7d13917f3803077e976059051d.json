{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule, Injectable } from '@angular/core';\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass LayoutModule {}\nLayoutModule.ɵfac = function LayoutModule_Factory(t) {\n  return new (t || LayoutModule)();\n};\nLayoutModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: LayoutModule\n});\nLayoutModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LayoutModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nclass MediaMatcher {\n  constructor(_platform) {\n    this._platform = _platform;\n    this._matchMedia = this._platform.isBrowser && window.matchMedia ?\n    // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n    // call it from a different scope.\n    window.matchMedia.bind(window) : noopMatchMedia;\n  }\n  /**\n   * Evaluates the given media query and returns the native MediaQueryList from which results\n   * can be retrieved.\n   * Confirms the layout engine will trigger for the selector query provided and returns the\n   * MediaQueryList for the query provided.\n   */\n  matchMedia(query) {\n    if (this._platform.WEBKIT || this._platform.BLINK) {\n      createEmptyStyleRule(query);\n    }\n    return this._matchMedia(query);\n  }\n}\nMediaMatcher.ɵfac = function MediaMatcher_Factory(t) {\n  return new (t || MediaMatcher)(i0.ɵɵinject(i1.Platform));\n};\nMediaMatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: MediaMatcher,\n  factory: MediaMatcher.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MediaMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.Platform\n    }];\n  }, null);\n})();\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query) {\n  if (mediaQueriesForWebkitCompatibility.has(query)) {\n    return;\n  }\n  try {\n    if (!mediaQueryStyleNode) {\n      mediaQueryStyleNode = document.createElement('style');\n      mediaQueryStyleNode.setAttribute('type', 'text/css');\n      document.head.appendChild(mediaQueryStyleNode);\n    }\n    if (mediaQueryStyleNode.sheet) {\n      mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n      mediaQueriesForWebkitCompatibility.add(query);\n    }\n  } catch (e) {\n    console.error(e);\n  }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n  // Use `as any` here to avoid adding additional necessary properties for\n  // the noop matcher.\n  return {\n    matches: query === 'all' || query === '',\n    media: query,\n    addListener: () => {},\n    removeListener: () => {}\n  };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Utility for checking the matching state of @media queries. */\nclass BreakpointObserver {\n  constructor(_mediaMatcher, _zone) {\n    this._mediaMatcher = _mediaMatcher;\n    this._zone = _zone;\n    /**  A map of all media queries currently being listened for. */\n    this._queries = new Map();\n    /** A subject for all other observables to takeUntil based on. */\n    this._destroySubject = new Subject();\n  }\n  /** Completes the active subject, signalling to all other observables to complete. */\n  ngOnDestroy() {\n    this._destroySubject.next();\n    this._destroySubject.complete();\n  }\n  /**\n   * Whether one or more media queries match the current viewport size.\n   * @param value One or more media queries to check.\n   * @returns Whether any of the media queries match.\n   */\n  isMatched(value) {\n    const queries = splitQueries(coerceArray(value));\n    return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n  }\n  /**\n   * Gets an observable of results for the given queries that will emit new results for any changes\n   * in matching of the given queries.\n   * @param value One or more media queries to check.\n   * @returns A stream of matches for the given queries.\n   */\n  observe(value) {\n    const queries = splitQueries(coerceArray(value));\n    const observables = queries.map(query => this._registerQuery(query).observable);\n    let stateObservable = combineLatest(observables);\n    // Emit the first state immediately, and then debounce the subsequent emissions.\n    stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n    return stateObservable.pipe(map(breakpointStates => {\n      const response = {\n        matches: false,\n        breakpoints: {}\n      };\n      breakpointStates.forEach(({\n        matches,\n        query\n      }) => {\n        response.matches = response.matches || matches;\n        response.breakpoints[query] = matches;\n      });\n      return response;\n    }));\n  }\n  /** Registers a specific query to be listened for. */\n  _registerQuery(query) {\n    // Only set up a new MediaQueryList if it is not already being listened for.\n    if (this._queries.has(query)) {\n      return this._queries.get(query);\n    }\n    const mql = this._mediaMatcher.matchMedia(query);\n    // Create callback for match changes and add it is as a listener.\n    const queryObservable = new Observable(observer => {\n      // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n      // back into the zone because matchMedia is only included in Zone.js by loading the\n      // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n      // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n      // patches it.\n      const handler = e => this._zone.run(() => observer.next(e));\n      mql.addListener(handler);\n      return () => {\n        mql.removeListener(handler);\n      };\n    }).pipe(startWith(mql), map(({\n      matches\n    }) => ({\n      query,\n      matches\n    })), takeUntil(this._destroySubject));\n    // Add the MediaQueryList to the set of queries.\n    const output = {\n      observable: queryObservable,\n      mql\n    };\n    this._queries.set(query, output);\n    return output;\n  }\n}\nBreakpointObserver.ɵfac = function BreakpointObserver_Factory(t) {\n  return new (t || BreakpointObserver)(i0.ɵɵinject(MediaMatcher), i0.ɵɵinject(i0.NgZone));\n};\nBreakpointObserver.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BreakpointObserver,\n  factory: BreakpointObserver.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BreakpointObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: MediaMatcher\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n  return queries.map(query => query.split(',')).reduce((a1, a2) => a1.concat(a2)).map(query => query.trim());\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// PascalCase is being used as Breakpoints is used like an enum.\n// tslint:disable-next-line:variable-name\nconst Breakpoints = {\n  XSmall: '(max-width: 599.98px)',\n  Small: '(min-width: 600px) and (max-width: 959.98px)',\n  Medium: '(min-width: 960px) and (max-width: 1279.98px)',\n  Large: '(min-width: 1280px) and (max-width: 1919.98px)',\n  XLarge: '(min-width: 1920px)',\n  Handset: '(max-width: 599.98px) and (orientation: portrait), ' + '(max-width: 959.98px) and (orientation: landscape)',\n  Tablet: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), ' + '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  Web: '(min-width: 840px) and (orientation: portrait), ' + '(min-width: 1280px) and (orientation: landscape)',\n  HandsetPortrait: '(max-width: 599.98px) and (orientation: portrait)',\n  TabletPortrait: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)',\n  WebPortrait: '(min-width: 840px) and (orientation: portrait)',\n  HandsetLandscape: '(max-width: 959.98px) and (orientation: landscape)',\n  TabletLandscape: '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  WebLandscape: '(min-width: 1280px) and (orientation: landscape)'\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BreakpointObserver, Breakpoints, LayoutModule, MediaMatcher };", "map": {"version": 3, "names": ["i0", "NgModule", "Injectable", "coerce<PERSON><PERSON><PERSON>", "Subject", "combineLatest", "concat", "Observable", "take", "skip", "debounceTime", "map", "startWith", "takeUntil", "i1", "LayoutModule", "ɵfac", "ɵmod", "ɵinj", "type", "args", "mediaQueriesForWebkitCompatibility", "Set", "mediaQueryStyleNode", "MediaMatcher", "constructor", "_platform", "_matchMedia", "<PERSON><PERSON><PERSON><PERSON>", "window", "matchMedia", "bind", "noopMatchMedia", "query", "WEBKIT", "BLINK", "createEmptyStyleRule", "Platform", "ɵprov", "providedIn", "has", "document", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "sheet", "insertRule", "add", "e", "console", "error", "matches", "media", "addListener", "removeListener", "BreakpointObserver", "_mediaMatcher", "_zone", "_queries", "Map", "_destroySubject", "ngOnDestroy", "next", "complete", "isMatched", "value", "queries", "splitQueries", "some", "mediaQuery", "_registerQuery", "mql", "observe", "observables", "observable", "stateObservable", "pipe", "breakpointStates", "response", "breakpoints", "for<PERSON>ach", "get", "queryObservable", "observer", "handler", "run", "output", "set", "NgZone", "split", "reduce", "a1", "a2", "trim", "Breakpoints", "XSmall", "Small", "Medium", "Large", "<PERSON>L<PERSON>ge", "Handset", "Tablet", "Web", "HandsetPortrait", "TabletPortrait", "WebPortrait", "HandsetLandscape", "TabletLandscape", "WebLandscape"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/cdk/fesm2020/layout.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { NgModule, Injectable } from '@angular/core';\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass LayoutModule {\n}\nLayoutModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: LayoutModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nLayoutModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: LayoutModule });\nLayoutModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: LayoutModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: LayoutModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nclass MediaMatcher {\n    constructor(_platform) {\n        this._platform = _platform;\n        this._matchMedia =\n            this._platform.isBrowser && window.matchMedia\n                ? // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n                    // call it from a different scope.\n                    window.matchMedia.bind(window)\n                : noopMatchMedia;\n    }\n    /**\n     * Evaluates the given media query and returns the native MediaQueryList from which results\n     * can be retrieved.\n     * Confirms the layout engine will trigger for the selector query provided and returns the\n     * MediaQueryList for the query provided.\n     */\n    matchMedia(query) {\n        if (this._platform.WEBKIT || this._platform.BLINK) {\n            createEmptyStyleRule(query);\n        }\n        return this._matchMedia(query);\n    }\n}\nMediaMatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MediaMatcher, deps: [{ token: i1.Platform }], target: i0.ɵɵFactoryTarget.Injectable });\nMediaMatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MediaMatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MediaMatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }]; } });\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query) {\n    if (mediaQueriesForWebkitCompatibility.has(query)) {\n        return;\n    }\n    try {\n        if (!mediaQueryStyleNode) {\n            mediaQueryStyleNode = document.createElement('style');\n            mediaQueryStyleNode.setAttribute('type', 'text/css');\n            document.head.appendChild(mediaQueryStyleNode);\n        }\n        if (mediaQueryStyleNode.sheet) {\n            mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n            mediaQueriesForWebkitCompatibility.add(query);\n        }\n    }\n    catch (e) {\n        console.error(e);\n    }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n    // Use `as any` here to avoid adding additional necessary properties for\n    // the noop matcher.\n    return {\n        matches: query === 'all' || query === '',\n        media: query,\n        addListener: () => { },\n        removeListener: () => { },\n    };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Utility for checking the matching state of @media queries. */\nclass BreakpointObserver {\n    constructor(_mediaMatcher, _zone) {\n        this._mediaMatcher = _mediaMatcher;\n        this._zone = _zone;\n        /**  A map of all media queries currently being listened for. */\n        this._queries = new Map();\n        /** A subject for all other observables to takeUntil based on. */\n        this._destroySubject = new Subject();\n    }\n    /** Completes the active subject, signalling to all other observables to complete. */\n    ngOnDestroy() {\n        this._destroySubject.next();\n        this._destroySubject.complete();\n    }\n    /**\n     * Whether one or more media queries match the current viewport size.\n     * @param value One or more media queries to check.\n     * @returns Whether any of the media queries match.\n     */\n    isMatched(value) {\n        const queries = splitQueries(coerceArray(value));\n        return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n    }\n    /**\n     * Gets an observable of results for the given queries that will emit new results for any changes\n     * in matching of the given queries.\n     * @param value One or more media queries to check.\n     * @returns A stream of matches for the given queries.\n     */\n    observe(value) {\n        const queries = splitQueries(coerceArray(value));\n        const observables = queries.map(query => this._registerQuery(query).observable);\n        let stateObservable = combineLatest(observables);\n        // Emit the first state immediately, and then debounce the subsequent emissions.\n        stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n        return stateObservable.pipe(map(breakpointStates => {\n            const response = {\n                matches: false,\n                breakpoints: {},\n            };\n            breakpointStates.forEach(({ matches, query }) => {\n                response.matches = response.matches || matches;\n                response.breakpoints[query] = matches;\n            });\n            return response;\n        }));\n    }\n    /** Registers a specific query to be listened for. */\n    _registerQuery(query) {\n        // Only set up a new MediaQueryList if it is not already being listened for.\n        if (this._queries.has(query)) {\n            return this._queries.get(query);\n        }\n        const mql = this._mediaMatcher.matchMedia(query);\n        // Create callback for match changes and add it is as a listener.\n        const queryObservable = new Observable((observer) => {\n            // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n            // back into the zone because matchMedia is only included in Zone.js by loading the\n            // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n            // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n            // patches it.\n            const handler = (e) => this._zone.run(() => observer.next(e));\n            mql.addListener(handler);\n            return () => {\n                mql.removeListener(handler);\n            };\n        }).pipe(startWith(mql), map(({ matches }) => ({ query, matches })), takeUntil(this._destroySubject));\n        // Add the MediaQueryList to the set of queries.\n        const output = { observable: queryObservable, mql };\n        this._queries.set(query, output);\n        return output;\n    }\n}\nBreakpointObserver.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: BreakpointObserver, deps: [{ token: MediaMatcher }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\nBreakpointObserver.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: BreakpointObserver, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: BreakpointObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: MediaMatcher }, { type: i0.NgZone }]; } });\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n    return queries\n        .map(query => query.split(','))\n        .reduce((a1, a2) => a1.concat(a2))\n        .map(query => query.trim());\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// PascalCase is being used as Breakpoints is used like an enum.\n// tslint:disable-next-line:variable-name\nconst Breakpoints = {\n    XSmall: '(max-width: 599.98px)',\n    Small: '(min-width: 600px) and (max-width: 959.98px)',\n    Medium: '(min-width: 960px) and (max-width: 1279.98px)',\n    Large: '(min-width: 1280px) and (max-width: 1919.98px)',\n    XLarge: '(min-width: 1920px)',\n    Handset: '(max-width: 599.98px) and (orientation: portrait), ' +\n        '(max-width: 959.98px) and (orientation: landscape)',\n    Tablet: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), ' +\n        '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n    Web: '(min-width: 840px) and (orientation: portrait), ' +\n        '(min-width: 1280px) and (orientation: landscape)',\n    HandsetPortrait: '(max-width: 599.98px) and (orientation: portrait)',\n    TabletPortrait: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)',\n    WebPortrait: '(min-width: 840px) and (orientation: portrait)',\n    HandsetLandscape: '(max-width: 959.98px) and (orientation: landscape)',\n    TabletLandscape: '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n    WebLandscape: '(min-width: 1280px) and (orientation: landscape)',\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BreakpointObserver, Breakpoints, LayoutModule, MediaMatcher };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,EAAEC,UAAU,QAAQ,eAAe;AACpD,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,OAAO,EAAEC,aAAa,EAAEC,MAAM,EAAEC,UAAU,QAAQ,MAAM;AACjE,SAASC,IAAI,EAAEC,IAAI,EAAEC,YAAY,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACpF,OAAO,KAAKC,EAAE,MAAM,uBAAuB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;AAEnBA,YAAY,CAACC,IAAI;EAAA,iBAA6FD,YAAY;AAAA,CAAkD;AAC5KA,YAAY,CAACE,IAAI,kBADmFjB,EAAE;EAAA,MACSe;AAAY,EAAG;AAC9HA,YAAY,CAACG,IAAI,kBAFmFlB,EAAE,qBAEwB;AAC9H;EAAA,mDAHoGA,EAAE,mBAGNe,YAAY,EAAc,CAAC;IAC/GI,IAAI,EAAElB,QAAQ;IACdmB,IAAI,EAAE,CAAC,CAAC,CAAC;EACb,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kCAAkC,GAAG,IAAIC,GAAG,EAAE;AACpD;AACA,IAAIC,mBAAmB;AACvB;AACA,MAAMC,YAAY,CAAC;EACfC,WAAW,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GACZ,IAAI,CAACD,SAAS,CAACE,SAAS,IAAIC,MAAM,CAACC,UAAU;IACvC;IACE;IACAD,MAAM,CAACC,UAAU,CAACC,IAAI,CAACF,MAAM,CAAC,GAChCG,cAAc;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIF,UAAU,CAACG,KAAK,EAAE;IACd,IAAI,IAAI,CAACP,SAAS,CAACQ,MAAM,IAAI,IAAI,CAACR,SAAS,CAACS,KAAK,EAAE;MAC/CC,oBAAoB,CAACH,KAAK,CAAC;IAC/B;IACA,OAAO,IAAI,CAACN,WAAW,CAACM,KAAK,CAAC;EAClC;AACJ;AACAT,YAAY,CAACR,IAAI;EAAA,iBAA6FQ,YAAY,EA3CtBxB,EAAE,UA2CsCc,EAAE,CAACuB,QAAQ;AAAA,CAA6C;AACpMb,YAAY,CAACc,KAAK,kBA5CkFtC,EAAE;EAAA,OA4CYwB,YAAY;EAAA,SAAZA,YAAY;EAAA,YAAc;AAAM,EAAG;AACrJ;EAAA,mDA7CoGxB,EAAE,mBA6CNwB,YAAY,EAAc,CAAC;IAC/GL,IAAI,EAAEjB,UAAU;IAChBkB,IAAI,EAAE,CAAC;MAAEmB,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEpB,IAAI,EAAEL,EAAE,CAACuB;IAAS,CAAC,CAAC;EAAE,CAAC;AAAA;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,oBAAoB,CAACH,KAAK,EAAE;EACjC,IAAIZ,kCAAkC,CAACmB,GAAG,CAACP,KAAK,CAAC,EAAE;IAC/C;EACJ;EACA,IAAI;IACA,IAAI,CAACV,mBAAmB,EAAE;MACtBA,mBAAmB,GAAGkB,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MACrDnB,mBAAmB,CAACoB,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;MACpDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACtB,mBAAmB,CAAC;IAClD;IACA,IAAIA,mBAAmB,CAACuB,KAAK,EAAE;MAC3BvB,mBAAmB,CAACuB,KAAK,CAACC,UAAU,CAAE,UAASd,KAAM,YAAW,EAAE,CAAC,CAAC;MACpEZ,kCAAkC,CAAC2B,GAAG,CAACf,KAAK,CAAC;IACjD;EACJ,CAAC,CACD,OAAOgB,CAAC,EAAE;IACNC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;EACpB;AACJ;AACA;AACA,SAASjB,cAAc,CAACC,KAAK,EAAE;EAC3B;EACA;EACA,OAAO;IACHmB,OAAO,EAAEnB,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,EAAE;IACxCoB,KAAK,EAAEpB,KAAK;IACZqB,WAAW,EAAE,MAAM,CAAE,CAAC;IACtBC,cAAc,EAAE,MAAM,CAAE;EAC5B,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrB/B,WAAW,CAACgC,aAAa,EAAEC,KAAK,EAAE;IAC9B,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB;IACA,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,EAAE;IACzB;IACA,IAAI,CAACC,eAAe,GAAG,IAAIzD,OAAO,EAAE;EACxC;EACA;EACA0D,WAAW,GAAG;IACV,IAAI,CAACD,eAAe,CAACE,IAAI,EAAE;IAC3B,IAAI,CAACF,eAAe,CAACG,QAAQ,EAAE;EACnC;EACA;AACJ;AACA;AACA;AACA;EACIC,SAAS,CAACC,KAAK,EAAE;IACb,MAAMC,OAAO,GAAGC,YAAY,CAACjE,WAAW,CAAC+D,KAAK,CAAC,CAAC;IAChD,OAAOC,OAAO,CAACE,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,cAAc,CAACD,UAAU,CAAC,CAACE,GAAG,CAACpB,OAAO,CAAC;EAClF;EACA;AACJ;AACA;AACA;AACA;AACA;EACIqB,OAAO,CAACP,KAAK,EAAE;IACX,MAAMC,OAAO,GAAGC,YAAY,CAACjE,WAAW,CAAC+D,KAAK,CAAC,CAAC;IAChD,MAAMQ,WAAW,GAAGP,OAAO,CAACxD,GAAG,CAACsB,KAAK,IAAI,IAAI,CAACsC,cAAc,CAACtC,KAAK,CAAC,CAAC0C,UAAU,CAAC;IAC/E,IAAIC,eAAe,GAAGvE,aAAa,CAACqE,WAAW,CAAC;IAChD;IACAE,eAAe,GAAGtE,MAAM,CAACsE,eAAe,CAACC,IAAI,CAACrE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEoE,eAAe,CAACC,IAAI,CAACpE,IAAI,CAAC,CAAC,CAAC,EAAEC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACvG,OAAOkE,eAAe,CAACC,IAAI,CAAClE,GAAG,CAACmE,gBAAgB,IAAI;MAChD,MAAMC,QAAQ,GAAG;QACb3B,OAAO,EAAE,KAAK;QACd4B,WAAW,EAAE,CAAC;MAClB,CAAC;MACDF,gBAAgB,CAACG,OAAO,CAAC,CAAC;QAAE7B,OAAO;QAAEnB;MAAM,CAAC,KAAK;QAC7C8C,QAAQ,CAAC3B,OAAO,GAAG2B,QAAQ,CAAC3B,OAAO,IAAIA,OAAO;QAC9C2B,QAAQ,CAACC,WAAW,CAAC/C,KAAK,CAAC,GAAGmB,OAAO;MACzC,CAAC,CAAC;MACF,OAAO2B,QAAQ;IACnB,CAAC,CAAC,CAAC;EACP;EACA;EACAR,cAAc,CAACtC,KAAK,EAAE;IAClB;IACA,IAAI,IAAI,CAAC0B,QAAQ,CAACnB,GAAG,CAACP,KAAK,CAAC,EAAE;MAC1B,OAAO,IAAI,CAAC0B,QAAQ,CAACuB,GAAG,CAACjD,KAAK,CAAC;IACnC;IACA,MAAMuC,GAAG,GAAG,IAAI,CAACf,aAAa,CAAC3B,UAAU,CAACG,KAAK,CAAC;IAChD;IACA,MAAMkD,eAAe,GAAG,IAAI5E,UAAU,CAAE6E,QAAQ,IAAK;MACjD;MACA;MACA;MACA;MACA;MACA,MAAMC,OAAO,GAAIpC,CAAC,IAAK,IAAI,CAACS,KAAK,CAAC4B,GAAG,CAAC,MAAMF,QAAQ,CAACrB,IAAI,CAACd,CAAC,CAAC,CAAC;MAC7DuB,GAAG,CAAClB,WAAW,CAAC+B,OAAO,CAAC;MACxB,OAAO,MAAM;QACTb,GAAG,CAACjB,cAAc,CAAC8B,OAAO,CAAC;MAC/B,CAAC;IACL,CAAC,CAAC,CAACR,IAAI,CAACjE,SAAS,CAAC4D,GAAG,CAAC,EAAE7D,GAAG,CAAC,CAAC;MAAEyC;IAAQ,CAAC,MAAM;MAAEnB,KAAK;MAAEmB;IAAQ,CAAC,CAAC,CAAC,EAAEvC,SAAS,CAAC,IAAI,CAACgD,eAAe,CAAC,CAAC;IACpG;IACA,MAAM0B,MAAM,GAAG;MAAEZ,UAAU,EAAEQ,eAAe;MAAEX;IAAI,CAAC;IACnD,IAAI,CAACb,QAAQ,CAAC6B,GAAG,CAACvD,KAAK,EAAEsD,MAAM,CAAC;IAChC,OAAOA,MAAM;EACjB;AACJ;AACA/B,kBAAkB,CAACxC,IAAI;EAAA,iBAA6FwC,kBAAkB,EA1KlCxD,EAAE,UA0KkDwB,YAAY,GA1KhExB,EAAE,UA0K2EA,EAAE,CAACyF,MAAM;AAAA,CAA6C;AACvOjC,kBAAkB,CAAClB,KAAK,kBA3K4EtC,EAAE;EAAA,OA2KkBwD,kBAAkB;EAAA,SAAlBA,kBAAkB;EAAA,YAAc;AAAM,EAAG;AACjK;EAAA,mDA5KoGxD,EAAE,mBA4KNwD,kBAAkB,EAAc,CAAC;IACrHrC,IAAI,EAAEjB,UAAU;IAChBkB,IAAI,EAAE,CAAC;MAAEmB,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEpB,IAAI,EAAEK;IAAa,CAAC,EAAE;MAAEL,IAAI,EAAEnB,EAAE,CAACyF;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AACjG;AACA;AACA;AACA;AACA,SAASrB,YAAY,CAACD,OAAO,EAAE;EAC3B,OAAOA,OAAO,CACTxD,GAAG,CAACsB,KAAK,IAAIA,KAAK,CAACyD,KAAK,CAAC,GAAG,CAAC,CAAC,CAC9BC,MAAM,CAAC,CAACC,EAAE,EAAEC,EAAE,KAAKD,EAAE,CAACtF,MAAM,CAACuF,EAAE,CAAC,CAAC,CACjClF,GAAG,CAACsB,KAAK,IAAIA,KAAK,CAAC6D,IAAI,EAAE,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG;EAChBC,MAAM,EAAE,uBAAuB;EAC/BC,KAAK,EAAE,8CAA8C;EACrDC,MAAM,EAAE,+CAA+C;EACvDC,KAAK,EAAE,gDAAgD;EACvDC,MAAM,EAAE,qBAAqB;EAC7BC,OAAO,EAAE,qDAAqD,GAC1D,oDAAoD;EACxDC,MAAM,EAAE,4EAA4E,GAChF,4EAA4E;EAChFC,GAAG,EAAE,kDAAkD,GACnD,kDAAkD;EACtDC,eAAe,EAAE,mDAAmD;EACpEC,cAAc,EAAE,0EAA0E;EAC1FC,WAAW,EAAE,gDAAgD;EAC7DC,gBAAgB,EAAE,oDAAoD;EACtEC,eAAe,EAAE,4EAA4E;EAC7FC,YAAY,EAAE;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASrD,kBAAkB,EAAEuC,WAAW,EAAEhF,YAAY,EAAES,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}