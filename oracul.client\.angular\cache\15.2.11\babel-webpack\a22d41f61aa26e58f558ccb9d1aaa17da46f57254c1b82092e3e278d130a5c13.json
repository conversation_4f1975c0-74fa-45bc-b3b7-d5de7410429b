{"ast": null, "code": "/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ApplicationInitStatus, APP_INITIALIZER, Injector, ɵglobal, Injectable, Inject, ViewEncapsulation, APP_ID, RendererStyleFlags2, ɵinternalCreateApplication, ErrorHandler, ɵsetDocument, PLATFORM_ID, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, ApplicationModule, NgModule, Optional, SkipSelf, ɵɵinject, ApplicationRef, inject, ɵConsole, forwardRef, ɵXSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, Version } from '@angular/core';\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n  constructor() {\n    super(...arguments);\n    this.supportsDOMEvents = true;\n  }\n}\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n/* tslint:disable:requireParameterType no-console */\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n  static makeCurrent() {\n    ɵsetRootDomAdapter(new BrowserDomAdapter());\n  }\n  onAndCancel(el, evt, listener) {\n    el.addEventListener(evt, listener, false);\n    // Needed to follow Dart's subscription semantic, until fix of\n    // https://code.google.com/p/dart/issues/detail?id=17406\n    return () => {\n      el.removeEventListener(evt, listener, false);\n    };\n  }\n  dispatchEvent(el, evt) {\n    el.dispatchEvent(evt);\n  }\n  remove(node) {\n    if (node.parentNode) {\n      node.parentNode.removeChild(node);\n    }\n  }\n  createElement(tagName, doc) {\n    doc = doc || this.getDefaultDocument();\n    return doc.createElement(tagName);\n  }\n  createHtmlDocument() {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n  getDefaultDocument() {\n    return document;\n  }\n  isElementNode(node) {\n    return node.nodeType === Node.ELEMENT_NODE;\n  }\n  isShadowRoot(node) {\n    return node instanceof DocumentFragment;\n  }\n  /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n  getGlobalEventTarget(doc, target) {\n    if (target === 'window') {\n      return window;\n    }\n    if (target === 'document') {\n      return doc;\n    }\n    if (target === 'body') {\n      return doc.body;\n    }\n    return null;\n  }\n  getBaseHref(doc) {\n    const href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n  resetBaseElement() {\n    baseElement = null;\n  }\n  getUserAgent() {\n    return window.navigator.userAgent;\n  }\n  getCookie(name) {\n    return ɵparseCookieValue(document.cookie, name);\n  }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n  baseElement = baseElement || document.querySelector('base');\n  return baseElement ? baseElement.getAttribute('href') : null;\n}\n// based on urlUtils.js in AngularJS 1\nlet urlParsingNode;\nfunction relativePath(url) {\n  urlParsingNode = urlParsingNode || document.createElement('a');\n  urlParsingNode.setAttribute('href', url);\n  const pathName = urlParsingNode.pathname;\n  return pathName.charAt(0) === '/' ? pathName : `/${pathName}`;\n}\n\n/**\n * An id that identifies a particular application being bootstrapped, that should\n * match across the client/server boundary.\n */\nconst TRANSITION_ID = new InjectionToken('TRANSITION_ID');\nfunction appInitializerFactory(transitionId, document, injector) {\n  return () => {\n    // Wait for all application initializers to be completed before removing the styles set by\n    // the server.\n    injector.get(ApplicationInitStatus).donePromise.then(() => {\n      const dom = ɵgetDOM();\n      const styles = document.querySelectorAll(`style[ng-transition=\"${transitionId}\"]`);\n      for (let i = 0; i < styles.length; i++) {\n        dom.remove(styles[i]);\n      }\n    });\n  };\n}\nconst SERVER_TRANSITION_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: appInitializerFactory,\n  deps: [TRANSITION_ID, DOCUMENT, Injector],\n  multi: true\n}];\nclass BrowserGetTestability {\n  addToWindow(registry) {\n    ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n      const testability = registry.findTestabilityInTree(elem, findInAncestors);\n      if (testability == null) {\n        throw new Error('Could not find testability for element.');\n      }\n      return testability;\n    };\n    ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n    ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n    const whenAllStable = (callback /** TODO #9100 */) => {\n      const testabilities = ɵglobal['getAllAngularTestabilities']();\n      let count = testabilities.length;\n      let didWork = false;\n      const decrement = function (didWork_ /** TODO #9100 */) {\n        didWork = didWork || didWork_;\n        count--;\n        if (count == 0) {\n          callback(didWork);\n        }\n      };\n      testabilities.forEach(function (testability /** TODO #9100 */) {\n        testability.whenStable(decrement);\n      });\n    };\n    if (!ɵglobal['frameworkStabilizers']) {\n      ɵglobal['frameworkStabilizers'] = [];\n    }\n    ɵglobal['frameworkStabilizers'].push(whenAllStable);\n  }\n  findTestabilityInTree(registry, elem, findInAncestors) {\n    if (elem == null) {\n      return null;\n    }\n    const t = registry.getTestability(elem);\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n    if (ɵgetDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, elem.host, true);\n    }\n    return this.findTestabilityInTree(registry, elem.parentElement, true);\n  }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n  build() {\n    return new XMLHttpRequest();\n  }\n}\nBrowserXhr.ɵfac = function BrowserXhr_Factory(t) {\n  return new (t || BrowserXhr)();\n};\nBrowserXhr.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BrowserXhr,\n  factory: BrowserXhr.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserXhr, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * The injection token for the event-manager plug-in service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken('EventManagerPlugins');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(plugins, _zone) {\n    this._zone = _zone;\n    this._eventNameToPlugin = new Map();\n    plugins.forEach(plugin => {\n      plugin.manager = this;\n    });\n    this._plugins = plugins.slice().reverse();\n  }\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n  addEventListener(element, eventName, handler) {\n    const plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler);\n  }\n  /**\n   * Registers a global handler for an event in a target view.\n   *\n   * @param target A target for global event notifications. One of \"window\", \"document\", or \"body\".\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns A callback function that can be used to remove the handler.\n   * @deprecated No longer being used in Ivy code. To be removed in version 14.\n   */\n  addGlobalEventListener(target, eventName, handler) {\n    const plugin = this._findPluginFor(eventName);\n    return plugin.addGlobalEventListener(target, eventName, handler);\n  }\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n  getZone() {\n    return this._zone;\n  }\n  /** @internal */\n  _findPluginFor(eventName) {\n    const plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n    const plugins = this._plugins;\n    for (let i = 0; i < plugins.length; i++) {\n      const plugin = plugins[i];\n      if (plugin.supports(eventName)) {\n        this._eventNameToPlugin.set(eventName, plugin);\n        return plugin;\n      }\n    }\n    throw new Error(`No event manager plugin found for event ${eventName}`);\n  }\n}\nEventManager.ɵfac = function EventManager_Factory(t) {\n  return new (t || EventManager)(i0.ɵɵinject(EVENT_MANAGER_PLUGINS), i0.ɵɵinject(i0.NgZone));\n};\nEventManager.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: EventManager,\n  factory: EventManager.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EventManager, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [EVENT_MANAGER_PLUGINS]\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\nclass EventManagerPlugin {\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  addGlobalEventListener(element, eventName, handler) {\n    const target = ɵgetDOM().getGlobalEventTarget(this._doc, element);\n    if (!target) {\n      throw new Error(`Unsupported event target ${target} for event ${eventName}`);\n    }\n    return this.addEventListener(target, eventName, handler);\n  }\n}\nclass SharedStylesHost {\n  constructor() {\n    this.usageCount = new Map();\n  }\n  addStyles(styles) {\n    for (const style of styles) {\n      const usageCount = this.changeUsageCount(style, 1);\n      if (usageCount === 1) {\n        this.onStyleAdded(style);\n      }\n    }\n  }\n  removeStyles(styles) {\n    for (const style of styles) {\n      const usageCount = this.changeUsageCount(style, -1);\n      if (usageCount === 0) {\n        this.onStyleRemoved(style);\n      }\n    }\n  }\n  onStyleRemoved(style) {}\n  onStyleAdded(style) {}\n  getAllStyles() {\n    return this.usageCount.keys();\n  }\n  changeUsageCount(style, delta) {\n    const map = this.usageCount;\n    let usage = map.get(style) ?? 0;\n    usage += delta;\n    if (usage > 0) {\n      map.set(style, usage);\n    } else {\n      map.delete(style);\n    }\n    return usage;\n  }\n  ngOnDestroy() {\n    for (const style of this.getAllStyles()) {\n      this.onStyleRemoved(style);\n    }\n    this.usageCount.clear();\n  }\n}\nSharedStylesHost.ɵfac = function SharedStylesHost_Factory(t) {\n  return new (t || SharedStylesHost)();\n};\nSharedStylesHost.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: SharedStylesHost,\n  factory: SharedStylesHost.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedStylesHost, [{\n    type: Injectable\n  }], null, null);\n})();\nclass DomSharedStylesHost extends SharedStylesHost {\n  constructor(doc) {\n    super();\n    this.doc = doc;\n    // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n    this.styleRef = new Map();\n    this.hostNodes = new Set();\n    this.resetHostNodes();\n  }\n  onStyleAdded(style) {\n    for (const host of this.hostNodes) {\n      this.addStyleToHost(host, style);\n    }\n  }\n  onStyleRemoved(style) {\n    const styleRef = this.styleRef;\n    const styleElements = styleRef.get(style);\n    styleElements?.forEach(e => e.remove());\n    styleRef.delete(style);\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this.styleRef.clear();\n    this.resetHostNodes();\n  }\n  addHost(hostNode) {\n    this.hostNodes.add(hostNode);\n    for (const style of this.getAllStyles()) {\n      this.addStyleToHost(hostNode, style);\n    }\n  }\n  removeHost(hostNode) {\n    this.hostNodes.delete(hostNode);\n  }\n  addStyleToHost(host, style) {\n    const styleEl = this.doc.createElement('style');\n    styleEl.textContent = style;\n    host.appendChild(styleEl);\n    const styleElRef = this.styleRef.get(style);\n    if (styleElRef) {\n      styleElRef.push(styleEl);\n    } else {\n      this.styleRef.set(style, [styleEl]);\n    }\n  }\n  resetHostNodes() {\n    const hostNodes = this.hostNodes;\n    hostNodes.clear();\n    // Re-add the head element back since this is the default host.\n    hostNodes.add(this.doc.head);\n  }\n}\nDomSharedStylesHost.ɵfac = function DomSharedStylesHost_Factory(t) {\n  return new (t || DomSharedStylesHost)(i0.ɵɵinject(DOCUMENT));\n};\nDomSharedStylesHost.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomSharedStylesHost,\n  factory: DomSharedStylesHost.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSharedStylesHost, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\nconst NAMESPACE_URIS = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n  'math': 'http://www.w3.org/1998/MathML/'\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst NG_DEV_MODE$1 = typeof ngDevMode === 'undefined' || !!ngDevMode;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = false;\n/**\n * A [DI token](guide/glossary#di-token \"DI token definition\") that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `false`. This will be changed in the next major version.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken('RemoveStylesOnCompDestory', {\n  providedIn: 'root',\n  factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT\n});\nfunction shimContentAttribute(componentShortId) {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction flattenStyles(compId, styles) {\n  // Cannot use `Infinity` as depth as `infinity` is not a number literal in TypeScript.\n  // See: https://github.com/microsoft/TypeScript/issues/32277\n  return styles.flat(100).map(s => s.replace(COMPONENT_REGEX, compId));\n}\nfunction decoratePreventDefault(eventHandler) {\n  // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n  // decoratePreventDefault or is a listener added outside the Angular context so it can handle the\n  // two differently. In the first case, the special '__ngUnwrap__' token is passed to the unwrap\n  // the listener (see below).\n  return event => {\n    // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n    // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The debug_node\n    // can inspect the listener toString contents for the existence of this special token. Because\n    // the token is a string literal, it is ensured to not be modified by compiled code.\n    if (event === '__ngUnwrap__') {\n      return eventHandler;\n    }\n    const allowDefaultBehavior = eventHandler(event);\n    if (allowDefaultBehavior === false) {\n      // TODO(tbosch): move preventDefault into event plugins...\n      event.preventDefault();\n      event.returnValue = false;\n    }\n    return undefined;\n  };\n}\nclass DomRendererFactory2 {\n  constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestory) {\n    this.eventManager = eventManager;\n    this.sharedStylesHost = sharedStylesHost;\n    this.appId = appId;\n    this.removeStylesOnCompDestory = removeStylesOnCompDestory;\n    this.rendererByCompId = new Map();\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager);\n  }\n  createRenderer(element, type) {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n    const renderer = this.getOrCreateRenderer(element, type);\n    // Renderers have different logic due to different encapsulation behaviours.\n    // Ex: for emulated, an attribute is added to the element.\n    if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n      renderer.applyToHost(element);\n    } else if (renderer instanceof NoneEncapsulationDomRenderer) {\n      renderer.applyStyles();\n    }\n    return renderer;\n  }\n  getOrCreateRenderer(element, type) {\n    const rendererByCompId = this.rendererByCompId;\n    let renderer = rendererByCompId.get(type.id);\n    if (!renderer) {\n      const eventManager = this.eventManager;\n      const sharedStylesHost = this.sharedStylesHost;\n      const removeStylesOnCompDestory = this.removeStylesOnCompDestory;\n      switch (type.encapsulation) {\n        case ViewEncapsulation.Emulated:\n          renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestory);\n          break;\n        case ViewEncapsulation.ShadowDom:\n          return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type);\n        default:\n          renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestory);\n          break;\n      }\n      renderer.onDestroy = () => rendererByCompId.delete(type.id);\n      rendererByCompId.set(type.id, renderer);\n    }\n    return renderer;\n  }\n  ngOnDestroy() {\n    this.rendererByCompId.clear();\n  }\n  begin() {}\n  end() {}\n}\nDomRendererFactory2.ɵfac = function DomRendererFactory2_Factory(t) {\n  return new (t || DomRendererFactory2)(i0.ɵɵinject(EventManager), i0.ɵɵinject(DomSharedStylesHost), i0.ɵɵinject(APP_ID), i0.ɵɵinject(REMOVE_STYLES_ON_COMPONENT_DESTROY));\n};\nDomRendererFactory2.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomRendererFactory2,\n  factory: DomRendererFactory2.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomRendererFactory2, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: EventManager\n    }, {\n      type: DomSharedStylesHost\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [APP_ID]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n      }]\n    }];\n  }, null);\n})();\nclass DefaultDomRenderer2 {\n  constructor(eventManager) {\n    this.eventManager = eventManager;\n    this.data = Object.create(null);\n    this.destroyNode = null;\n  }\n  destroy() {}\n  createElement(name, namespace) {\n    if (namespace) {\n      // TODO: `|| namespace` was added in\n      // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n      // support how Ivy passed around the namespace URI rather than short name at the time. It did\n      // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n      // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n      // namespaces should be and make it consistent.\n      // Related issues:\n      // https://github.com/angular/angular/issues/44028\n      // https://github.com/angular/angular/issues/44883\n      return document.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n    return document.createElement(name);\n  }\n  createComment(value) {\n    return document.createComment(value);\n  }\n  createText(value) {\n    return document.createTextNode(value);\n  }\n  appendChild(parent, newChild) {\n    const targetParent = isTemplateNode(parent) ? parent.content : parent;\n    targetParent.appendChild(newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    if (parent) {\n      const targetParent = isTemplateNode(parent) ? parent.content : parent;\n      targetParent.insertBefore(newChild, refChild);\n    }\n  }\n  removeChild(parent, oldChild) {\n    if (parent) {\n      parent.removeChild(oldChild);\n    }\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    let el = typeof selectorOrNode === 'string' ? document.querySelector(selectorOrNode) : selectorOrNode;\n    if (!el) {\n      throw new Error(`The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n    return el;\n  }\n  parentNode(node) {\n    return node.parentNode;\n  }\n  nextSibling(node) {\n    return node.nextSibling;\n  }\n  setAttribute(el, name, value, namespace) {\n    if (namespace) {\n      name = namespace + ':' + name;\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n  removeAttribute(el, name, namespace) {\n    if (namespace) {\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n  addClass(el, name) {\n    el.classList.add(name);\n  }\n  removeClass(el, name) {\n    el.classList.remove(name);\n  }\n  setStyle(el, style, value, flags) {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n  removeStyle(el, style, flags) {\n    if (flags & RendererStyleFlags2.DashCase) {\n      // removeProperty has no effect when used on camelCased properties.\n      el.style.removeProperty(style);\n    } else {\n      el.style[style] = '';\n    }\n  }\n  setProperty(el, name, value) {\n    NG_DEV_MODE$1 && checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n  setValue(node, value) {\n    node.nodeValue = value;\n  }\n  listen(target, event, callback) {\n    NG_DEV_MODE$1 && checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      return this.eventManager.addGlobalEventListener(target, event, decoratePreventDefault(callback));\n    }\n    return this.eventManager.addEventListener(target, event, decoratePreventDefault(callback));\n  }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new Error(`Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n  }\n}\nfunction isTemplateNode(node) {\n  return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, hostEl, component) {\n    super(eventManager);\n    this.sharedStylesHost = sharedStylesHost;\n    this.hostEl = hostEl;\n    this.shadowRoot = hostEl.attachShadow({\n      mode: 'open'\n    });\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    const styles = flattenStyles(component.id, component.styles);\n    for (const style of styles) {\n      const styleEl = document.createElement('style');\n      styleEl.textContent = style;\n      this.shadowRoot.appendChild(styleEl);\n    }\n  }\n  nodeOrShadowRoot(node) {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n  appendChild(parent, newChild) {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n  removeChild(parent, oldChild) {\n    return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n  }\n  parentNode(node) {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n  destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestory, compId = component.id) {\n    super(eventManager);\n    this.sharedStylesHost = sharedStylesHost;\n    this.removeStylesOnCompDestory = removeStylesOnCompDestory;\n    this.rendererUsageCount = 0;\n    this.styles = flattenStyles(compId, component.styles);\n  }\n  applyStyles() {\n    this.sharedStylesHost.addStyles(this.styles);\n    this.rendererUsageCount++;\n  }\n  destroy() {\n    if (!this.removeStylesOnCompDestory) {\n      return;\n    }\n    this.sharedStylesHost.removeStyles(this.styles);\n    this.rendererUsageCount--;\n    if (this.rendererUsageCount === 0) {\n      this.onDestroy?.();\n    }\n  }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n  constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestory) {\n    const compId = appId + '-' + component.id;\n    super(eventManager, sharedStylesHost, component, removeStylesOnCompDestory, compId);\n    this.contentAttr = shimContentAttribute(compId);\n    this.hostAttr = shimHostAttribute(compId);\n  }\n  applyToHost(element) {\n    this.applyStyles();\n    this.setAttribute(element, this.hostAttr, '');\n  }\n  createElement(parent, name) {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\nclass DomEventsPlugin extends EventManagerPlugin {\n  constructor(doc) {\n    super(doc);\n  }\n  // This plugin should come last in the list of plugins, because it accepts all\n  // events.\n  supports(eventName) {\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    element.addEventListener(eventName, handler, false);\n    return () => this.removeEventListener(element, eventName, handler);\n  }\n  removeEventListener(target, eventName, callback) {\n    return target.removeEventListener(eventName, callback);\n  }\n}\nDomEventsPlugin.ɵfac = function DomEventsPlugin_Factory(t) {\n  return new (t || DomEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n};\nDomEventsPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomEventsPlugin,\n  factory: DomEventsPlugin.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomEventsPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n  'alt': event => event.altKey,\n  'control': event => event.ctrlKey,\n  'meta': event => event.metaKey,\n  'shift': event => event.shiftKey\n};\n/**\n * @publicApi\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n  /**\n   * Initializes an instance of the browser plug-in.\n   * @param doc The document in which key events will be detected.\n   */\n  constructor(doc) {\n    super(doc);\n  }\n  /**\n   * Reports whether a named key event is supported.\n   * @param eventName The event name to query.\n   * @return True if the named key event is supported.\n   */\n  supports(eventName) {\n    return KeyEventsPlugin.parseEventName(eventName) != null;\n  }\n  /**\n   * Registers a handler for a specific element and key event.\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the key event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns The key event that was registered.\n   */\n  addEventListener(element, eventName, handler) {\n    const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n    const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n    return this.manager.getZone().runOutsideAngular(() => {\n      return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n    });\n  }\n  /**\n   * Parses the user provided full keyboard event definition and normalizes it for\n   * later internal use. It ensures the string is all lowercase, converts special\n   * characters to a standard spelling, and orders all the values consistently.\n   *\n   * @param eventName The name of the key event to listen for.\n   * @returns an object with the full, normalized string, and the dom event name\n   * or null in the case when the event doesn't match a keyboard event.\n   */\n  static parseEventName(eventName) {\n    const parts = eventName.toLowerCase().split('.');\n    const domEventName = parts.shift();\n    if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n    const key = KeyEventsPlugin._normalizeKey(parts.pop());\n    let fullKey = '';\n    let codeIX = parts.indexOf('code');\n    if (codeIX > -1) {\n      parts.splice(codeIX, 1);\n      fullKey = 'code.';\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      const index = parts.indexOf(modifierName);\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    }\n    // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n    //       The code must remain in the `result['domEventName']` form.\n    // return {domEventName, fullKey};\n    const result = {};\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n  /**\n   * Determines whether the actual keys pressed match the configured key code string.\n   * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n   * event is attached to the DOM during the `addEventListener` call. This is unseen\n   * by the end user and is normalized for internal consistency and parsing.\n   *\n   * @param event The keyboard event.\n   * @param fullKeyCode The normalized user defined expected key event string\n   * @returns boolean.\n   */\n  static matchEventFullKeyCode(event, fullKeyCode) {\n    let keycode = _keyMap[event.key] || event.key;\n    let key = '';\n    if (fullKeyCode.indexOf('code.') > -1) {\n      keycode = event.code;\n      key = 'code.';\n    }\n    // the keycode could be unidentified so we have to check here\n    if (keycode == null || !keycode) return false;\n    keycode = keycode.toLowerCase();\n    if (keycode === ' ') {\n      keycode = 'space'; // for readability\n    } else if (keycode === '.') {\n      keycode = 'dot'; // because '.' is used as a separator in event names\n    }\n\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName !== keycode) {\n        const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n        if (modifierGetter(event)) {\n          key += modifierName + '.';\n        }\n      }\n    });\n    key += keycode;\n    return key === fullKeyCode;\n  }\n  /**\n   * Configures a handler callback for a key event.\n   * @param fullKey The event name that combines all simultaneous keystrokes.\n   * @param handler The function that responds to the key event.\n   * @param zone The zone in which the event occurred.\n   * @returns A callback function.\n   */\n  static eventCallback(fullKey, handler, zone) {\n    return event => {\n      if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n  /** @internal */\n  static _normalizeKey(keyName) {\n    // TODO: switch to a Map if the mapping grows too much\n    switch (keyName) {\n      case 'esc':\n        return 'escape';\n      default:\n        return keyName;\n    }\n  }\n}\nKeyEventsPlugin.ɵfac = function KeyEventsPlugin_Factory(t) {\n  return new (t || KeyEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n};\nKeyEventsPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: KeyEventsPlugin,\n  factory: KeyEventsPlugin.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeyEventsPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/standalone-components).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```typescript\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```typescript\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n  return ɵinternalCreateApplication({\n    rootComponent,\n    ...createProvidersConfig(options)\n  });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n  return ɵinternalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n  return {\n    appProviders: [...BROWSER_MODULE_PROVIDERS, ...(options?.providers ?? [])],\n    platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n  };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideProtractorTestingSupport` call results in app code.\n  return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n  return new ErrorHandler();\n}\nfunction _document() {\n  // Tell ivy about the global document\n  ɵsetDocument(document);\n  return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_ID,\n  useValue: ɵPLATFORM_BROWSER_ID\n}, {\n  provide: PLATFORM_INITIALIZER,\n  useValue: initDomAdapter,\n  multi: true\n}, {\n  provide: DOCUMENT,\n  useFactory: _document,\n  deps: []\n}];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(NG_DEV_MODE ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [{\n  provide: ɵTESTABILITY_GETTER,\n  useClass: BrowserGetTestability,\n  deps: []\n}, {\n  provide: ɵTESTABILITY,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}, {\n  provide: Testability,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}];\nconst BROWSER_MODULE_PROVIDERS = [{\n  provide: ɵINJECTOR_SCOPE,\n  useValue: 'root'\n}, {\n  provide: ErrorHandler,\n  useFactory: errorHandler,\n  deps: []\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: DomEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT, NgZone, PLATFORM_ID]\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: KeyEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, {\n  provide: DomRendererFactory2,\n  useClass: DomRendererFactory2,\n  deps: [EventManager, DomSharedStylesHost, APP_ID, REMOVE_STYLES_ON_COMPONENT_DESTROY]\n}, {\n  provide: RendererFactory2,\n  useExisting: DomRendererFactory2\n}, {\n  provide: SharedStylesHost,\n  useExisting: DomSharedStylesHost\n}, {\n  provide: DomSharedStylesHost,\n  useClass: DomSharedStylesHost,\n  deps: [DOCUMENT]\n}, {\n  provide: EventManager,\n  useClass: EventManager,\n  deps: [EVENT_MANAGER_PLUGINS, NgZone]\n}, {\n  provide: XhrFactory,\n  useClass: BrowserXhr,\n  deps: []\n}, NG_DEV_MODE ? {\n  provide: BROWSER_MODULE_PROVIDERS_MARKER,\n  useValue: true\n} : []];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n  constructor(providersAlreadyPresent) {\n    if (NG_DEV_MODE && providersAlreadyPresent) {\n      throw new Error(`Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` + `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n    }\n  }\n  /**\n   * Configures a browser-based app to transition from a server-rendered app, if\n   * one is present on the page.\n   *\n   * @param params An object containing an identifier for the app to transition.\n   * The ID must match between the client and server versions of the app.\n   * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n   */\n  static withServerTransition(params) {\n    return {\n      ngModule: BrowserModule,\n      providers: [{\n        provide: APP_ID,\n        useValue: params.appId\n      }, {\n        provide: TRANSITION_ID,\n        useExisting: APP_ID\n      }, SERVER_TRANSITION_PROVIDERS]\n    };\n  }\n}\nBrowserModule.ɵfac = function BrowserModule_Factory(t) {\n  return new (t || BrowserModule)(i0.ɵɵinject(BROWSER_MODULE_PROVIDERS_MARKER, 12));\n};\nBrowserModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BrowserModule,\n  exports: [CommonModule, ApplicationModule]\n});\nBrowserModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n  imports: [CommonModule, ApplicationModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserModule, [{\n    type: NgModule,\n    args: [{\n      providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n      exports: [CommonModule, ApplicationModule]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }, {\n        type: Inject,\n        args: [BROWSER_MODULE_PROVIDERS_MARKER]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Factory to create a `Meta` service instance for the current DOM document.\n */\nfunction createMeta() {\n  return new Meta(ɵɵinject(DOCUMENT));\n}\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n  constructor(_doc) {\n    this._doc = _doc;\n    this._dom = ɵgetDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n  addTag(tag, forceCreation = false) {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n  addTags(tags, forceCreation = false) {\n    if (!tags) return [];\n    return tags.reduce((result, tag) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n  getTag(attrSelector) {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n  getTags(attrSelector) {\n    if (!attrSelector) return [];\n    const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n  updateTag(tag, selector) {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta = this.getTag(selector);\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n  removeTag(attrSelector) {\n    this.removeTagElement(this.getTag(attrSelector));\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n  removeTagElement(meta) {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n  _getOrCreateElement(meta, forceCreation = false) {\n    if (!forceCreation) {\n      const selector = this._parseSelector(meta);\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n    const element = this._dom.createElement('meta');\n    this._setMetaElementAttributes(meta, element);\n    const head = this._doc.getElementsByTagName('head')[0];\n    head.appendChild(element);\n    return element;\n  }\n  _setMetaElementAttributes(tag, el) {\n    Object.keys(tag).forEach(prop => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n    return el;\n  }\n  _parseSelector(tag) {\n    const attr = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n  _containsAttributes(tag, elem) {\n    return Object.keys(tag).every(key => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n  }\n  _getMetaKeyMap(prop) {\n    return META_KEYS_MAP[prop] || prop;\n  }\n}\nMeta.ɵfac = function Meta_Factory(t) {\n  return new (t || Meta)(i0.ɵɵinject(DOCUMENT));\n};\nMeta.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Meta,\n  factory: function Meta_Factory(t) {\n    let r = null;\n    if (t) {\n      r = new t();\n    } else {\n      r = createMeta();\n    }\n    return r;\n  },\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Meta, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: createMeta,\n      deps: []\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n  httpEquiv: 'http-equiv'\n};\n\n/**\n * Factory to create Title service.\n */\nfunction createTitle() {\n  return new Title(ɵɵinject(DOCUMENT));\n}\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  /**\n   * Get the title of the current HTML document.\n   */\n  getTitle() {\n    return this._doc.title;\n  }\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n  setTitle(newTitle) {\n    this._doc.title = newTitle || '';\n  }\n}\nTitle.ɵfac = function Title_Factory(t) {\n  return new (t || Title)(i0.ɵɵinject(DOCUMENT));\n};\nTitle.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Title,\n  factory: function Title_Factory(t) {\n    let r = null;\n    if (t) {\n      r = new t();\n    } else {\n      r = createTitle();\n    }\n    return r;\n  },\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Title, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: createTitle,\n      deps: []\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n    ng[name] = value;\n  }\n}\nconst win = typeof window !== 'undefined' && window || {};\nclass ChangeDetectionPerfRecord {\n  constructor(msPerTick, numTicks) {\n    this.msPerTick = msPerTick;\n    this.numTicks = numTicks;\n  }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n  constructor(ref) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  }\n  // tslint:disable:no-console\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n  timeChangeDetection(config) {\n    const record = config && config['record'];\n    const profileName = 'Change Detection';\n    // Profiler is not available in Android browsers without dev tools opened\n    const isProfilerAvailable = win.console.profile != null;\n    if (record && isProfilerAvailable) {\n      win.console.profile(profileName);\n    }\n    const start = performanceNow();\n    let numTicks = 0;\n    while (numTicks < 5 || performanceNow() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const end = performanceNow();\n    if (record && isProfilerAvailable) {\n      win.console.profileEnd(profileName);\n    }\n    const msPerTick = (end - start) / numTicks;\n    win.console.log(`ran ${numTicks} change detection cycles`);\n    win.console.log(`${msPerTick.toFixed(2)} ms per check`);\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\nfunction performanceNow() {\n  return win.performance && win.performance.now ? win.performance.now() : new Date().getTime();\n}\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\nfunction escapeHtml(text) {\n  const escapedText = {\n    '&': '&a;',\n    '\"': '&q;',\n    '\\'': '&s;',\n    '<': '&l;',\n    '>': '&g;'\n  };\n  return text.replace(/[&\"'<>]/g, s => escapedText[s]);\n}\nfunction unescapeHtml(text) {\n  const unescapedText = {\n    '&a;': '&',\n    '&q;': '\"',\n    '&s;': '\\'',\n    '&l;': '<',\n    '&g;': '>'\n  };\n  return text.replace(/&[^;]+;/g, s => unescapedText[s]);\n}\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n */\nfunction makeStateKey(key) {\n  return key;\n}\n/**\n * A key value store that is transferred from the application on the server side to the application\n * on the client side.\n *\n * The `TransferState` is available as an injectable token.\n * On the client, just inject this token using DI and use it, it will be lazily initialized.\n * On the server it's already included if `renderApplication` function is used. Otherwise, import\n * the `ServerTransferStateModule` module to make the `TransferState` available.\n *\n * The values in the store are serialized/deserialized using JSON.stringify/JSON.parse. So only\n * boolean, number, string, null and non-class objects will be serialized and deserialized in a\n * non-lossy manner.\n *\n * @publicApi\n */\nclass TransferState {\n  constructor() {\n    this.store = {};\n    this.onSerializeCallbacks = {};\n    this.store = retrieveTransferredState(inject(DOCUMENT), inject(APP_ID));\n  }\n  /**\n   * Get the value corresponding to a key. Return `defaultValue` if key is not found.\n   */\n  get(key, defaultValue) {\n    return this.store[key] !== undefined ? this.store[key] : defaultValue;\n  }\n  /**\n   * Set the value corresponding to a key.\n   */\n  set(key, value) {\n    this.store[key] = value;\n  }\n  /**\n   * Remove a key from the store.\n   */\n  remove(key) {\n    delete this.store[key];\n  }\n  /**\n   * Test whether a key exists in the store.\n   */\n  hasKey(key) {\n    return this.store.hasOwnProperty(key);\n  }\n  /**\n   * Indicates whether the state is empty.\n   */\n  get isEmpty() {\n    return Object.keys(this.store).length === 0;\n  }\n  /**\n   * Register a callback to provide the value for a key when `toJson` is called.\n   */\n  onSerialize(key, callback) {\n    this.onSerializeCallbacks[key] = callback;\n  }\n  /**\n   * Serialize the current state of the store to JSON.\n   */\n  toJson() {\n    // Call the onSerialize callbacks and put those values into the store.\n    for (const key in this.onSerializeCallbacks) {\n      if (this.onSerializeCallbacks.hasOwnProperty(key)) {\n        try {\n          this.store[key] = this.onSerializeCallbacks[key]();\n        } catch (e) {\n          console.warn('Exception in onSerialize callback: ', e);\n        }\n      }\n    }\n    return JSON.stringify(this.store);\n  }\n}\nTransferState.ɵfac = function TransferState_Factory(t) {\n  return new (t || TransferState)();\n};\nTransferState.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TransferState,\n  factory: TransferState.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TransferState, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nfunction retrieveTransferredState(doc, appId) {\n  // Locate the script tag with the JSON data transferred from the server.\n  // The id of the script tag is set to the Angular appId + 'state'.\n  const script = doc.getElementById(appId + '-state');\n  let initialState = {};\n  if (script && script.textContent) {\n    try {\n      // Avoid using any here as it triggers lint errors in google3 (any is not allowed).\n      initialState = JSON.parse(unescapeHtml(script.textContent));\n    } catch (e) {\n      console.warn('Exception while restoring TransferState for app ' + appId, e);\n    }\n  }\n  return initialState;\n}\n/**\n * NgModule to install on the client side while using the `TransferState` to transfer state from\n * server to client.\n *\n * @publicApi\n * @deprecated no longer needed, you can inject the `TransferState` in an app without providing\n *     this module.\n */\nclass BrowserTransferStateModule {}\nBrowserTransferStateModule.ɵfac = function BrowserTransferStateModule_Factory(t) {\n  return new (t || BrowserTransferStateModule)();\n};\nBrowserTransferStateModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BrowserTransferStateModule\n});\nBrowserTransferStateModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserTransferStateModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all() {\n    return () => true;\n  }\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n  static css(selector) {\n    return debugElement => {\n      return debugElement.nativeElement != null ? elementMatches(debugElement.nativeElement, selector) : false;\n    };\n  }\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n  static directive(type) {\n    return debugNode => debugNode.providerTokens.indexOf(type) !== -1;\n  }\n}\nfunction elementMatches(n, selector) {\n  if (ɵgetDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) || n.msMatchesSelector && n.msMatchesSelector(selector) || n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n  return false;\n}\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see `HammerGestureConfig`\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n  constructor() {\n    /**\n     * A set of supported event names for gestures to be used in Angular.\n     * Angular supports all built-in recognizers, as listed in\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     */\n    this.events = [];\n    /**\n     * Maps gesture event names to a set of configuration options\n     * that specify overrides to the default values for specific properties.\n     *\n     * The key is a supported event name to be configured,\n     * and the options object contains a set of properties, with override values\n     * to be applied to the named recognizer event.\n     * For example, to disable recognition of the rotate event, specify\n     *  `{\"rotate\": {\"enable\": false}}`.\n     *\n     * Properties that are not present take the HammerJS default values.\n     * For information about which properties are supported for which events,\n     * and their allowed and default values, see\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     *\n     */\n    this.overrides = {};\n  }\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n  buildHammer(element) {\n    const mc = new Hammer(element, this.options);\n    mc.get('pinch').set({\n      enable: true\n    });\n    mc.get('rotate').set({\n      enable: true\n    });\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n    return mc;\n  }\n}\nHammerGestureConfig.ɵfac = function HammerGestureConfig_Factory(t) {\n  return new (t || HammerGestureConfig)();\n};\nHammerGestureConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HammerGestureConfig,\n  factory: HammerGestureConfig.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGestureConfig, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n  constructor(doc, _config, console, loader) {\n    super(doc);\n    this._config = _config;\n    this.console = console;\n    this.loader = loader;\n    this._loaderPromise = null;\n  }\n  supports(eventName) {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n    if (!window.Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` + `loaded and no custom loader has been specified.`);\n      }\n      return false;\n    }\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n    // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n    if (!window.Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n      // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n      let cancelRegistration = false;\n      let deregister = () => {\n        cancelRegistration = true;\n      };\n      zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n        // If Hammer isn't actually loaded when the custom loader resolves, give up.\n        if (!window.Hammer) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n          }\n          deregister = () => {};\n          return;\n        }\n        if (!cancelRegistration) {\n          // Now that Hammer is loaded and the listener is being loaded for real,\n          // the deregistration function changes from canceling registration to\n          // removal.\n          deregister = this.addEventListener(element, eventName, handler);\n        }\n      }).catch(() => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` + `Hammer.JS loader failed.`);\n        }\n        deregister = () => {};\n      }));\n      // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n      return () => {\n        deregister();\n      };\n    }\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n      const callback = function (eventObj) {\n        zone.runGuarded(function () {\n          handler(eventObj);\n        });\n      };\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback);\n        // destroy mc to prevent memory leak\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n  isCustomEvent(eventName) {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n}\nHammerGesturesPlugin.ɵfac = function HammerGesturesPlugin_Factory(t) {\n  return new (t || HammerGesturesPlugin)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(HAMMER_GESTURE_CONFIG), i0.ɵɵinject(i0.ɵConsole), i0.ɵɵinject(HAMMER_LOADER, 8));\n};\nHammerGesturesPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HammerGesturesPlugin,\n  factory: HammerGesturesPlugin.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGesturesPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: HammerGestureConfig,\n      decorators: [{\n        type: Inject,\n        args: [HAMMER_GESTURE_CONFIG]\n      }]\n    }, {\n      type: i0.ɵConsole\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [HAMMER_LOADER]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's EventManager.\n *\n * @publicApi\n */\nclass HammerModule {}\nHammerModule.ɵfac = function HammerModule_Factory(t) {\n  return new (t || HammerModule)();\n};\nHammerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: HammerModule\n});\nHammerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: EVENT_MANAGER_PLUGINS,\n    useClass: HammerGesturesPlugin,\n    multi: true,\n    deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n  }, {\n    provide: HAMMER_GESTURE_CONFIG,\n    useClass: HammerGestureConfig,\n    deps: []\n  }]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig,\n        deps: []\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {}\nDomSanitizer.ɵfac = function DomSanitizer_Factory(t) {\n  return new (t || DomSanitizer)();\n};\nDomSanitizer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomSanitizer,\n  factory: function DomSanitizer_Factory(t) {\n    let r = null;\n    if (t) {\n      r = new (t || DomSanitizer)();\n    } else {\n      r = i0.ɵɵinject(DomSanitizerImpl);\n    }\n    return r;\n  },\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useExisting: forwardRef(() => DomSanitizerImpl)\n    }]\n  }], null, null);\n})();\nfunction domSanitizerImplFactory(injector) {\n  return new DomSanitizerImpl(injector.get(DOCUMENT));\n}\nclass DomSanitizerImpl extends DomSanitizer {\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n  sanitize(ctx, value) {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value;\n      case SecurityContext.HTML:\n        if (ɵallowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n      case SecurityContext.STYLE:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return value;\n      case SecurityContext.SCRIPT:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        throw new Error('unsafe value used in a script context');\n      case SecurityContext.URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return ɵ_sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        throw new Error(`unsafe value used in a resource URL context (see ${ɵXSS_SECURITY_URL})`);\n      default:\n        throw new Error(`Unexpected SecurityContext ${ctx} (see ${ɵXSS_SECURITY_URL})`);\n    }\n  }\n  bypassSecurityTrustHtml(value) {\n    return ɵbypassSanitizationTrustHtml(value);\n  }\n  bypassSecurityTrustStyle(value) {\n    return ɵbypassSanitizationTrustStyle(value);\n  }\n  bypassSecurityTrustScript(value) {\n    return ɵbypassSanitizationTrustScript(value);\n  }\n  bypassSecurityTrustUrl(value) {\n    return ɵbypassSanitizationTrustUrl(value);\n  }\n  bypassSecurityTrustResourceUrl(value) {\n    return ɵbypassSanitizationTrustResourceUrl(value);\n  }\n}\nDomSanitizerImpl.ɵfac = function DomSanitizerImpl_Factory(t) {\n  return new (t || DomSanitizerImpl)(i0.ɵɵinject(DOCUMENT));\n};\nDomSanitizerImpl.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomSanitizerImpl,\n  factory: function DomSanitizerImpl_Factory(t) {\n    let r = null;\n    if (t) {\n      r = new t();\n    } else {\n      r = domSanitizerImplFactory(i0.ɵɵinject(Injector));\n    }\n    return r;\n  },\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizerImpl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: domSanitizerImplFactory,\n      deps: [Injector]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('15.2.10');\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, BrowserTransferStateModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, Meta, REMOVE_STYLES_ON_COMPONENT_DESTROY, Title, TransferState, VERSION, bootstrapApplication, createApplication, disableDebugTools, enableDebugTools, makeStateKey, platformBrowser, provideProtractorTestingSupport, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, DomSharedStylesHost as ɵDomSharedStylesHost, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, NAMESPACE_URIS as ɵNAMESPACE_URIS, SharedStylesHost as ɵSharedStylesHost, TRANSITION_ID as ɵTRANSITION_ID, escapeHtml as ɵescapeHtml, flattenStyles as ɵflattenStyles, initDomAdapter as ɵinitDomAdapter, shimContentAttribute as ɵshimContentAttribute, shimHostAttribute as ɵshimHostAttribute };", "map": {"version": 3, "names": ["ɵDomAdapter", "ɵsetRootDomAdapter", "ɵparseCookieValue", "ɵgetDOM", "DOCUMENT", "ɵPLATFORM_BROWSER_ID", "XhrFactory", "CommonModule", "i0", "InjectionToken", "ApplicationInitStatus", "APP_INITIALIZER", "Injector", "ɵglobal", "Injectable", "Inject", "ViewEncapsulation", "APP_ID", "RendererStyleFlags2", "ɵinternalCreateApplication", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵsetDocument", "PLATFORM_ID", "PLATFORM_INITIALIZER", "createPlatformFactory", "platformCore", "ɵTESTABILITY_GETTER", "ɵTESTABILITY", "Testability", "NgZone", "TestabilityRegistry", "ɵINJECTOR_SCOPE", "RendererFactory2", "ApplicationModule", "NgModule", "Optional", "SkipSelf", "ɵɵinject", "ApplicationRef", "inject", "ɵConsole", "forwardRef", "ɵXSS_SECURITY_URL", "SecurityContext", "ɵallowSanitizationBypassAndThrow", "ɵunwrapSafeValue", "ɵ_sanitizeUrl", "ɵ_sanitizeHtml", "ɵbypassSanitizationTrustHtml", "ɵbypassSanitizationTrustStyle", "ɵbypassSanitizationTrustScript", "ɵbypassSanitizationTrustUrl", "ɵbypassSanitizationTrustResourceUrl", "Version", "GenericBrowserDomAdapter", "constructor", "arguments", "supportsDOMEvents", "BrowserDomAdapter", "makeCurrent", "onAndCancel", "el", "evt", "listener", "addEventListener", "removeEventListener", "dispatchEvent", "remove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "tagName", "doc", "getDefaultDocument", "createHtmlDocument", "document", "implementation", "createHTMLDocument", "isElementNode", "nodeType", "Node", "ELEMENT_NODE", "isShadowRoot", "DocumentFragment", "getGlobalEventTarget", "target", "window", "body", "getBaseHref", "href", "getBaseElementHref", "relativePath", "resetBaseElement", "baseElement", "getUserAgent", "navigator", "userAgent", "<PERSON><PERSON><PERSON><PERSON>", "name", "cookie", "querySelector", "getAttribute", "urlParsingNode", "url", "setAttribute", "pathName", "pathname", "char<PERSON>t", "TRANSITION_ID", "appInitializerFactory", "transitionId", "injector", "get", "donePromise", "then", "dom", "styles", "querySelectorAll", "i", "length", "SERVER_TRANSITION_PROVIDERS", "provide", "useFactory", "deps", "multi", "BrowserGetTestability", "addToWindow", "registry", "elem", "findInAncestors", "testability", "findTestabilityInTree", "Error", "getAllTestabilities", "getAllRootElements", "whenAllStable", "callback", "testabilities", "count", "didWork", "decrement", "didWork_", "for<PERSON>ach", "whenStable", "push", "t", "getTestability", "host", "parentElement", "BrowserXhr", "build", "XMLHttpRequest", "ɵfac", "ɵprov", "type", "EVENT_MANAGER_PLUGINS", "EventManager", "plugins", "_zone", "_eventNameToPlugin", "Map", "plugin", "manager", "_plugins", "slice", "reverse", "element", "eventName", "handler", "_findPluginFor", "addGlobalEventListener", "getZone", "supports", "set", "undefined", "decorators", "args", "EventManagerPlugin", "_doc", "SharedStylesHost", "usageCount", "addStyles", "style", "changeUsageCount", "onStyleAdded", "removeStyles", "onStyleRemoved", "getAllStyles", "keys", "delta", "map", "usage", "delete", "ngOnDestroy", "clear", "DomSharedStylesHost", "styleRef", "hostNodes", "Set", "resetHostNodes", "addStyleToHost", "styleElements", "e", "addHost", "hostNode", "add", "removeHost", "styleEl", "textContent", "append<PERSON><PERSON><PERSON>", "styleElRef", "head", "NAMESPACE_URIS", "COMPONENT_REGEX", "NG_DEV_MODE$1", "ngDevMode", "COMPONENT_VARIABLE", "HOST_ATTR", "CONTENT_ATTR", "REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT", "REMOVE_STYLES_ON_COMPONENT_DESTROY", "providedIn", "factory", "shimContentAttribute", "componentShortId", "replace", "shimHostAttribute", "flattenStyles", "compId", "flat", "s", "decoratePreventDefault", "<PERSON><PERSON><PERSON><PERSON>", "event", "allowDefaultBehavior", "preventDefault", "returnValue", "DomRendererFactory2", "eventManager", "sharedStylesHost", "appId", "removeStylesOnCompDestory", "rendererByCompId", "defaultRenderer", "DefaultDomRenderer2", "<PERSON><PERSON><PERSON><PERSON>", "renderer", "getOr<PERSON><PERSON><PERSON><PERSON><PERSON>", "EmulatedEncapsulationDomRenderer2", "applyToHost", "NoneEncapsulationDomRenderer", "applyStyles", "id", "encapsulation", "Emulated", "ShadowDom", "ShadowDom<PERSON><PERSON><PERSON>", "onDestroy", "begin", "end", "data", "Object", "create", "destroyNode", "destroy", "namespace", "createElementNS", "createComment", "value", "createText", "createTextNode", "parent", "<PERSON><PERSON><PERSON><PERSON>", "targetParent", "isTemplateNode", "content", "insertBefore", "refChild", "<PERSON><PERSON><PERSON><PERSON>", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "nextS<PERSON>ling", "namespaceUri", "setAttributeNS", "removeAttribute", "removeAttributeNS", "addClass", "classList", "removeClass", "setStyle", "flags", "DashCase", "Important", "setProperty", "removeStyle", "removeProperty", "checkNoSyntheticProp", "setValue", "nodeValue", "listen", "AT_CHARCODE", "charCodeAt", "<PERSON><PERSON><PERSON>", "hostEl", "component", "shadowRoot", "attachShadow", "mode", "nodeOrShadowRoot", "rendererUsageCount", "contentAttr", "hostAttr", "DomEventsPlugin", "MODIFIER_KEYS", "_keyMap", "MODIFIER_KEY_GETTERS", "altKey", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "KeyEventsPlugin", "parseEventName", "parsedEvent", "outsideH<PERSON>ler", "eventCallback", "runOutsideAngular", "parts", "toLowerCase", "split", "domEventName", "shift", "key", "_normalizeKey", "pop", "<PERSON><PERSON><PERSON>", "codeIX", "indexOf", "splice", "modifierName", "index", "result", "matchEventFullKeyCode", "fullKeyCode", "keycode", "code", "modifierGetter", "zone", "runGuarded", "keyName", "NG_DEV_MODE", "bootstrapApplication", "rootComponent", "options", "createProvidersConfig", "createApplication", "appProviders", "BROWSER_MODULE_PROVIDERS", "providers", "platformProviders", "INTERNAL_BROWSER_PLATFORM_PROVIDERS", "provideProtractorTestingSupport", "TESTABILITY_PROVIDERS", "initDomAdapter", "<PERSON><PERSON><PERSON><PERSON>", "_document", "useValue", "platformBrowser", "BROWSER_MODULE_PROVIDERS_MARKER", "useClass", "useExisting", "BrowserModule", "providersAlreadyPresent", "withServerTransition", "params", "ngModule", "ɵmod", "ɵinj", "exports", "createMeta", "Meta", "_dom", "addTag", "tag", "forceCreation", "_getOrCreateElement", "addTags", "tags", "reduce", "getTag", "attrSelector", "getTags", "list", "call", "updateTag", "selector", "_parseSelector", "meta", "_setMetaElementAttributes", "removeTag", "removeTagElement", "filter", "_containsAttributes", "getElementsByTagName", "prop", "_getMetaKeyMap", "attr", "every", "META_KEYS_MAP", "httpEquiv", "createTitle", "Title", "getTitle", "title", "setTitle", "newTitle", "exportNgVar", "COMPILED", "ng", "win", "ChangeDetectionPerfRecord", "msPerTick", "numTicks", "AngularProfiler", "ref", "appRef", "timeChangeDetection", "config", "record", "profileName", "isProfilerAvailable", "console", "profile", "start", "performanceNow", "tick", "profileEnd", "log", "toFixed", "performance", "now", "Date", "getTime", "PROFILER_GLOBAL_NAME", "enableDebugTools", "disableDebugTools", "escapeHtml", "text", "escapedText", "unescapeHtml", "unescapedText", "makeStateKey", "TransferState", "store", "onSerializeCallbacks", "retrieveTransferredState", "defaultValue", "<PERSON><PERSON><PERSON>", "hasOwnProperty", "isEmpty", "onSerialize", "to<PERSON><PERSON>", "warn", "JSON", "stringify", "script", "getElementById", "initialState", "parse", "BrowserTransferStateModule", "By", "all", "css", "debugElement", "nativeElement", "elementMatches", "directive", "debugNode", "providerTokens", "n", "matches", "msMatchesSelector", "webkitMatchesSelector", "EVENT_NAMES", "HAMMER_GESTURE_CONFIG", "HAMMER_LOADER", "HammerGestureConfig", "events", "overrides", "buildHammer", "mc", "Hammer", "enable", "HammerGesturesPlugin", "_config", "loader", "_loaderPromise", "isCustomEvent", "cancelRegistration", "deregister", "catch", "eventObj", "on", "off", "HammerModule", "Dom<PERSON><PERSON><PERSON>zer", "DomSanitizerImpl", "domSanitizerImplFactory", "sanitize", "ctx", "NONE", "HTML", "String", "toString", "STYLE", "SCRIPT", "URL", "RESOURCE_URL", "bypassSecurityTrustHtml", "bypassSecurityTrustStyle", "bypassSecurityTrustScript", "bypassSecurityTrustUrl", "bypassSecurityTrustResourceUrl", "VERSION", "ɵBrowserDomAdapter", "ɵBrowserGetTestability", "ɵDomEventsPlugin", "ɵDomRendererFactory2", "ɵDomSanitizerImpl", "ɵDomSharedStylesHost", "ɵHammerGesturesPlugin", "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS", "ɵKeyEventsPlugin", "ɵNAMESPACE_URIS", "ɵSharedStylesHost", "ɵTRANSITION_ID", "ɵescapeHtml", "ɵflattenStyles", "ɵinitDomAdapter", "ɵshimContentAttribute", "ɵshimHostAttribute"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/platform-browser/fesm2020/platform-browser.mjs"], "sourcesContent": ["/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ApplicationInitStatus, APP_INITIALIZER, Injector, ɵglobal, Injectable, Inject, ViewEncapsulation, APP_ID, RendererStyleFlags2, ɵinternalCreateApplication, ErrorHandler, ɵsetDocument, PLATFORM_ID, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, ApplicationModule, NgModule, Optional, SkipSelf, ɵɵinject, ApplicationRef, inject, ɵConsole, forwardRef, ɵXSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, Version } from '@angular/core';\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n    constructor() {\n        super(...arguments);\n        this.supportsDOMEvents = true;\n    }\n}\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n/* tslint:disable:requireParameterType no-console */\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n    static makeCurrent() {\n        ɵsetRootDomAdapter(new BrowserDomAdapter());\n    }\n    onAndCancel(el, evt, listener) {\n        el.addEventListener(evt, listener, false);\n        // Needed to follow Dart's subscription semantic, until fix of\n        // https://code.google.com/p/dart/issues/detail?id=17406\n        return () => {\n            el.removeEventListener(evt, listener, false);\n        };\n    }\n    dispatchEvent(el, evt) {\n        el.dispatchEvent(evt);\n    }\n    remove(node) {\n        if (node.parentNode) {\n            node.parentNode.removeChild(node);\n        }\n    }\n    createElement(tagName, doc) {\n        doc = doc || this.getDefaultDocument();\n        return doc.createElement(tagName);\n    }\n    createHtmlDocument() {\n        return document.implementation.createHTMLDocument('fakeTitle');\n    }\n    getDefaultDocument() {\n        return document;\n    }\n    isElementNode(node) {\n        return node.nodeType === Node.ELEMENT_NODE;\n    }\n    isShadowRoot(node) {\n        return node instanceof DocumentFragment;\n    }\n    /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n    getGlobalEventTarget(doc, target) {\n        if (target === 'window') {\n            return window;\n        }\n        if (target === 'document') {\n            return doc;\n        }\n        if (target === 'body') {\n            return doc.body;\n        }\n        return null;\n    }\n    getBaseHref(doc) {\n        const href = getBaseElementHref();\n        return href == null ? null : relativePath(href);\n    }\n    resetBaseElement() {\n        baseElement = null;\n    }\n    getUserAgent() {\n        return window.navigator.userAgent;\n    }\n    getCookie(name) {\n        return ɵparseCookieValue(document.cookie, name);\n    }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n    baseElement = baseElement || document.querySelector('base');\n    return baseElement ? baseElement.getAttribute('href') : null;\n}\n// based on urlUtils.js in AngularJS 1\nlet urlParsingNode;\nfunction relativePath(url) {\n    urlParsingNode = urlParsingNode || document.createElement('a');\n    urlParsingNode.setAttribute('href', url);\n    const pathName = urlParsingNode.pathname;\n    return pathName.charAt(0) === '/' ? pathName : `/${pathName}`;\n}\n\n/**\n * An id that identifies a particular application being bootstrapped, that should\n * match across the client/server boundary.\n */\nconst TRANSITION_ID = new InjectionToken('TRANSITION_ID');\nfunction appInitializerFactory(transitionId, document, injector) {\n    return () => {\n        // Wait for all application initializers to be completed before removing the styles set by\n        // the server.\n        injector.get(ApplicationInitStatus).donePromise.then(() => {\n            const dom = ɵgetDOM();\n            const styles = document.querySelectorAll(`style[ng-transition=\"${transitionId}\"]`);\n            for (let i = 0; i < styles.length; i++) {\n                dom.remove(styles[i]);\n            }\n        });\n    };\n}\nconst SERVER_TRANSITION_PROVIDERS = [\n    {\n        provide: APP_INITIALIZER,\n        useFactory: appInitializerFactory,\n        deps: [TRANSITION_ID, DOCUMENT, Injector],\n        multi: true\n    },\n];\n\nclass BrowserGetTestability {\n    addToWindow(registry) {\n        ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n            const testability = registry.findTestabilityInTree(elem, findInAncestors);\n            if (testability == null) {\n                throw new Error('Could not find testability for element.');\n            }\n            return testability;\n        };\n        ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n        ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n        const whenAllStable = (callback /** TODO #9100 */) => {\n            const testabilities = ɵglobal['getAllAngularTestabilities']();\n            let count = testabilities.length;\n            let didWork = false;\n            const decrement = function (didWork_ /** TODO #9100 */) {\n                didWork = didWork || didWork_;\n                count--;\n                if (count == 0) {\n                    callback(didWork);\n                }\n            };\n            testabilities.forEach(function (testability /** TODO #9100 */) {\n                testability.whenStable(decrement);\n            });\n        };\n        if (!ɵglobal['frameworkStabilizers']) {\n            ɵglobal['frameworkStabilizers'] = [];\n        }\n        ɵglobal['frameworkStabilizers'].push(whenAllStable);\n    }\n    findTestabilityInTree(registry, elem, findInAncestors) {\n        if (elem == null) {\n            return null;\n        }\n        const t = registry.getTestability(elem);\n        if (t != null) {\n            return t;\n        }\n        else if (!findInAncestors) {\n            return null;\n        }\n        if (ɵgetDOM().isShadowRoot(elem)) {\n            return this.findTestabilityInTree(registry, elem.host, true);\n        }\n        return this.findTestabilityInTree(registry, elem.parentElement, true);\n    }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n    build() {\n        return new XMLHttpRequest();\n    }\n}\nBrowserXhr.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserXhr, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nBrowserXhr.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserXhr });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserXhr, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * The injection token for the event-manager plug-in service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken('EventManagerPlugins');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n    /**\n     * Initializes an instance of the event-manager service.\n     */\n    constructor(plugins, _zone) {\n        this._zone = _zone;\n        this._eventNameToPlugin = new Map();\n        plugins.forEach((plugin) => {\n            plugin.manager = this;\n        });\n        this._plugins = plugins.slice().reverse();\n    }\n    /**\n     * Registers a handler for a specific element and event.\n     *\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns  A callback function that can be used to remove the handler.\n     */\n    addEventListener(element, eventName, handler) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addEventListener(element, eventName, handler);\n    }\n    /**\n     * Registers a global handler for an event in a target view.\n     *\n     * @param target A target for global event notifications. One of \"window\", \"document\", or \"body\".\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns A callback function that can be used to remove the handler.\n     * @deprecated No longer being used in Ivy code. To be removed in version 14.\n     */\n    addGlobalEventListener(target, eventName, handler) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addGlobalEventListener(target, eventName, handler);\n    }\n    /**\n     * Retrieves the compilation zone in which event listeners are registered.\n     */\n    getZone() {\n        return this._zone;\n    }\n    /** @internal */\n    _findPluginFor(eventName) {\n        const plugin = this._eventNameToPlugin.get(eventName);\n        if (plugin) {\n            return plugin;\n        }\n        const plugins = this._plugins;\n        for (let i = 0; i < plugins.length; i++) {\n            const plugin = plugins[i];\n            if (plugin.supports(eventName)) {\n                this._eventNameToPlugin.set(eventName, plugin);\n                return plugin;\n            }\n        }\n        throw new Error(`No event manager plugin found for event ${eventName}`);\n    }\n}\nEventManager.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: EventManager, deps: [{ token: EVENT_MANAGER_PLUGINS }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\nEventManager.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: EventManager });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: EventManager, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [EVENT_MANAGER_PLUGINS]\n                }] }, { type: i0.NgZone }]; } });\nclass EventManagerPlugin {\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    addGlobalEventListener(element, eventName, handler) {\n        const target = ɵgetDOM().getGlobalEventTarget(this._doc, element);\n        if (!target) {\n            throw new Error(`Unsupported event target ${target} for event ${eventName}`);\n        }\n        return this.addEventListener(target, eventName, handler);\n    }\n}\n\nclass SharedStylesHost {\n    constructor() {\n        this.usageCount = new Map();\n    }\n    addStyles(styles) {\n        for (const style of styles) {\n            const usageCount = this.changeUsageCount(style, 1);\n            if (usageCount === 1) {\n                this.onStyleAdded(style);\n            }\n        }\n    }\n    removeStyles(styles) {\n        for (const style of styles) {\n            const usageCount = this.changeUsageCount(style, -1);\n            if (usageCount === 0) {\n                this.onStyleRemoved(style);\n            }\n        }\n    }\n    onStyleRemoved(style) { }\n    onStyleAdded(style) { }\n    getAllStyles() {\n        return this.usageCount.keys();\n    }\n    changeUsageCount(style, delta) {\n        const map = this.usageCount;\n        let usage = map.get(style) ?? 0;\n        usage += delta;\n        if (usage > 0) {\n            map.set(style, usage);\n        }\n        else {\n            map.delete(style);\n        }\n        return usage;\n    }\n    ngOnDestroy() {\n        for (const style of this.getAllStyles()) {\n            this.onStyleRemoved(style);\n        }\n        this.usageCount.clear();\n    }\n}\nSharedStylesHost.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: SharedStylesHost, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nSharedStylesHost.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: SharedStylesHost });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: SharedStylesHost, decorators: [{\n            type: Injectable\n        }] });\nclass DomSharedStylesHost extends SharedStylesHost {\n    constructor(doc) {\n        super();\n        this.doc = doc;\n        // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n        this.styleRef = new Map();\n        this.hostNodes = new Set();\n        this.resetHostNodes();\n    }\n    onStyleAdded(style) {\n        for (const host of this.hostNodes) {\n            this.addStyleToHost(host, style);\n        }\n    }\n    onStyleRemoved(style) {\n        const styleRef = this.styleRef;\n        const styleElements = styleRef.get(style);\n        styleElements?.forEach(e => e.remove());\n        styleRef.delete(style);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this.styleRef.clear();\n        this.resetHostNodes();\n    }\n    addHost(hostNode) {\n        this.hostNodes.add(hostNode);\n        for (const style of this.getAllStyles()) {\n            this.addStyleToHost(hostNode, style);\n        }\n    }\n    removeHost(hostNode) {\n        this.hostNodes.delete(hostNode);\n    }\n    addStyleToHost(host, style) {\n        const styleEl = this.doc.createElement('style');\n        styleEl.textContent = style;\n        host.appendChild(styleEl);\n        const styleElRef = this.styleRef.get(style);\n        if (styleElRef) {\n            styleElRef.push(styleEl);\n        }\n        else {\n            this.styleRef.set(style, [styleEl]);\n        }\n    }\n    resetHostNodes() {\n        const hostNodes = this.hostNodes;\n        hostNodes.clear();\n        // Re-add the head element back since this is the default host.\n        hostNodes.add(this.doc.head);\n    }\n}\nDomSharedStylesHost.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSharedStylesHost, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDomSharedStylesHost.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSharedStylesHost });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSharedStylesHost, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\nconst NAMESPACE_URIS = {\n    'svg': 'http://www.w3.org/2000/svg',\n    'xhtml': 'http://www.w3.org/1999/xhtml',\n    'xlink': 'http://www.w3.org/1999/xlink',\n    'xml': 'http://www.w3.org/XML/1998/namespace',\n    'xmlns': 'http://www.w3.org/2000/xmlns/',\n    'math': 'http://www.w3.org/1998/MathML/',\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst NG_DEV_MODE$1 = typeof ngDevMode === 'undefined' || !!ngDevMode;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = false;\n/**\n * A [DI token](guide/glossary#di-token \"DI token definition\") that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `false`. This will be changed in the next major version.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken('RemoveStylesOnCompDestory', {\n    providedIn: 'root',\n    factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT,\n});\nfunction shimContentAttribute(componentShortId) {\n    return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n    return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction flattenStyles(compId, styles) {\n    // Cannot use `Infinity` as depth as `infinity` is not a number literal in TypeScript.\n    // See: https://github.com/microsoft/TypeScript/issues/32277\n    return styles.flat(100).map(s => s.replace(COMPONENT_REGEX, compId));\n}\nfunction decoratePreventDefault(eventHandler) {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle the\n    // two differently. In the first case, the special '__ngUnwrap__' token is passed to the unwrap\n    // the listener (see below).\n    return (event) => {\n        // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n        // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The debug_node\n        // can inspect the listener toString contents for the existence of this special token. Because\n        // the token is a string literal, it is ensured to not be modified by compiled code.\n        if (event === '__ngUnwrap__') {\n            return eventHandler;\n        }\n        const allowDefaultBehavior = eventHandler(event);\n        if (allowDefaultBehavior === false) {\n            // TODO(tbosch): move preventDefault into event plugins...\n            event.preventDefault();\n            event.returnValue = false;\n        }\n        return undefined;\n    };\n}\nclass DomRendererFactory2 {\n    constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestory) {\n        this.eventManager = eventManager;\n        this.sharedStylesHost = sharedStylesHost;\n        this.appId = appId;\n        this.removeStylesOnCompDestory = removeStylesOnCompDestory;\n        this.rendererByCompId = new Map();\n        this.defaultRenderer = new DefaultDomRenderer2(eventManager);\n    }\n    createRenderer(element, type) {\n        if (!element || !type) {\n            return this.defaultRenderer;\n        }\n        const renderer = this.getOrCreateRenderer(element, type);\n        // Renderers have different logic due to different encapsulation behaviours.\n        // Ex: for emulated, an attribute is added to the element.\n        if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n            renderer.applyToHost(element);\n        }\n        else if (renderer instanceof NoneEncapsulationDomRenderer) {\n            renderer.applyStyles();\n        }\n        return renderer;\n    }\n    getOrCreateRenderer(element, type) {\n        const rendererByCompId = this.rendererByCompId;\n        let renderer = rendererByCompId.get(type.id);\n        if (!renderer) {\n            const eventManager = this.eventManager;\n            const sharedStylesHost = this.sharedStylesHost;\n            const removeStylesOnCompDestory = this.removeStylesOnCompDestory;\n            switch (type.encapsulation) {\n                case ViewEncapsulation.Emulated:\n                    renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestory);\n                    break;\n                case ViewEncapsulation.ShadowDom:\n                    return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type);\n                default:\n                    renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestory);\n                    break;\n            }\n            renderer.onDestroy = () => rendererByCompId.delete(type.id);\n            rendererByCompId.set(type.id, renderer);\n        }\n        return renderer;\n    }\n    ngOnDestroy() {\n        this.rendererByCompId.clear();\n    }\n    begin() { }\n    end() { }\n}\nDomRendererFactory2.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomRendererFactory2, deps: [{ token: EventManager }, { token: DomSharedStylesHost }, { token: APP_ID }, { token: REMOVE_STYLES_ON_COMPONENT_DESTROY }], target: i0.ɵɵFactoryTarget.Injectable });\nDomRendererFactory2.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomRendererFactory2 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomRendererFactory2, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: EventManager }, { type: DomSharedStylesHost }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n                }] }]; } });\nclass DefaultDomRenderer2 {\n    constructor(eventManager) {\n        this.eventManager = eventManager;\n        this.data = Object.create(null);\n        this.destroyNode = null;\n    }\n    destroy() { }\n    createElement(name, namespace) {\n        if (namespace) {\n            // TODO: `|| namespace` was added in\n            // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n            // support how Ivy passed around the namespace URI rather than short name at the time. It did\n            // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n            // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n            // namespaces should be and make it consistent.\n            // Related issues:\n            // https://github.com/angular/angular/issues/44028\n            // https://github.com/angular/angular/issues/44883\n            return document.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n        }\n        return document.createElement(name);\n    }\n    createComment(value) {\n        return document.createComment(value);\n    }\n    createText(value) {\n        return document.createTextNode(value);\n    }\n    appendChild(parent, newChild) {\n        const targetParent = isTemplateNode(parent) ? parent.content : parent;\n        targetParent.appendChild(newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        if (parent) {\n            const targetParent = isTemplateNode(parent) ? parent.content : parent;\n            targetParent.insertBefore(newChild, refChild);\n        }\n    }\n    removeChild(parent, oldChild) {\n        if (parent) {\n            parent.removeChild(oldChild);\n        }\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        let el = typeof selectorOrNode === 'string' ? document.querySelector(selectorOrNode) :\n            selectorOrNode;\n        if (!el) {\n            throw new Error(`The selector \"${selectorOrNode}\" did not match any elements`);\n        }\n        if (!preserveContent) {\n            el.textContent = '';\n        }\n        return el;\n    }\n    parentNode(node) {\n        return node.parentNode;\n    }\n    nextSibling(node) {\n        return node.nextSibling;\n    }\n    setAttribute(el, name, value, namespace) {\n        if (namespace) {\n            name = namespace + ':' + name;\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.setAttributeNS(namespaceUri, name, value);\n            }\n            else {\n                el.setAttribute(name, value);\n            }\n        }\n        else {\n            el.setAttribute(name, value);\n        }\n    }\n    removeAttribute(el, name, namespace) {\n        if (namespace) {\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.removeAttributeNS(namespaceUri, name);\n            }\n            else {\n                el.removeAttribute(`${namespace}:${name}`);\n            }\n        }\n        else {\n            el.removeAttribute(name);\n        }\n    }\n    addClass(el, name) {\n        el.classList.add(name);\n    }\n    removeClass(el, name) {\n        el.classList.remove(name);\n    }\n    setStyle(el, style, value, flags) {\n        if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n            el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n        }\n        else {\n            el.style[style] = value;\n        }\n    }\n    removeStyle(el, style, flags) {\n        if (flags & RendererStyleFlags2.DashCase) {\n            // removeProperty has no effect when used on camelCased properties.\n            el.style.removeProperty(style);\n        }\n        else {\n            el.style[style] = '';\n        }\n    }\n    setProperty(el, name, value) {\n        NG_DEV_MODE$1 && checkNoSyntheticProp(name, 'property');\n        el[name] = value;\n    }\n    setValue(node, value) {\n        node.nodeValue = value;\n    }\n    listen(target, event, callback) {\n        NG_DEV_MODE$1 && checkNoSyntheticProp(event, 'listener');\n        if (typeof target === 'string') {\n            return this.eventManager.addGlobalEventListener(target, event, decoratePreventDefault(callback));\n        }\n        return this.eventManager.addEventListener(target, event, decoratePreventDefault(callback));\n    }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n    if (name.charCodeAt(0) === AT_CHARCODE) {\n        throw new Error(`Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n    }\n}\nfunction isTemplateNode(node) {\n    return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, hostEl, component) {\n        super(eventManager);\n        this.sharedStylesHost = sharedStylesHost;\n        this.hostEl = hostEl;\n        this.shadowRoot = hostEl.attachShadow({ mode: 'open' });\n        this.sharedStylesHost.addHost(this.shadowRoot);\n        const styles = flattenStyles(component.id, component.styles);\n        for (const style of styles) {\n            const styleEl = document.createElement('style');\n            styleEl.textContent = style;\n            this.shadowRoot.appendChild(styleEl);\n        }\n    }\n    nodeOrShadowRoot(node) {\n        return node === this.hostEl ? this.shadowRoot : node;\n    }\n    appendChild(parent, newChild) {\n        return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n    }\n    removeChild(parent, oldChild) {\n        return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n    }\n    parentNode(node) {\n        return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n    }\n    destroy() {\n        this.sharedStylesHost.removeHost(this.shadowRoot);\n    }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestory, compId = component.id) {\n        super(eventManager);\n        this.sharedStylesHost = sharedStylesHost;\n        this.removeStylesOnCompDestory = removeStylesOnCompDestory;\n        this.rendererUsageCount = 0;\n        this.styles = flattenStyles(compId, component.styles);\n    }\n    applyStyles() {\n        this.sharedStylesHost.addStyles(this.styles);\n        this.rendererUsageCount++;\n    }\n    destroy() {\n        if (!this.removeStylesOnCompDestory) {\n            return;\n        }\n        this.sharedStylesHost.removeStyles(this.styles);\n        this.rendererUsageCount--;\n        if (this.rendererUsageCount === 0) {\n            this.onDestroy?.();\n        }\n    }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n    constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestory) {\n        const compId = appId + '-' + component.id;\n        super(eventManager, sharedStylesHost, component, removeStylesOnCompDestory, compId);\n        this.contentAttr = shimContentAttribute(compId);\n        this.hostAttr = shimHostAttribute(compId);\n    }\n    applyToHost(element) {\n        this.applyStyles();\n        this.setAttribute(element, this.hostAttr, '');\n    }\n    createElement(parent, name) {\n        const el = super.createElement(parent, name);\n        super.setAttribute(el, this.contentAttr, '');\n        return el;\n    }\n}\n\nclass DomEventsPlugin extends EventManagerPlugin {\n    constructor(doc) {\n        super(doc);\n    }\n    // This plugin should come last in the list of plugins, because it accepts all\n    // events.\n    supports(eventName) {\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        element.addEventListener(eventName, handler, false);\n        return () => this.removeEventListener(element, eventName, handler);\n    }\n    removeEventListener(target, eventName, callback) {\n        return target.removeEventListener(eventName, callback);\n    }\n}\nDomEventsPlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDomEventsPlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomEventsPlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n    '\\b': 'Backspace',\n    '\\t': 'Tab',\n    '\\x7F': 'Delete',\n    '\\x1B': 'Escape',\n    'Del': 'Delete',\n    'Esc': 'Escape',\n    'Left': 'ArrowLeft',\n    'Right': 'ArrowRight',\n    'Up': 'ArrowUp',\n    'Down': 'ArrowDown',\n    'Menu': 'ContextMenu',\n    'Scroll': 'ScrollLock',\n    'Win': 'OS'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n    'alt': (event) => event.altKey,\n    'control': (event) => event.ctrlKey,\n    'meta': (event) => event.metaKey,\n    'shift': (event) => event.shiftKey\n};\n/**\n * @publicApi\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n    /**\n     * Initializes an instance of the browser plug-in.\n     * @param doc The document in which key events will be detected.\n     */\n    constructor(doc) {\n        super(doc);\n    }\n    /**\n     * Reports whether a named key event is supported.\n     * @param eventName The event name to query.\n     * @return True if the named key event is supported.\n     */\n    supports(eventName) {\n        return KeyEventsPlugin.parseEventName(eventName) != null;\n    }\n    /**\n     * Registers a handler for a specific element and key event.\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the key event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns The key event that was registered.\n     */\n    addEventListener(element, eventName, handler) {\n        const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n        const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n        return this.manager.getZone().runOutsideAngular(() => {\n            return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n        });\n    }\n    /**\n     * Parses the user provided full keyboard event definition and normalizes it for\n     * later internal use. It ensures the string is all lowercase, converts special\n     * characters to a standard spelling, and orders all the values consistently.\n     *\n     * @param eventName The name of the key event to listen for.\n     * @returns an object with the full, normalized string, and the dom event name\n     * or null in the case when the event doesn't match a keyboard event.\n     */\n    static parseEventName(eventName) {\n        const parts = eventName.toLowerCase().split('.');\n        const domEventName = parts.shift();\n        if ((parts.length === 0) || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n            return null;\n        }\n        const key = KeyEventsPlugin._normalizeKey(parts.pop());\n        let fullKey = '';\n        let codeIX = parts.indexOf('code');\n        if (codeIX > -1) {\n            parts.splice(codeIX, 1);\n            fullKey = 'code.';\n        }\n        MODIFIER_KEYS.forEach(modifierName => {\n            const index = parts.indexOf(modifierName);\n            if (index > -1) {\n                parts.splice(index, 1);\n                fullKey += modifierName + '.';\n            }\n        });\n        fullKey += key;\n        if (parts.length != 0 || key.length === 0) {\n            // returning null instead of throwing to let another plugin process the event\n            return null;\n        }\n        // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n        //       The code must remain in the `result['domEventName']` form.\n        // return {domEventName, fullKey};\n        const result = {};\n        result['domEventName'] = domEventName;\n        result['fullKey'] = fullKey;\n        return result;\n    }\n    /**\n     * Determines whether the actual keys pressed match the configured key code string.\n     * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n     * event is attached to the DOM during the `addEventListener` call. This is unseen\n     * by the end user and is normalized for internal consistency and parsing.\n     *\n     * @param event The keyboard event.\n     * @param fullKeyCode The normalized user defined expected key event string\n     * @returns boolean.\n     */\n    static matchEventFullKeyCode(event, fullKeyCode) {\n        let keycode = _keyMap[event.key] || event.key;\n        let key = '';\n        if (fullKeyCode.indexOf('code.') > -1) {\n            keycode = event.code;\n            key = 'code.';\n        }\n        // the keycode could be unidentified so we have to check here\n        if (keycode == null || !keycode)\n            return false;\n        keycode = keycode.toLowerCase();\n        if (keycode === ' ') {\n            keycode = 'space'; // for readability\n        }\n        else if (keycode === '.') {\n            keycode = 'dot'; // because '.' is used as a separator in event names\n        }\n        MODIFIER_KEYS.forEach(modifierName => {\n            if (modifierName !== keycode) {\n                const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n                if (modifierGetter(event)) {\n                    key += modifierName + '.';\n                }\n            }\n        });\n        key += keycode;\n        return key === fullKeyCode;\n    }\n    /**\n     * Configures a handler callback for a key event.\n     * @param fullKey The event name that combines all simultaneous keystrokes.\n     * @param handler The function that responds to the key event.\n     * @param zone The zone in which the event occurred.\n     * @returns A callback function.\n     */\n    static eventCallback(fullKey, handler, zone) {\n        return (event) => {\n            if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n                zone.runGuarded(() => handler(event));\n            }\n        };\n    }\n    /** @internal */\n    static _normalizeKey(keyName) {\n        // TODO: switch to a Map if the mapping grows too much\n        switch (keyName) {\n            case 'esc':\n                return 'escape';\n            default:\n                return keyName;\n        }\n    }\n}\nKeyEventsPlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: KeyEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nKeyEventsPlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: KeyEventsPlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: KeyEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/standalone-components).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```typescript\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```typescript\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n    return ɵinternalCreateApplication({ rootComponent, ...createProvidersConfig(options) });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n    return ɵinternalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n    return {\n        appProviders: [\n            ...BROWSER_MODULE_PROVIDERS,\n            ...(options?.providers ?? []),\n        ],\n        platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n    };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n    // Return a copy to prevent changes to the original array in case any in-place\n    // alterations are performed to the `provideProtractorTestingSupport` call results in app code.\n    return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n    BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n    return new ErrorHandler();\n}\nfunction _document() {\n    // Tell ivy about the global document\n    ɵsetDocument(document);\n    return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [\n    { provide: PLATFORM_ID, useValue: ɵPLATFORM_BROWSER_ID },\n    { provide: PLATFORM_INITIALIZER, useValue: initDomAdapter, multi: true },\n    { provide: DOCUMENT, useFactory: _document, deps: [] },\n];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(NG_DEV_MODE ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [\n    {\n        provide: ɵTESTABILITY_GETTER,\n        useClass: BrowserGetTestability,\n        deps: [],\n    },\n    {\n        provide: ɵTESTABILITY,\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n    },\n    {\n        provide: Testability,\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n    }\n];\nconst BROWSER_MODULE_PROVIDERS = [\n    { provide: ɵINJECTOR_SCOPE, useValue: 'root' },\n    { provide: ErrorHandler, useFactory: errorHandler, deps: [] }, {\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: DomEventsPlugin,\n        multi: true,\n        deps: [DOCUMENT, NgZone, PLATFORM_ID]\n    },\n    { provide: EVENT_MANAGER_PLUGINS, useClass: KeyEventsPlugin, multi: true, deps: [DOCUMENT] }, {\n        provide: DomRendererFactory2,\n        useClass: DomRendererFactory2,\n        deps: [EventManager, DomSharedStylesHost, APP_ID, REMOVE_STYLES_ON_COMPONENT_DESTROY]\n    },\n    { provide: RendererFactory2, useExisting: DomRendererFactory2 },\n    { provide: SharedStylesHost, useExisting: DomSharedStylesHost },\n    { provide: DomSharedStylesHost, useClass: DomSharedStylesHost, deps: [DOCUMENT] },\n    { provide: EventManager, useClass: EventManager, deps: [EVENT_MANAGER_PLUGINS, NgZone] },\n    { provide: XhrFactory, useClass: BrowserXhr, deps: [] },\n    NG_DEV_MODE ? { provide: BROWSER_MODULE_PROVIDERS_MARKER, useValue: true } : []\n];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n    constructor(providersAlreadyPresent) {\n        if (NG_DEV_MODE && providersAlreadyPresent) {\n            throw new Error(`Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` +\n                `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n        }\n    }\n    /**\n     * Configures a browser-based app to transition from a server-rendered app, if\n     * one is present on the page.\n     *\n     * @param params An object containing an identifier for the app to transition.\n     * The ID must match between the client and server versions of the app.\n     * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n     */\n    static withServerTransition(params) {\n        return {\n            ngModule: BrowserModule,\n            providers: [\n                { provide: APP_ID, useValue: params.appId },\n                { provide: TRANSITION_ID, useExisting: APP_ID },\n                SERVER_TRANSITION_PROVIDERS,\n            ],\n        };\n    }\n}\nBrowserModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserModule, deps: [{ token: BROWSER_MODULE_PROVIDERS_MARKER, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.NgModule });\nBrowserModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserModule, exports: [CommonModule, ApplicationModule] });\nBrowserModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserModule, providers: [\n        ...BROWSER_MODULE_PROVIDERS,\n        ...TESTABILITY_PROVIDERS\n    ], imports: [CommonModule, ApplicationModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        ...BROWSER_MODULE_PROVIDERS,\n                        ...TESTABILITY_PROVIDERS\n                    ],\n                    exports: [CommonModule, ApplicationModule],\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [BROWSER_MODULE_PROVIDERS_MARKER]\n                }] }]; } });\n\n/**\n * Factory to create a `Meta` service instance for the current DOM document.\n */\nfunction createMeta() {\n    return new Meta(ɵɵinject(DOCUMENT));\n}\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n    constructor(_doc) {\n        this._doc = _doc;\n        this._dom = ɵgetDOM();\n    }\n    /**\n     * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * If an existing element is found, it is returned and is not modified in any way.\n     * @param tag The definition of a `<meta>` element to match or create.\n     * @param forceCreation True to create a new element without checking whether one already exists.\n     * @returns The existing element with the same attributes and values if found,\n     * the new element if no match is found, or `null` if the tag parameter is not defined.\n     */\n    addTag(tag, forceCreation = false) {\n        if (!tag)\n            return null;\n        return this._getOrCreateElement(tag, forceCreation);\n    }\n    /**\n     * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * @param tags An array of tag definitions to match or create.\n     * @param forceCreation True to create new elements without checking whether they already exist.\n     * @returns The matching elements if found, or the new elements.\n     */\n    addTags(tags, forceCreation = false) {\n        if (!tags)\n            return [];\n        return tags.reduce((result, tag) => {\n            if (tag) {\n                result.push(this._getOrCreateElement(tag, forceCreation));\n            }\n            return result;\n        }, []);\n    }\n    /**\n     * Retrieves a `<meta>` tag element in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching element, if any.\n     */\n    getTag(attrSelector) {\n        if (!attrSelector)\n            return null;\n        return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n    }\n    /**\n     * Retrieves a set of `<meta>` tag elements in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching elements, if any.\n     */\n    getTags(attrSelector) {\n        if (!attrSelector)\n            return [];\n        const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n        return list ? [].slice.call(list) : [];\n    }\n    /**\n     * Modifies an existing `<meta>` tag element in the current HTML document.\n     * @param tag The tag description with which to replace the existing tag content.\n     * @param selector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n     * replacement tag.\n     * @return The modified element.\n     */\n    updateTag(tag, selector) {\n        if (!tag)\n            return null;\n        selector = selector || this._parseSelector(tag);\n        const meta = this.getTag(selector);\n        if (meta) {\n            return this._setMetaElementAttributes(tag, meta);\n        }\n        return this._getOrCreateElement(tag, true);\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param attrSelector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     */\n    removeTag(attrSelector) {\n        this.removeTagElement(this.getTag(attrSelector));\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param meta The tag definition to match against to identify an existing tag.\n     */\n    removeTagElement(meta) {\n        if (meta) {\n            this._dom.remove(meta);\n        }\n    }\n    _getOrCreateElement(meta, forceCreation = false) {\n        if (!forceCreation) {\n            const selector = this._parseSelector(meta);\n            // It's allowed to have multiple elements with the same name so it's not enough to\n            // just check that element with the same name already present on the page. We also need to\n            // check if element has tag attributes\n            const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n            if (elem !== undefined)\n                return elem;\n        }\n        const element = this._dom.createElement('meta');\n        this._setMetaElementAttributes(meta, element);\n        const head = this._doc.getElementsByTagName('head')[0];\n        head.appendChild(element);\n        return element;\n    }\n    _setMetaElementAttributes(tag, el) {\n        Object.keys(tag).forEach((prop) => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n        return el;\n    }\n    _parseSelector(tag) {\n        const attr = tag.name ? 'name' : 'property';\n        return `${attr}=\"${tag[attr]}\"`;\n    }\n    _containsAttributes(tag, elem) {\n        return Object.keys(tag).every((key) => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n    }\n    _getMetaKeyMap(prop) {\n        return META_KEYS_MAP[prop] || prop;\n    }\n}\nMeta.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: Meta, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nMeta.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: Meta, providedIn: 'root', useFactory: createMeta, deps: [] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: Meta, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: createMeta, deps: [] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n    httpEquiv: 'http-equiv'\n};\n\n/**\n * Factory to create Title service.\n */\nfunction createTitle() {\n    return new Title(ɵɵinject(DOCUMENT));\n}\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    /**\n     * Get the title of the current HTML document.\n     */\n    getTitle() {\n        return this._doc.title;\n    }\n    /**\n     * Set the title of the current HTML document.\n     * @param newTitle\n     */\n    setTitle(newTitle) {\n        this._doc.title = newTitle || '';\n    }\n}\nTitle.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: Title, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nTitle.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: Title, providedIn: 'root', useFactory: createTitle, deps: [] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: Title, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: createTitle, deps: [] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n    if (typeof COMPILED === 'undefined' || !COMPILED) {\n        // Note: we can't export `ng` when using closure enhanced optimization as:\n        // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n        // - we can't declare a closure extern as the namespace `ng` is already used within Google\n        //   for typings for angularJS (via `goog.provide('ng....')`).\n        const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n        ng[name] = value;\n    }\n}\n\nconst win = typeof window !== 'undefined' && window || {};\n\nclass ChangeDetectionPerfRecord {\n    constructor(msPerTick, numTicks) {\n        this.msPerTick = msPerTick;\n        this.numTicks = numTicks;\n    }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n    constructor(ref) {\n        this.appRef = ref.injector.get(ApplicationRef);\n    }\n    // tslint:disable:no-console\n    /**\n     * Exercises change detection in a loop and then prints the average amount of\n     * time in milliseconds how long a single round of change detection takes for\n     * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n     * of 500 milliseconds.\n     *\n     * Optionally, a user may pass a `config` parameter containing a map of\n     * options. Supported options are:\n     *\n     * `record` (boolean) - causes the profiler to record a CPU profile while\n     * it exercises the change detector. Example:\n     *\n     * ```\n     * ng.profiler.timeChangeDetection({record: true})\n     * ```\n     */\n    timeChangeDetection(config) {\n        const record = config && config['record'];\n        const profileName = 'Change Detection';\n        // Profiler is not available in Android browsers without dev tools opened\n        const isProfilerAvailable = win.console.profile != null;\n        if (record && isProfilerAvailable) {\n            win.console.profile(profileName);\n        }\n        const start = performanceNow();\n        let numTicks = 0;\n        while (numTicks < 5 || (performanceNow() - start) < 500) {\n            this.appRef.tick();\n            numTicks++;\n        }\n        const end = performanceNow();\n        if (record && isProfilerAvailable) {\n            win.console.profileEnd(profileName);\n        }\n        const msPerTick = (end - start) / numTicks;\n        win.console.log(`ran ${numTicks} change detection cycles`);\n        win.console.log(`${msPerTick.toFixed(2)} ms per check`);\n        return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n    }\n}\nfunction performanceNow() {\n    return win.performance && win.performance.now ? win.performance.now() :\n        new Date().getTime();\n}\n\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n    exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n    return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n    exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\nfunction escapeHtml(text) {\n    const escapedText = {\n        '&': '&a;',\n        '\"': '&q;',\n        '\\'': '&s;',\n        '<': '&l;',\n        '>': '&g;',\n    };\n    return text.replace(/[&\"'<>]/g, s => escapedText[s]);\n}\nfunction unescapeHtml(text) {\n    const unescapedText = {\n        '&a;': '&',\n        '&q;': '\"',\n        '&s;': '\\'',\n        '&l;': '<',\n        '&g;': '>',\n    };\n    return text.replace(/&[^;]+;/g, s => unescapedText[s]);\n}\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n */\nfunction makeStateKey(key) {\n    return key;\n}\n/**\n * A key value store that is transferred from the application on the server side to the application\n * on the client side.\n *\n * The `TransferState` is available as an injectable token.\n * On the client, just inject this token using DI and use it, it will be lazily initialized.\n * On the server it's already included if `renderApplication` function is used. Otherwise, import\n * the `ServerTransferStateModule` module to make the `TransferState` available.\n *\n * The values in the store are serialized/deserialized using JSON.stringify/JSON.parse. So only\n * boolean, number, string, null and non-class objects will be serialized and deserialized in a\n * non-lossy manner.\n *\n * @publicApi\n */\nclass TransferState {\n    constructor() {\n        this.store = {};\n        this.onSerializeCallbacks = {};\n        this.store = retrieveTransferredState(inject(DOCUMENT), inject(APP_ID));\n    }\n    /**\n     * Get the value corresponding to a key. Return `defaultValue` if key is not found.\n     */\n    get(key, defaultValue) {\n        return this.store[key] !== undefined ? this.store[key] : defaultValue;\n    }\n    /**\n     * Set the value corresponding to a key.\n     */\n    set(key, value) {\n        this.store[key] = value;\n    }\n    /**\n     * Remove a key from the store.\n     */\n    remove(key) {\n        delete this.store[key];\n    }\n    /**\n     * Test whether a key exists in the store.\n     */\n    hasKey(key) {\n        return this.store.hasOwnProperty(key);\n    }\n    /**\n     * Indicates whether the state is empty.\n     */\n    get isEmpty() {\n        return Object.keys(this.store).length === 0;\n    }\n    /**\n     * Register a callback to provide the value for a key when `toJson` is called.\n     */\n    onSerialize(key, callback) {\n        this.onSerializeCallbacks[key] = callback;\n    }\n    /**\n     * Serialize the current state of the store to JSON.\n     */\n    toJson() {\n        // Call the onSerialize callbacks and put those values into the store.\n        for (const key in this.onSerializeCallbacks) {\n            if (this.onSerializeCallbacks.hasOwnProperty(key)) {\n                try {\n                    this.store[key] = this.onSerializeCallbacks[key]();\n                }\n                catch (e) {\n                    console.warn('Exception in onSerialize callback: ', e);\n                }\n            }\n        }\n        return JSON.stringify(this.store);\n    }\n}\nTransferState.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: TransferState, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nTransferState.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: TransferState, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: TransferState, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return []; } });\nfunction retrieveTransferredState(doc, appId) {\n    // Locate the script tag with the JSON data transferred from the server.\n    // The id of the script tag is set to the Angular appId + 'state'.\n    const script = doc.getElementById(appId + '-state');\n    let initialState = {};\n    if (script && script.textContent) {\n        try {\n            // Avoid using any here as it triggers lint errors in google3 (any is not allowed).\n            initialState = JSON.parse(unescapeHtml(script.textContent));\n        }\n        catch (e) {\n            console.warn('Exception while restoring TransferState for app ' + appId, e);\n        }\n    }\n    return initialState;\n}\n/**\n * NgModule to install on the client side while using the `TransferState` to transfer state from\n * server to client.\n *\n * @publicApi\n * @deprecated no longer needed, you can inject the `TransferState` in an app without providing\n *     this module.\n */\nclass BrowserTransferStateModule {\n}\nBrowserTransferStateModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserTransferStateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nBrowserTransferStateModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserTransferStateModule });\nBrowserTransferStateModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserTransferStateModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserTransferStateModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n    /**\n     * Match all nodes.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n     */\n    static all() {\n        return () => true;\n    }\n    /**\n     * Match elements by the given CSS selector.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n     */\n    static css(selector) {\n        return (debugElement) => {\n            return debugElement.nativeElement != null ?\n                elementMatches(debugElement.nativeElement, selector) :\n                false;\n        };\n    }\n    /**\n     * Match nodes that have the given directive present.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n     */\n    static directive(type) {\n        return (debugNode) => debugNode.providerTokens.indexOf(type) !== -1;\n    }\n}\nfunction elementMatches(n, selector) {\n    if (ɵgetDOM().isElementNode(n)) {\n        return n.matches && n.matches(selector) ||\n            n.msMatchesSelector && n.msMatchesSelector(selector) ||\n            n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n    }\n    return false;\n}\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n    // pan\n    'pan': true,\n    'panstart': true,\n    'panmove': true,\n    'panend': true,\n    'pancancel': true,\n    'panleft': true,\n    'panright': true,\n    'panup': true,\n    'pandown': true,\n    // pinch\n    'pinch': true,\n    'pinchstart': true,\n    'pinchmove': true,\n    'pinchend': true,\n    'pinchcancel': true,\n    'pinchin': true,\n    'pinchout': true,\n    // press\n    'press': true,\n    'pressup': true,\n    // rotate\n    'rotate': true,\n    'rotatestart': true,\n    'rotatemove': true,\n    'rotateend': true,\n    'rotatecancel': true,\n    // swipe\n    'swipe': true,\n    'swipeleft': true,\n    'swiperight': true,\n    'swipeup': true,\n    'swipedown': true,\n    // tap\n    'tap': true,\n    'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see `HammerGestureConfig`\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n    constructor() {\n        /**\n         * A set of supported event names for gestures to be used in Angular.\n         * Angular supports all built-in recognizers, as listed in\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         */\n        this.events = [];\n        /**\n         * Maps gesture event names to a set of configuration options\n         * that specify overrides to the default values for specific properties.\n         *\n         * The key is a supported event name to be configured,\n         * and the options object contains a set of properties, with override values\n         * to be applied to the named recognizer event.\n         * For example, to disable recognition of the rotate event, specify\n         *  `{\"rotate\": {\"enable\": false}}`.\n         *\n         * Properties that are not present take the HammerJS default values.\n         * For information about which properties are supported for which events,\n         * and their allowed and default values, see\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         *\n         */\n        this.overrides = {};\n    }\n    /**\n     * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n     * and attaches it to a given HTML element.\n     * @param element The element that will recognize gestures.\n     * @returns A HammerJS event-manager object.\n     */\n    buildHammer(element) {\n        const mc = new Hammer(element, this.options);\n        mc.get('pinch').set({ enable: true });\n        mc.get('rotate').set({ enable: true });\n        for (const eventName in this.overrides) {\n            mc.get(eventName).set(this.overrides[eventName]);\n        }\n        return mc;\n    }\n}\nHammerGestureConfig.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerGestureConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nHammerGestureConfig.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerGestureConfig });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerGestureConfig, decorators: [{\n            type: Injectable\n        }] });\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n    constructor(doc, _config, console, loader) {\n        super(doc);\n        this._config = _config;\n        this.console = console;\n        this.loader = loader;\n        this._loaderPromise = null;\n    }\n    supports(eventName) {\n        if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n            return false;\n        }\n        if (!window.Hammer && !this.loader) {\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` +\n                    `loaded and no custom loader has been specified.`);\n            }\n            return false;\n        }\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        const zone = this.manager.getZone();\n        eventName = eventName.toLowerCase();\n        // If Hammer is not present but a loader is specified, we defer adding the event listener\n        // until Hammer is loaded.\n        if (!window.Hammer && this.loader) {\n            this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n            // This `addEventListener` method returns a function to remove the added listener.\n            // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n            // than remove anything.\n            let cancelRegistration = false;\n            let deregister = () => {\n                cancelRegistration = true;\n            };\n            zone.runOutsideAngular(() => this._loaderPromise\n                .then(() => {\n                // If Hammer isn't actually loaded when the custom loader resolves, give up.\n                if (!window.Hammer) {\n                    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                        this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n                    }\n                    deregister = () => { };\n                    return;\n                }\n                if (!cancelRegistration) {\n                    // Now that Hammer is loaded and the listener is being loaded for real,\n                    // the deregistration function changes from canceling registration to\n                    // removal.\n                    deregister = this.addEventListener(element, eventName, handler);\n                }\n            })\n                .catch(() => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` +\n                        `Hammer.JS loader failed.`);\n                }\n                deregister = () => { };\n            }));\n            // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n            // can change the behavior of `deregister` once the listener is added. Using a closure in\n            // this way allows us to avoid any additional data structures to track listener removal.\n            return () => {\n                deregister();\n            };\n        }\n        return zone.runOutsideAngular(() => {\n            // Creating the manager bind events, must be done outside of angular\n            const mc = this._config.buildHammer(element);\n            const callback = function (eventObj) {\n                zone.runGuarded(function () {\n                    handler(eventObj);\n                });\n            };\n            mc.on(eventName, callback);\n            return () => {\n                mc.off(eventName, callback);\n                // destroy mc to prevent memory leak\n                if (typeof mc.destroy === 'function') {\n                    mc.destroy();\n                }\n            };\n        });\n    }\n    isCustomEvent(eventName) {\n        return this._config.events.indexOf(eventName) > -1;\n    }\n}\nHammerGesturesPlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerGesturesPlugin, deps: [{ token: DOCUMENT }, { token: HAMMER_GESTURE_CONFIG }, { token: i0.ɵConsole }, { token: HAMMER_LOADER, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nHammerGesturesPlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerGesturesPlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerGesturesPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: HammerGestureConfig, decorators: [{\n                    type: Inject,\n                    args: [HAMMER_GESTURE_CONFIG]\n                }] }, { type: i0.ɵConsole }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [HAMMER_LOADER]\n                }] }]; } });\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's EventManager.\n *\n * @publicApi\n */\nclass HammerModule {\n}\nHammerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nHammerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerModule });\nHammerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerModule, providers: [\n        {\n            provide: EVENT_MANAGER_PLUGINS,\n            useClass: HammerGesturesPlugin,\n            multi: true,\n            deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n        },\n        { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        {\n                            provide: EVENT_MANAGER_PLUGINS,\n                            useClass: HammerGesturesPlugin,\n                            multi: true,\n                            deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n                        },\n                        { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n                    ]\n                }]\n        }] });\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n}\nDomSanitizer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSanitizer, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nDomSanitizer.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSanitizer, providedIn: 'root', useExisting: i0.forwardRef(function () { return DomSanitizerImpl; }) });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSanitizer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useExisting: forwardRef(() => DomSanitizerImpl) }]\n        }] });\nfunction domSanitizerImplFactory(injector) {\n    return new DomSanitizerImpl(injector.get(DOCUMENT));\n}\nclass DomSanitizerImpl extends DomSanitizer {\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n    }\n    sanitize(ctx, value) {\n        if (value == null)\n            return null;\n        switch (ctx) {\n            case SecurityContext.NONE:\n                return value;\n            case SecurityContext.HTML:\n                if (ɵallowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n            case SecurityContext.STYLE:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return value;\n            case SecurityContext.SCRIPT:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new Error('unsafe value used in a script context');\n            case SecurityContext.URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeUrl(String(value));\n            case SecurityContext.RESOURCE_URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new Error(`unsafe value used in a resource URL context (see ${ɵXSS_SECURITY_URL})`);\n            default:\n                throw new Error(`Unexpected SecurityContext ${ctx} (see ${ɵXSS_SECURITY_URL})`);\n        }\n    }\n    bypassSecurityTrustHtml(value) {\n        return ɵbypassSanitizationTrustHtml(value);\n    }\n    bypassSecurityTrustStyle(value) {\n        return ɵbypassSanitizationTrustStyle(value);\n    }\n    bypassSecurityTrustScript(value) {\n        return ɵbypassSanitizationTrustScript(value);\n    }\n    bypassSecurityTrustUrl(value) {\n        return ɵbypassSanitizationTrustUrl(value);\n    }\n    bypassSecurityTrustResourceUrl(value) {\n        return ɵbypassSanitizationTrustResourceUrl(value);\n    }\n}\nDomSanitizerImpl.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSanitizerImpl, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDomSanitizerImpl.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSanitizerImpl, providedIn: 'root', useFactory: domSanitizerImplFactory, deps: [{ token: Injector }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSanitizerImpl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: domSanitizerImplFactory, deps: [Injector] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('15.2.10');\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, BrowserTransferStateModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, Meta, REMOVE_STYLES_ON_COMPONENT_DESTROY, Title, TransferState, VERSION, bootstrapApplication, createApplication, disableDebugTools, enableDebugTools, makeStateKey, platformBrowser, provideProtractorTestingSupport, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, DomSharedStylesHost as ɵDomSharedStylesHost, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, NAMESPACE_URIS as ɵNAMESPACE_URIS, SharedStylesHost as ɵSharedStylesHost, TRANSITION_ID as ɵTRANSITION_ID, escapeHtml as ɵescapeHtml, flattenStyles as ɵflattenStyles, initDomAdapter as ɵinitDomAdapter, shimContentAttribute as ɵshimContentAttribute, shimHostAttribute as ɵshimHostAttribute };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,WAAW,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,oBAAoB,EAAEC,UAAU,EAAEC,YAAY,QAAQ,iBAAiB;AACvJ,SAASJ,OAAO,QAAQ,iBAAiB;AACzC,OAAO,KAAKK,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,0BAA0B,EAAEC,YAAY,EAAEC,YAAY,EAAEC,WAAW,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,mBAAmB,EAAEC,YAAY,EAAEC,WAAW,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,gCAAgC,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,4BAA4B,EAAEC,6BAA6B,EAAEC,8BAA8B,EAAEC,2BAA2B,EAAEC,mCAAmC,EAAEC,OAAO,QAAQ,eAAe;;AAEryB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,SAAStD,WAAW,CAAC;EAC/CuD,WAAW,GAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,iBAAiB,GAAG,IAAI;EACjC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,SAASJ,wBAAwB,CAAC;EACrD,OAAOK,WAAW,GAAG;IACjB1D,kBAAkB,CAAC,IAAIyD,iBAAiB,EAAE,CAAC;EAC/C;EACAE,WAAW,CAACC,EAAE,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAC3BF,EAAE,CAACG,gBAAgB,CAACF,GAAG,EAAEC,QAAQ,EAAE,KAAK,CAAC;IACzC;IACA;IACA,OAAO,MAAM;MACTF,EAAE,CAACI,mBAAmB,CAACH,GAAG,EAAEC,QAAQ,EAAE,KAAK,CAAC;IAChD,CAAC;EACL;EACAG,aAAa,CAACL,EAAE,EAAEC,GAAG,EAAE;IACnBD,EAAE,CAACK,aAAa,CAACJ,GAAG,CAAC;EACzB;EACAK,MAAM,CAACC,IAAI,EAAE;IACT,IAAIA,IAAI,CAACC,UAAU,EAAE;MACjBD,IAAI,CAACC,UAAU,CAACC,WAAW,CAACF,IAAI,CAAC;IACrC;EACJ;EACAG,aAAa,CAACC,OAAO,EAAEC,GAAG,EAAE;IACxBA,GAAG,GAAGA,GAAG,IAAI,IAAI,CAACC,kBAAkB,EAAE;IACtC,OAAOD,GAAG,CAACF,aAAa,CAACC,OAAO,CAAC;EACrC;EACAG,kBAAkB,GAAG;IACjB,OAAOC,QAAQ,CAACC,cAAc,CAACC,kBAAkB,CAAC,WAAW,CAAC;EAClE;EACAJ,kBAAkB,GAAG;IACjB,OAAOE,QAAQ;EACnB;EACAG,aAAa,CAACX,IAAI,EAAE;IAChB,OAAOA,IAAI,CAACY,QAAQ,KAAKC,IAAI,CAACC,YAAY;EAC9C;EACAC,YAAY,CAACf,IAAI,EAAE;IACf,OAAOA,IAAI,YAAYgB,gBAAgB;EAC3C;EACA;EACAC,oBAAoB,CAACZ,GAAG,EAAEa,MAAM,EAAE;IAC9B,IAAIA,MAAM,KAAK,QAAQ,EAAE;MACrB,OAAOC,MAAM;IACjB;IACA,IAAID,MAAM,KAAK,UAAU,EAAE;MACvB,OAAOb,GAAG;IACd;IACA,IAAIa,MAAM,KAAK,MAAM,EAAE;MACnB,OAAOb,GAAG,CAACe,IAAI;IACnB;IACA,OAAO,IAAI;EACf;EACAC,WAAW,CAAChB,GAAG,EAAE;IACb,MAAMiB,IAAI,GAAGC,kBAAkB,EAAE;IACjC,OAAOD,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGE,YAAY,CAACF,IAAI,CAAC;EACnD;EACAG,gBAAgB,GAAG;IACfC,WAAW,GAAG,IAAI;EACtB;EACAC,YAAY,GAAG;IACX,OAAOR,MAAM,CAACS,SAAS,CAACC,SAAS;EACrC;EACAC,SAAS,CAACC,IAAI,EAAE;IACZ,OAAOjG,iBAAiB,CAAC0E,QAAQ,CAACwB,MAAM,EAAED,IAAI,CAAC;EACnD;AACJ;AACA,IAAIL,WAAW,GAAG,IAAI;AACtB,SAASH,kBAAkB,GAAG;EAC1BG,WAAW,GAAGA,WAAW,IAAIlB,QAAQ,CAACyB,aAAa,CAAC,MAAM,CAAC;EAC3D,OAAOP,WAAW,GAAGA,WAAW,CAACQ,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI;AAChE;AACA;AACA,IAAIC,cAAc;AAClB,SAASX,YAAY,CAACY,GAAG,EAAE;EACvBD,cAAc,GAAGA,cAAc,IAAI3B,QAAQ,CAACL,aAAa,CAAC,GAAG,CAAC;EAC9DgC,cAAc,CAACE,YAAY,CAAC,MAAM,EAAED,GAAG,CAAC;EACxC,MAAME,QAAQ,GAAGH,cAAc,CAACI,QAAQ;EACxC,OAAOD,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGF,QAAQ,GAAI,IAAGA,QAAS,EAAC;AACjE;;AAEA;AACA;AACA;AACA;AACA,MAAMG,aAAa,GAAG,IAAIpG,cAAc,CAAC,eAAe,CAAC;AACzD,SAASqG,qBAAqB,CAACC,YAAY,EAAEnC,QAAQ,EAAEoC,QAAQ,EAAE;EAC7D,OAAO,MAAM;IACT;IACA;IACAA,QAAQ,CAACC,GAAG,CAACvG,qBAAqB,CAAC,CAACwG,WAAW,CAACC,IAAI,CAAC,MAAM;MACvD,MAAMC,GAAG,GAAGjH,OAAO,EAAE;MACrB,MAAMkH,MAAM,GAAGzC,QAAQ,CAAC0C,gBAAgB,CAAE,wBAAuBP,YAAa,IAAG,CAAC;MAClF,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACpCH,GAAG,CAACjD,MAAM,CAACkD,MAAM,CAACE,CAAC,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;EACN,CAAC;AACL;AACA,MAAME,2BAA2B,GAAG,CAChC;EACIC,OAAO,EAAE/G,eAAe;EACxBgH,UAAU,EAAEb,qBAAqB;EACjCc,IAAI,EAAE,CAACf,aAAa,EAAEzG,QAAQ,EAAEQ,QAAQ,CAAC;EACzCiH,KAAK,EAAE;AACX,CAAC,CACJ;AAED,MAAMC,qBAAqB,CAAC;EACxBC,WAAW,CAACC,QAAQ,EAAE;IAClBnH,OAAO,CAAC,uBAAuB,CAAC,GAAG,CAACoH,IAAI,EAAEC,eAAe,GAAG,IAAI,KAAK;MACjE,MAAMC,WAAW,GAAGH,QAAQ,CAACI,qBAAqB,CAACH,IAAI,EAAEC,eAAe,CAAC;MACzE,IAAIC,WAAW,IAAI,IAAI,EAAE;QACrB,MAAM,IAAIE,KAAK,CAAC,yCAAyC,CAAC;MAC9D;MACA,OAAOF,WAAW;IACtB,CAAC;IACDtH,OAAO,CAAC,4BAA4B,CAAC,GAAG,MAAMmH,QAAQ,CAACM,mBAAmB,EAAE;IAC5EzH,OAAO,CAAC,2BAA2B,CAAC,GAAG,MAAMmH,QAAQ,CAACO,kBAAkB,EAAE;IAC1E,MAAMC,aAAa,GAAG,CAACC,QAAQ,CAAC,sBAAsB;MAClD,MAAMC,aAAa,GAAG7H,OAAO,CAAC,4BAA4B,CAAC,EAAE;MAC7D,IAAI8H,KAAK,GAAGD,aAAa,CAAClB,MAAM;MAChC,IAAIoB,OAAO,GAAG,KAAK;MACnB,MAAMC,SAAS,GAAG,UAAUC,QAAQ,CAAC,mBAAmB;QACpDF,OAAO,GAAGA,OAAO,IAAIE,QAAQ;QAC7BH,KAAK,EAAE;QACP,IAAIA,KAAK,IAAI,CAAC,EAAE;UACZF,QAAQ,CAACG,OAAO,CAAC;QACrB;MACJ,CAAC;MACDF,aAAa,CAACK,OAAO,CAAC,UAAUZ,WAAW,CAAC,mBAAmB;QAC3DA,WAAW,CAACa,UAAU,CAACH,SAAS,CAAC;MACrC,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAChI,OAAO,CAAC,sBAAsB,CAAC,EAAE;MAClCA,OAAO,CAAC,sBAAsB,CAAC,GAAG,EAAE;IACxC;IACAA,OAAO,CAAC,sBAAsB,CAAC,CAACoI,IAAI,CAACT,aAAa,CAAC;EACvD;EACAJ,qBAAqB,CAACJ,QAAQ,EAAEC,IAAI,EAAEC,eAAe,EAAE;IACnD,IAAID,IAAI,IAAI,IAAI,EAAE;MACd,OAAO,IAAI;IACf;IACA,MAAMiB,CAAC,GAAGlB,QAAQ,CAACmB,cAAc,CAAClB,IAAI,CAAC;IACvC,IAAIiB,CAAC,IAAI,IAAI,EAAE;MACX,OAAOA,CAAC;IACZ,CAAC,MACI,IAAI,CAAChB,eAAe,EAAE;MACvB,OAAO,IAAI;IACf;IACA,IAAI/H,OAAO,EAAE,CAACgF,YAAY,CAAC8C,IAAI,CAAC,EAAE;MAC9B,OAAO,IAAI,CAACG,qBAAqB,CAACJ,QAAQ,EAAEC,IAAI,CAACmB,IAAI,EAAE,IAAI,CAAC;IAChE;IACA,OAAO,IAAI,CAAChB,qBAAqB,CAACJ,QAAQ,EAAEC,IAAI,CAACoB,aAAa,EAAE,IAAI,CAAC;EACzE;AACJ;;AAEA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,KAAK,GAAG;IACJ,OAAO,IAAIC,cAAc,EAAE;EAC/B;AACJ;AACAF,UAAU,CAACG,IAAI;EAAA,iBAAyFH,UAAU;AAAA,CAAoD;AACtKA,UAAU,CAACI,KAAK,kBAD8ElJ,EAAE;EAAA,OACY8I,UAAU;EAAA,SAAVA,UAAU;AAAA,EAAG;AACzH;EAAA,mDAF8F9I,EAAE,mBAEJ8I,UAAU,EAAc,CAAC;IACzGK,IAAI,EAAE7I;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAM8I,qBAAqB,GAAG,IAAInJ,cAAc,CAAC,qBAAqB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoJ,YAAY,CAAC;EACf;AACJ;AACA;EACItG,WAAW,CAACuG,OAAO,EAAEC,KAAK,EAAE;IACxB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,kBAAkB,GAAG,IAAIC,GAAG,EAAE;IACnCH,OAAO,CAACf,OAAO,CAAEmB,MAAM,IAAK;MACxBA,MAAM,CAACC,OAAO,GAAG,IAAI;IACzB,CAAC,CAAC;IACF,IAAI,CAACC,QAAQ,GAAGN,OAAO,CAACO,KAAK,EAAE,CAACC,OAAO,EAAE;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACItG,gBAAgB,CAACuG,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAMP,MAAM,GAAG,IAAI,CAACQ,cAAc,CAACF,SAAS,CAAC;IAC7C,OAAON,MAAM,CAAClG,gBAAgB,CAACuG,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;EAC/D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,sBAAsB,CAACrF,MAAM,EAAEkF,SAAS,EAAEC,OAAO,EAAE;IAC/C,MAAMP,MAAM,GAAG,IAAI,CAACQ,cAAc,CAACF,SAAS,CAAC;IAC7C,OAAON,MAAM,CAACS,sBAAsB,CAACrF,MAAM,EAAEkF,SAAS,EAAEC,OAAO,CAAC;EACpE;EACA;AACJ;AACA;EACIG,OAAO,GAAG;IACN,OAAO,IAAI,CAACb,KAAK;EACrB;EACA;EACAW,cAAc,CAACF,SAAS,EAAE;IACtB,MAAMN,MAAM,GAAG,IAAI,CAACF,kBAAkB,CAAC/C,GAAG,CAACuD,SAAS,CAAC;IACrD,IAAIN,MAAM,EAAE;MACR,OAAOA,MAAM;IACjB;IACA,MAAMJ,OAAO,GAAG,IAAI,CAACM,QAAQ;IAC7B,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuC,OAAO,CAACtC,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,MAAM2C,MAAM,GAAGJ,OAAO,CAACvC,CAAC,CAAC;MACzB,IAAI2C,MAAM,CAACW,QAAQ,CAACL,SAAS,CAAC,EAAE;QAC5B,IAAI,CAACR,kBAAkB,CAACc,GAAG,CAACN,SAAS,EAAEN,MAAM,CAAC;QAC9C,OAAOA,MAAM;MACjB;IACJ;IACA,MAAM,IAAI7B,KAAK,CAAE,2CAA0CmC,SAAU,EAAC,CAAC;EAC3E;AACJ;AACAX,YAAY,CAACJ,IAAI;EAAA,iBAAyFI,YAAY,EAhFxBrJ,EAAE,UAgFwCoJ,qBAAqB,GAhF/DpJ,EAAE,UAgF0EA,EAAE,CAACqB,MAAM;AAAA,CAA6C;AAChOgI,YAAY,CAACH,KAAK,kBAjF4ElJ,EAAE;EAAA,OAiFcqJ,YAAY;EAAA,SAAZA,YAAY;AAAA,EAAG;AAC7H;EAAA,mDAlF8FrJ,EAAE,mBAkFJqJ,YAAY,EAAc,CAAC;IAC3GF,IAAI,EAAE7I;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE6I,IAAI,EAAEoB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DrB,IAAI,EAAE5I,MAAM;QACZkK,IAAI,EAAE,CAACrB,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAED,IAAI,EAAEnJ,EAAE,CAACqB;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AAC7C,MAAMqJ,kBAAkB,CAAC;EACrB3H,WAAW,CAAC4H,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACAR,sBAAsB,CAACJ,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAChD,MAAMnF,MAAM,GAAGnF,OAAO,EAAE,CAACkF,oBAAoB,CAAC,IAAI,CAAC8F,IAAI,EAAEZ,OAAO,CAAC;IACjE,IAAI,CAACjF,MAAM,EAAE;MACT,MAAM,IAAI+C,KAAK,CAAE,4BAA2B/C,MAAO,cAAakF,SAAU,EAAC,CAAC;IAChF;IACA,OAAO,IAAI,CAACxG,gBAAgB,CAACsB,MAAM,EAAEkF,SAAS,EAAEC,OAAO,CAAC;EAC5D;AACJ;AAEA,MAAMW,gBAAgB,CAAC;EACnB7H,WAAW,GAAG;IACV,IAAI,CAAC8H,UAAU,GAAG,IAAIpB,GAAG,EAAE;EAC/B;EACAqB,SAAS,CAACjE,MAAM,EAAE;IACd,KAAK,MAAMkE,KAAK,IAAIlE,MAAM,EAAE;MACxB,MAAMgE,UAAU,GAAG,IAAI,CAACG,gBAAgB,CAACD,KAAK,EAAE,CAAC,CAAC;MAClD,IAAIF,UAAU,KAAK,CAAC,EAAE;QAClB,IAAI,CAACI,YAAY,CAACF,KAAK,CAAC;MAC5B;IACJ;EACJ;EACAG,YAAY,CAACrE,MAAM,EAAE;IACjB,KAAK,MAAMkE,KAAK,IAAIlE,MAAM,EAAE;MACxB,MAAMgE,UAAU,GAAG,IAAI,CAACG,gBAAgB,CAACD,KAAK,EAAE,CAAC,CAAC,CAAC;MACnD,IAAIF,UAAU,KAAK,CAAC,EAAE;QAClB,IAAI,CAACM,cAAc,CAACJ,KAAK,CAAC;MAC9B;IACJ;EACJ;EACAI,cAAc,CAACJ,KAAK,EAAE,CAAE;EACxBE,YAAY,CAACF,KAAK,EAAE,CAAE;EACtBK,YAAY,GAAG;IACX,OAAO,IAAI,CAACP,UAAU,CAACQ,IAAI,EAAE;EACjC;EACAL,gBAAgB,CAACD,KAAK,EAAEO,KAAK,EAAE;IAC3B,MAAMC,GAAG,GAAG,IAAI,CAACV,UAAU;IAC3B,IAAIW,KAAK,GAAGD,GAAG,CAAC9E,GAAG,CAACsE,KAAK,CAAC,IAAI,CAAC;IAC/BS,KAAK,IAAIF,KAAK;IACd,IAAIE,KAAK,GAAG,CAAC,EAAE;MACXD,GAAG,CAACjB,GAAG,CAACS,KAAK,EAAES,KAAK,CAAC;IACzB,CAAC,MACI;MACDD,GAAG,CAACE,MAAM,CAACV,KAAK,CAAC;IACrB;IACA,OAAOS,KAAK;EAChB;EACAE,WAAW,GAAG;IACV,KAAK,MAAMX,KAAK,IAAI,IAAI,CAACK,YAAY,EAAE,EAAE;MACrC,IAAI,CAACD,cAAc,CAACJ,KAAK,CAAC;IAC9B;IACA,IAAI,CAACF,UAAU,CAACc,KAAK,EAAE;EAC3B;AACJ;AACAf,gBAAgB,CAAC3B,IAAI;EAAA,iBAAyF2B,gBAAgB;AAAA,CAAoD;AAClLA,gBAAgB,CAAC1B,KAAK,kBAlJwElJ,EAAE;EAAA,OAkJkB4K,gBAAgB;EAAA,SAAhBA,gBAAgB;AAAA,EAAG;AACrI;EAAA,mDAnJ8F5K,EAAE,mBAmJJ4K,gBAAgB,EAAc,CAAC;IAC/GzB,IAAI,EAAE7I;EACV,CAAC,CAAC;AAAA;AACV,MAAMsL,mBAAmB,SAAShB,gBAAgB,CAAC;EAC/C7H,WAAW,CAACkB,GAAG,EAAE;IACb,KAAK,EAAE;IACP,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd;IACA,IAAI,CAAC4H,QAAQ,GAAG,IAAIpC,GAAG,EAAE;IACzB,IAAI,CAACqC,SAAS,GAAG,IAAIC,GAAG,EAAE;IAC1B,IAAI,CAACC,cAAc,EAAE;EACzB;EACAf,YAAY,CAACF,KAAK,EAAE;IAChB,KAAK,MAAMnC,IAAI,IAAI,IAAI,CAACkD,SAAS,EAAE;MAC/B,IAAI,CAACG,cAAc,CAACrD,IAAI,EAAEmC,KAAK,CAAC;IACpC;EACJ;EACAI,cAAc,CAACJ,KAAK,EAAE;IAClB,MAAMc,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMK,aAAa,GAAGL,QAAQ,CAACpF,GAAG,CAACsE,KAAK,CAAC;IACzCmB,aAAa,EAAE3D,OAAO,CAAC4D,CAAC,IAAIA,CAAC,CAACxI,MAAM,EAAE,CAAC;IACvCkI,QAAQ,CAACJ,MAAM,CAACV,KAAK,CAAC;EAC1B;EACAW,WAAW,GAAG;IACV,KAAK,CAACA,WAAW,EAAE;IACnB,IAAI,CAACG,QAAQ,CAACF,KAAK,EAAE;IACrB,IAAI,CAACK,cAAc,EAAE;EACzB;EACAI,OAAO,CAACC,QAAQ,EAAE;IACd,IAAI,CAACP,SAAS,CAACQ,GAAG,CAACD,QAAQ,CAAC;IAC5B,KAAK,MAAMtB,KAAK,IAAI,IAAI,CAACK,YAAY,EAAE,EAAE;MACrC,IAAI,CAACa,cAAc,CAACI,QAAQ,EAAEtB,KAAK,CAAC;IACxC;EACJ;EACAwB,UAAU,CAACF,QAAQ,EAAE;IACjB,IAAI,CAACP,SAAS,CAACL,MAAM,CAACY,QAAQ,CAAC;EACnC;EACAJ,cAAc,CAACrD,IAAI,EAAEmC,KAAK,EAAE;IACxB,MAAMyB,OAAO,GAAG,IAAI,CAACvI,GAAG,CAACF,aAAa,CAAC,OAAO,CAAC;IAC/CyI,OAAO,CAACC,WAAW,GAAG1B,KAAK;IAC3BnC,IAAI,CAAC8D,WAAW,CAACF,OAAO,CAAC;IACzB,MAAMG,UAAU,GAAG,IAAI,CAACd,QAAQ,CAACpF,GAAG,CAACsE,KAAK,CAAC;IAC3C,IAAI4B,UAAU,EAAE;MACZA,UAAU,CAAClE,IAAI,CAAC+D,OAAO,CAAC;IAC5B,CAAC,MACI;MACD,IAAI,CAACX,QAAQ,CAACvB,GAAG,CAACS,KAAK,EAAE,CAACyB,OAAO,CAAC,CAAC;IACvC;EACJ;EACAR,cAAc,GAAG;IACb,MAAMF,SAAS,GAAG,IAAI,CAACA,SAAS;IAChCA,SAAS,CAACH,KAAK,EAAE;IACjB;IACAG,SAAS,CAACQ,GAAG,CAAC,IAAI,CAACrI,GAAG,CAAC2I,IAAI,CAAC;EAChC;AACJ;AACAhB,mBAAmB,CAAC3C,IAAI;EAAA,iBAAyF2C,mBAAmB,EA3MtC5L,EAAE,UA2MsDJ,QAAQ;AAAA,CAA6C;AAC3MgM,mBAAmB,CAAC1C,KAAK,kBA5MqElJ,EAAE;EAAA,OA4MqB4L,mBAAmB;EAAA,SAAnBA,mBAAmB;AAAA,EAAG;AAC3I;EAAA,mDA7M8F5L,EAAE,mBA6MJ4L,mBAAmB,EAAc,CAAC;IAClHzC,IAAI,EAAE7I;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE6I,IAAI,EAAEoB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DrB,IAAI,EAAE5I,MAAM;QACZkK,IAAI,EAAE,CAAC7K,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMiN,cAAc,GAAG;EACnB,KAAK,EAAE,4BAA4B;EACnC,OAAO,EAAE,8BAA8B;EACvC,OAAO,EAAE,8BAA8B;EACvC,KAAK,EAAE,sCAAsC;EAC7C,OAAO,EAAE,+BAA+B;EACxC,MAAM,EAAE;AACZ,CAAC;AACD,MAAMC,eAAe,GAAG,SAAS;AACjC,MAAMC,aAAa,GAAG,OAAOC,SAAS,KAAK,WAAW,IAAI,CAAC,CAACA,SAAS;AACrE,MAAMC,kBAAkB,GAAG,QAAQ;AACnC,MAAMC,SAAS,GAAI,WAAUD,kBAAmB,EAAC;AACjD,MAAME,YAAY,GAAI,cAAaF,kBAAmB,EAAC;AACvD;AACA;AACA;AACA,MAAMG,0CAA0C,GAAG,KAAK;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kCAAkC,GAAG,IAAIpN,cAAc,CAAC,2BAA2B,EAAE;EACvFqN,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAE,MAAMH;AACnB,CAAC,CAAC;AACF,SAASI,oBAAoB,CAACC,gBAAgB,EAAE;EAC5C,OAAON,YAAY,CAACO,OAAO,CAACZ,eAAe,EAAEW,gBAAgB,CAAC;AAClE;AACA,SAASE,iBAAiB,CAACF,gBAAgB,EAAE;EACzC,OAAOP,SAAS,CAACQ,OAAO,CAACZ,eAAe,EAAEW,gBAAgB,CAAC;AAC/D;AACA,SAASG,aAAa,CAACC,MAAM,EAAEhH,MAAM,EAAE;EACnC;EACA;EACA,OAAOA,MAAM,CAACiH,IAAI,CAAC,GAAG,CAAC,CAACvC,GAAG,CAACwC,CAAC,IAAIA,CAAC,CAACL,OAAO,CAACZ,eAAe,EAAEe,MAAM,CAAC,CAAC;AACxE;AACA,SAASG,sBAAsB,CAACC,YAAY,EAAE;EAC1C;EACA;EACA;EACA;EACA,OAAQC,KAAK,IAAK;IACd;IACA;IACA;IACA;IACA,IAAIA,KAAK,KAAK,cAAc,EAAE;MAC1B,OAAOD,YAAY;IACvB;IACA,MAAME,oBAAoB,GAAGF,YAAY,CAACC,KAAK,CAAC;IAChD,IAAIC,oBAAoB,KAAK,KAAK,EAAE;MAChC;MACAD,KAAK,CAACE,cAAc,EAAE;MACtBF,KAAK,CAACG,WAAW,GAAG,KAAK;IAC7B;IACA,OAAO9D,SAAS;EACpB,CAAC;AACL;AACA,MAAM+D,mBAAmB,CAAC;EACtBvL,WAAW,CAACwL,YAAY,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,yBAAyB,EAAE;IAC1E,IAAI,CAACH,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,gBAAgB,GAAG,IAAIlF,GAAG,EAAE;IACjC,IAAI,CAACmF,eAAe,GAAG,IAAIC,mBAAmB,CAACN,YAAY,CAAC;EAChE;EACAO,cAAc,CAAC/E,OAAO,EAAEZ,IAAI,EAAE;IAC1B,IAAI,CAACY,OAAO,IAAI,CAACZ,IAAI,EAAE;MACnB,OAAO,IAAI,CAACyF,eAAe;IAC/B;IACA,MAAMG,QAAQ,GAAG,IAAI,CAACC,mBAAmB,CAACjF,OAAO,EAAEZ,IAAI,CAAC;IACxD;IACA;IACA,IAAI4F,QAAQ,YAAYE,iCAAiC,EAAE;MACvDF,QAAQ,CAACG,WAAW,CAACnF,OAAO,CAAC;IACjC,CAAC,MACI,IAAIgF,QAAQ,YAAYI,4BAA4B,EAAE;MACvDJ,QAAQ,CAACK,WAAW,EAAE;IAC1B;IACA,OAAOL,QAAQ;EACnB;EACAC,mBAAmB,CAACjF,OAAO,EAAEZ,IAAI,EAAE;IAC/B,MAAMwF,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,IAAII,QAAQ,GAAGJ,gBAAgB,CAAClI,GAAG,CAAC0C,IAAI,CAACkG,EAAE,CAAC;IAC5C,IAAI,CAACN,QAAQ,EAAE;MACX,MAAMR,YAAY,GAAG,IAAI,CAACA,YAAY;MACtC,MAAMC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;MAC9C,MAAME,yBAAyB,GAAG,IAAI,CAACA,yBAAyB;MAChE,QAAQvF,IAAI,CAACmG,aAAa;QACtB,KAAK9O,iBAAiB,CAAC+O,QAAQ;UAC3BR,QAAQ,GAAG,IAAIE,iCAAiC,CAACV,YAAY,EAAEC,gBAAgB,EAAErF,IAAI,EAAE,IAAI,CAACsF,KAAK,EAAEC,yBAAyB,CAAC;UAC7H;QACJ,KAAKlO,iBAAiB,CAACgP,SAAS;UAC5B,OAAO,IAAIC,iBAAiB,CAAClB,YAAY,EAAEC,gBAAgB,EAAEzE,OAAO,EAAEZ,IAAI,CAAC;QAC/E;UACI4F,QAAQ,GAAG,IAAII,4BAA4B,CAACZ,YAAY,EAAEC,gBAAgB,EAAErF,IAAI,EAAEuF,yBAAyB,CAAC;UAC5G;MAAM;MAEdK,QAAQ,CAACW,SAAS,GAAG,MAAMf,gBAAgB,CAAClD,MAAM,CAACtC,IAAI,CAACkG,EAAE,CAAC;MAC3DV,gBAAgB,CAACrE,GAAG,CAACnB,IAAI,CAACkG,EAAE,EAAEN,QAAQ,CAAC;IAC3C;IACA,OAAOA,QAAQ;EACnB;EACArD,WAAW,GAAG;IACV,IAAI,CAACiD,gBAAgB,CAAChD,KAAK,EAAE;EACjC;EACAgE,KAAK,GAAG,CAAE;EACVC,GAAG,GAAG,CAAE;AACZ;AACAtB,mBAAmB,CAACrF,IAAI;EAAA,iBAAyFqF,mBAAmB,EArUtCtO,EAAE,UAqUsDqJ,YAAY,GArUpErJ,EAAE,UAqU+E4L,mBAAmB,GArUpG5L,EAAE,UAqU+GS,MAAM,GArUvHT,EAAE,UAqUkIqN,kCAAkC;AAAA,CAA6C;AACjTiB,mBAAmB,CAACpF,KAAK,kBAtUqElJ,EAAE;EAAA,OAsUqBsO,mBAAmB;EAAA,SAAnBA,mBAAmB;AAAA,EAAG;AAC3I;EAAA,mDAvU8FtO,EAAE,mBAuUJsO,mBAAmB,EAAc,CAAC;IAClHnF,IAAI,EAAE7I;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE6I,IAAI,EAAEE;IAAa,CAAC,EAAE;MAAEF,IAAI,EAAEyC;IAAoB,CAAC,EAAE;MAAEzC,IAAI,EAAEoB,SAAS;MAAEC,UAAU,EAAE,CAAC;QACrHrB,IAAI,EAAE5I,MAAM;QACZkK,IAAI,EAAE,CAAChK,MAAM;MACjB,CAAC;IAAE,CAAC,EAAE;MAAE0I,IAAI,EAAEoB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCrB,IAAI,EAAE5I,MAAM;QACZkK,IAAI,EAAE,CAAC4C,kCAAkC;MAC7C,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB,MAAMwB,mBAAmB,CAAC;EACtB9L,WAAW,CAACwL,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACsB,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;EACAC,OAAO,GAAG,CAAE;EACZlM,aAAa,CAAC4B,IAAI,EAAEuK,SAAS,EAAE;IAC3B,IAAIA,SAAS,EAAE;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO9L,QAAQ,CAAC+L,eAAe,CAACtD,cAAc,CAACqD,SAAS,CAAC,IAAIA,SAAS,EAAEvK,IAAI,CAAC;IACjF;IACA,OAAOvB,QAAQ,CAACL,aAAa,CAAC4B,IAAI,CAAC;EACvC;EACAyK,aAAa,CAACC,KAAK,EAAE;IACjB,OAAOjM,QAAQ,CAACgM,aAAa,CAACC,KAAK,CAAC;EACxC;EACAC,UAAU,CAACD,KAAK,EAAE;IACd,OAAOjM,QAAQ,CAACmM,cAAc,CAACF,KAAK,CAAC;EACzC;EACA3D,WAAW,CAAC8D,MAAM,EAAEC,QAAQ,EAAE;IAC1B,MAAMC,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;IACrEE,YAAY,CAAChE,WAAW,CAAC+D,QAAQ,CAAC;EACtC;EACAI,YAAY,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,IAAIN,MAAM,EAAE;MACR,MAAME,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;MACrEE,YAAY,CAACG,YAAY,CAACJ,QAAQ,EAAEK,QAAQ,CAAC;IACjD;EACJ;EACAhN,WAAW,CAAC0M,MAAM,EAAEO,QAAQ,EAAE;IAC1B,IAAIP,MAAM,EAAE;MACRA,MAAM,CAAC1M,WAAW,CAACiN,QAAQ,CAAC;IAChC;EACJ;EACAC,iBAAiB,CAACC,cAAc,EAAEC,eAAe,EAAE;IAC/C,IAAI7N,EAAE,GAAG,OAAO4N,cAAc,KAAK,QAAQ,GAAG7M,QAAQ,CAACyB,aAAa,CAACoL,cAAc,CAAC,GAChFA,cAAc;IAClB,IAAI,CAAC5N,EAAE,EAAE;MACL,MAAM,IAAIwE,KAAK,CAAE,iBAAgBoJ,cAAe,8BAA6B,CAAC;IAClF;IACA,IAAI,CAACC,eAAe,EAAE;MAClB7N,EAAE,CAACoJ,WAAW,GAAG,EAAE;IACvB;IACA,OAAOpJ,EAAE;EACb;EACAQ,UAAU,CAACD,IAAI,EAAE;IACb,OAAOA,IAAI,CAACC,UAAU;EAC1B;EACAsN,WAAW,CAACvN,IAAI,EAAE;IACd,OAAOA,IAAI,CAACuN,WAAW;EAC3B;EACAlL,YAAY,CAAC5C,EAAE,EAAEsC,IAAI,EAAE0K,KAAK,EAAEH,SAAS,EAAE;IACrC,IAAIA,SAAS,EAAE;MACXvK,IAAI,GAAGuK,SAAS,GAAG,GAAG,GAAGvK,IAAI;MAC7B,MAAMyL,YAAY,GAAGvE,cAAc,CAACqD,SAAS,CAAC;MAC9C,IAAIkB,YAAY,EAAE;QACd/N,EAAE,CAACgO,cAAc,CAACD,YAAY,EAAEzL,IAAI,EAAE0K,KAAK,CAAC;MAChD,CAAC,MACI;QACDhN,EAAE,CAAC4C,YAAY,CAACN,IAAI,EAAE0K,KAAK,CAAC;MAChC;IACJ,CAAC,MACI;MACDhN,EAAE,CAAC4C,YAAY,CAACN,IAAI,EAAE0K,KAAK,CAAC;IAChC;EACJ;EACAiB,eAAe,CAACjO,EAAE,EAAEsC,IAAI,EAAEuK,SAAS,EAAE;IACjC,IAAIA,SAAS,EAAE;MACX,MAAMkB,YAAY,GAAGvE,cAAc,CAACqD,SAAS,CAAC;MAC9C,IAAIkB,YAAY,EAAE;QACd/N,EAAE,CAACkO,iBAAiB,CAACH,YAAY,EAAEzL,IAAI,CAAC;MAC5C,CAAC,MACI;QACDtC,EAAE,CAACiO,eAAe,CAAE,GAAEpB,SAAU,IAAGvK,IAAK,EAAC,CAAC;MAC9C;IACJ,CAAC,MACI;MACDtC,EAAE,CAACiO,eAAe,CAAC3L,IAAI,CAAC;IAC5B;EACJ;EACA6L,QAAQ,CAACnO,EAAE,EAAEsC,IAAI,EAAE;IACftC,EAAE,CAACoO,SAAS,CAACnF,GAAG,CAAC3G,IAAI,CAAC;EAC1B;EACA+L,WAAW,CAACrO,EAAE,EAAEsC,IAAI,EAAE;IAClBtC,EAAE,CAACoO,SAAS,CAAC9N,MAAM,CAACgC,IAAI,CAAC;EAC7B;EACAgM,QAAQ,CAACtO,EAAE,EAAE0H,KAAK,EAAEsF,KAAK,EAAEuB,KAAK,EAAE;IAC9B,IAAIA,KAAK,IAAIlR,mBAAmB,CAACmR,QAAQ,GAAGnR,mBAAmB,CAACoR,SAAS,CAAC,EAAE;MACxEzO,EAAE,CAAC0H,KAAK,CAACgH,WAAW,CAAChH,KAAK,EAAEsF,KAAK,EAAEuB,KAAK,GAAGlR,mBAAmB,CAACoR,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;IAChG,CAAC,MACI;MACDzO,EAAE,CAAC0H,KAAK,CAACA,KAAK,CAAC,GAAGsF,KAAK;IAC3B;EACJ;EACA2B,WAAW,CAAC3O,EAAE,EAAE0H,KAAK,EAAE6G,KAAK,EAAE;IAC1B,IAAIA,KAAK,GAAGlR,mBAAmB,CAACmR,QAAQ,EAAE;MACtC;MACAxO,EAAE,CAAC0H,KAAK,CAACkH,cAAc,CAAClH,KAAK,CAAC;IAClC,CAAC,MACI;MACD1H,EAAE,CAAC0H,KAAK,CAACA,KAAK,CAAC,GAAG,EAAE;IACxB;EACJ;EACAgH,WAAW,CAAC1O,EAAE,EAAEsC,IAAI,EAAE0K,KAAK,EAAE;IACzBtD,aAAa,IAAImF,oBAAoB,CAACvM,IAAI,EAAE,UAAU,CAAC;IACvDtC,EAAE,CAACsC,IAAI,CAAC,GAAG0K,KAAK;EACpB;EACA8B,QAAQ,CAACvO,IAAI,EAAEyM,KAAK,EAAE;IAClBzM,IAAI,CAACwO,SAAS,GAAG/B,KAAK;EAC1B;EACAgC,MAAM,CAACvN,MAAM,EAAEoJ,KAAK,EAAEjG,QAAQ,EAAE;IAC5B8E,aAAa,IAAImF,oBAAoB,CAAChE,KAAK,EAAE,UAAU,CAAC;IACxD,IAAI,OAAOpJ,MAAM,KAAK,QAAQ,EAAE;MAC5B,OAAO,IAAI,CAACyJ,YAAY,CAACpE,sBAAsB,CAACrF,MAAM,EAAEoJ,KAAK,EAAEF,sBAAsB,CAAC/F,QAAQ,CAAC,CAAC;IACpG;IACA,OAAO,IAAI,CAACsG,YAAY,CAAC/K,gBAAgB,CAACsB,MAAM,EAAEoJ,KAAK,EAAEF,sBAAsB,CAAC/F,QAAQ,CAAC,CAAC;EAC9F;AACJ;AACA,MAAMqK,WAAW,GAAG,CAAC,MAAM,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,GAAG;AAC/C,SAASL,oBAAoB,CAACvM,IAAI,EAAE6M,QAAQ,EAAE;EAC1C,IAAI7M,IAAI,CAAC4M,UAAU,CAAC,CAAC,CAAC,KAAKD,WAAW,EAAE;IACpC,MAAM,IAAIzK,KAAK,CAAE,wBAAuB2K,QAAS,IAAG7M,IAAK;AACjE;AACA,qEAAqEA,IAAK,gIAA+H,CAAC;EACtM;AACJ;AACA,SAASgL,cAAc,CAAC/M,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACI,OAAO,KAAK,UAAU,IAAIJ,IAAI,CAACgN,OAAO,KAAKrG,SAAS;AACpE;AACA,MAAMkF,iBAAiB,SAASZ,mBAAmB,CAAC;EAChD9L,WAAW,CAACwL,YAAY,EAAEC,gBAAgB,EAAEiE,MAAM,EAAEC,SAAS,EAAE;IAC3D,KAAK,CAACnE,YAAY,CAAC;IACnB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACiE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,UAAU,GAAGF,MAAM,CAACG,YAAY,CAAC;MAAEC,IAAI,EAAE;IAAO,CAAC,CAAC;IACvD,IAAI,CAACrE,gBAAgB,CAACpC,OAAO,CAAC,IAAI,CAACuG,UAAU,CAAC;IAC9C,MAAM9L,MAAM,GAAG+G,aAAa,CAAC8E,SAAS,CAACrD,EAAE,EAAEqD,SAAS,CAAC7L,MAAM,CAAC;IAC5D,KAAK,MAAMkE,KAAK,IAAIlE,MAAM,EAAE;MACxB,MAAM2F,OAAO,GAAGpI,QAAQ,CAACL,aAAa,CAAC,OAAO,CAAC;MAC/CyI,OAAO,CAACC,WAAW,GAAG1B,KAAK;MAC3B,IAAI,CAAC4H,UAAU,CAACjG,WAAW,CAACF,OAAO,CAAC;IACxC;EACJ;EACAsG,gBAAgB,CAAClP,IAAI,EAAE;IACnB,OAAOA,IAAI,KAAK,IAAI,CAAC6O,MAAM,GAAG,IAAI,CAACE,UAAU,GAAG/O,IAAI;EACxD;EACA8I,WAAW,CAAC8D,MAAM,EAAEC,QAAQ,EAAE;IAC1B,OAAO,KAAK,CAAC/D,WAAW,CAAC,IAAI,CAACoG,gBAAgB,CAACtC,MAAM,CAAC,EAAEC,QAAQ,CAAC;EACrE;EACAI,YAAY,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,OAAO,KAAK,CAACD,YAAY,CAAC,IAAI,CAACiC,gBAAgB,CAACtC,MAAM,CAAC,EAAEC,QAAQ,EAAEK,QAAQ,CAAC;EAChF;EACAhN,WAAW,CAAC0M,MAAM,EAAEO,QAAQ,EAAE;IAC1B,OAAO,KAAK,CAACjN,WAAW,CAAC,IAAI,CAACgP,gBAAgB,CAACtC,MAAM,CAAC,EAAEO,QAAQ,CAAC;EACrE;EACAlN,UAAU,CAACD,IAAI,EAAE;IACb,OAAO,IAAI,CAACkP,gBAAgB,CAAC,KAAK,CAACjP,UAAU,CAAC,IAAI,CAACiP,gBAAgB,CAAClP,IAAI,CAAC,CAAC,CAAC;EAC/E;EACAqM,OAAO,GAAG;IACN,IAAI,CAACzB,gBAAgB,CAACjC,UAAU,CAAC,IAAI,CAACoG,UAAU,CAAC;EACrD;AACJ;AACA,MAAMxD,4BAA4B,SAASN,mBAAmB,CAAC;EAC3D9L,WAAW,CAACwL,YAAY,EAAEC,gBAAgB,EAAEkE,SAAS,EAAEhE,yBAAyB,EAAEb,MAAM,GAAG6E,SAAS,CAACrD,EAAE,EAAE;IACrG,KAAK,CAACd,YAAY,CAAC;IACnB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACE,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACqE,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAAClM,MAAM,GAAG+G,aAAa,CAACC,MAAM,EAAE6E,SAAS,CAAC7L,MAAM,CAAC;EACzD;EACAuI,WAAW,GAAG;IACV,IAAI,CAACZ,gBAAgB,CAAC1D,SAAS,CAAC,IAAI,CAACjE,MAAM,CAAC;IAC5C,IAAI,CAACkM,kBAAkB,EAAE;EAC7B;EACA9C,OAAO,GAAG;IACN,IAAI,CAAC,IAAI,CAACvB,yBAAyB,EAAE;MACjC;IACJ;IACA,IAAI,CAACF,gBAAgB,CAACtD,YAAY,CAAC,IAAI,CAACrE,MAAM,CAAC;IAC/C,IAAI,CAACkM,kBAAkB,EAAE;IACzB,IAAI,IAAI,CAACA,kBAAkB,KAAK,CAAC,EAAE;MAC/B,IAAI,CAACrD,SAAS,IAAI;IACtB;EACJ;AACJ;AACA,MAAMT,iCAAiC,SAASE,4BAA4B,CAAC;EACzEpM,WAAW,CAACwL,YAAY,EAAEC,gBAAgB,EAAEkE,SAAS,EAAEjE,KAAK,EAAEC,yBAAyB,EAAE;IACrF,MAAMb,MAAM,GAAGY,KAAK,GAAG,GAAG,GAAGiE,SAAS,CAACrD,EAAE;IACzC,KAAK,CAACd,YAAY,EAAEC,gBAAgB,EAAEkE,SAAS,EAAEhE,yBAAyB,EAAEb,MAAM,CAAC;IACnF,IAAI,CAACmF,WAAW,GAAGxF,oBAAoB,CAACK,MAAM,CAAC;IAC/C,IAAI,CAACoF,QAAQ,GAAGtF,iBAAiB,CAACE,MAAM,CAAC;EAC7C;EACAqB,WAAW,CAACnF,OAAO,EAAE;IACjB,IAAI,CAACqF,WAAW,EAAE;IAClB,IAAI,CAACnJ,YAAY,CAAC8D,OAAO,EAAE,IAAI,CAACkJ,QAAQ,EAAE,EAAE,CAAC;EACjD;EACAlP,aAAa,CAACyM,MAAM,EAAE7K,IAAI,EAAE;IACxB,MAAMtC,EAAE,GAAG,KAAK,CAACU,aAAa,CAACyM,MAAM,EAAE7K,IAAI,CAAC;IAC5C,KAAK,CAACM,YAAY,CAAC5C,EAAE,EAAE,IAAI,CAAC2P,WAAW,EAAE,EAAE,CAAC;IAC5C,OAAO3P,EAAE;EACb;AACJ;AAEA,MAAM6P,eAAe,SAASxI,kBAAkB,CAAC;EAC7C3H,WAAW,CAACkB,GAAG,EAAE;IACb,KAAK,CAACA,GAAG,CAAC;EACd;EACA;EACA;EACAoG,QAAQ,CAACL,SAAS,EAAE;IAChB,OAAO,IAAI;EACf;EACAxG,gBAAgB,CAACuG,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1CF,OAAO,CAACvG,gBAAgB,CAACwG,SAAS,EAAEC,OAAO,EAAE,KAAK,CAAC;IACnD,OAAO,MAAM,IAAI,CAACxG,mBAAmB,CAACsG,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;EACtE;EACAxG,mBAAmB,CAACqB,MAAM,EAAEkF,SAAS,EAAE/B,QAAQ,EAAE;IAC7C,OAAOnD,MAAM,CAACrB,mBAAmB,CAACuG,SAAS,EAAE/B,QAAQ,CAAC;EAC1D;AACJ;AACAiL,eAAe,CAACjK,IAAI;EAAA,iBAAyFiK,eAAe,EArjB9BlT,EAAE,UAqjB8CJ,QAAQ;AAAA,CAA6C;AACnMsT,eAAe,CAAChK,KAAK,kBAtjByElJ,EAAE;EAAA,OAsjBiBkT,eAAe;EAAA,SAAfA,eAAe;AAAA,EAAG;AACnI;EAAA,mDAvjB8FlT,EAAE,mBAujBJkT,eAAe,EAAc,CAAC;IAC9G/J,IAAI,EAAE7I;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE6I,IAAI,EAAEoB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DrB,IAAI,EAAE5I,MAAM;QACZkK,IAAI,EAAE,CAAC7K,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA,MAAMuT,aAAa,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;AACzD;AACA;AACA,MAAMC,OAAO,GAAG;EACZ,IAAI,EAAE,WAAW;EACjB,IAAI,EAAE,KAAK;EACX,MAAM,EAAE,QAAQ;EAChB,MAAM,EAAE,QAAQ;EAChB,KAAK,EAAE,QAAQ;EACf,KAAK,EAAE,QAAQ;EACf,MAAM,EAAE,WAAW;EACnB,OAAO,EAAE,YAAY;EACrB,IAAI,EAAE,SAAS;EACf,MAAM,EAAE,WAAW;EACnB,MAAM,EAAE,aAAa;EACrB,QAAQ,EAAE,YAAY;EACtB,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG;EACzB,KAAK,EAAGnF,KAAK,IAAKA,KAAK,CAACoF,MAAM;EAC9B,SAAS,EAAGpF,KAAK,IAAKA,KAAK,CAACqF,OAAO;EACnC,MAAM,EAAGrF,KAAK,IAAKA,KAAK,CAACsF,OAAO;EAChC,OAAO,EAAGtF,KAAK,IAAKA,KAAK,CAACuF;AAC9B,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAAShJ,kBAAkB,CAAC;EAC7C;AACJ;AACA;AACA;EACI3H,WAAW,CAACkB,GAAG,EAAE;IACb,KAAK,CAACA,GAAG,CAAC;EACd;EACA;AACJ;AACA;AACA;AACA;EACIoG,QAAQ,CAACL,SAAS,EAAE;IAChB,OAAO0J,eAAe,CAACC,cAAc,CAAC3J,SAAS,CAAC,IAAI,IAAI;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIxG,gBAAgB,CAACuG,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAM2J,WAAW,GAAGF,eAAe,CAACC,cAAc,CAAC3J,SAAS,CAAC;IAC7D,MAAM6J,cAAc,GAAGH,eAAe,CAACI,aAAa,CAACF,WAAW,CAAC,SAAS,CAAC,EAAE3J,OAAO,EAAE,IAAI,CAACN,OAAO,CAACS,OAAO,EAAE,CAAC;IAC7G,OAAO,IAAI,CAACT,OAAO,CAACS,OAAO,EAAE,CAAC2J,iBAAiB,CAAC,MAAM;MAClD,OAAOpU,OAAO,EAAE,CAACyD,WAAW,CAAC2G,OAAO,EAAE6J,WAAW,CAAC,cAAc,CAAC,EAAEC,cAAc,CAAC;IACtF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOF,cAAc,CAAC3J,SAAS,EAAE;IAC7B,MAAMgK,KAAK,GAAGhK,SAAS,CAACiK,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAChD,MAAMC,YAAY,GAAGH,KAAK,CAACI,KAAK,EAAE;IAClC,IAAKJ,KAAK,CAAChN,MAAM,KAAK,CAAC,IAAK,EAAEmN,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,OAAO,CAAC,EAAE;MACnF,OAAO,IAAI;IACf;IACA,MAAME,GAAG,GAAGX,eAAe,CAACY,aAAa,CAACN,KAAK,CAACO,GAAG,EAAE,CAAC;IACtD,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,MAAM,GAAGT,KAAK,CAACU,OAAO,CAAC,MAAM,CAAC;IAClC,IAAID,MAAM,GAAG,CAAC,CAAC,EAAE;MACbT,KAAK,CAACW,MAAM,CAACF,MAAM,EAAE,CAAC,CAAC;MACvBD,OAAO,GAAG,OAAO;IACrB;IACArB,aAAa,CAAC5K,OAAO,CAACqM,YAAY,IAAI;MAClC,MAAMC,KAAK,GAAGb,KAAK,CAACU,OAAO,CAACE,YAAY,CAAC;MACzC,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QACZb,KAAK,CAACW,MAAM,CAACE,KAAK,EAAE,CAAC,CAAC;QACtBL,OAAO,IAAII,YAAY,GAAG,GAAG;MACjC;IACJ,CAAC,CAAC;IACFJ,OAAO,IAAIH,GAAG;IACd,IAAIL,KAAK,CAAChN,MAAM,IAAI,CAAC,IAAIqN,GAAG,CAACrN,MAAM,KAAK,CAAC,EAAE;MACvC;MACA,OAAO,IAAI;IACf;IACA;IACA;IACA;IACA,MAAM8N,MAAM,GAAG,CAAC,CAAC;IACjBA,MAAM,CAAC,cAAc,CAAC,GAAGX,YAAY;IACrCW,MAAM,CAAC,SAAS,CAAC,GAAGN,OAAO;IAC3B,OAAOM,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,qBAAqB,CAAC7G,KAAK,EAAE8G,WAAW,EAAE;IAC7C,IAAIC,OAAO,GAAG7B,OAAO,CAAClF,KAAK,CAACmG,GAAG,CAAC,IAAInG,KAAK,CAACmG,GAAG;IAC7C,IAAIA,GAAG,GAAG,EAAE;IACZ,IAAIW,WAAW,CAACN,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MACnCO,OAAO,GAAG/G,KAAK,CAACgH,IAAI;MACpBb,GAAG,GAAG,OAAO;IACjB;IACA;IACA,IAAIY,OAAO,IAAI,IAAI,IAAI,CAACA,OAAO,EAC3B,OAAO,KAAK;IAChBA,OAAO,GAAGA,OAAO,CAAChB,WAAW,EAAE;IAC/B,IAAIgB,OAAO,KAAK,GAAG,EAAE;MACjBA,OAAO,GAAG,OAAO,CAAC,CAAC;IACvB,CAAC,MACI,IAAIA,OAAO,KAAK,GAAG,EAAE;MACtBA,OAAO,GAAG,KAAK,CAAC,CAAC;IACrB;;IACA9B,aAAa,CAAC5K,OAAO,CAACqM,YAAY,IAAI;MAClC,IAAIA,YAAY,KAAKK,OAAO,EAAE;QAC1B,MAAME,cAAc,GAAG9B,oBAAoB,CAACuB,YAAY,CAAC;QACzD,IAAIO,cAAc,CAACjH,KAAK,CAAC,EAAE;UACvBmG,GAAG,IAAIO,YAAY,GAAG,GAAG;QAC7B;MACJ;IACJ,CAAC,CAAC;IACFP,GAAG,IAAIY,OAAO;IACd,OAAOZ,GAAG,KAAKW,WAAW;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOlB,aAAa,CAACU,OAAO,EAAEvK,OAAO,EAAEmL,IAAI,EAAE;IACzC,OAAQlH,KAAK,IAAK;MACd,IAAIwF,eAAe,CAACqB,qBAAqB,CAAC7G,KAAK,EAAEsG,OAAO,CAAC,EAAE;QACvDY,IAAI,CAACC,UAAU,CAAC,MAAMpL,OAAO,CAACiE,KAAK,CAAC,CAAC;MACzC;IACJ,CAAC;EACL;EACA;EACA,OAAOoG,aAAa,CAACgB,OAAO,EAAE;IAC1B;IACA,QAAQA,OAAO;MACX,KAAK,KAAK;QACN,OAAO,QAAQ;MACnB;QACI,OAAOA,OAAO;IAAC;EAE3B;AACJ;AACA5B,eAAe,CAACzK,IAAI;EAAA,iBAAyFyK,eAAe,EAxuB9B1T,EAAE,UAwuB8CJ,QAAQ;AAAA,CAA6C;AACnM8T,eAAe,CAACxK,KAAK,kBAzuByElJ,EAAE;EAAA,OAyuBiB0T,eAAe;EAAA,SAAfA,eAAe;AAAA,EAAG;AACnI;EAAA,mDA1uB8F1T,EAAE,mBA0uBJ0T,eAAe,EAAc,CAAC;IAC9GvK,IAAI,EAAE7I;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE6I,IAAI,EAAEoB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DrB,IAAI,EAAE5I,MAAM;QACZkK,IAAI,EAAE,CAAC7K,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAM2V,WAAW,GAAG,OAAOvI,SAAS,KAAK,WAAW,IAAI,CAAC,CAACA,SAAS;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwI,oBAAoB,CAACC,aAAa,EAAEC,OAAO,EAAE;EAClD,OAAO/U,0BAA0B,CAAC;IAAE8U,aAAa;IAAE,GAAGE,qBAAqB,CAACD,OAAO;EAAE,CAAC,CAAC;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,iBAAiB,CAACF,OAAO,EAAE;EAChC,OAAO/U,0BAA0B,CAACgV,qBAAqB,CAACD,OAAO,CAAC,CAAC;AACrE;AACA,SAASC,qBAAqB,CAACD,OAAO,EAAE;EACpC,OAAO;IACHG,YAAY,EAAE,CACV,GAAGC,wBAAwB,EAC3B,IAAIJ,OAAO,EAAEK,SAAS,IAAI,EAAE,CAAC,CAChC;IACDC,iBAAiB,EAAEC;EACvB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,+BAA+B,GAAG;EACvC;EACA;EACA,OAAO,CAAC,GAAGC,qBAAqB,CAAC;AACrC;AACA,SAASC,cAAc,GAAG;EACtBlT,iBAAiB,CAACC,WAAW,EAAE;AACnC;AACA,SAASkT,YAAY,GAAG;EACpB,OAAO,IAAIzV,YAAY,EAAE;AAC7B;AACA,SAAS0V,SAAS,GAAG;EACjB;EACAzV,YAAY,CAACuD,QAAQ,CAAC;EACtB,OAAOA,QAAQ;AACnB;AACA,MAAM6R,mCAAmC,GAAG,CACxC;EAAE/O,OAAO,EAAEpG,WAAW;EAAEyV,QAAQ,EAAE1W;AAAqB,CAAC,EACxD;EAAEqH,OAAO,EAAEnG,oBAAoB;EAAEwV,QAAQ,EAAEH,cAAc;EAAE/O,KAAK,EAAE;AAAK,CAAC,EACxE;EAAEH,OAAO,EAAEtH,QAAQ;EAAEuH,UAAU,EAAEmP,SAAS;EAAElP,IAAI,EAAE;AAAG,CAAC,CACzD;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoP,eAAe,GAAGxV,qBAAqB,CAACC,YAAY,EAAE,SAAS,EAAEgV,mCAAmC,CAAC;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,+BAA+B,GAAG,IAAIxW,cAAc,CAACsV,WAAW,GAAG,gCAAgC,GAAG,EAAE,CAAC;AAC/G,MAAMY,qBAAqB,GAAG,CAC1B;EACIjP,OAAO,EAAEhG,mBAAmB;EAC5BwV,QAAQ,EAAEpP,qBAAqB;EAC/BF,IAAI,EAAE;AACV,CAAC,EACD;EACIF,OAAO,EAAE/F,YAAY;EACrBuV,QAAQ,EAAEtV,WAAW;EACrBgG,IAAI,EAAE,CAAC/F,MAAM,EAAEC,mBAAmB,EAAEJ,mBAAmB;AAC3D,CAAC,EACD;EACIgG,OAAO,EAAE9F,WAAW;EACpBsV,QAAQ,EAAEtV,WAAW;EACrBgG,IAAI,EAAE,CAAC/F,MAAM,EAAEC,mBAAmB,EAAEJ,mBAAmB;AAC3D,CAAC,CACJ;AACD,MAAM4U,wBAAwB,GAAG,CAC7B;EAAE5O,OAAO,EAAE3F,eAAe;EAAEgV,QAAQ,EAAE;AAAO,CAAC,EAC9C;EAAErP,OAAO,EAAEtG,YAAY;EAAEuG,UAAU,EAAEkP,YAAY;EAAEjP,IAAI,EAAE;AAAG,CAAC,EAAE;EAC3DF,OAAO,EAAEkC,qBAAqB;EAC9BsN,QAAQ,EAAExD,eAAe;EACzB7L,KAAK,EAAE,IAAI;EACXD,IAAI,EAAE,CAACxH,QAAQ,EAAEyB,MAAM,EAAEP,WAAW;AACxC,CAAC,EACD;EAAEoG,OAAO,EAAEkC,qBAAqB;EAAEsN,QAAQ,EAAEhD,eAAe;EAAErM,KAAK,EAAE,IAAI;EAAED,IAAI,EAAE,CAACxH,QAAQ;AAAE,CAAC,EAAE;EAC1FsH,OAAO,EAAEoH,mBAAmB;EAC5BoI,QAAQ,EAAEpI,mBAAmB;EAC7BlH,IAAI,EAAE,CAACiC,YAAY,EAAEuC,mBAAmB,EAAEnL,MAAM,EAAE4M,kCAAkC;AACxF,CAAC,EACD;EAAEnG,OAAO,EAAE1F,gBAAgB;EAAEmV,WAAW,EAAErI;AAAoB,CAAC,EAC/D;EAAEpH,OAAO,EAAE0D,gBAAgB;EAAE+L,WAAW,EAAE/K;AAAoB,CAAC,EAC/D;EAAE1E,OAAO,EAAE0E,mBAAmB;EAAE8K,QAAQ,EAAE9K,mBAAmB;EAAExE,IAAI,EAAE,CAACxH,QAAQ;AAAE,CAAC,EACjF;EAAEsH,OAAO,EAAEmC,YAAY;EAAEqN,QAAQ,EAAErN,YAAY;EAAEjC,IAAI,EAAE,CAACgC,qBAAqB,EAAE/H,MAAM;AAAE,CAAC,EACxF;EAAE6F,OAAO,EAAEpH,UAAU;EAAE4W,QAAQ,EAAE5N,UAAU;EAAE1B,IAAI,EAAE;AAAG,CAAC,EACvDmO,WAAW,GAAG;EAAErO,OAAO,EAAEuP,+BAA+B;EAAEF,QAAQ,EAAE;AAAK,CAAC,GAAG,EAAE,CAClF;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,aAAa,CAAC;EAChB7T,WAAW,CAAC8T,uBAAuB,EAAE;IACjC,IAAItB,WAAW,IAAIsB,uBAAuB,EAAE;MACxC,MAAM,IAAIhP,KAAK,CAAE,oFAAmF,GAC/F,mFAAkF,CAAC;IAC5F;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOiP,oBAAoB,CAACC,MAAM,EAAE;IAChC,OAAO;MACHC,QAAQ,EAAEJ,aAAa;MACvBb,SAAS,EAAE,CACP;QAAE7O,OAAO,EAAEzG,MAAM;QAAE8V,QAAQ,EAAEQ,MAAM,CAACtI;MAAM,CAAC,EAC3C;QAAEvH,OAAO,EAAEb,aAAa;QAAEsQ,WAAW,EAAElW;MAAO,CAAC,EAC/CwG,2BAA2B;IAEnC,CAAC;EACL;AACJ;AACA2P,aAAa,CAAC3N,IAAI;EAAA,iBAAyF2N,aAAa,EA97B1B5W,EAAE,UA87B0CyW,+BAA+B;AAAA,CAA2E;AACpPG,aAAa,CAACK,IAAI,kBA/7B4EjX,EAAE;EAAA,MA+7BY4W,aAAa;EAAA,UAAY7W,YAAY,EAAE0B,iBAAiB;AAAA,EAAI;AACxKmV,aAAa,CAACM,IAAI,kBAh8B4ElX,EAAE;EAAA,WAg8BsC,CAC9H,GAAG8V,wBAAwB,EAC3B,GAAGK,qBAAqB,CAC3B;EAAA,UAAYpW,YAAY,EAAE0B,iBAAiB;AAAA,EAAI;AACpD;EAAA,mDAp8B8FzB,EAAE,mBAo8BJ4W,aAAa,EAAc,CAAC;IAC5GzN,IAAI,EAAEzH,QAAQ;IACd+I,IAAI,EAAE,CAAC;MACCsL,SAAS,EAAE,CACP,GAAGD,wBAAwB,EAC3B,GAAGK,qBAAqB,CAC3B;MACDgB,OAAO,EAAE,CAACpX,YAAY,EAAE0B,iBAAiB;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE0H,IAAI,EAAEoB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DrB,IAAI,EAAExH;MACV,CAAC,EAAE;QACCwH,IAAI,EAAEvH;MACV,CAAC,EAAE;QACCuH,IAAI,EAAE5I,MAAM;QACZkK,IAAI,EAAE,CAACgM,+BAA+B;MAC1C,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA,SAASW,UAAU,GAAG;EAClB,OAAO,IAAIC,IAAI,CAACxV,QAAQ,CAACjC,QAAQ,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyX,IAAI,CAAC;EACPtU,WAAW,CAAC4H,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC2M,IAAI,GAAG3X,OAAO,EAAE;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI4X,MAAM,CAACC,GAAG,EAAEC,aAAa,GAAG,KAAK,EAAE;IAC/B,IAAI,CAACD,GAAG,EACJ,OAAO,IAAI;IACf,OAAO,IAAI,CAACE,mBAAmB,CAACF,GAAG,EAAEC,aAAa,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,OAAO,CAACC,IAAI,EAAEH,aAAa,GAAG,KAAK,EAAE;IACjC,IAAI,CAACG,IAAI,EACL,OAAO,EAAE;IACb,OAAOA,IAAI,CAACC,MAAM,CAAC,CAAC/C,MAAM,EAAE0C,GAAG,KAAK;MAChC,IAAIA,GAAG,EAAE;QACL1C,MAAM,CAACrM,IAAI,CAAC,IAAI,CAACiP,mBAAmB,CAACF,GAAG,EAAEC,aAAa,CAAC,CAAC;MAC7D;MACA,OAAO3C,MAAM;IACjB,CAAC,EAAE,EAAE,CAAC;EACV;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgD,MAAM,CAACC,YAAY,EAAE;IACjB,IAAI,CAACA,YAAY,EACb,OAAO,IAAI;IACf,OAAO,IAAI,CAACpN,IAAI,CAAC9E,aAAa,CAAE,QAAOkS,YAAa,GAAE,CAAC,IAAI,IAAI;EACnE;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,OAAO,CAACD,YAAY,EAAE;IAClB,IAAI,CAACA,YAAY,EACb,OAAO,EAAE;IACb,MAAME,IAAI,CAAC,eAAe,IAAI,CAACtN,IAAI,CAAC7D,gBAAgB,CAAE,QAAOiR,YAAa,GAAE,CAAC;IAC7E,OAAOE,IAAI,GAAG,EAAE,CAACpO,KAAK,CAACqO,IAAI,CAACD,IAAI,CAAC,GAAG,EAAE;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,SAAS,CAACX,GAAG,EAAEY,QAAQ,EAAE;IACrB,IAAI,CAACZ,GAAG,EACJ,OAAO,IAAI;IACfY,QAAQ,GAAGA,QAAQ,IAAI,IAAI,CAACC,cAAc,CAACb,GAAG,CAAC;IAC/C,MAAMc,IAAI,GAAG,IAAI,CAACR,MAAM,CAACM,QAAQ,CAAC;IAClC,IAAIE,IAAI,EAAE;MACN,OAAO,IAAI,CAACC,yBAAyB,CAACf,GAAG,EAAEc,IAAI,CAAC;IACpD;IACA,OAAO,IAAI,CAACZ,mBAAmB,CAACF,GAAG,EAAE,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIgB,SAAS,CAACT,YAAY,EAAE;IACpB,IAAI,CAACU,gBAAgB,CAAC,IAAI,CAACX,MAAM,CAACC,YAAY,CAAC,CAAC;EACpD;EACA;AACJ;AACA;AACA;EACIU,gBAAgB,CAACH,IAAI,EAAE;IACnB,IAAIA,IAAI,EAAE;MACN,IAAI,CAAChB,IAAI,CAAC3T,MAAM,CAAC2U,IAAI,CAAC;IAC1B;EACJ;EACAZ,mBAAmB,CAACY,IAAI,EAAEb,aAAa,GAAG,KAAK,EAAE;IAC7C,IAAI,CAACA,aAAa,EAAE;MAChB,MAAMW,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACC,IAAI,CAAC;MAC1C;MACA;MACA;MACA,MAAM7Q,IAAI,GAAG,IAAI,CAACuQ,OAAO,CAACI,QAAQ,CAAC,CAACM,MAAM,CAACjR,IAAI,IAAI,IAAI,CAACkR,mBAAmB,CAACL,IAAI,EAAE7Q,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3F,IAAIA,IAAI,KAAK8C,SAAS,EAClB,OAAO9C,IAAI;IACnB;IACA,MAAMsC,OAAO,GAAG,IAAI,CAACuN,IAAI,CAACvT,aAAa,CAAC,MAAM,CAAC;IAC/C,IAAI,CAACwU,yBAAyB,CAACD,IAAI,EAAEvO,OAAO,CAAC;IAC7C,MAAM6C,IAAI,GAAG,IAAI,CAACjC,IAAI,CAACiO,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACtDhM,IAAI,CAACF,WAAW,CAAC3C,OAAO,CAAC;IACzB,OAAOA,OAAO;EAClB;EACAwO,yBAAyB,CAACf,GAAG,EAAEnU,EAAE,EAAE;IAC/ByM,MAAM,CAACzE,IAAI,CAACmM,GAAG,CAAC,CAACjP,OAAO,CAAEsQ,IAAI,IAAKxV,EAAE,CAAC4C,YAAY,CAAC,IAAI,CAAC6S,cAAc,CAACD,IAAI,CAAC,EAAErB,GAAG,CAACqB,IAAI,CAAC,CAAC,CAAC;IACzF,OAAOxV,EAAE;EACb;EACAgV,cAAc,CAACb,GAAG,EAAE;IAChB,MAAMuB,IAAI,GAAGvB,GAAG,CAAC7R,IAAI,GAAG,MAAM,GAAG,UAAU;IAC3C,OAAQ,GAAEoT,IAAK,KAAIvB,GAAG,CAACuB,IAAI,CAAE,GAAE;EACnC;EACAJ,mBAAmB,CAACnB,GAAG,EAAE/P,IAAI,EAAE;IAC3B,OAAOqI,MAAM,CAACzE,IAAI,CAACmM,GAAG,CAAC,CAACwB,KAAK,CAAE3E,GAAG,IAAK5M,IAAI,CAAC3B,YAAY,CAAC,IAAI,CAACgT,cAAc,CAACzE,GAAG,CAAC,CAAC,KAAKmD,GAAG,CAACnD,GAAG,CAAC,CAAC;EACpG;EACAyE,cAAc,CAACD,IAAI,EAAE;IACjB,OAAOI,aAAa,CAACJ,IAAI,CAAC,IAAIA,IAAI;EACtC;AACJ;AACAxB,IAAI,CAACpO,IAAI;EAAA,iBAAyFoO,IAAI,EAlnCRrX,EAAE,UAknCwBJ,QAAQ;AAAA,CAA6C;AAC7KyX,IAAI,CAACnO,KAAK,kBAnnCoFlJ,EAAE;EAAA,OAmnCMqX,IAAI;EAAA;IAAA;IAAA;MAAA;IAAA;MAAA,IAAkCD,UAAU;IAAA;IAAA;EAAA;EAAA,YAA9B;AAAM,EAAqC;AACnK;EAAA,mDApnC8FpX,EAAE,mBAonCJqX,IAAI,EAAc,CAAC;IACnGlO,IAAI,EAAE7I,UAAU;IAChBmK,IAAI,EAAE,CAAC;MAAE6C,UAAU,EAAE,MAAM;MAAEnG,UAAU,EAAEiQ,UAAU;MAAEhQ,IAAI,EAAE;IAAG,CAAC;EACnE,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE+B,IAAI,EAAEoB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DrB,IAAI,EAAE5I,MAAM;QACZkK,IAAI,EAAE,CAAC7K,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA,MAAMqZ,aAAa,GAAG;EAClBC,SAAS,EAAE;AACf,CAAC;;AAED;AACA;AACA;AACA,SAASC,WAAW,GAAG;EACnB,OAAO,IAAIC,KAAK,CAACvX,QAAQ,CAACjC,QAAQ,CAAC,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwZ,KAAK,CAAC;EACRrW,WAAW,CAAC4H,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA;AACJ;AACA;EACI0O,QAAQ,GAAG;IACP,OAAO,IAAI,CAAC1O,IAAI,CAAC2O,KAAK;EAC1B;EACA;AACJ;AACA;AACA;EACIC,QAAQ,CAACC,QAAQ,EAAE;IACf,IAAI,CAAC7O,IAAI,CAAC2O,KAAK,GAAGE,QAAQ,IAAI,EAAE;EACpC;AACJ;AACAJ,KAAK,CAACnQ,IAAI;EAAA,iBAAyFmQ,KAAK,EApqCVpZ,EAAE,UAoqC0BJ,QAAQ;AAAA,CAA6C;AAC/KwZ,KAAK,CAAClQ,KAAK,kBArqCmFlJ,EAAE;EAAA,OAqqCOoZ,KAAK;EAAA;IAAA;IAAA;MAAA;IAAA;MAAA,IAAkCD,WAAW;IAAA;IAAA;EAAA;EAAA,YAA/B;AAAM,EAAsC;AACtK;EAAA,mDAtqC8FnZ,EAAE,mBAsqCJoZ,KAAK,EAAc,CAAC;IACpGjQ,IAAI,EAAE7I,UAAU;IAChBmK,IAAI,EAAE,CAAC;MAAE6C,UAAU,EAAE,MAAM;MAAEnG,UAAU,EAAEgS,WAAW;MAAE/R,IAAI,EAAE;IAAG,CAAC;EACpE,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE+B,IAAI,EAAEoB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DrB,IAAI,EAAE5I,MAAM;QACZkK,IAAI,EAAE,CAAC7K,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6Z,WAAW,CAAC9T,IAAI,EAAE0K,KAAK,EAAE;EAC9B,IAAI,OAAOqJ,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,EAAE;IAC9C;IACA;IACA;IACA;IACA,MAAMC,EAAE,GAAGtZ,OAAO,CAAC,IAAI,CAAC,GAAGA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9CsZ,EAAE,CAAChU,IAAI,CAAC,GAAG0K,KAAK;EACpB;AACJ;AAEA,MAAMuJ,GAAG,GAAG,OAAO7U,MAAM,KAAK,WAAW,IAAIA,MAAM,IAAI,CAAC,CAAC;AAEzD,MAAM8U,yBAAyB,CAAC;EAC5B9W,WAAW,CAAC+W,SAAS,EAAEC,QAAQ,EAAE;IAC7B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBjX,WAAW,CAACkX,GAAG,EAAE;IACb,IAAI,CAACC,MAAM,GAAGD,GAAG,CAACzT,QAAQ,CAACC,GAAG,CAAC3E,cAAc,CAAC;EAClD;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIqY,mBAAmB,CAACC,MAAM,EAAE;IACxB,MAAMC,MAAM,GAAGD,MAAM,IAAIA,MAAM,CAAC,QAAQ,CAAC;IACzC,MAAME,WAAW,GAAG,kBAAkB;IACtC;IACA,MAAMC,mBAAmB,GAAGX,GAAG,CAACY,OAAO,CAACC,OAAO,IAAI,IAAI;IACvD,IAAIJ,MAAM,IAAIE,mBAAmB,EAAE;MAC/BX,GAAG,CAACY,OAAO,CAACC,OAAO,CAACH,WAAW,CAAC;IACpC;IACA,MAAMI,KAAK,GAAGC,cAAc,EAAE;IAC9B,IAAIZ,QAAQ,GAAG,CAAC;IAChB,OAAOA,QAAQ,GAAG,CAAC,IAAKY,cAAc,EAAE,GAAGD,KAAK,GAAI,GAAG,EAAE;MACrD,IAAI,CAACR,MAAM,CAACU,IAAI,EAAE;MAClBb,QAAQ,EAAE;IACd;IACA,MAAMnK,GAAG,GAAG+K,cAAc,EAAE;IAC5B,IAAIN,MAAM,IAAIE,mBAAmB,EAAE;MAC/BX,GAAG,CAACY,OAAO,CAACK,UAAU,CAACP,WAAW,CAAC;IACvC;IACA,MAAMR,SAAS,GAAG,CAAClK,GAAG,GAAG8K,KAAK,IAAIX,QAAQ;IAC1CH,GAAG,CAACY,OAAO,CAACM,GAAG,CAAE,OAAMf,QAAS,0BAAyB,CAAC;IAC1DH,GAAG,CAACY,OAAO,CAACM,GAAG,CAAE,GAAEhB,SAAS,CAACiB,OAAO,CAAC,CAAC,CAAE,eAAc,CAAC;IACvD,OAAO,IAAIlB,yBAAyB,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAC7D;AACJ;AACA,SAASY,cAAc,GAAG;EACtB,OAAOf,GAAG,CAACoB,WAAW,IAAIpB,GAAG,CAACoB,WAAW,CAACC,GAAG,GAAGrB,GAAG,CAACoB,WAAW,CAACC,GAAG,EAAE,GACjE,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;AAC5B;AAEA,MAAMC,oBAAoB,GAAG,UAAU;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgB,CAACpB,GAAG,EAAE;EAC3BR,WAAW,CAAC2B,oBAAoB,EAAE,IAAIpB,eAAe,CAACC,GAAG,CAAC,CAAC;EAC3D,OAAOA,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA,SAASqB,iBAAiB,GAAG;EACzB7B,WAAW,CAAC2B,oBAAoB,EAAE,IAAI,CAAC;AAC3C;AAEA,SAASG,UAAU,CAACC,IAAI,EAAE;EACtB,MAAMC,WAAW,GAAG;IAChB,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,KAAK;IACV,IAAI,EAAE,KAAK;IACX,GAAG,EAAE,KAAK;IACV,GAAG,EAAE;EACT,CAAC;EACD,OAAOD,IAAI,CAAC9N,OAAO,CAAC,UAAU,EAAEK,CAAC,IAAI0N,WAAW,CAAC1N,CAAC,CAAC,CAAC;AACxD;AACA,SAAS2N,YAAY,CAACF,IAAI,EAAE;EACxB,MAAMG,aAAa,GAAG;IAClB,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,GAAG;IACV,KAAK,EAAE;EACX,CAAC;EACD,OAAOH,IAAI,CAAC9N,OAAO,CAAC,UAAU,EAAEK,CAAC,IAAI4N,aAAa,CAAC5N,CAAC,CAAC,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6N,YAAY,CAACvH,GAAG,EAAE;EACvB,OAAOA,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwH,aAAa,CAAC;EAChB9Y,WAAW,GAAG;IACV,IAAI,CAAC+Y,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,oBAAoB,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACD,KAAK,GAAGE,wBAAwB,CAACja,MAAM,CAACnC,QAAQ,CAAC,EAAEmC,MAAM,CAACtB,MAAM,CAAC,CAAC;EAC3E;EACA;AACJ;AACA;EACIgG,GAAG,CAAC4N,GAAG,EAAE4H,YAAY,EAAE;IACnB,OAAO,IAAI,CAACH,KAAK,CAACzH,GAAG,CAAC,KAAK9J,SAAS,GAAG,IAAI,CAACuR,KAAK,CAACzH,GAAG,CAAC,GAAG4H,YAAY;EACzE;EACA;AACJ;AACA;EACI3R,GAAG,CAAC+J,GAAG,EAAEhE,KAAK,EAAE;IACZ,IAAI,CAACyL,KAAK,CAACzH,GAAG,CAAC,GAAGhE,KAAK;EAC3B;EACA;AACJ;AACA;EACI1M,MAAM,CAAC0Q,GAAG,EAAE;IACR,OAAO,IAAI,CAACyH,KAAK,CAACzH,GAAG,CAAC;EAC1B;EACA;AACJ;AACA;EACI6H,MAAM,CAAC7H,GAAG,EAAE;IACR,OAAO,IAAI,CAACyH,KAAK,CAACK,cAAc,CAAC9H,GAAG,CAAC;EACzC;EACA;AACJ;AACA;EACI,IAAI+H,OAAO,GAAG;IACV,OAAOtM,MAAM,CAACzE,IAAI,CAAC,IAAI,CAACyQ,KAAK,CAAC,CAAC9U,MAAM,KAAK,CAAC;EAC/C;EACA;AACJ;AACA;EACIqV,WAAW,CAAChI,GAAG,EAAEpM,QAAQ,EAAE;IACvB,IAAI,CAAC8T,oBAAoB,CAAC1H,GAAG,CAAC,GAAGpM,QAAQ;EAC7C;EACA;AACJ;AACA;EACIqU,MAAM,GAAG;IACL;IACA,KAAK,MAAMjI,GAAG,IAAI,IAAI,CAAC0H,oBAAoB,EAAE;MACzC,IAAI,IAAI,CAACA,oBAAoB,CAACI,cAAc,CAAC9H,GAAG,CAAC,EAAE;QAC/C,IAAI;UACA,IAAI,CAACyH,KAAK,CAACzH,GAAG,CAAC,GAAG,IAAI,CAAC0H,oBAAoB,CAAC1H,GAAG,CAAC,EAAE;QACtD,CAAC,CACD,OAAOlI,CAAC,EAAE;UACNqO,OAAO,CAAC+B,IAAI,CAAC,qCAAqC,EAAEpQ,CAAC,CAAC;QAC1D;MACJ;IACJ;IACA,OAAOqQ,IAAI,CAACC,SAAS,CAAC,IAAI,CAACX,KAAK,CAAC;EACrC;AACJ;AACAD,aAAa,CAAC5S,IAAI;EAAA,iBAAyF4S,aAAa;AAAA,CAAoD;AAC5KA,aAAa,CAAC3S,KAAK,kBA14C2ElJ,EAAE;EAAA,OA04Ce6b,aAAa;EAAA,SAAbA,aAAa;EAAA,YAAc;AAAM,EAAG;AACnJ;EAAA,mDA34C8F7b,EAAE,mBA24CJ6b,aAAa,EAAc,CAAC;IAC5G1S,IAAI,EAAE7I,UAAU;IAChBmK,IAAI,EAAE,CAAC;MAAE6C,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AACtD,SAAS0O,wBAAwB,CAAC/X,GAAG,EAAEwK,KAAK,EAAE;EAC1C;EACA;EACA,MAAMiO,MAAM,GAAGzY,GAAG,CAAC0Y,cAAc,CAAClO,KAAK,GAAG,QAAQ,CAAC;EACnD,IAAImO,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIF,MAAM,IAAIA,MAAM,CAACjQ,WAAW,EAAE;IAC9B,IAAI;MACA;MACAmQ,YAAY,GAAGJ,IAAI,CAACK,KAAK,CAACnB,YAAY,CAACgB,MAAM,CAACjQ,WAAW,CAAC,CAAC;IAC/D,CAAC,CACD,OAAON,CAAC,EAAE;MACNqO,OAAO,CAAC+B,IAAI,CAAC,kDAAkD,GAAG9N,KAAK,EAAEtC,CAAC,CAAC;IAC/E;EACJ;EACA,OAAOyQ,YAAY;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,0BAA0B,CAAC;AAEjCA,0BAA0B,CAAC7T,IAAI;EAAA,iBAAyF6T,0BAA0B;AAAA,CAAkD;AACpMA,0BAA0B,CAAC7F,IAAI,kBA16C+DjX,EAAE;EAAA,MA06CyB8c;AAA0B,EAAG;AACtJA,0BAA0B,CAAC5F,IAAI,kBA36C+DlX,EAAE,qBA26CsD;AACtJ;EAAA,mDA56C8FA,EAAE,mBA46CJ8c,0BAA0B,EAAc,CAAC;IACzH3T,IAAI,EAAEzH,QAAQ;IACd+I,IAAI,EAAE,CAAC,CAAC,CAAC;EACb,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAMsS,EAAE,CAAC;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,GAAG,GAAG;IACT,OAAO,MAAM,IAAI;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,GAAG,CAAC7E,QAAQ,EAAE;IACjB,OAAQ8E,YAAY,IAAK;MACrB,OAAOA,YAAY,CAACC,aAAa,IAAI,IAAI,GACrCC,cAAc,CAACF,YAAY,CAACC,aAAa,EAAE/E,QAAQ,CAAC,GACpD,KAAK;IACb,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOiF,SAAS,CAAClU,IAAI,EAAE;IACnB,OAAQmU,SAAS,IAAKA,SAAS,CAACC,cAAc,CAAC7I,OAAO,CAACvL,IAAI,CAAC,KAAK,CAAC,CAAC;EACvE;AACJ;AACA,SAASiU,cAAc,CAACI,CAAC,EAAEpF,QAAQ,EAAE;EACjC,IAAIzY,OAAO,EAAE,CAAC4E,aAAa,CAACiZ,CAAC,CAAC,EAAE;IAC5B,OAAOA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACC,OAAO,CAACrF,QAAQ,CAAC,IACnCoF,CAAC,CAACE,iBAAiB,IAAIF,CAAC,CAACE,iBAAiB,CAACtF,QAAQ,CAAC,IACpDoF,CAAC,CAACG,qBAAqB,IAAIH,CAAC,CAACG,qBAAqB,CAACvF,QAAQ,CAAC;EACpE;EACA,OAAO,KAAK;AAChB;;AAEA;AACA;AACA;AACA,MAAMwF,WAAW,GAAG;EAChB;EACA,KAAK,EAAE,IAAI;EACX,UAAU,EAAE,IAAI;EAChB,SAAS,EAAE,IAAI;EACf,QAAQ,EAAE,IAAI;EACd,WAAW,EAAE,IAAI;EACjB,SAAS,EAAE,IAAI;EACf,UAAU,EAAE,IAAI;EAChB,OAAO,EAAE,IAAI;EACb,SAAS,EAAE,IAAI;EACf;EACA,OAAO,EAAE,IAAI;EACb,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,UAAU,EAAE,IAAI;EAChB,aAAa,EAAE,IAAI;EACnB,SAAS,EAAE,IAAI;EACf,UAAU,EAAE,IAAI;EAChB;EACA,OAAO,EAAE,IAAI;EACb,SAAS,EAAE,IAAI;EACf;EACA,QAAQ,EAAE,IAAI;EACd,aAAa,EAAE,IAAI;EACnB,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,cAAc,EAAE,IAAI;EACpB;EACA,OAAO,EAAE,IAAI;EACb,WAAW,EAAE,IAAI;EACjB,YAAY,EAAE,IAAI;EAClB,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,IAAI;EACjB;EACA,KAAK,EAAE,IAAI;EACX,WAAW,EAAE;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG,IAAI5d,cAAc,CAAC,qBAAqB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA,MAAM6d,aAAa,GAAG,IAAI7d,cAAc,CAAC,cAAc,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA,MAAM8d,mBAAmB,CAAC;EACtBhb,WAAW,GAAG;IACV;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACib,MAAM,GAAG,EAAE;IAChB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAW,CAACnU,OAAO,EAAE;IACjB,MAAMoU,EAAE,GAAG,IAAIC,MAAM,CAACrU,OAAO,EAAE,IAAI,CAAC2L,OAAO,CAAC;IAC5CyI,EAAE,CAAC1X,GAAG,CAAC,OAAO,CAAC,CAAC6D,GAAG,CAAC;MAAE+T,MAAM,EAAE;IAAK,CAAC,CAAC;IACrCF,EAAE,CAAC1X,GAAG,CAAC,QAAQ,CAAC,CAAC6D,GAAG,CAAC;MAAE+T,MAAM,EAAE;IAAK,CAAC,CAAC;IACtC,KAAK,MAAMrU,SAAS,IAAI,IAAI,CAACiU,SAAS,EAAE;MACpCE,EAAE,CAAC1X,GAAG,CAACuD,SAAS,CAAC,CAACM,GAAG,CAAC,IAAI,CAAC2T,SAAS,CAACjU,SAAS,CAAC,CAAC;IACpD;IACA,OAAOmU,EAAE;EACb;AACJ;AACAJ,mBAAmB,CAAC9U,IAAI;EAAA,iBAAyF8U,mBAAmB;AAAA,CAAoD;AACxLA,mBAAmB,CAAC7U,KAAK,kBA7kDqElJ,EAAE;EAAA,OA6kDqB+d,mBAAmB;EAAA,SAAnBA,mBAAmB;AAAA,EAAG;AAC3I;EAAA,mDA9kD8F/d,EAAE,mBA8kDJ+d,mBAAmB,EAAc,CAAC;IAClH5U,IAAI,EAAE7I;EACV,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAMge,oBAAoB,SAAS5T,kBAAkB,CAAC;EAClD3H,WAAW,CAACkB,GAAG,EAAEsa,OAAO,EAAE/D,OAAO,EAAEgE,MAAM,EAAE;IACvC,KAAK,CAACva,GAAG,CAAC;IACV,IAAI,CAACsa,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC/D,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACgE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC9B;EACApU,QAAQ,CAACL,SAAS,EAAE;IAChB,IAAI,CAAC4T,WAAW,CAACzB,cAAc,CAACnS,SAAS,CAACiK,WAAW,EAAE,CAAC,IAAI,CAAC,IAAI,CAACyK,aAAa,CAAC1U,SAAS,CAAC,EAAE;MACxF,OAAO,KAAK;IAChB;IACA,IAAI,CAACjF,MAAM,CAACqZ,MAAM,IAAI,CAAC,IAAI,CAACI,MAAM,EAAE;MAChC,IAAI,OAAOxR,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C,IAAI,CAACwN,OAAO,CAAC+B,IAAI,CAAE,QAAOvS,SAAU,mDAAkD,GACjF,iDAAgD,CAAC;MAC1D;MACA,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACAxG,gBAAgB,CAACuG,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAMmL,IAAI,GAAG,IAAI,CAACzL,OAAO,CAACS,OAAO,EAAE;IACnCJ,SAAS,GAAGA,SAAS,CAACiK,WAAW,EAAE;IACnC;IACA;IACA,IAAI,CAAClP,MAAM,CAACqZ,MAAM,IAAI,IAAI,CAACI,MAAM,EAAE;MAC/B,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,IAAIrJ,IAAI,CAACrB,iBAAiB,CAAC,MAAM,IAAI,CAACyK,MAAM,EAAE,CAAC;MACxF;MACA;MACA;MACA,IAAIG,kBAAkB,GAAG,KAAK;MAC9B,IAAIC,UAAU,GAAG,MAAM;QACnBD,kBAAkB,GAAG,IAAI;MAC7B,CAAC;MACDvJ,IAAI,CAACrB,iBAAiB,CAAC,MAAM,IAAI,CAAC0K,cAAc,CAC3C9X,IAAI,CAAC,MAAM;QACZ;QACA,IAAI,CAAC5B,MAAM,CAACqZ,MAAM,EAAE;UAChB,IAAI,OAAOpR,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;YAC/C,IAAI,CAACwN,OAAO,CAAC+B,IAAI,CAAE,mEAAkE,CAAC;UAC1F;UACAqC,UAAU,GAAG,MAAM,CAAE,CAAC;UACtB;QACJ;QACA,IAAI,CAACD,kBAAkB,EAAE;UACrB;UACA;UACA;UACAC,UAAU,GAAG,IAAI,CAACpb,gBAAgB,CAACuG,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;QACnE;MACJ,CAAC,CAAC,CACG4U,KAAK,CAAC,MAAM;QACb,IAAI,OAAO7R,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/C,IAAI,CAACwN,OAAO,CAAC+B,IAAI,CAAE,QAAOvS,SAAU,6CAA4C,GAC3E,0BAAyB,CAAC;QACnC;QACA4U,UAAU,GAAG,MAAM,CAAE,CAAC;MAC1B,CAAC,CAAC,CAAC;MACH;MACA;MACA;MACA,OAAO,MAAM;QACTA,UAAU,EAAE;MAChB,CAAC;IACL;IACA,OAAOxJ,IAAI,CAACrB,iBAAiB,CAAC,MAAM;MAChC;MACA,MAAMoK,EAAE,GAAG,IAAI,CAACI,OAAO,CAACL,WAAW,CAACnU,OAAO,CAAC;MAC5C,MAAM9B,QAAQ,GAAG,UAAU6W,QAAQ,EAAE;QACjC1J,IAAI,CAACC,UAAU,CAAC,YAAY;UACxBpL,OAAO,CAAC6U,QAAQ,CAAC;QACrB,CAAC,CAAC;MACN,CAAC;MACDX,EAAE,CAACY,EAAE,CAAC/U,SAAS,EAAE/B,QAAQ,CAAC;MAC1B,OAAO,MAAM;QACTkW,EAAE,CAACa,GAAG,CAAChV,SAAS,EAAE/B,QAAQ,CAAC;QAC3B;QACA,IAAI,OAAOkW,EAAE,CAAClO,OAAO,KAAK,UAAU,EAAE;UAClCkO,EAAE,CAAClO,OAAO,EAAE;QAChB;MACJ,CAAC;IACL,CAAC,CAAC;EACN;EACAyO,aAAa,CAAC1U,SAAS,EAAE;IACrB,OAAO,IAAI,CAACuU,OAAO,CAACP,MAAM,CAACtJ,OAAO,CAAC1K,SAAS,CAAC,GAAG,CAAC,CAAC;EACtD;AACJ;AACAsU,oBAAoB,CAACrV,IAAI;EAAA,iBAAyFqV,oBAAoB,EA9qDxCte,EAAE,UA8qDwDJ,QAAQ,GA9qDlEI,EAAE,UA8qD6E6d,qBAAqB,GA9qDpG7d,EAAE,UA8qD+GA,EAAE,CAACgC,QAAQ,GA9qD5HhC,EAAE,UA8qDuI8d,aAAa;AAAA,CAA6D;AACjTQ,oBAAoB,CAACpV,KAAK,kBA/qDoElJ,EAAE;EAAA,OA+qDsBse,oBAAoB;EAAA,SAApBA,oBAAoB;AAAA,EAAG;AAC7I;EAAA,mDAhrD8Fte,EAAE,mBAgrDJse,oBAAoB,EAAc,CAAC;IACnHnV,IAAI,EAAE7I;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE6I,IAAI,EAAEoB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DrB,IAAI,EAAE5I,MAAM;QACZkK,IAAI,EAAE,CAAC7K,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEuJ,IAAI,EAAE4U,mBAAmB;MAAEvT,UAAU,EAAE,CAAC;QAC5CrB,IAAI,EAAE5I,MAAM;QACZkK,IAAI,EAAE,CAACoT,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAE1U,IAAI,EAAEnJ,EAAE,CAACgC;IAAS,CAAC,EAAE;MAAEmH,IAAI,EAAEoB,SAAS;MAAEC,UAAU,EAAE,CAAC;QACzDrB,IAAI,EAAExH;MACV,CAAC,EAAE;QACCwH,IAAI,EAAE5I,MAAM;QACZkK,IAAI,EAAE,CAACqT,aAAa;MACxB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmB,YAAY,CAAC;AAEnBA,YAAY,CAAChW,IAAI;EAAA,iBAAyFgW,YAAY;AAAA,CAAkD;AACxKA,YAAY,CAAChI,IAAI,kBA5sD6EjX,EAAE;EAAA,MA4sDWif;AAAY,EAAG;AAC1HA,YAAY,CAAC/H,IAAI,kBA7sD6ElX,EAAE;EAAA,WA6sDoC,CAC5H;IACIkH,OAAO,EAAEkC,qBAAqB;IAC9BsN,QAAQ,EAAE4H,oBAAoB;IAC9BjX,KAAK,EAAE,IAAI;IACXD,IAAI,EAAE,CAACxH,QAAQ,EAAEie,qBAAqB,EAAE7b,QAAQ,EAAE,CAAC,IAAIL,QAAQ,EAAE,EAAEmc,aAAa,CAAC;EACrF,CAAC,EACD;IAAE5W,OAAO,EAAE2W,qBAAqB;IAAEnH,QAAQ,EAAEqH,mBAAmB;IAAE3W,IAAI,EAAE;EAAG,CAAC;AAC9E,EAAG;AACR;EAAA,mDAttD8FpH,EAAE,mBAstDJif,YAAY,EAAc,CAAC;IAC3G9V,IAAI,EAAEzH,QAAQ;IACd+I,IAAI,EAAE,CAAC;MACCsL,SAAS,EAAE,CACP;QACI7O,OAAO,EAAEkC,qBAAqB;QAC9BsN,QAAQ,EAAE4H,oBAAoB;QAC9BjX,KAAK,EAAE,IAAI;QACXD,IAAI,EAAE,CAACxH,QAAQ,EAAEie,qBAAqB,EAAE7b,QAAQ,EAAE,CAAC,IAAIL,QAAQ,EAAE,EAAEmc,aAAa,CAAC;MACrF,CAAC,EACD;QAAE5W,OAAO,EAAE2W,qBAAqB;QAAEnH,QAAQ,EAAEqH,mBAAmB;QAAE3W,IAAI,EAAE;MAAG,CAAC;IAEnF,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8X,YAAY,CAAC;AAEnBA,YAAY,CAACjW,IAAI;EAAA,iBAAyFiW,YAAY;AAAA,CAAoD;AAC1KA,YAAY,CAAChW,KAAK,kBAvwD4ElJ,EAAE;EAAA,OAuwDckf,YAAY;EAAA;IAAA;IAAA;MAAA,cAAZA,YAAY;IAAA;MAAA,IAvwD5Blf,EAAE,UAuwDgGmf,gBAAgB;IAAA;IAAA;EAAA;EAAA,YAAxE;AAAM,EAAyE;AACvN;EAAA,mDAxwD8Fnf,EAAE,mBAwwDJkf,YAAY,EAAc,CAAC;IAC3G/V,IAAI,EAAE7I,UAAU;IAChBmK,IAAI,EAAE,CAAC;MAAE6C,UAAU,EAAE,MAAM;MAAEqJ,WAAW,EAAE1U,UAAU,CAAC,MAAMkd,gBAAgB;IAAE,CAAC;EAClF,CAAC,CAAC;AAAA;AACV,SAASC,uBAAuB,CAAC5Y,QAAQ,EAAE;EACvC,OAAO,IAAI2Y,gBAAgB,CAAC3Y,QAAQ,CAACC,GAAG,CAAC7G,QAAQ,CAAC,CAAC;AACvD;AACA,MAAMuf,gBAAgB,SAASD,YAAY,CAAC;EACxCnc,WAAW,CAAC4H,IAAI,EAAE;IACd,KAAK,EAAE;IACP,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA0U,QAAQ,CAACC,GAAG,EAAEjP,KAAK,EAAE;IACjB,IAAIA,KAAK,IAAI,IAAI,EACb,OAAO,IAAI;IACf,QAAQiP,GAAG;MACP,KAAKnd,eAAe,CAACod,IAAI;QACrB,OAAOlP,KAAK;MAChB,KAAKlO,eAAe,CAACqd,IAAI;QACrB,IAAIpd,gCAAgC,CAACiO,KAAK,EAAE,MAAM,CAAC,sBAAsB,EAAE;UACvE,OAAOhO,gBAAgB,CAACgO,KAAK,CAAC;QAClC;QACA,OAAO9N,cAAc,CAAC,IAAI,CAACoI,IAAI,EAAE8U,MAAM,CAACpP,KAAK,CAAC,CAAC,CAACqP,QAAQ,EAAE;MAC9D,KAAKvd,eAAe,CAACwd,KAAK;QACtB,IAAIvd,gCAAgC,CAACiO,KAAK,EAAE,OAAO,CAAC,uBAAuB,EAAE;UACzE,OAAOhO,gBAAgB,CAACgO,KAAK,CAAC;QAClC;QACA,OAAOA,KAAK;MAChB,KAAKlO,eAAe,CAACyd,MAAM;QACvB,IAAIxd,gCAAgC,CAACiO,KAAK,EAAE,QAAQ,CAAC,wBAAwB,EAAE;UAC3E,OAAOhO,gBAAgB,CAACgO,KAAK,CAAC;QAClC;QACA,MAAM,IAAIxI,KAAK,CAAC,uCAAuC,CAAC;MAC5D,KAAK1F,eAAe,CAAC0d,GAAG;QACpB,IAAIzd,gCAAgC,CAACiO,KAAK,EAAE,KAAK,CAAC,qBAAqB,EAAE;UACrE,OAAOhO,gBAAgB,CAACgO,KAAK,CAAC;QAClC;QACA,OAAO/N,aAAa,CAACmd,MAAM,CAACpP,KAAK,CAAC,CAAC;MACvC,KAAKlO,eAAe,CAAC2d,YAAY;QAC7B,IAAI1d,gCAAgC,CAACiO,KAAK,EAAE,aAAa,CAAC,6BAA6B,EAAE;UACrF,OAAOhO,gBAAgB,CAACgO,KAAK,CAAC;QAClC;QACA,MAAM,IAAIxI,KAAK,CAAE,oDAAmD3F,iBAAkB,GAAE,CAAC;MAC7F;QACI,MAAM,IAAI2F,KAAK,CAAE,8BAA6ByX,GAAI,SAAQpd,iBAAkB,GAAE,CAAC;IAAC;EAE5F;EACA6d,uBAAuB,CAAC1P,KAAK,EAAE;IAC3B,OAAO7N,4BAA4B,CAAC6N,KAAK,CAAC;EAC9C;EACA2P,wBAAwB,CAAC3P,KAAK,EAAE;IAC5B,OAAO5N,6BAA6B,CAAC4N,KAAK,CAAC;EAC/C;EACA4P,yBAAyB,CAAC5P,KAAK,EAAE;IAC7B,OAAO3N,8BAA8B,CAAC2N,KAAK,CAAC;EAChD;EACA6P,sBAAsB,CAAC7P,KAAK,EAAE;IAC1B,OAAO1N,2BAA2B,CAAC0N,KAAK,CAAC;EAC7C;EACA8P,8BAA8B,CAAC9P,KAAK,EAAE;IAClC,OAAOzN,mCAAmC,CAACyN,KAAK,CAAC;EACrD;AACJ;AACA8O,gBAAgB,CAAClW,IAAI;EAAA,iBAAyFkW,gBAAgB,EAv0DhCnf,EAAE,UAu0DgDJ,QAAQ;AAAA,CAA6C;AACrMuf,gBAAgB,CAACjW,KAAK,kBAx0DwElJ,EAAE;EAAA,OAw0DkBmf,gBAAgB;EAAA;IAAA;IAAA;MAAA;IAAA;MAAA,IAAkCC,uBAAuB,CAx0D7Fpf,EAAE,UAw0D6GI,QAAQ;IAAA;IAAA;EAAA;EAAA,YAArE;AAAM,EAAqE;AAC3N;EAAA,mDAz0D8FJ,EAAE,mBAy0DJmf,gBAAgB,EAAc,CAAC;IAC/GhW,IAAI,EAAE7I,UAAU;IAChBmK,IAAI,EAAE,CAAC;MAAE6C,UAAU,EAAE,MAAM;MAAEnG,UAAU,EAAEiY,uBAAuB;MAAEhY,IAAI,EAAE,CAAChH,QAAQ;IAAE,CAAC;EACxF,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE+I,IAAI,EAAEoB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DrB,IAAI,EAAE5I,MAAM;QACZkK,IAAI,EAAE,CAAC7K,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwgB,OAAO,GAAG,IAAIvd,OAAO,CAAC,SAAS,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAAS+T,aAAa,EAAEkG,0BAA0B,EAAEC,EAAE,EAAEmC,YAAY,EAAE9V,qBAAqB,EAAEC,YAAY,EAAEwU,qBAAqB,EAAEC,aAAa,EAAEC,mBAAmB,EAAEkB,YAAY,EAAE5H,IAAI,EAAEhK,kCAAkC,EAAE+L,KAAK,EAAEyC,aAAa,EAAEuE,OAAO,EAAE5K,oBAAoB,EAAEI,iBAAiB,EAAE0F,iBAAiB,EAAED,gBAAgB,EAAEO,YAAY,EAAEpF,eAAe,EAAEN,+BAA+B,EAAEhT,iBAAiB,IAAImd,kBAAkB,EAAE/Y,qBAAqB,IAAIgZ,sBAAsB,EAAEpN,eAAe,IAAIqN,gBAAgB,EAAEjS,mBAAmB,IAAIkS,oBAAoB,EAAErB,gBAAgB,IAAIsB,iBAAiB,EAAE7U,mBAAmB,IAAI8U,oBAAoB,EAAEpC,oBAAoB,IAAIqC,qBAAqB,EAAE1K,mCAAmC,IAAI2K,oCAAoC,EAAElN,eAAe,IAAImN,gBAAgB,EAAEhU,cAAc,IAAIiU,eAAe,EAAElW,gBAAgB,IAAImW,iBAAiB,EAAE1a,aAAa,IAAI2a,cAAc,EAAEzF,UAAU,IAAI0F,WAAW,EAAErT,aAAa,IAAIsT,cAAc,EAAE9K,cAAc,IAAI+K,eAAe,EAAE3T,oBAAoB,IAAI4T,qBAAqB,EAAEzT,iBAAiB,IAAI0T,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}