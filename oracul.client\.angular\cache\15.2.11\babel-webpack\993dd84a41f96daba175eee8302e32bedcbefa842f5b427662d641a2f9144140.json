{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/Harmonia/oracul.client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { ReactiveFormsModule, FormBuilder } from '@angular/forms';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { of, throwError } from 'rxjs';\nimport { ProfileEditComponent } from './profile-edit.component';\nimport { ProfileService } from '../../services/profile.service';\nimport { AuthService } from '../../../auth/services/auth.service';\ndescribe('ProfileEditComponent', () => {\n  let component;\n  let fixture;\n  let mockProfileService;\n  let mockAuthService;\n  const mockProfile = {\n    id: 1,\n    userId: 1,\n    username: 'testuser',\n    slug: 'testuser',\n    firstName: 'Test',\n    lastName: 'User',\n    email: '<EMAIL>',\n    profilePhotoUrl: 'https://example.com/photo.jpg',\n    coverPhotoUrl: 'https://example.com/cover.jpg',\n    professionalTitle: 'Astrologer',\n    headline: 'Professional Astrologer',\n    summary: 'Experienced astrologer with 10 years of practice',\n    isPublic: true,\n    location: {\n      city: 'Sofia',\n      state: 'Sofia',\n      country: 'Bulgaria',\n      displayLocation: 'Sofia, Bulgaria'\n    },\n    contactInfo: {\n      email: '<EMAIL>',\n      isEmailPublic: true,\n      website: 'https://example.com',\n      portfolioUrl: 'https://portfolio.example.com',\n      phoneNumbers: [],\n      businessAddress: {\n        street: '123 Test St',\n        city: 'Sofia',\n        state: 'Sofia',\n        postalCode: '1000',\n        country: 'Bulgaria',\n        isPublic: true\n      }\n    },\n    skills: [{\n      id: 1,\n      name: 'Tarot Reading',\n      category: 'Astrology',\n      proficiencyLevel: 'expert',\n      endorsements: 5\n    }],\n    consultationRates: {\n      hourlyRate: 50,\n      sessionRate: 80,\n      currency: 'BGN'\n    },\n    serviceOfferings: [{\n      id: 1,\n      name: 'Personal Reading',\n      description: 'Comprehensive personal astrology reading',\n      price: 100,\n      currency: 'BGN',\n      duration: 60,\n      category: 'Reading',\n      isActive: true\n    }],\n    socialLinks: [],\n    profileViews: 0,\n    completionPercentage: 85,\n    createdAt: new Date(),\n    updatedAt: new Date()\n  };\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    const profileServiceSpy = jasmine.createSpyObj('ProfileService', ['getCurrentUserProfile', 'updateProfile', 'uploadProfilePhoto', 'uploadCoverPhoto']);\n    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    yield TestBed.configureTestingModule({\n      declarations: [ProfileEditComponent],\n      imports: [ReactiveFormsModule, MatSnackBarModule, MatCardModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatSlideToggleModule, MatButtonModule, MatIconModule, MatProgressSpinnerModule, RouterTestingModule, BrowserAnimationsModule, HttpClientTestingModule],\n      providers: [FormBuilder, {\n        provide: ProfileService,\n        useValue: profileServiceSpy\n      }, {\n        provide: AuthService,\n        useValue: authServiceSpy\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(ProfileEditComponent);\n    component = fixture.componentInstance;\n    mockProfileService = TestBed.inject(ProfileService);\n    mockAuthService = TestBed.inject(AuthService);\n    // Setup default mock responses\n    mockProfileService.getCurrentUserProfile.and.returnValue(of(mockProfile));\n    mockProfileService.updateProfile.and.returnValue(of(mockProfile));\n    mockProfileService.uploadProfilePhoto.and.returnValue(of({\n      url: 'https://example.com/new-photo.jpg'\n    }));\n    mockProfileService.uploadCoverPhoto.and.returnValue(of({\n      url: 'https://example.com/new-cover.jpg'\n    }));\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should load profile on init', () => {\n    component.ngOnInit();\n    expect(mockProfileService.getCurrentUserProfile).toHaveBeenCalled();\n    expect(component.profile).toEqual(mockProfile);\n    expect(component.isLoading).toBeFalse();\n  });\n  it('should populate form with profile data', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n    expect(component.profileForm.get('firstName')?.value).toBe('Test');\n    expect(component.profileForm.get('lastName')?.value).toBe('User');\n    expect(component.profileForm.get('professionalTitle')?.value).toBe('Astrologer');\n    expect(component.profileForm.get('headline')?.value).toBe('Professional Astrologer');\n    expect(component.profileForm.get('summary')?.value).toBe('Experienced astrologer with 10 years of practice');\n    expect(component.profileForm.get('isPublic')?.value).toBe(true);\n  });\n  it('should populate consultation rates', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n    const consultationRates = component.profileForm.get('consultationRates');\n    expect(consultationRates?.get('hourlyRate')?.value).toBe(50);\n    expect(consultationRates?.get('sessionRate')?.value).toBe(80);\n    expect(consultationRates?.get('currency')?.value).toBe('BGN');\n  });\n  it('should populate skills array', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n    expect(component.skills.length).toBe(1);\n    const firstSkill = component.skills.at(0);\n    expect(firstSkill?.get('name')?.value).toBe('Tarot Reading');\n    expect(firstSkill?.get('category')?.value).toBe('Astrology');\n    expect(firstSkill?.get('proficiencyLevel')?.value).toBe('expert');\n  });\n  it('should populate service offerings array', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n    expect(component.serviceOfferings.length).toBe(1);\n    const firstService = component.serviceOfferings.at(0);\n    expect(firstService?.get('name')?.value).toBe('Personal Reading');\n    expect(firstService?.get('description')?.value).toBe('Comprehensive personal astrology reading');\n    expect(firstService?.get('price')?.value).toBe(100);\n    expect(firstService?.get('currency')?.value).toBe('BGN');\n    expect(firstService?.get('duration')?.value).toBe(60);\n    expect(firstService?.get('category')?.value).toBe('Reading');\n    expect(firstService?.get('isActive')?.value).toBe(true);\n  });\n  it('should add new skill', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n    const initialSkillsCount = component.skills.length;\n    component.addSkill();\n    expect(component.skills.length).toBe(initialSkillsCount + 1);\n    const newSkill = component.skills.at(component.skills.length - 1);\n    expect(newSkill?.get('name')?.value).toBe('');\n    expect(newSkill?.get('proficiencyLevel')?.value).toBe('intermediate');\n  });\n  it('should remove skill', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n    component.addSkill(); // Add a skill first\n    const skillsCount = component.skills.length;\n    component.removeSkill(0);\n    expect(component.skills.length).toBe(skillsCount - 1);\n  });\n  it('should add new service offering', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n    const initialServicesCount = component.serviceOfferings.length;\n    component.addServiceOffering();\n    expect(component.serviceOfferings.length).toBe(initialServicesCount + 1);\n    const newService = component.serviceOfferings.at(component.serviceOfferings.length - 1);\n    expect(newService?.get('name')?.value).toBe('');\n    expect(newService?.get('price')?.value).toBe(0);\n    expect(newService?.get('currency')?.value).toBe('BGN');\n    expect(newService?.get('duration')?.value).toBe(60);\n    expect(newService?.get('isActive')?.value).toBe(true);\n  });\n  it('should remove service offering', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n    component.addServiceOffering(); // Add a service first\n    const servicesCount = component.serviceOfferings.length;\n    component.removeServiceOffering(0);\n    expect(component.serviceOfferings.length).toBe(servicesCount - 1);\n  });\n  it('should validate form before submission', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n    // Make form invalid\n    component.profileForm.get('firstName')?.setValue('');\n    spyOn(component, 'validateForm').and.returnValue(false);\n    component.onSubmit();\n    expect(component.validateForm).toHaveBeenCalled();\n    expect(mockProfileService.updateProfile).not.toHaveBeenCalled();\n  });\n  it('should submit valid form', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n    spyOn(component, 'validateForm').and.returnValue(true);\n    component.onSubmit();\n    expect(component.validateForm).toHaveBeenCalled();\n    expect(mockProfileService.updateProfile).toHaveBeenCalled();\n    expect(component.isSaving).toBeFalse();\n  });\n  it('should handle form submission error', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n    mockProfileService.updateProfile.and.returnValue(throwError(() => new Error('Update failed')));\n    spyOn(component, 'validateForm').and.returnValue(true);\n    component.onSubmit();\n    expect(component.isSaving).toBeFalse();\n  });\n  it('should validate image file correctly', () => {\n    const validFile = new File([''], 'test.jpg', {\n      type: 'image/jpeg'\n    });\n    Object.defineProperty(validFile, 'size', {\n      value: 1024 * 1024\n    }); // 1MB\n    const result = component['validateImageFile'](validFile, 'profile');\n    expect(result).toBe(true);\n  });\n  it('should reject invalid image file type', () => {\n    const invalidFile = new File([''], 'test.txt', {\n      type: 'text/plain'\n    });\n    Object.defineProperty(invalidFile, 'size', {\n      value: 1024\n    }); // 1KB\n    const result = component['validateImageFile'](invalidFile, 'profile');\n    expect(result).toBe(false);\n  });\n  it('should reject oversized image file', () => {\n    const oversizedFile = new File([''], 'test.jpg', {\n      type: 'image/jpeg'\n    });\n    Object.defineProperty(oversizedFile, 'size', {\n      value: 10 * 1024 * 1024\n    }); // 10MB\n    const result = component['validateImageFile'](oversizedFile, 'profile');\n    expect(result).toBe(false);\n  });\n  it('should handle profile photo upload', () => {\n    const file = new File([''], 'test.jpg', {\n      type: 'image/jpeg'\n    });\n    Object.defineProperty(file, 'size', {\n      value: 1024 * 1024\n    }); // 1MB\n    component.ngOnInit();\n    fixture.detectChanges();\n    spyOn(component, 'validateImageFile').and.returnValue(true);\n    spyOn(component, 'createImagePreview');\n    const event = {\n      target: {\n        files: [file],\n        value: ''\n      }\n    };\n    component.onProfilePhotoSelected(event);\n    expect(component['validateImageFile']).toHaveBeenCalledWith(file, 'profile');\n    expect(component['createImagePreview']).toHaveBeenCalledWith(file, 'profile');\n    expect(mockProfileService.uploadProfilePhoto).toHaveBeenCalledWith(file);\n  });\n  it('should handle cover photo upload', () => {\n    const file = new File([''], 'test.jpg', {\n      type: 'image/jpeg'\n    });\n    Object.defineProperty(file, 'size', {\n      value: 2 * 1024 * 1024\n    }); // 2MB\n    component.ngOnInit();\n    fixture.detectChanges();\n    spyOn(component, 'validateImageFile').and.returnValue(true);\n    spyOn(component, 'createImagePreview');\n    const event = {\n      target: {\n        files: [file],\n        value: ''\n      }\n    };\n    component.onCoverPhotoSelected(event);\n    expect(component['validateImageFile']).toHaveBeenCalledWith(file, 'cover');\n    expect(component['createImagePreview']).toHaveBeenCalledWith(file, 'cover');\n    expect(mockProfileService.uploadCoverPhoto).toHaveBeenCalledWith(file);\n  });\n  it('should provide skill category options', () => {\n    const options = component.getSkillCategoryOptions();\n    expect(options.length).toBeGreaterThan(0);\n    expect(options.some(opt => opt.value === 'Astrology')).toBe(true);\n    expect(options.some(opt => opt.value === 'Tarot Reading')).toBe(true);\n  });\n  it('should provide proficiency level options', () => {\n    const options = component.getProficiencyLevelOptions();\n    expect(options.length).toBe(4);\n    expect(options.some(opt => opt.value === 'beginner')).toBe(true);\n    expect(options.some(opt => opt.value === 'expert')).toBe(true);\n  });\n  it('should provide service category options', () => {\n    const options = component.getServiceCategoryOptions();\n    expect(options.length).toBeGreaterThan(0);\n    expect(options.some(opt => opt.value === 'Reading')).toBe(true);\n    expect(options.some(opt => opt.value === 'Consultation')).toBe(true);\n  });\n  it('should provide currency options', () => {\n    const options = component.getCurrencyOptions();\n    expect(options.length).toBeGreaterThan(0);\n    expect(options.some(opt => opt.value === 'BGN')).toBe(true);\n    expect(options.some(opt => opt.value === 'EUR')).toBe(true);\n  });\n  it('should handle loading error', () => {\n    mockProfileService.getCurrentUserProfile.and.returnValue(throwError(() => new Error('Load failed')));\n    component.ngOnInit();\n    expect(component.isLoading).toBeFalse();\n  });\n  afterEach(() => {\n    fixture.destroy();\n  });\n});", "map": {"version": 3, "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AAErC,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,WAAW,QAAQ,qCAAqC;AAGjEC,QAAQ,CAAC,sBAAsB,EAAE,MAAK;EACpC,IAAIC,SAA+B;EACnC,IAAIC,OAA+C;EACnD,IAAIC,kBAAkD;EACtD,IAAIC,eAA4C;EAEhD,MAAMC,WAAW,GAAgB;IAC/BC,EAAE,EAAE,CAAC;IACLC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,kBAAkB;IACzBC,eAAe,EAAE,+BAA+B;IAChDC,aAAa,EAAE,+BAA+B;IAC9CC,iBAAiB,EAAE,YAAY;IAC/BC,QAAQ,EAAE,yBAAyB;IACnCC,OAAO,EAAE,kDAAkD;IAC3DC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;MACRC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,UAAU;MACnBC,eAAe,EAAE;KAClB;IACDC,WAAW,EAAE;MACXZ,KAAK,EAAE,kBAAkB;MACzBa,aAAa,EAAE,IAAI;MACnBC,OAAO,EAAE,qBAAqB;MAC9BC,YAAY,EAAE,+BAA+B;MAC7CC,YAAY,EAAE,EAAE;MAChBC,eAAe,EAAE;QACfC,MAAM,EAAE,aAAa;QACrBV,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,OAAO;QACdU,UAAU,EAAE,MAAM;QAClBT,OAAO,EAAE,UAAU;QACnBJ,QAAQ,EAAE;;KAEb;IACDc,MAAM,EAAE,CACN;MACE1B,EAAE,EAAE,CAAC;MACL2B,IAAI,EAAE,eAAe;MACrBC,QAAQ,EAAE,WAAW;MACrBC,gBAAgB,EAAE,QAAQ;MAC1BC,YAAY,EAAE;KACf,CACF;IACDC,iBAAiB,EAAE;MACjBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE;KACX;IACDC,gBAAgB,EAAE,CAChB;MACEnC,EAAE,EAAE,CAAC;MACL2B,IAAI,EAAE,kBAAkB;MACxBS,WAAW,EAAE,0CAA0C;MACvDC,KAAK,EAAE,GAAG;MACVH,QAAQ,EAAE,KAAK;MACfI,QAAQ,EAAE,EAAE;MACZV,QAAQ,EAAE,SAAS;MACnBW,QAAQ,EAAE;KACX,CACF;IACDC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,CAAC;IACfC,oBAAoB,EAAE,EAAE;IACxBC,SAAS,EAAE,IAAIC,IAAI,EAAE;IACrBC,SAAS,EAAE,IAAID,IAAI;GACpB;EAEDE,UAAU,iCAAC,aAAW;IACpB,MAAMC,iBAAiB,GAAGC,OAAO,CAACC,YAAY,CAAC,gBAAgB,EAAE,CAC/D,uBAAuB,EACvB,eAAe,EACf,oBAAoB,EACpB,kBAAkB,CACnB,CAAC;IACF,MAAMC,cAAc,GAAGF,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAE9E,MAAM3E,OAAO,CAAC6E,sBAAsB,CAAC;MACnCC,YAAY,EAAE,CAAC7D,oBAAoB,CAAC;MACpC8D,OAAO,EAAE,CACP9E,mBAAmB,EACnBE,iBAAiB,EACjBC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,wBAAwB,EACxBC,mBAAmB,EACnBC,uBAAuB,EACvBC,uBAAuB,CACxB;MACDkE,SAAS,EAAE,CACT9E,WAAW,EACX;QAAE+E,OAAO,EAAE/D,cAAc;QAAEgE,QAAQ,EAAET;MAAiB,CAAE,EACxD;QAAEQ,OAAO,EAAE9D,WAAW;QAAE+D,QAAQ,EAAEN;MAAc,CAAE;KAErD,CAAC,CAACO,iBAAiB,EAAE;IAEtB7D,OAAO,GAAGtB,OAAO,CAACoF,eAAe,CAACnE,oBAAoB,CAAC;IACvDI,SAAS,GAAGC,OAAO,CAAC+D,iBAAiB;IACrC9D,kBAAkB,GAAGvB,OAAO,CAACsF,MAAM,CAACpE,cAAc,CAAmC;IACrFM,eAAe,GAAGxB,OAAO,CAACsF,MAAM,CAACnE,WAAW,CAAgC;IAE5E;IACAI,kBAAkB,CAACgE,qBAAqB,CAACC,GAAG,CAACC,WAAW,CAAC1E,EAAE,CAACU,WAAW,CAAC,CAAC;IACzEF,kBAAkB,CAACmE,aAAa,CAACF,GAAG,CAACC,WAAW,CAAC1E,EAAE,CAACU,WAAW,CAAC,CAAC;IACjEF,kBAAkB,CAACoE,kBAAkB,CAACH,GAAG,CAACC,WAAW,CAAC1E,EAAE,CAAC;MAAE6E,GAAG,EAAE;IAAmC,CAAE,CAAC,CAAC;IACvGrE,kBAAkB,CAACsE,gBAAgB,CAACL,GAAG,CAACC,WAAW,CAAC1E,EAAE,CAAC;MAAE6E,GAAG,EAAE;IAAmC,CAAE,CAAC,CAAC;EACvG,CAAC,EAAC;EAEFE,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAC1E,SAAS,CAAC,CAAC2E,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,6BAA6B,EAAE,MAAK;IACrCzE,SAAS,CAAC4E,QAAQ,EAAE;IACpBF,MAAM,CAACxE,kBAAkB,CAACgE,qBAAqB,CAAC,CAACW,gBAAgB,EAAE;IACnEH,MAAM,CAAC1E,SAAS,CAAC8E,OAAO,CAAC,CAACC,OAAO,CAAC3E,WAAW,CAAC;IAC9CsE,MAAM,CAAC1E,SAAS,CAACgF,SAAS,CAAC,CAACC,SAAS,EAAE;EACzC,CAAC,CAAC;EAEFR,EAAE,CAAC,wCAAwC,EAAE,MAAK;IAChDzE,SAAS,CAAC4E,QAAQ,EAAE;IACpB3E,OAAO,CAACiF,aAAa,EAAE;IAEvBR,MAAM,CAAC1E,SAAS,CAACmF,WAAW,CAACC,GAAG,CAAC,WAAW,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;IAClEZ,MAAM,CAAC1E,SAAS,CAACmF,WAAW,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;IACjEZ,MAAM,CAAC1E,SAAS,CAACmF,WAAW,CAACC,GAAG,CAAC,mBAAmB,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,YAAY,CAAC;IAChFZ,MAAM,CAAC1E,SAAS,CAACmF,WAAW,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,yBAAyB,CAAC;IACpFZ,MAAM,CAAC1E,SAAS,CAACmF,WAAW,CAACC,GAAG,CAAC,SAAS,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,kDAAkD,CAAC;IAC5GZ,MAAM,CAAC1E,SAAS,CAACmF,WAAW,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACjE,CAAC,CAAC;EAEFb,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5CzE,SAAS,CAAC4E,QAAQ,EAAE;IACpB3E,OAAO,CAACiF,aAAa,EAAE;IAEvB,MAAM9C,iBAAiB,GAAGpC,SAAS,CAACmF,WAAW,CAACC,GAAG,CAAC,mBAAmB,CAAC;IACxEV,MAAM,CAACtC,iBAAiB,EAAEgD,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;IAC5DZ,MAAM,CAACtC,iBAAiB,EAAEgD,GAAG,CAAC,aAAa,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;IAC7DZ,MAAM,CAACtC,iBAAiB,EAAEgD,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;EAC/D,CAAC,CAAC;EAEFb,EAAE,CAAC,8BAA8B,EAAE,MAAK;IACtCzE,SAAS,CAAC4E,QAAQ,EAAE;IACpB3E,OAAO,CAACiF,aAAa,EAAE;IAEvBR,MAAM,CAAC1E,SAAS,CAAC+B,MAAM,CAACwD,MAAM,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC;IACvC,MAAME,UAAU,GAAGxF,SAAS,CAAC+B,MAAM,CAAC0D,EAAE,CAAC,CAAC,CAAC;IACzCf,MAAM,CAACc,UAAU,EAAEJ,GAAG,CAAC,MAAM,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,eAAe,CAAC;IAC5DZ,MAAM,CAACc,UAAU,EAAEJ,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,WAAW,CAAC;IAC5DZ,MAAM,CAACc,UAAU,EAAEJ,GAAG,CAAC,kBAAkB,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;EACnE,CAAC,CAAC;EAEFb,EAAE,CAAC,yCAAyC,EAAE,MAAK;IACjDzE,SAAS,CAAC4E,QAAQ,EAAE;IACpB3E,OAAO,CAACiF,aAAa,EAAE;IAEvBR,MAAM,CAAC1E,SAAS,CAACwC,gBAAgB,CAAC+C,MAAM,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC;IACjD,MAAMI,YAAY,GAAG1F,SAAS,CAACwC,gBAAgB,CAACiD,EAAE,CAAC,CAAC,CAAC;IACrDf,MAAM,CAACgB,YAAY,EAAEN,GAAG,CAAC,MAAM,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,kBAAkB,CAAC;IACjEZ,MAAM,CAACgB,YAAY,EAAEN,GAAG,CAAC,aAAa,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,0CAA0C,CAAC;IAChGZ,MAAM,CAACgB,YAAY,EAAEN,GAAG,CAAC,OAAO,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACnDZ,MAAM,CAACgB,YAAY,EAAEN,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IACxDZ,MAAM,CAACgB,YAAY,EAAEN,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;IACrDZ,MAAM,CAACgB,YAAY,EAAEN,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,SAAS,CAAC;IAC5DZ,MAAM,CAACgB,YAAY,EAAEN,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACzD,CAAC,CAAC;EAEFb,EAAE,CAAC,sBAAsB,EAAE,MAAK;IAC9BzE,SAAS,CAAC4E,QAAQ,EAAE;IACpB3E,OAAO,CAACiF,aAAa,EAAE;IAEvB,MAAMS,kBAAkB,GAAG3F,SAAS,CAAC+B,MAAM,CAACwD,MAAM;IAClDvF,SAAS,CAAC4F,QAAQ,EAAE;IAEpBlB,MAAM,CAAC1E,SAAS,CAAC+B,MAAM,CAACwD,MAAM,CAAC,CAACD,IAAI,CAACK,kBAAkB,GAAG,CAAC,CAAC;IAC5D,MAAME,QAAQ,GAAG7F,SAAS,CAAC+B,MAAM,CAAC0D,EAAE,CAACzF,SAAS,CAAC+B,MAAM,CAACwD,MAAM,GAAG,CAAC,CAAC;IACjEb,MAAM,CAACmB,QAAQ,EAAET,GAAG,CAAC,MAAM,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;IAC7CZ,MAAM,CAACmB,QAAQ,EAAET,GAAG,CAAC,kBAAkB,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,cAAc,CAAC;EACvE,CAAC,CAAC;EAEFb,EAAE,CAAC,qBAAqB,EAAE,MAAK;IAC7BzE,SAAS,CAAC4E,QAAQ,EAAE;IACpB3E,OAAO,CAACiF,aAAa,EAAE;IAEvBlF,SAAS,CAAC4F,QAAQ,EAAE,CAAC,CAAC;IACtB,MAAME,WAAW,GAAG9F,SAAS,CAAC+B,MAAM,CAACwD,MAAM;IAE3CvF,SAAS,CAAC+F,WAAW,CAAC,CAAC,CAAC;IACxBrB,MAAM,CAAC1E,SAAS,CAAC+B,MAAM,CAACwD,MAAM,CAAC,CAACD,IAAI,CAACQ,WAAW,GAAG,CAAC,CAAC;EACvD,CAAC,CAAC;EAEFrB,EAAE,CAAC,iCAAiC,EAAE,MAAK;IACzCzE,SAAS,CAAC4E,QAAQ,EAAE;IACpB3E,OAAO,CAACiF,aAAa,EAAE;IAEvB,MAAMc,oBAAoB,GAAGhG,SAAS,CAACwC,gBAAgB,CAAC+C,MAAM;IAC9DvF,SAAS,CAACiG,kBAAkB,EAAE;IAE9BvB,MAAM,CAAC1E,SAAS,CAACwC,gBAAgB,CAAC+C,MAAM,CAAC,CAACD,IAAI,CAACU,oBAAoB,GAAG,CAAC,CAAC;IACxE,MAAME,UAAU,GAAGlG,SAAS,CAACwC,gBAAgB,CAACiD,EAAE,CAACzF,SAAS,CAACwC,gBAAgB,CAAC+C,MAAM,GAAG,CAAC,CAAC;IACvFb,MAAM,CAACwB,UAAU,EAAEd,GAAG,CAAC,MAAM,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;IAC/CZ,MAAM,CAACwB,UAAU,EAAEd,GAAG,CAAC,OAAO,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;IAC/CZ,MAAM,CAACwB,UAAU,EAAEd,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IACtDZ,MAAM,CAACwB,UAAU,EAAEd,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;IACnDZ,MAAM,CAACwB,UAAU,EAAEd,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACvD,CAAC,CAAC;EAEFb,EAAE,CAAC,gCAAgC,EAAE,MAAK;IACxCzE,SAAS,CAAC4E,QAAQ,EAAE;IACpB3E,OAAO,CAACiF,aAAa,EAAE;IAEvBlF,SAAS,CAACiG,kBAAkB,EAAE,CAAC,CAAC;IAChC,MAAME,aAAa,GAAGnG,SAAS,CAACwC,gBAAgB,CAAC+C,MAAM;IAEvDvF,SAAS,CAACoG,qBAAqB,CAAC,CAAC,CAAC;IAClC1B,MAAM,CAAC1E,SAAS,CAACwC,gBAAgB,CAAC+C,MAAM,CAAC,CAACD,IAAI,CAACa,aAAa,GAAG,CAAC,CAAC;EACnE,CAAC,CAAC;EAEF1B,EAAE,CAAC,wCAAwC,EAAE,MAAK;IAChDzE,SAAS,CAAC4E,QAAQ,EAAE;IACpB3E,OAAO,CAACiF,aAAa,EAAE;IAEvB;IACAlF,SAAS,CAACmF,WAAW,CAACC,GAAG,CAAC,WAAW,CAAC,EAAEiB,QAAQ,CAAC,EAAE,CAAC;IAEpDC,KAAK,CAACtG,SAAS,EAAE,cAAc,CAAC,CAACmE,GAAG,CAACC,WAAW,CAAC,KAAK,CAAC;IACvDpE,SAAS,CAACuG,QAAQ,EAAE;IAEpB7B,MAAM,CAAC1E,SAAS,CAACwG,YAAY,CAAC,CAAC3B,gBAAgB,EAAE;IACjDH,MAAM,CAACxE,kBAAkB,CAACmE,aAAa,CAAC,CAACoC,GAAG,CAAC5B,gBAAgB,EAAE;EACjE,CAAC,CAAC;EAEFJ,EAAE,CAAC,0BAA0B,EAAE,MAAK;IAClCzE,SAAS,CAAC4E,QAAQ,EAAE;IACpB3E,OAAO,CAACiF,aAAa,EAAE;IAEvBoB,KAAK,CAACtG,SAAS,EAAE,cAAc,CAAC,CAACmE,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;IACtDpE,SAAS,CAACuG,QAAQ,EAAE;IAEpB7B,MAAM,CAAC1E,SAAS,CAACwG,YAAY,CAAC,CAAC3B,gBAAgB,EAAE;IACjDH,MAAM,CAACxE,kBAAkB,CAACmE,aAAa,CAAC,CAACQ,gBAAgB,EAAE;IAC3DH,MAAM,CAAC1E,SAAS,CAAC0G,QAAQ,CAAC,CAACzB,SAAS,EAAE;EACxC,CAAC,CAAC;EAEFR,EAAE,CAAC,qCAAqC,EAAE,MAAK;IAC7CzE,SAAS,CAAC4E,QAAQ,EAAE;IACpB3E,OAAO,CAACiF,aAAa,EAAE;IAEvBhF,kBAAkB,CAACmE,aAAa,CAACF,GAAG,CAACC,WAAW,CAACzE,UAAU,CAAC,MAAM,IAAIgH,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;IAC9FL,KAAK,CAACtG,SAAS,EAAE,cAAc,CAAC,CAACmE,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;IAEtDpE,SAAS,CAACuG,QAAQ,EAAE;IAEpB7B,MAAM,CAAC1E,SAAS,CAAC0G,QAAQ,CAAC,CAACzB,SAAS,EAAE;EACxC,CAAC,CAAC;EAEFR,EAAE,CAAC,sCAAsC,EAAE,MAAK;IAC9C,MAAMmC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;MAAEC,IAAI,EAAE;IAAY,CAAE,CAAC;IACpEC,MAAM,CAACC,cAAc,CAACJ,SAAS,EAAE,MAAM,EAAE;MAAEvB,KAAK,EAAE,IAAI,GAAG;IAAI,CAAE,CAAC,CAAC,CAAC;IAElE,MAAM4B,MAAM,GAAGjH,SAAS,CAAC,mBAAmB,CAAC,CAAC4G,SAAS,EAAE,SAAS,CAAC;IACnElC,MAAM,CAACuC,MAAM,CAAC,CAAC3B,IAAI,CAAC,IAAI,CAAC;EAC3B,CAAC,CAAC;EAEFb,EAAE,CAAC,uCAAuC,EAAE,MAAK;IAC/C,MAAMyC,WAAW,GAAG,IAAIL,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;MAAEC,IAAI,EAAE;IAAY,CAAE,CAAC;IACtEC,MAAM,CAACC,cAAc,CAACE,WAAW,EAAE,MAAM,EAAE;MAAE7B,KAAK,EAAE;IAAI,CAAE,CAAC,CAAC,CAAC;IAE7D,MAAM4B,MAAM,GAAGjH,SAAS,CAAC,mBAAmB,CAAC,CAACkH,WAAW,EAAE,SAAS,CAAC;IACrExC,MAAM,CAACuC,MAAM,CAAC,CAAC3B,IAAI,CAAC,KAAK,CAAC;EAC5B,CAAC,CAAC;EAEFb,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5C,MAAM0C,aAAa,GAAG,IAAIN,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;MAAEC,IAAI,EAAE;IAAY,CAAE,CAAC;IACxEC,MAAM,CAACC,cAAc,CAACG,aAAa,EAAE,MAAM,EAAE;MAAE9B,KAAK,EAAE,EAAE,GAAG,IAAI,GAAG;IAAI,CAAE,CAAC,CAAC,CAAC;IAE3E,MAAM4B,MAAM,GAAGjH,SAAS,CAAC,mBAAmB,CAAC,CAACmH,aAAa,EAAE,SAAS,CAAC;IACvEzC,MAAM,CAACuC,MAAM,CAAC,CAAC3B,IAAI,CAAC,KAAK,CAAC;EAC5B,CAAC,CAAC;EAEFb,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5C,MAAM2C,IAAI,GAAG,IAAIP,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;MAAEC,IAAI,EAAE;IAAY,CAAE,CAAC;IAC/DC,MAAM,CAACC,cAAc,CAACI,IAAI,EAAE,MAAM,EAAE;MAAE/B,KAAK,EAAE,IAAI,GAAG;IAAI,CAAE,CAAC,CAAC,CAAC;IAE7DrF,SAAS,CAAC4E,QAAQ,EAAE;IACpB3E,OAAO,CAACiF,aAAa,EAAE;IAEvBoB,KAAK,CAACtG,SAAgB,EAAE,mBAAmB,CAAC,CAACmE,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;IAClEkC,KAAK,CAACtG,SAAgB,EAAE,oBAAoB,CAAC;IAE7C,MAAMqH,KAAK,GAAG;MAAEC,MAAM,EAAE;QAAEC,KAAK,EAAE,CAACH,IAAI,CAAC;QAAE/B,KAAK,EAAE;MAAE;IAAE,CAAE;IACtDrF,SAAS,CAACwH,sBAAsB,CAACH,KAAK,CAAC;IAEvC3C,MAAM,CAAC1E,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAACyH,oBAAoB,CAACL,IAAI,EAAE,SAAS,CAAC;IAC5E1C,MAAM,CAAC1E,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAACyH,oBAAoB,CAACL,IAAI,EAAE,SAAS,CAAC;IAC7E1C,MAAM,CAACxE,kBAAkB,CAACoE,kBAAkB,CAAC,CAACmD,oBAAoB,CAACL,IAAI,CAAC;EAC1E,CAAC,CAAC;EAEF3C,EAAE,CAAC,kCAAkC,EAAE,MAAK;IAC1C,MAAM2C,IAAI,GAAG,IAAIP,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;MAAEC,IAAI,EAAE;IAAY,CAAE,CAAC;IAC/DC,MAAM,CAACC,cAAc,CAACI,IAAI,EAAE,MAAM,EAAE;MAAE/B,KAAK,EAAE,CAAC,GAAG,IAAI,GAAG;IAAI,CAAE,CAAC,CAAC,CAAC;IAEjErF,SAAS,CAAC4E,QAAQ,EAAE;IACpB3E,OAAO,CAACiF,aAAa,EAAE;IAEvBoB,KAAK,CAACtG,SAAgB,EAAE,mBAAmB,CAAC,CAACmE,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;IAClEkC,KAAK,CAACtG,SAAgB,EAAE,oBAAoB,CAAC;IAE7C,MAAMqH,KAAK,GAAG;MAAEC,MAAM,EAAE;QAAEC,KAAK,EAAE,CAACH,IAAI,CAAC;QAAE/B,KAAK,EAAE;MAAE;IAAE,CAAE;IACtDrF,SAAS,CAAC0H,oBAAoB,CAACL,KAAK,CAAC;IAErC3C,MAAM,CAAC1E,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAACyH,oBAAoB,CAACL,IAAI,EAAE,OAAO,CAAC;IAC1E1C,MAAM,CAAC1E,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAACyH,oBAAoB,CAACL,IAAI,EAAE,OAAO,CAAC;IAC3E1C,MAAM,CAACxE,kBAAkB,CAACsE,gBAAgB,CAAC,CAACiD,oBAAoB,CAACL,IAAI,CAAC;EACxE,CAAC,CAAC;EAEF3C,EAAE,CAAC,uCAAuC,EAAE,MAAK;IAC/C,MAAMkD,OAAO,GAAG3H,SAAS,CAAC4H,uBAAuB,EAAE;IACnDlD,MAAM,CAACiD,OAAO,CAACpC,MAAM,CAAC,CAACsC,eAAe,CAAC,CAAC,CAAC;IACzCnD,MAAM,CAACiD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACjEZ,MAAM,CAACiD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,eAAe,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACvE,CAAC,CAAC;EAEFb,EAAE,CAAC,0CAA0C,EAAE,MAAK;IAClD,MAAMkD,OAAO,GAAG3H,SAAS,CAACgI,0BAA0B,EAAE;IACtDtD,MAAM,CAACiD,OAAO,CAACpC,MAAM,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC;IAC9BZ,MAAM,CAACiD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,UAAU,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IAChEZ,MAAM,CAACiD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EAChE,CAAC,CAAC;EAEFb,EAAE,CAAC,yCAAyC,EAAE,MAAK;IACjD,MAAMkD,OAAO,GAAG3H,SAAS,CAACiI,yBAAyB,EAAE;IACrDvD,MAAM,CAACiD,OAAO,CAACpC,MAAM,CAAC,CAACsC,eAAe,CAAC,CAAC,CAAC;IACzCnD,MAAM,CAACiD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,SAAS,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IAC/DZ,MAAM,CAACiD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,cAAc,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACtE,CAAC,CAAC;EAEFb,EAAE,CAAC,iCAAiC,EAAE,MAAK;IACzC,MAAMkD,OAAO,GAAG3H,SAAS,CAACkI,kBAAkB,EAAE;IAC9CxD,MAAM,CAACiD,OAAO,CAACpC,MAAM,CAAC,CAACsC,eAAe,CAAC,CAAC,CAAC;IACzCnD,MAAM,CAACiD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IAC3DZ,MAAM,CAACiD,OAAO,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EAC7D,CAAC,CAAC;EAEFb,EAAE,CAAC,6BAA6B,EAAE,MAAK;IACrCvE,kBAAkB,CAACgE,qBAAqB,CAACC,GAAG,CAACC,WAAW,CAACzE,UAAU,CAAC,MAAM,IAAIgH,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;IAEpG3G,SAAS,CAAC4E,QAAQ,EAAE;IAEpBF,MAAM,CAAC1E,SAAS,CAACgF,SAAS,CAAC,CAACC,SAAS,EAAE;EACzC,CAAC,CAAC;EAEFkD,SAAS,CAAC,MAAK;IACblI,OAAO,CAACmI,OAAO,EAAE;EACnB,CAAC,CAAC;AACJ,CAAC,CAAC", "names": ["TestBed", "ReactiveFormsModule", "FormBuilder", "MatSnackBarModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatSlideToggleModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "RouterTestingModule", "BrowserAnimationsModule", "HttpClientTestingModule", "of", "throwError", "ProfileEditComponent", "ProfileService", "AuthService", "describe", "component", "fixture", "mockProfileService", "mockAuthService", "mockProfile", "id", "userId", "username", "slug", "firstName", "lastName", "email", "profilePhotoUrl", "coverPhotoUrl", "professional<PERSON>itle", "headline", "summary", "isPublic", "location", "city", "state", "country", "displayLocation", "contactInfo", "isEmailPublic", "website", "portfolioUrl", "phoneNumbers", "businessAddress", "street", "postalCode", "skills", "name", "category", "proficiencyLevel", "endorsements", "consultationRates", "hourlyRate", "sessionRate", "currency", "serviceOfferings", "description", "price", "duration", "isActive", "socialLinks", "profileViews", "completionPercentage", "createdAt", "Date", "updatedAt", "beforeEach", "profileServiceSpy", "jasmine", "createSpyObj", "authServiceSpy", "configureTestingModule", "declarations", "imports", "providers", "provide", "useValue", "compileComponents", "createComponent", "componentInstance", "inject", "getCurrentUserProfile", "and", "returnValue", "updateProfile", "uploadProfilePhoto", "url", "uploadCoverPhoto", "it", "expect", "toBeTruthy", "ngOnInit", "toHaveBeenCalled", "profile", "toEqual", "isLoading", "toBeFalse", "detectChanges", "profileForm", "get", "value", "toBe", "length", "firstSkill", "at", "firstService", "initialSkillsCount", "addSkill", "newSkill", "skillsCount", "removeSkill", "initialServicesCount", "addServiceOffering", "newService", "servicesCount", "removeServiceOffering", "setValue", "spyOn", "onSubmit", "validateForm", "not", "isSaving", "Error", "validFile", "File", "type", "Object", "defineProperty", "result", "invalidFile", "oversizedFile", "file", "event", "target", "files", "onProfilePhotoSelected", "toHaveBeenCalledWith", "onCoverPhotoSelected", "options", "getSkillCategoryOptions", "toBeGreaterThan", "some", "opt", "getProficiencyLevelOptions", "getServiceCategoryOptions", "getCurrencyOptions", "after<PERSON>ach", "destroy"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\profile-edit\\profile-edit.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { ReactiveFormsModule, FormBuilder } from '@angular/forms';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { of, throwError } from 'rxjs';\n\nimport { ProfileEditComponent } from './profile-edit.component';\nimport { ProfileService } from '../../services/profile.service';\nimport { AuthService } from '../../../auth/services/auth.service';\nimport { UserProfile, ProfileSkill, ConsultationRates, ServiceOffering } from '../../models/profile.models';\n\ndescribe('ProfileEditComponent', () => {\n  let component: ProfileEditComponent;\n  let fixture: ComponentFixture<ProfileEditComponent>;\n  let mockProfileService: jasmine.SpyObj<ProfileService>;\n  let mockAuthService: jasmine.SpyObj<AuthService>;\n\n  const mockProfile: UserProfile = {\n    id: 1,\n    userId: 1,\n    username: 'testuser',\n    slug: 'testuser',\n    firstName: 'Test',\n    lastName: 'User',\n    email: '<EMAIL>',\n    profilePhotoUrl: 'https://example.com/photo.jpg',\n    coverPhotoUrl: 'https://example.com/cover.jpg',\n    professionalTitle: 'Astrologer',\n    headline: 'Professional Astrologer',\n    summary: 'Experienced astrologer with 10 years of practice',\n    isPublic: true,\n    location: {\n      city: 'Sofia',\n      state: 'Sofia',\n      country: 'Bulgaria',\n      displayLocation: 'Sofia, Bulgaria'\n    },\n    contactInfo: {\n      email: '<EMAIL>',\n      isEmailPublic: true,\n      website: 'https://example.com',\n      portfolioUrl: 'https://portfolio.example.com',\n      phoneNumbers: [],\n      businessAddress: {\n        street: '123 Test St',\n        city: 'Sofia',\n        state: 'Sofia',\n        postalCode: '1000',\n        country: 'Bulgaria',\n        isPublic: true\n      }\n    },\n    skills: [\n      {\n        id: 1,\n        name: 'Tarot Reading',\n        category: 'Astrology',\n        proficiencyLevel: 'expert',\n        endorsements: 5\n      }\n    ],\n    consultationRates: {\n      hourlyRate: 50,\n      sessionRate: 80,\n      currency: 'BGN'\n    },\n    serviceOfferings: [\n      {\n        id: 1,\n        name: 'Personal Reading',\n        description: 'Comprehensive personal astrology reading',\n        price: 100,\n        currency: 'BGN',\n        duration: 60,\n        category: 'Reading',\n        isActive: true\n      }\n    ],\n    socialLinks: [],\n    profileViews: 0,\n    completionPercentage: 85,\n    createdAt: new Date(),\n    updatedAt: new Date()\n  };\n\n  beforeEach(async () => {\n    const profileServiceSpy = jasmine.createSpyObj('ProfileService', [\n      'getCurrentUserProfile',\n      'updateProfile',\n      'uploadProfilePhoto',\n      'uploadCoverPhoto'\n    ]);\n    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n\n    await TestBed.configureTestingModule({\n      declarations: [ProfileEditComponent],\n      imports: [\n        ReactiveFormsModule,\n        MatSnackBarModule,\n        MatCardModule,\n        MatFormFieldModule,\n        MatInputModule,\n        MatSelectModule,\n        MatSlideToggleModule,\n        MatButtonModule,\n        MatIconModule,\n        MatProgressSpinnerModule,\n        RouterTestingModule,\n        BrowserAnimationsModule,\n        HttpClientTestingModule\n      ],\n      providers: [\n        FormBuilder,\n        { provide: ProfileService, useValue: profileServiceSpy },\n        { provide: AuthService, useValue: authServiceSpy }\n      ]\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(ProfileEditComponent);\n    component = fixture.componentInstance;\n    mockProfileService = TestBed.inject(ProfileService) as jasmine.SpyObj<ProfileService>;\n    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;\n\n    // Setup default mock responses\n    mockProfileService.getCurrentUserProfile.and.returnValue(of(mockProfile));\n    mockProfileService.updateProfile.and.returnValue(of(mockProfile));\n    mockProfileService.uploadProfilePhoto.and.returnValue(of({ url: 'https://example.com/new-photo.jpg' }));\n    mockProfileService.uploadCoverPhoto.and.returnValue(of({ url: 'https://example.com/new-cover.jpg' }));\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should load profile on init', () => {\n    component.ngOnInit();\n    expect(mockProfileService.getCurrentUserProfile).toHaveBeenCalled();\n    expect(component.profile).toEqual(mockProfile);\n    expect(component.isLoading).toBeFalse();\n  });\n\n  it('should populate form with profile data', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n\n    expect(component.profileForm.get('firstName')?.value).toBe('Test');\n    expect(component.profileForm.get('lastName')?.value).toBe('User');\n    expect(component.profileForm.get('professionalTitle')?.value).toBe('Astrologer');\n    expect(component.profileForm.get('headline')?.value).toBe('Professional Astrologer');\n    expect(component.profileForm.get('summary')?.value).toBe('Experienced astrologer with 10 years of practice');\n    expect(component.profileForm.get('isPublic')?.value).toBe(true);\n  });\n\n  it('should populate consultation rates', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n\n    const consultationRates = component.profileForm.get('consultationRates');\n    expect(consultationRates?.get('hourlyRate')?.value).toBe(50);\n    expect(consultationRates?.get('sessionRate')?.value).toBe(80);\n    expect(consultationRates?.get('currency')?.value).toBe('BGN');\n  });\n\n  it('should populate skills array', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n\n    expect(component.skills.length).toBe(1);\n    const firstSkill = component.skills.at(0);\n    expect(firstSkill?.get('name')?.value).toBe('Tarot Reading');\n    expect(firstSkill?.get('category')?.value).toBe('Astrology');\n    expect(firstSkill?.get('proficiencyLevel')?.value).toBe('expert');\n  });\n\n  it('should populate service offerings array', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n\n    expect(component.serviceOfferings.length).toBe(1);\n    const firstService = component.serviceOfferings.at(0);\n    expect(firstService?.get('name')?.value).toBe('Personal Reading');\n    expect(firstService?.get('description')?.value).toBe('Comprehensive personal astrology reading');\n    expect(firstService?.get('price')?.value).toBe(100);\n    expect(firstService?.get('currency')?.value).toBe('BGN');\n    expect(firstService?.get('duration')?.value).toBe(60);\n    expect(firstService?.get('category')?.value).toBe('Reading');\n    expect(firstService?.get('isActive')?.value).toBe(true);\n  });\n\n  it('should add new skill', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n\n    const initialSkillsCount = component.skills.length;\n    component.addSkill();\n\n    expect(component.skills.length).toBe(initialSkillsCount + 1);\n    const newSkill = component.skills.at(component.skills.length - 1);\n    expect(newSkill?.get('name')?.value).toBe('');\n    expect(newSkill?.get('proficiencyLevel')?.value).toBe('intermediate');\n  });\n\n  it('should remove skill', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n\n    component.addSkill(); // Add a skill first\n    const skillsCount = component.skills.length;\n    \n    component.removeSkill(0);\n    expect(component.skills.length).toBe(skillsCount - 1);\n  });\n\n  it('should add new service offering', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n\n    const initialServicesCount = component.serviceOfferings.length;\n    component.addServiceOffering();\n\n    expect(component.serviceOfferings.length).toBe(initialServicesCount + 1);\n    const newService = component.serviceOfferings.at(component.serviceOfferings.length - 1);\n    expect(newService?.get('name')?.value).toBe('');\n    expect(newService?.get('price')?.value).toBe(0);\n    expect(newService?.get('currency')?.value).toBe('BGN');\n    expect(newService?.get('duration')?.value).toBe(60);\n    expect(newService?.get('isActive')?.value).toBe(true);\n  });\n\n  it('should remove service offering', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n\n    component.addServiceOffering(); // Add a service first\n    const servicesCount = component.serviceOfferings.length;\n    \n    component.removeServiceOffering(0);\n    expect(component.serviceOfferings.length).toBe(servicesCount - 1);\n  });\n\n  it('should validate form before submission', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n\n    // Make form invalid\n    component.profileForm.get('firstName')?.setValue('');\n    \n    spyOn(component, 'validateForm').and.returnValue(false);\n    component.onSubmit();\n\n    expect(component.validateForm).toHaveBeenCalled();\n    expect(mockProfileService.updateProfile).not.toHaveBeenCalled();\n  });\n\n  it('should submit valid form', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n\n    spyOn(component, 'validateForm').and.returnValue(true);\n    component.onSubmit();\n\n    expect(component.validateForm).toHaveBeenCalled();\n    expect(mockProfileService.updateProfile).toHaveBeenCalled();\n    expect(component.isSaving).toBeFalse();\n  });\n\n  it('should handle form submission error', () => {\n    component.ngOnInit();\n    fixture.detectChanges();\n\n    mockProfileService.updateProfile.and.returnValue(throwError(() => new Error('Update failed')));\n    spyOn(component, 'validateForm').and.returnValue(true);\n    \n    component.onSubmit();\n\n    expect(component.isSaving).toBeFalse();\n  });\n\n  it('should validate image file correctly', () => {\n    const validFile = new File([''], 'test.jpg', { type: 'image/jpeg' });\n    Object.defineProperty(validFile, 'size', { value: 1024 * 1024 }); // 1MB\n\n    const result = component['validateImageFile'](validFile, 'profile');\n    expect(result).toBe(true);\n  });\n\n  it('should reject invalid image file type', () => {\n    const invalidFile = new File([''], 'test.txt', { type: 'text/plain' });\n    Object.defineProperty(invalidFile, 'size', { value: 1024 }); // 1KB\n\n    const result = component['validateImageFile'](invalidFile, 'profile');\n    expect(result).toBe(false);\n  });\n\n  it('should reject oversized image file', () => {\n    const oversizedFile = new File([''], 'test.jpg', { type: 'image/jpeg' });\n    Object.defineProperty(oversizedFile, 'size', { value: 10 * 1024 * 1024 }); // 10MB\n\n    const result = component['validateImageFile'](oversizedFile, 'profile');\n    expect(result).toBe(false);\n  });\n\n  it('should handle profile photo upload', () => {\n    const file = new File([''], 'test.jpg', { type: 'image/jpeg' });\n    Object.defineProperty(file, 'size', { value: 1024 * 1024 }); // 1MB\n\n    component.ngOnInit();\n    fixture.detectChanges();\n\n    spyOn(component as any, 'validateImageFile').and.returnValue(true);\n    spyOn(component as any, 'createImagePreview');\n\n    const event = { target: { files: [file], value: '' } };\n    component.onProfilePhotoSelected(event);\n\n    expect(component['validateImageFile']).toHaveBeenCalledWith(file, 'profile');\n    expect(component['createImagePreview']).toHaveBeenCalledWith(file, 'profile');\n    expect(mockProfileService.uploadProfilePhoto).toHaveBeenCalledWith(file);\n  });\n\n  it('should handle cover photo upload', () => {\n    const file = new File([''], 'test.jpg', { type: 'image/jpeg' });\n    Object.defineProperty(file, 'size', { value: 2 * 1024 * 1024 }); // 2MB\n\n    component.ngOnInit();\n    fixture.detectChanges();\n\n    spyOn(component as any, 'validateImageFile').and.returnValue(true);\n    spyOn(component as any, 'createImagePreview');\n\n    const event = { target: { files: [file], value: '' } };\n    component.onCoverPhotoSelected(event);\n\n    expect(component['validateImageFile']).toHaveBeenCalledWith(file, 'cover');\n    expect(component['createImagePreview']).toHaveBeenCalledWith(file, 'cover');\n    expect(mockProfileService.uploadCoverPhoto).toHaveBeenCalledWith(file);\n  });\n\n  it('should provide skill category options', () => {\n    const options = component.getSkillCategoryOptions();\n    expect(options.length).toBeGreaterThan(0);\n    expect(options.some(opt => opt.value === 'Astrology')).toBe(true);\n    expect(options.some(opt => opt.value === 'Tarot Reading')).toBe(true);\n  });\n\n  it('should provide proficiency level options', () => {\n    const options = component.getProficiencyLevelOptions();\n    expect(options.length).toBe(4);\n    expect(options.some(opt => opt.value === 'beginner')).toBe(true);\n    expect(options.some(opt => opt.value === 'expert')).toBe(true);\n  });\n\n  it('should provide service category options', () => {\n    const options = component.getServiceCategoryOptions();\n    expect(options.length).toBeGreaterThan(0);\n    expect(options.some(opt => opt.value === 'Reading')).toBe(true);\n    expect(options.some(opt => opt.value === 'Consultation')).toBe(true);\n  });\n\n  it('should provide currency options', () => {\n    const options = component.getCurrencyOptions();\n    expect(options.length).toBeGreaterThan(0);\n    expect(options.some(opt => opt.value === 'BGN')).toBe(true);\n    expect(options.some(opt => opt.value === 'EUR')).toBe(true);\n  });\n\n  it('should handle loading error', () => {\n    mockProfileService.getCurrentUserProfile.and.returnValue(throwError(() => new Error('Load failed')));\n    \n    component.ngOnInit();\n    \n    expect(component.isLoading).toBeFalse();\n  });\n\n  afterEach(() => {\n    fixture.destroy();\n  });\n});\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}