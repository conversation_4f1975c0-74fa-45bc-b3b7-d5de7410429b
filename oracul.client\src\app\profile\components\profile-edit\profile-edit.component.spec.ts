import { TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';

import { ProfileEditComponent } from './profile-edit.component';
import { ProfileService } from '../../services/profile.service';
import { AuthService } from '../../../auth/services/auth.service';
import { UserProfile, ProfileSkill, ConsultationRates, ServiceOffering } from '../../models/profile.models';

describe('ProfileEditComponent', () => {
  let component: ProfileEditComponent;
  let mockProfileService: jasmine.SpyObj<ProfileService>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockSnackBar: jasmine.SpyObj<MatSnackBar>;
  let mockRouter: jasmine.SpyObj<Router>;
  let formBuilder: FormBuilder;

  const mockProfile: UserProfile = {
    id: 1,
    userId: 1,
    username: 'testuser',
    slug: 'testuser',
    firstName: 'Test',
    lastName: 'User',
    profilePhotoUrl: 'https://example.com/photo.jpg',
    coverPhotoUrl: 'https://example.com/cover.jpg',
    professionalTitle: 'Astrologer',
    headline: 'Professional Astrologer',
    summary: 'Experienced astrologer with 10 years of practice',
    isPublic: true,
    profileCompletionPercentage: 85,
    location: {
      city: 'Sofia',
      state: 'Sofia',
      country: 'Bulgaria',
      displayLocation: 'Sofia, Bulgaria'
    },
    contactInfo: {
      email: '<EMAIL>',
      isEmailPublic: true,
      website: 'https://example.com',
      portfolioUrl: 'https://portfolio.example.com',
      phoneNumbers: [],
      businessAddress: {
        street: '123 Test St',
        city: 'Sofia',
        state: 'Sofia',
        postalCode: '1000',
        country: 'Bulgaria',
        isPublic: true
      }
    },
    skills: [
      {
        id: 1,
        name: 'Tarot Reading',
        category: 'Astrology',
        proficiencyLevel: 'expert',
        endorsements: 5
      }
    ],
    blogPosts: [],
    achievements: [],
    certifications: [],
    experiences: [],
    portfolioItems: [],
    consultationRates: {
      hourlyRate: 50,
      sessionRate: 80,
      currency: 'BGN'
    },
    serviceOfferings: [
      {
        id: 1,
        name: 'Personal Reading',
        description: 'Comprehensive personal astrology reading',
        price: 100,
        currency: 'BGN',
        duration: 60,
        category: 'Reading',
        isActive: true
      }
    ],
    socialLinks: [],
    profileViews: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(() => {
    const profileServiceSpy = jasmine.createSpyObj('ProfileService', [
      'getCurrentUserProfile',
      'updateProfile',
      'uploadProfilePhoto',
      'uploadCoverPhoto'
    ]);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);
    const snackBarSpy = jasmine.createSpyObj('MatSnackBar', ['open']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      providers: [
        FormBuilder,
        { provide: ProfileService, useValue: profileServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: MatSnackBar, useValue: snackBarSpy },
        { provide: Router, useValue: routerSpy }
      ]
    });

    formBuilder = TestBed.inject(FormBuilder);
    mockProfileService = TestBed.inject(ProfileService) as jasmine.SpyObj<ProfileService>;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockSnackBar = TestBed.inject(MatSnackBar) as jasmine.SpyObj<MatSnackBar>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    // Create component manually
    component = new ProfileEditComponent(
      formBuilder,
      mockProfileService,
      mockAuthService,
      mockRouter,
      mockSnackBar
    );

    // Setup default mock responses
    mockProfileService.getCurrentUserProfile.and.returnValue(of(mockProfile));
    mockProfileService.updateProfile.and.returnValue(of(mockProfile));
    mockProfileService.uploadProfilePhoto.and.returnValue(of({ url: 'https://example.com/new-photo.jpg' }));
    mockProfileService.uploadCoverPhoto.and.returnValue(of({ url: 'https://example.com/new-cover.jpg' }));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load profile on init', () => {
    component.ngOnInit();
    expect(mockProfileService.getCurrentUserProfile).toHaveBeenCalled();
    expect(component.profile).toEqual(mockProfile);
    expect(component.isLoading).toBeFalse();
  });

  it('should populate form with profile data', () => {
    component.ngOnInit();

    expect(component.profileForm.get('firstName')?.value).toBe('Test');
    expect(component.profileForm.get('lastName')?.value).toBe('User');
    expect(component.profileForm.get('professionalTitle')?.value).toBe('Astrologer');
    expect(component.profileForm.get('headline')?.value).toBe('Professional Astrologer');
    expect(component.profileForm.get('summary')?.value).toBe('Experienced astrologer with 10 years of practice');
    expect(component.profileForm.get('isPublic')?.value).toBe(true);
  });

  it('should populate consultation rates', () => {
    component.ngOnInit();

    const consultationRates = component.profileForm.get('consultationRates');
    expect(consultationRates?.get('hourlyRate')?.value).toBe(50);
    expect(consultationRates?.get('sessionRate')?.value).toBe(80);
    expect(consultationRates?.get('currency')?.value).toBe('BGN');
  });

  it('should populate skills array', () => {
    component.ngOnInit();

    expect(component.skills.length).toBe(1);
    const firstSkill = component.skills.at(0);
    expect(firstSkill?.get('name')?.value).toBe('Tarot Reading');
    expect(firstSkill?.get('category')?.value).toBe('Astrology');
    expect(firstSkill?.get('proficiencyLevel')?.value).toBe('expert');
  });

  it('should populate service offerings array', () => {
    component.ngOnInit();

    expect(component.serviceOfferings.length).toBe(1);
    const firstService = component.serviceOfferings.at(0);
    expect(firstService?.get('name')?.value).toBe('Personal Reading');
    expect(firstService?.get('description')?.value).toBe('Comprehensive personal astrology reading');
    expect(firstService?.get('price')?.value).toBe(100);
    expect(firstService?.get('currency')?.value).toBe('BGN');
    expect(firstService?.get('duration')?.value).toBe(60);
    expect(firstService?.get('category')?.value).toBe('Reading');
    expect(firstService?.get('isActive')?.value).toBe(true);
  });

  it('should add new skill', () => {
    component.ngOnInit();

    const initialSkillsCount = component.skills.length;
    component.addSkill();

    expect(component.skills.length).toBe(initialSkillsCount + 1);
    const newSkill = component.skills.at(component.skills.length - 1);
    expect(newSkill?.get('name')?.value).toBe('');
    expect(newSkill?.get('proficiencyLevel')?.value).toBe('intermediate');
  });

  it('should remove skill', () => {
    component.ngOnInit();

    component.addSkill(); // Add a skill first
    const skillsCount = component.skills.length;

    component.removeSkill(0);
    expect(component.skills.length).toBe(skillsCount - 1);
  });

  it('should add new service offering', () => {
    component.ngOnInit();

    const initialServicesCount = component.serviceOfferings.length;
    component.addServiceOffering();

    expect(component.serviceOfferings.length).toBe(initialServicesCount + 1);
    const newService = component.serviceOfferings.at(component.serviceOfferings.length - 1);
    expect(newService?.get('name')?.value).toBe('');
    expect(newService?.get('price')?.value).toBe(0);
    expect(newService?.get('currency')?.value).toBe('BGN');
    expect(newService?.get('duration')?.value).toBe(60);
    expect(newService?.get('isActive')?.value).toBe(true);
  });

  it('should remove service offering', () => {
    component.ngOnInit();

    component.addServiceOffering(); // Add a service first
    const servicesCount = component.serviceOfferings.length;

    component.removeServiceOffering(0);
    expect(component.serviceOfferings.length).toBe(servicesCount - 1);
  });

  it('should validate form before submission', () => {
    component.ngOnInit();

    // Make form invalid
    component.profileForm.get('firstName')?.setValue('');

    spyOn(component, 'validateForm').and.returnValue(false);
    component.onSubmit();

    expect(component.validateForm).toHaveBeenCalled();
    expect(mockProfileService.updateProfile).not.toHaveBeenCalled();
  });

  it('should submit valid form', () => {
    component.ngOnInit();

    spyOn(component, 'validateForm').and.returnValue(true);
    component.onSubmit();

    expect(component.validateForm).toHaveBeenCalled();
    expect(mockProfileService.updateProfile).toHaveBeenCalled();
    expect(component.isSaving).toBeFalse();
  });

  it('should handle form submission error', () => {
    component.ngOnInit();

    mockProfileService.updateProfile.and.returnValue(throwError(() => new Error('Update failed')));
    spyOn(component, 'validateForm').and.returnValue(true);

    component.onSubmit();

    expect(component.isSaving).toBeFalse();
  });

  it('should validate image file correctly', () => {
    const validFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
    Object.defineProperty(validFile, 'size', { value: 1024 * 1024 }); // 1MB

    const result = component['validateImageFile'](validFile, 'profile');
    expect(result).toBe(true);
  });

  it('should reject invalid image file type', () => {
    const invalidFile = new File([''], 'test.txt', { type: 'text/plain' });
    Object.defineProperty(invalidFile, 'size', { value: 1024 }); // 1KB

    const result = component['validateImageFile'](invalidFile, 'profile');
    expect(result).toBe(false);
  });

  it('should reject oversized image file', () => {
    const oversizedFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
    Object.defineProperty(oversizedFile, 'size', { value: 10 * 1024 * 1024 }); // 10MB

    const result = component['validateImageFile'](oversizedFile, 'profile');
    expect(result).toBe(false);
  });

  it('should handle profile photo upload', () => {
    const file = new File([''], 'test.jpg', { type: 'image/jpeg' });
    Object.defineProperty(file, 'size', { value: 1024 * 1024 }); // 1MB

    component.ngOnInit();

    spyOn(component as any, 'validateImageFile').and.returnValue(true);
    spyOn(component as any, 'createImagePreview');

    const event = { target: { files: [file], value: '' } };
    component.onProfilePhotoSelected(event);

    expect(component['validateImageFile']).toHaveBeenCalledWith(file, 'profile');
    expect(component['createImagePreview']).toHaveBeenCalledWith(file, 'profile');
    expect(mockProfileService.uploadProfilePhoto).toHaveBeenCalledWith(file);
  });

  it('should handle cover photo upload', () => {
    const file = new File([''], 'test.jpg', { type: 'image/jpeg' });
    Object.defineProperty(file, 'size', { value: 2 * 1024 * 1024 }); // 2MB

    component.ngOnInit();

    spyOn(component as any, 'validateImageFile').and.returnValue(true);
    spyOn(component as any, 'createImagePreview');

    const event = { target: { files: [file], value: '' } };
    component.onCoverPhotoSelected(event);

    expect(component['validateImageFile']).toHaveBeenCalledWith(file, 'cover');
    expect(component['createImagePreview']).toHaveBeenCalledWith(file, 'cover');
    expect(mockProfileService.uploadCoverPhoto).toHaveBeenCalledWith(file);
  });

  it('should provide skill category options', () => {
    const options = component.getSkillCategoryOptions();
    expect(options.length).toBeGreaterThan(0);
    expect(options.some(opt => opt.value === 'Astrology')).toBe(true);
    expect(options.some(opt => opt.value === 'Tarot Reading')).toBe(true);
  });

  it('should provide proficiency level options', () => {
    const options = component.getProficiencyLevelOptions();
    expect(options.length).toBe(4);
    expect(options.some(opt => opt.value === 'beginner')).toBe(true);
    expect(options.some(opt => opt.value === 'expert')).toBe(true);
  });

  it('should provide service category options', () => {
    const options = component.getServiceCategoryOptions();
    expect(options.length).toBeGreaterThan(0);
    expect(options.some(opt => opt.value === 'Reading')).toBe(true);
    expect(options.some(opt => opt.value === 'Consultation')).toBe(true);
  });

  it('should provide currency options', () => {
    const options = component.getCurrencyOptions();
    expect(options.length).toBeGreaterThan(0);
    expect(options.some(opt => opt.value === 'BGN')).toBe(true);
    expect(options.some(opt => opt.value === 'EUR')).toBe(true);
  });

  it('should handle loading error', () => {
    mockProfileService.getCurrentUserProfile.and.returnValue(throwError(() => new Error('Load failed')));

    component.ngOnInit();

    expect(component.isLoading).toBeFalse();
  });

  // Backend Integration Tests
  describe('Backend Integration Verification', () => {
    it('should include skills, consultation rates, and service offerings in update request', () => {
      component.ngOnInit();

      // Setup form with all data including new fields
      component.profileForm.patchValue({
        firstName: 'John',
        lastName: 'Doe',
        professionalTitle: 'Senior Astrologer',
        consultationRates: {
          hourlyRate: 75,
          sessionRate: 120,
          currency: 'BGN'
        }
      });

      // Add skills
      component.addSkill();
      const skillsArray = component.skills;
      skillsArray.at(skillsArray.length - 1)?.patchValue({
        name: 'Crystal Healing',
        category: 'Energy Work',
        proficiencyLevel: 'advanced'
      });

      // Add service offerings
      component.addServiceOffering();
      const servicesArray = component.serviceOfferings;
      servicesArray.at(servicesArray.length - 1)?.patchValue({
        name: 'Relationship Reading',
        description: 'Detailed relationship compatibility analysis',
        price: 150,
        currency: 'BGN',
        duration: 90,
        category: 'Consultation',
        isActive: true
      });

      // Mock successful update
      mockProfileService.updateProfile.and.returnValue(of(mockProfile));

      // Submit form
      spyOn(component, 'validateForm').and.returnValue(true);
      component.onSubmit();

      // Verify the update request includes all new fields
      expect(mockProfileService.updateProfile).toHaveBeenCalled();
      const updateCall = mockProfileService.updateProfile.calls.mostRecent();
      const updateRequest = updateCall.args[0];

      // Verify skills are included
      expect(updateRequest.skills).toBeDefined();
      expect(updateRequest.skills?.length).toBeGreaterThan(0);
      expect(updateRequest.skills?.some((skill: any) => skill.name === 'Crystal Healing')).toBe(true);

      // Verify consultation rates are included
      expect(updateRequest.consultationRates).toBeDefined();
      expect(updateRequest.consultationRates?.hourlyRate).toBe(75);
      expect(updateRequest.consultationRates?.sessionRate).toBe(120);
      expect(updateRequest.consultationRates?.currency).toBe('BGN');

      // Verify service offerings are included
      expect(updateRequest.serviceOfferings).toBeDefined();
      expect(updateRequest.serviceOfferings?.length).toBeGreaterThan(0);
      expect(updateRequest.serviceOfferings?.some((service: any) => service.name === 'Relationship Reading')).toBe(true);
    });

    it('should verify that backend UpdateProfileRequest model needs to be updated', () => {
      // This test documents the current backend limitation
      // The backend UpdateProfileRequest model in ProfileModels.cs is missing:
      // - skills: List<ProfileSkillDto>
      // - consultationRates: ConsultationRatesDto
      // - serviceOfferings: List<ServiceOfferingDto>

      // The frontend is sending these fields but the backend cannot receive them
      // This test serves as documentation of the integration gap

      expect(true).toBe(true); // Placeholder - this documents the issue
    });
  });


});
