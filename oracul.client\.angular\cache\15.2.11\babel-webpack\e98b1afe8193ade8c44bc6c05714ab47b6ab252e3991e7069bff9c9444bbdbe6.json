{"ast": null, "code": "function cov_24biu7evmw() {\n  var path = \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\profile\\\\components\\\\profile-edit\\\\profile-edit.component.ts\";\n  var hash = \"bea53bca159f823ab6c2aea6066c6571b4cd9fc8\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\profile\\\\components\\\\profile-edit\\\\profile-edit.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 11,\n          column: 27\n        },\n        end: {\n          line: 593,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 13,\n          column: 8\n        },\n        end: {\n          line: 13,\n          column: 39\n        }\n      },\n      \"2\": {\n        start: {\n          line: 14,\n          column: 8\n        },\n        end: {\n          line: 14,\n          column: 45\n        }\n      },\n      \"3\": {\n        start: {\n          line: 15,\n          column: 8\n        },\n        end: {\n          line: 15,\n          column: 39\n        }\n      },\n      \"4\": {\n        start: {\n          line: 16,\n          column: 8\n        },\n        end: {\n          line: 16,\n          column: 29\n        }\n      },\n      \"5\": {\n        start: {\n          line: 17,\n          column: 8\n        },\n        end: {\n          line: 17,\n          column: 33\n        }\n      },\n      \"6\": {\n        start: {\n          line: 18,\n          column: 8\n        },\n        end: {\n          line: 18,\n          column: 28\n        }\n      },\n      \"7\": {\n        start: {\n          line: 19,\n          column: 8\n        },\n        end: {\n          line: 19,\n          column: 30\n        }\n      },\n      \"8\": {\n        start: {\n          line: 20,\n          column: 8\n        },\n        end: {\n          line: 20,\n          column: 30\n        }\n      },\n      \"9\": {\n        start: {\n          line: 21,\n          column: 8\n        },\n        end: {\n          line: 21,\n          column: 45\n        }\n      },\n      \"10\": {\n        start: {\n          line: 22,\n          column: 8\n        },\n        end: {\n          line: 22,\n          column: 43\n        }\n      },\n      \"11\": {\n        start: {\n          line: 23,\n          column: 8\n        },\n        end: {\n          line: 23,\n          column: 40\n        }\n      },\n      \"12\": {\n        start: {\n          line: 24,\n          column: 8\n        },\n        end: {\n          line: 24,\n          column: 38\n        }\n      },\n      \"13\": {\n        start: {\n          line: 25,\n          column: 8\n        },\n        end: {\n          line: 25,\n          column: 38\n        }\n      },\n      \"14\": {\n        start: {\n          line: 26,\n          column: 8\n        },\n        end: {\n          line: 26,\n          column: 45\n        }\n      },\n      \"15\": {\n        start: {\n          line: 29,\n          column: 8\n        },\n        end: {\n          line: 29,\n          column: 27\n        }\n      },\n      \"16\": {\n        start: {\n          line: 32,\n          column: 8\n        },\n        end: {\n          line: 32,\n          column: 29\n        }\n      },\n      \"17\": {\n        start: {\n          line: 33,\n          column: 8\n        },\n        end: {\n          line: 33,\n          column: 33\n        }\n      },\n      \"18\": {\n        start: {\n          line: 36,\n          column: 8\n        },\n        end: {\n          line: 80,\n          column: 11\n        }\n      },\n      \"19\": {\n        start: {\n          line: 83,\n          column: 8\n        },\n        end: {\n          line: 97,\n          column: 11\n        }\n      },\n      \"20\": {\n        start: {\n          line: 87,\n          column: 16\n        },\n        end: {\n          line: 87,\n          column: 39\n        }\n      },\n      \"21\": {\n        start: {\n          line: 88,\n          column: 16\n        },\n        end: {\n          line: 88,\n          column: 43\n        }\n      },\n      \"22\": {\n        start: {\n          line: 89,\n          column: 16\n        },\n        end: {\n          line: 89,\n          column: 39\n        }\n      },\n      \"23\": {\n        start: {\n          line: 92,\n          column: 16\n        },\n        end: {\n          line: 92,\n          column: 63\n        }\n      },\n      \"24\": {\n        start: {\n          line: 93,\n          column: 16\n        },\n        end: {\n          line: 93,\n          column: 89\n        }\n      },\n      \"25\": {\n        start: {\n          line: 94,\n          column: 16\n        },\n        end: {\n          line: 94,\n          column: 53\n        }\n      },\n      \"26\": {\n        start: {\n          line: 95,\n          column: 16\n        },\n        end: {\n          line: 95,\n          column: 39\n        }\n      },\n      \"27\": {\n        start: {\n          line: 100,\n          column: 8\n        },\n        end: {\n          line: 132,\n          column: 11\n        }\n      },\n      \"28\": {\n        start: {\n          line: 134,\n          column: 8\n        },\n        end: {\n          line: 134,\n          column: 69\n        }\n      },\n      \"29\": {\n        start: {\n          line: 136,\n          column: 8\n        },\n        end: {\n          line: 136,\n          column: 55\n        }\n      },\n      \"30\": {\n        start: {\n          line: 138,\n          column: 8\n        },\n        end: {\n          line: 138,\n          column: 45\n        }\n      },\n      \"31\": {\n        start: {\n          line: 140,\n          column: 8\n        },\n        end: {\n          line: 140,\n          column: 65\n        }\n      },\n      \"32\": {\n        start: {\n          line: 144,\n          column: 8\n        },\n        end: {\n          line: 144,\n          column: 64\n        }\n      },\n      \"33\": {\n        start: {\n          line: 147,\n          column: 27\n        },\n        end: {\n          line: 147,\n          column: 44\n        }\n      },\n      \"34\": {\n        start: {\n          line: 148,\n          column: 8\n        },\n        end: {\n          line: 148,\n          column: 27\n        }\n      },\n      \"35\": {\n        start: {\n          line: 149,\n          column: 8\n        },\n        end: {\n          line: 157,\n          column: 11\n        }\n      },\n      \"36\": {\n        start: {\n          line: 150,\n          column: 12\n        },\n        end: {\n          line: 156,\n          column: 16\n        }\n      },\n      \"37\": {\n        start: {\n          line: 160,\n          column: 27\n        },\n        end: {\n          line: 166,\n          column: 10\n        }\n      },\n      \"38\": {\n        start: {\n          line: 167,\n          column: 8\n        },\n        end: {\n          line: 167,\n          column: 43\n        }\n      },\n      \"39\": {\n        start: {\n          line: 170,\n          column: 8\n        },\n        end: {\n          line: 170,\n          column: 42\n        }\n      },\n      \"40\": {\n        start: {\n          line: 174,\n          column: 8\n        },\n        end: {\n          line: 174,\n          column: 51\n        }\n      },\n      \"41\": {\n        start: {\n          line: 177,\n          column: 27\n        },\n        end: {\n          line: 177,\n          column: 43\n        }\n      },\n      \"42\": {\n        start: {\n          line: 178,\n          column: 8\n        },\n        end: {\n          line: 178,\n          column: 27\n        }\n      },\n      \"43\": {\n        start: {\n          line: 179,\n          column: 8\n        },\n        end: {\n          line: 187,\n          column: 11\n        }\n      },\n      \"44\": {\n        start: {\n          line: 180,\n          column: 12\n        },\n        end: {\n          line: 186,\n          column: 16\n        }\n      },\n      \"45\": {\n        start: {\n          line: 190,\n          column: 26\n        },\n        end: {\n          line: 196,\n          column: 10\n        }\n      },\n      \"46\": {\n        start: {\n          line: 197,\n          column: 8\n        },\n        end: {\n          line: 197,\n          column: 41\n        }\n      },\n      \"47\": {\n        start: {\n          line: 200,\n          column: 8\n        },\n        end: {\n          line: 200,\n          column: 41\n        }\n      },\n      \"48\": {\n        start: {\n          line: 204,\n          column: 8\n        },\n        end: {\n          line: 204,\n          column: 46\n        }\n      },\n      \"49\": {\n        start: {\n          line: 207,\n          column: 27\n        },\n        end: {\n          line: 207,\n          column: 38\n        }\n      },\n      \"50\": {\n        start: {\n          line: 208,\n          column: 8\n        },\n        end: {\n          line: 208,\n          column: 27\n        }\n      },\n      \"51\": {\n        start: {\n          line: 209,\n          column: 8\n        },\n        end: {\n          line: 216,\n          column: 11\n        }\n      },\n      \"52\": {\n        start: {\n          line: 210,\n          column: 12\n        },\n        end: {\n          line: 215,\n          column: 16\n        }\n      },\n      \"53\": {\n        start: {\n          line: 219,\n          column: 27\n        },\n        end: {\n          line: 224,\n          column: 10\n        }\n      },\n      \"54\": {\n        start: {\n          line: 225,\n          column: 8\n        },\n        end: {\n          line: 225,\n          column: 37\n        }\n      },\n      \"55\": {\n        start: {\n          line: 228,\n          column: 8\n        },\n        end: {\n          line: 228,\n          column: 36\n        }\n      },\n      \"56\": {\n        start: {\n          line: 232,\n          column: 8\n        },\n        end: {\n          line: 232,\n          column: 56\n        }\n      },\n      \"57\": {\n        start: {\n          line: 235,\n          column: 29\n        },\n        end: {\n          line: 235,\n          column: 50\n        }\n      },\n      \"58\": {\n        start: {\n          line: 236,\n          column: 8\n        },\n        end: {\n          line: 236,\n          column: 29\n        }\n      },\n      \"59\": {\n        start: {\n          line: 237,\n          column: 8\n        },\n        end: {\n          line: 248,\n          column: 11\n        }\n      },\n      \"60\": {\n        start: {\n          line: 238,\n          column: 12\n        },\n        end: {\n          line: 247,\n          column: 16\n        }\n      },\n      \"61\": {\n        start: {\n          line: 251,\n          column: 29\n        },\n        end: {\n          line: 260,\n          column: 10\n        }\n      },\n      \"62\": {\n        start: {\n          line: 261,\n          column: 8\n        },\n        end: {\n          line: 261,\n          column: 49\n        }\n      },\n      \"63\": {\n        start: {\n          line: 264,\n          column: 8\n        },\n        end: {\n          line: 264,\n          column: 46\n        }\n      },\n      \"64\": {\n        start: {\n          line: 268,\n          column: 8\n        },\n        end: {\n          line: 298,\n          column: 9\n        }\n      },\n      \"65\": {\n        start: {\n          line: 269,\n          column: 12\n        },\n        end: {\n          line: 269,\n          column: 33\n        }\n      },\n      \"66\": {\n        start: {\n          line: 270,\n          column: 30\n        },\n        end: {\n          line: 270,\n          column: 52\n        }\n      },\n      \"67\": {\n        start: {\n          line: 271,\n          column: 34\n        },\n        end: {\n          line: 283,\n          column: 13\n        }\n      },\n      \"68\": {\n        start: {\n          line: 284,\n          column: 12\n        },\n        end: {\n          line: 297,\n          column: 15\n        }\n      },\n      \"69\": {\n        start: {\n          line: 288,\n          column: 20\n        },\n        end: {\n          line: 288,\n          column: 42\n        }\n      },\n      \"70\": {\n        start: {\n          line: 289,\n          column: 20\n        },\n        end: {\n          line: 289,\n          column: 101\n        }\n      },\n      \"71\": {\n        start: {\n          line: 290,\n          column: 20\n        },\n        end: {\n          line: 290,\n          column: 76\n        }\n      },\n      \"72\": {\n        start: {\n          line: 293,\n          column: 20\n        },\n        end: {\n          line: 293,\n          column: 42\n        }\n      },\n      \"73\": {\n        start: {\n          line: 294,\n          column: 20\n        },\n        end: {\n          line: 294,\n          column: 68\n        }\n      },\n      \"74\": {\n        start: {\n          line: 295,\n          column: 20\n        },\n        end: {\n          line: 295,\n          column: 113\n        }\n      },\n      \"75\": {\n        start: {\n          line: 302,\n          column: 8\n        },\n        end: {\n          line: 307,\n          column: 9\n        }\n      },\n      \"76\": {\n        start: {\n          line: 303,\n          column: 12\n        },\n        end: {\n          line: 303,\n          column: 66\n        }\n      },\n      \"77\": {\n        start: {\n          line: 306,\n          column: 12\n        },\n        end: {\n          line: 306,\n          column: 49\n        }\n      },\n      \"78\": {\n        start: {\n          line: 311,\n          column: 21\n        },\n        end: {\n          line: 311,\n          column: 42\n        }\n      },\n      \"79\": {\n        start: {\n          line: 312,\n          column: 8\n        },\n        end: {\n          line: 317,\n          column: 9\n        }\n      },\n      \"80\": {\n        start: {\n          line: 313,\n          column: 12\n        },\n        end: {\n          line: 316,\n          column: 13\n        }\n      },\n      \"81\": {\n        start: {\n          line: 314,\n          column: 16\n        },\n        end: {\n          line: 314,\n          column: 57\n        }\n      },\n      \"82\": {\n        start: {\n          line: 315,\n          column: 16\n        },\n        end: {\n          line: 315,\n          column: 46\n        }\n      },\n      \"83\": {\n        start: {\n          line: 319,\n          column: 8\n        },\n        end: {\n          line: 319,\n          column: 32\n        }\n      },\n      \"84\": {\n        start: {\n          line: 322,\n          column: 21\n        },\n        end: {\n          line: 322,\n          column: 42\n        }\n      },\n      \"85\": {\n        start: {\n          line: 323,\n          column: 8\n        },\n        end: {\n          line: 328,\n          column: 9\n        }\n      },\n      \"86\": {\n        start: {\n          line: 324,\n          column: 12\n        },\n        end: {\n          line: 327,\n          column: 13\n        }\n      },\n      \"87\": {\n        start: {\n          line: 325,\n          column: 16\n        },\n        end: {\n          line: 325,\n          column: 55\n        }\n      },\n      \"88\": {\n        start: {\n          line: 326,\n          column: 16\n        },\n        end: {\n          line: 326,\n          column: 44\n        }\n      },\n      \"89\": {\n        start: {\n          line: 330,\n          column: 8\n        },\n        end: {\n          line: 330,\n          column: 32\n        }\n      },\n      \"90\": {\n        start: {\n          line: 334,\n          column: 29\n        },\n        end: {\n          line: 334,\n          column: 96\n        }\n      },\n      \"91\": {\n        start: {\n          line: 335,\n          column: 8\n        },\n        end: {\n          line: 338,\n          column: 9\n        }\n      },\n      \"92\": {\n        start: {\n          line: 336,\n          column: 12\n        },\n        end: {\n          line: 336,\n          column: 122\n        }\n      },\n      \"93\": {\n        start: {\n          line: 337,\n          column: 12\n        },\n        end: {\n          line: 337,\n          column: 25\n        }\n      },\n      \"94\": {\n        start: {\n          line: 340,\n          column: 24\n        },\n        end: {\n          line: 340,\n          column: 79\n        }\n      },\n      \"95\": {\n        start: {\n          line: 341,\n          column: 8\n        },\n        end: {\n          line: 345,\n          column: 9\n        }\n      },\n      \"96\": {\n        start: {\n          line: 342,\n          column: 30\n        },\n        end: {\n          line: 342,\n          column: 53\n        }\n      },\n      \"97\": {\n        start: {\n          line: 343,\n          column: 12\n        },\n        end: {\n          line: 343,\n          column: 106\n        }\n      },\n      \"98\": {\n        start: {\n          line: 344,\n          column: 12\n        },\n        end: {\n          line: 344,\n          column: 25\n        }\n      },\n      \"99\": {\n        start: {\n          line: 346,\n          column: 8\n        },\n        end: {\n          line: 346,\n          column: 20\n        }\n      },\n      \"100\": {\n        start: {\n          line: 349,\n          column: 23\n        },\n        end: {\n          line: 349,\n          column: 39\n        }\n      },\n      \"101\": {\n        start: {\n          line: 350,\n          column: 8\n        },\n        end: {\n          line: 357,\n          column: 10\n        }\n      },\n      \"102\": {\n        start: {\n          line: 351,\n          column: 12\n        },\n        end: {\n          line: 356,\n          column: 13\n        }\n      },\n      \"103\": {\n        start: {\n          line: 352,\n          column: 16\n        },\n        end: {\n          line: 352,\n          column: 59\n        }\n      },\n      \"104\": {\n        start: {\n          line: 355,\n          column: 16\n        },\n        end: {\n          line: 355,\n          column: 57\n        }\n      },\n      \"105\": {\n        start: {\n          line: 358,\n          column: 8\n        },\n        end: {\n          line: 358,\n          column: 35\n        }\n      },\n      \"106\": {\n        start: {\n          line: 361,\n          column: 8\n        },\n        end: {\n          line: 361,\n          column: 44\n        }\n      },\n      \"107\": {\n        start: {\n          line: 362,\n          column: 8\n        },\n        end: {\n          line: 380,\n          column: 11\n        }\n      },\n      \"108\": {\n        start: {\n          line: 366,\n          column: 16\n        },\n        end: {\n          line: 366,\n          column: 53\n        }\n      },\n      \"109\": {\n        start: {\n          line: 367,\n          column: 16\n        },\n        end: {\n          line: 369,\n          column: 17\n        }\n      },\n      \"110\": {\n        start: {\n          line: 368,\n          column: 20\n        },\n        end: {\n          line: 368,\n          column: 64\n        }\n      },\n      \"111\": {\n        start: {\n          line: 370,\n          column: 16\n        },\n        end: {\n          line: 370,\n          column: 48\n        }\n      },\n      \"112\": {\n        start: {\n          line: 371,\n          column: 16\n        },\n        end: {\n          line: 371,\n          column: 103\n        }\n      },\n      \"113\": {\n        start: {\n          line: 374,\n          column: 16\n        },\n        end: {\n          line: 374,\n          column: 53\n        }\n      },\n      \"114\": {\n        start: {\n          line: 375,\n          column: 16\n        },\n        end: {\n          line: 375,\n          column: 48\n        }\n      },\n      \"115\": {\n        start: {\n          line: 376,\n          column: 16\n        },\n        end: {\n          line: 376,\n          column: 71\n        }\n      },\n      \"116\": {\n        start: {\n          line: 377,\n          column: 37\n        },\n        end: {\n          line: 377,\n          column: 111\n        }\n      },\n      \"117\": {\n        start: {\n          line: 378,\n          column: 16\n        },\n        end: {\n          line: 378,\n          column: 78\n        }\n      },\n      \"118\": {\n        start: {\n          line: 383,\n          column: 8\n        },\n        end: {\n          line: 383,\n          column: 42\n        }\n      },\n      \"119\": {\n        start: {\n          line: 384,\n          column: 8\n        },\n        end: {\n          line: 402,\n          column: 11\n        }\n      },\n      \"120\": {\n        start: {\n          line: 388,\n          column: 16\n        },\n        end: {\n          line: 388,\n          column: 51\n        }\n      },\n      \"121\": {\n        start: {\n          line: 389,\n          column: 16\n        },\n        end: {\n          line: 391,\n          column: 17\n        }\n      },\n      \"122\": {\n        start: {\n          line: 390,\n          column: 20\n        },\n        end: {\n          line: 390,\n          column: 62\n        }\n      },\n      \"123\": {\n        start: {\n          line: 392,\n          column: 16\n        },\n        end: {\n          line: 392,\n          column: 46\n        }\n      },\n      \"124\": {\n        start: {\n          line: 393,\n          column: 16\n        },\n        end: {\n          line: 393,\n          column: 101\n        }\n      },\n      \"125\": {\n        start: {\n          line: 396,\n          column: 16\n        },\n        end: {\n          line: 396,\n          column: 51\n        }\n      },\n      \"126\": {\n        start: {\n          line: 397,\n          column: 16\n        },\n        end: {\n          line: 397,\n          column: 46\n        }\n      },\n      \"127\": {\n        start: {\n          line: 398,\n          column: 16\n        },\n        end: {\n          line: 398,\n          column: 69\n        }\n      },\n      \"128\": {\n        start: {\n          line: 399,\n          column: 37\n        },\n        end: {\n          line: 399,\n          column: 109\n        }\n      },\n      \"129\": {\n        start: {\n          line: 400,\n          column: 16\n        },\n        end: {\n          line: 400,\n          column: 78\n        }\n      },\n      \"130\": {\n        start: {\n          line: 406,\n          column: 8\n        },\n        end: {\n          line: 418,\n          column: 11\n        }\n      },\n      \"131\": {\n        start: {\n          line: 407,\n          column: 28\n        },\n        end: {\n          line: 407,\n          column: 53\n        }\n      },\n      \"132\": {\n        start: {\n          line: 408,\n          column: 12\n        },\n        end: {\n          line: 408,\n          column: 37\n        }\n      },\n      \"133\": {\n        start: {\n          line: 409,\n          column: 12\n        },\n        end: {\n          line: 417,\n          column: 13\n        }\n      },\n      \"134\": {\n        start: {\n          line: 410,\n          column: 16\n        },\n        end: {\n          line: 416,\n          column: 19\n        }\n      },\n      \"135\": {\n        start: {\n          line: 411,\n          column: 20\n        },\n        end: {\n          line: 415,\n          column: 21\n        }\n      },\n      \"136\": {\n        start: {\n          line: 412,\n          column: 24\n        },\n        end: {\n          line: 414,\n          column: 27\n        }\n      },\n      \"137\": {\n        start: {\n          line: 413,\n          column: 28\n        },\n        end: {\n          line: 413,\n          column: 72\n        }\n      },\n      \"138\": {\n        start: {\n          line: 421,\n          column: 24\n        },\n        end: {\n          line: 421,\n          column: 55\n        }\n      },\n      \"139\": {\n        start: {\n          line: 422,\n          column: 8\n        },\n        end: {\n          line: 423,\n          column: 22\n        }\n      },\n      \"140\": {\n        start: {\n          line: 423,\n          column: 12\n        },\n        end: {\n          line: 423,\n          column: 22\n        }\n      },\n      \"141\": {\n        start: {\n          line: 424,\n          column: 33\n        },\n        end: {\n          line: 424,\n          column: 68\n        }\n      },\n      \"142\": {\n        start: {\n          line: 425,\n          column: 8\n        },\n        end: {\n          line: 427,\n          column: 9\n        }\n      },\n      \"143\": {\n        start: {\n          line: 426,\n          column: 12\n        },\n        end: {\n          line: 426,\n          column: 53\n        }\n      },\n      \"144\": {\n        start: {\n          line: 428,\n          column: 8\n        },\n        end: {\n          line: 430,\n          column: 9\n        }\n      },\n      \"145\": {\n        start: {\n          line: 429,\n          column: 12\n        },\n        end: {\n          line: 429,\n          column: 56\n        }\n      },\n      \"146\": {\n        start: {\n          line: 431,\n          column: 8\n        },\n        end: {\n          line: 434,\n          column: 9\n        }\n      },\n      \"147\": {\n        start: {\n          line: 432,\n          column: 35\n        },\n        end: {\n          line: 432,\n          column: 77\n        }\n      },\n      \"148\": {\n        start: {\n          line: 433,\n          column: 12\n        },\n        end: {\n          line: 433,\n          column: 87\n        }\n      },\n      \"149\": {\n        start: {\n          line: 435,\n          column: 8\n        },\n        end: {\n          line: 438,\n          column: 9\n        }\n      },\n      \"150\": {\n        start: {\n          line: 436,\n          column: 30\n        },\n        end: {\n          line: 436,\n          column: 72\n        }\n      },\n      \"151\": {\n        start: {\n          line: 437,\n          column: 12\n        },\n        end: {\n          line: 437,\n          column: 86\n        }\n      },\n      \"152\": {\n        start: {\n          line: 439,\n          column: 8\n        },\n        end: {\n          line: 441,\n          column: 9\n        }\n      },\n      \"153\": {\n        start: {\n          line: 440,\n          column: 12\n        },\n        end: {\n          line: 440,\n          column: 46\n        }\n      },\n      \"154\": {\n        start: {\n          line: 442,\n          column: 8\n        },\n        end: {\n          line: 445,\n          column: 9\n        }\n      },\n      \"155\": {\n        start: {\n          line: 443,\n          column: 29\n        },\n        end: {\n          line: 443,\n          column: 54\n        }\n      },\n      \"156\": {\n        start: {\n          line: 444,\n          column: 12\n        },\n        end: {\n          line: 444,\n          column: 70\n        }\n      },\n      \"157\": {\n        start: {\n          line: 446,\n          column: 8\n        },\n        end: {\n          line: 449,\n          column: 9\n        }\n      },\n      \"158\": {\n        start: {\n          line: 447,\n          column: 29\n        },\n        end: {\n          line: 447,\n          column: 54\n        }\n      },\n      \"159\": {\n        start: {\n          line: 448,\n          column: 12\n        },\n        end: {\n          line: 448,\n          column: 67\n        }\n      },\n      \"160\": {\n        start: {\n          line: 450,\n          column: 8\n        },\n        end: {\n          line: 450,\n          column: 18\n        }\n      },\n      \"161\": {\n        start: {\n          line: 453,\n          column: 27\n        },\n        end: {\n          line: 469,\n          column: 9\n        }\n      },\n      \"162\": {\n        start: {\n          line: 470,\n          column: 8\n        },\n        end: {\n          line: 470,\n          column: 50\n        }\n      },\n      \"163\": {\n        start: {\n          line: 474,\n          column: 8\n        },\n        end: {\n          line: 487,\n          column: 9\n        }\n      },\n      \"164\": {\n        start: {\n          line: 475,\n          column: 12\n        },\n        end: {\n          line: 475,\n          column: 40\n        }\n      },\n      \"165\": {\n        start: {\n          line: 477,\n          column: 38\n        },\n        end: {\n          line: 477,\n          column: 66\n        }\n      },\n      \"166\": {\n        start: {\n          line: 478,\n          column: 12\n        },\n        end: {\n          line: 480,\n          column: 13\n        }\n      },\n      \"167\": {\n        start: {\n          line: 479,\n          column: 16\n        },\n        end: {\n          line: 479,\n          column: 42\n        }\n      },\n      \"168\": {\n        start: {\n          line: 482,\n          column: 27\n        },\n        end: {\n          line: 482,\n          column: 47\n        }\n      },\n      \"169\": {\n        start: {\n          line: 483,\n          column: 12\n        },\n        end: {\n          line: 485,\n          column: 13\n        }\n      },\n      \"170\": {\n        start: {\n          line: 484,\n          column: 16\n        },\n        end: {\n          line: 484,\n          column: 121\n        }\n      },\n      \"171\": {\n        start: {\n          line: 486,\n          column: 12\n        },\n        end: {\n          line: 486,\n          column: 25\n        }\n      },\n      \"172\": {\n        start: {\n          line: 488,\n          column: 8\n        },\n        end: {\n          line: 488,\n          column: 20\n        }\n      },\n      \"173\": {\n        start: {\n          line: 491,\n          column: 30\n        },\n        end: {\n          line: 491,\n          column: 167\n        }\n      },\n      \"174\": {\n        start: {\n          line: 492,\n          column: 8\n        },\n        end: {\n          line: 492,\n          column: 66\n        }\n      },\n      \"175\": {\n        start: {\n          line: 495,\n          column: 23\n        },\n        end: {\n          line: 495,\n          column: 25\n        }\n      },\n      \"176\": {\n        start: {\n          line: 497,\n          column: 8\n        },\n        end: {\n          line: 505,\n          column: 11\n        }\n      },\n      \"177\": {\n        start: {\n          line: 498,\n          column: 28\n        },\n        end: {\n          line: 498,\n          column: 53\n        }\n      },\n      \"178\": {\n        start: {\n          line: 499,\n          column: 12\n        },\n        end: {\n          line: 504,\n          column: 13\n        }\n      },\n      \"179\": {\n        start: {\n          line: 500,\n          column: 37\n        },\n        end: {\n          line: 500,\n          column: 62\n        }\n      },\n      \"180\": {\n        start: {\n          line: 501,\n          column: 16\n        },\n        end: {\n          line: 503,\n          column: 17\n        }\n      },\n      \"181\": {\n        start: {\n          line: 502,\n          column: 20\n        },\n        end: {\n          line: 502,\n          column: 46\n        }\n      },\n      \"182\": {\n        start: {\n          line: 507,\n          column: 28\n        },\n        end: {\n          line: 507,\n          column: 63\n        }\n      },\n      \"183\": {\n        start: {\n          line: 508,\n          column: 8\n        },\n        end: {\n          line: 518,\n          column: 9\n        }\n      },\n      \"184\": {\n        start: {\n          line: 509,\n          column: 12\n        },\n        end: {\n          line: 517,\n          column: 15\n        }\n      },\n      \"185\": {\n        start: {\n          line: 510,\n          column: 32\n        },\n        end: {\n          line: 510,\n          column: 52\n        }\n      },\n      \"186\": {\n        start: {\n          line: 511,\n          column: 16\n        },\n        end: {\n          line: 516,\n          column: 17\n        }\n      },\n      \"187\": {\n        start: {\n          line: 512,\n          column: 41\n        },\n        end: {\n          line: 512,\n          column: 83\n        }\n      },\n      \"188\": {\n        start: {\n          line: 513,\n          column: 20\n        },\n        end: {\n          line: 515,\n          column: 21\n        }\n      },\n      \"189\": {\n        start: {\n          line: 514,\n          column: 24\n        },\n        end: {\n          line: 514,\n          column: 50\n        }\n      },\n      \"190\": {\n        start: {\n          line: 519,\n          column: 8\n        },\n        end: {\n          line: 519,\n          column: 34\n        }\n      },\n      \"191\": {\n        start: {\n          line: 523,\n          column: 8\n        },\n        end: {\n          line: 533,\n          column: 10\n        }\n      },\n      \"192\": {\n        start: {\n          line: 537,\n          column: 8\n        },\n        end: {\n          line: 541,\n          column: 10\n        }\n      },\n      \"193\": {\n        start: {\n          line: 545,\n          column: 8\n        },\n        end: {\n          line: 555,\n          column: 10\n        }\n      },\n      \"194\": {\n        start: {\n          line: 559,\n          column: 8\n        },\n        end: {\n          line: 564,\n          column: 10\n        }\n      },\n      \"195\": {\n        start: {\n          line: 568,\n          column: 8\n        },\n        end: {\n          line: 575,\n          column: 10\n        }\n      },\n      \"196\": {\n        start: {\n          line: 579,\n          column: 8\n        },\n        end: {\n          line: 584,\n          column: 10\n        }\n      },\n      \"197\": {\n        start: {\n          line: 586,\n          column: 13\n        },\n        end: {\n          line: 592,\n          column: 6\n        }\n      },\n      \"198\": {\n        start: {\n          line: 586,\n          column: 41\n        },\n        end: {\n          line: 592,\n          column: 5\n        }\n      },\n      \"199\": {\n        start: {\n          line: 594,\n          column: 0\n        },\n        end: {\n          line: 600,\n          column: 25\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 12,\n            column: 4\n          },\n          end: {\n            line: 12,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 12,\n            column: 76\n          },\n          end: {\n            line: 27,\n            column: 5\n          }\n        },\n        line: 12\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 28,\n            column: 4\n          },\n          end: {\n            line: 28,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 28,\n            column: 15\n          },\n          end: {\n            line: 30,\n            column: 5\n          }\n        },\n        line: 28\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 31,\n            column: 4\n          },\n          end: {\n            line: 31,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 31,\n            column: 18\n          },\n          end: {\n            line: 34,\n            column: 5\n          }\n        },\n        line: 31\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 35,\n            column: 4\n          },\n          end: {\n            line: 35,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 35,\n            column: 17\n          },\n          end: {\n            line: 81,\n            column: 5\n          }\n        },\n        line: 35\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 82,\n            column: 4\n          },\n          end: {\n            line: 82,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 82,\n            column: 18\n          },\n          end: {\n            line: 98,\n            column: 5\n          }\n        },\n        line: 82\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 86,\n            column: 18\n          },\n          end: {\n            line: 86,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 86,\n            column: 31\n          },\n          end: {\n            line: 90,\n            column: 13\n          }\n        },\n        line: 86\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 91,\n            column: 19\n          },\n          end: {\n            line: 91,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 91,\n            column: 30\n          },\n          end: {\n            line: 96,\n            column: 13\n          }\n        },\n        line: 91\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 99,\n            column: 4\n          },\n          end: {\n            line: 99,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 99,\n            column: 26\n          },\n          end: {\n            line: 141,\n            column: 5\n          }\n        },\n        line: 99\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 143,\n            column: 4\n          },\n          end: {\n            line: 143,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 143,\n            column: 23\n          },\n          end: {\n            line: 145,\n            column: 5\n          }\n        },\n        line: 143\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 146,\n            column: 4\n          },\n          end: {\n            line: 146,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 146,\n            column: 28\n          },\n          end: {\n            line: 158,\n            column: 5\n          }\n        },\n        line: 146\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 149,\n            column: 23\n          },\n          end: {\n            line: 149,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 149,\n            column: 32\n          },\n          end: {\n            line: 157,\n            column: 9\n          }\n        },\n        line: 149\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 159,\n            column: 4\n          },\n          end: {\n            line: 159,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 159,\n            column: 21\n          },\n          end: {\n            line: 168,\n            column: 5\n          }\n        },\n        line: 159\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 169,\n            column: 4\n          },\n          end: {\n            line: 169,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 169,\n            column: 29\n          },\n          end: {\n            line: 171,\n            column: 5\n          }\n        },\n        line: 169\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 173,\n            column: 4\n          },\n          end: {\n            line: 173,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 173,\n            column: 22\n          },\n          end: {\n            line: 175,\n            column: 5\n          }\n        },\n        line: 173\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 176,\n            column: 4\n          },\n          end: {\n            line: 176,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 176,\n            column: 26\n          },\n          end: {\n            line: 188,\n            column: 5\n          }\n        },\n        line: 176\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 179,\n            column: 22\n          },\n          end: {\n            line: 179,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 179,\n            column: 30\n          },\n          end: {\n            line: 187,\n            column: 9\n          }\n        },\n        line: 179\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 189,\n            column: 4\n          },\n          end: {\n            line: 189,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 189,\n            column: 20\n          },\n          end: {\n            line: 198,\n            column: 5\n          }\n        },\n        line: 189\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 199,\n            column: 4\n          },\n          end: {\n            line: 199,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 199,\n            column: 28\n          },\n          end: {\n            line: 201,\n            column: 5\n          }\n        },\n        line: 199\n      },\n      \"18\": {\n        name: \"(anonymous_18)\",\n        decl: {\n          start: {\n            line: 203,\n            column: 4\n          },\n          end: {\n            line: 203,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 203,\n            column: 17\n          },\n          end: {\n            line: 205,\n            column: 5\n          }\n        },\n        line: 203\n      },\n      \"19\": {\n        name: \"(anonymous_19)\",\n        decl: {\n          start: {\n            line: 206,\n            column: 4\n          },\n          end: {\n            line: 206,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 206,\n            column: 22\n          },\n          end: {\n            line: 217,\n            column: 5\n          }\n        },\n        line: 206\n      },\n      \"20\": {\n        name: \"(anonymous_20)\",\n        decl: {\n          start: {\n            line: 209,\n            column: 23\n          },\n          end: {\n            line: 209,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 209,\n            column: 32\n          },\n          end: {\n            line: 216,\n            column: 9\n          }\n        },\n        line: 209\n      },\n      \"21\": {\n        name: \"(anonymous_21)\",\n        decl: {\n          start: {\n            line: 218,\n            column: 4\n          },\n          end: {\n            line: 218,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 218,\n            column: 15\n          },\n          end: {\n            line: 226,\n            column: 5\n          }\n        },\n        line: 218\n      },\n      \"22\": {\n        name: \"(anonymous_22)\",\n        decl: {\n          start: {\n            line: 227,\n            column: 4\n          },\n          end: {\n            line: 227,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 227,\n            column: 23\n          },\n          end: {\n            line: 229,\n            column: 5\n          }\n        },\n        line: 227\n      },\n      \"23\": {\n        name: \"(anonymous_23)\",\n        decl: {\n          start: {\n            line: 231,\n            column: 4\n          },\n          end: {\n            line: 231,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 231,\n            column: 27\n          },\n          end: {\n            line: 233,\n            column: 5\n          }\n        },\n        line: 231\n      },\n      \"24\": {\n        name: \"(anonymous_24)\",\n        decl: {\n          start: {\n            line: 234,\n            column: 4\n          },\n          end: {\n            line: 234,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 234,\n            column: 34\n          },\n          end: {\n            line: 249,\n            column: 5\n          }\n        },\n        line: 234\n      },\n      \"25\": {\n        name: \"(anonymous_25)\",\n        decl: {\n          start: {\n            line: 237,\n            column: 25\n          },\n          end: {\n            line: 237,\n            column: 26\n          }\n        },\n        loc: {\n          start: {\n            line: 237,\n            column: 36\n          },\n          end: {\n            line: 248,\n            column: 9\n          }\n        },\n        line: 237\n      },\n      \"26\": {\n        name: \"(anonymous_26)\",\n        decl: {\n          start: {\n            line: 250,\n            column: 4\n          },\n          end: {\n            line: 250,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 250,\n            column: 25\n          },\n          end: {\n            line: 262,\n            column: 5\n          }\n        },\n        line: 250\n      },\n      \"27\": {\n        name: \"(anonymous_27)\",\n        decl: {\n          start: {\n            line: 263,\n            column: 4\n          },\n          end: {\n            line: 263,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 263,\n            column: 33\n          },\n          end: {\n            line: 265,\n            column: 5\n          }\n        },\n        line: 263\n      },\n      \"28\": {\n        name: \"(anonymous_28)\",\n        decl: {\n          start: {\n            line: 267,\n            column: 4\n          },\n          end: {\n            line: 267,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 267,\n            column: 15\n          },\n          end: {\n            line: 300,\n            column: 5\n          }\n        },\n        line: 267\n      },\n      \"29\": {\n        name: \"(anonymous_29)\",\n        decl: {\n          start: {\n            line: 287,\n            column: 22\n          },\n          end: {\n            line: 287,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 287,\n            column: 42\n          },\n          end: {\n            line: 291,\n            column: 17\n          }\n        },\n        line: 287\n      },\n      \"30\": {\n        name: \"(anonymous_30)\",\n        decl: {\n          start: {\n            line: 292,\n            column: 23\n          },\n          end: {\n            line: 292,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 292,\n            column: 34\n          },\n          end: {\n            line: 296,\n            column: 17\n          }\n        },\n        line: 292\n      },\n      \"31\": {\n        name: \"(anonymous_31)\",\n        decl: {\n          start: {\n            line: 301,\n            column: 4\n          },\n          end: {\n            line: 301,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 301,\n            column: 15\n          },\n          end: {\n            line: 308,\n            column: 5\n          }\n        },\n        line: 301\n      },\n      \"32\": {\n        name: \"(anonymous_32)\",\n        decl: {\n          start: {\n            line: 310,\n            column: 4\n          },\n          end: {\n            line: 310,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 310,\n            column: 34\n          },\n          end: {\n            line: 320,\n            column: 5\n          }\n        },\n        line: 310\n      },\n      \"33\": {\n        name: \"(anonymous_33)\",\n        decl: {\n          start: {\n            line: 321,\n            column: 4\n          },\n          end: {\n            line: 321,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 321,\n            column: 32\n          },\n          end: {\n            line: 331,\n            column: 5\n          }\n        },\n        line: 321\n      },\n      \"34\": {\n        name: \"(anonymous_34)\",\n        decl: {\n          start: {\n            line: 332,\n            column: 4\n          },\n          end: {\n            line: 332,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 332,\n            column: 34\n          },\n          end: {\n            line: 347,\n            column: 5\n          }\n        },\n        line: 332\n      },\n      \"35\": {\n        name: \"(anonymous_35)\",\n        decl: {\n          start: {\n            line: 348,\n            column: 4\n          },\n          end: {\n            line: 348,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 348,\n            column: 35\n          },\n          end: {\n            line: 359,\n            column: 5\n          }\n        },\n        line: 348\n      },\n      \"36\": {\n        name: \"(anonymous_36)\",\n        decl: {\n          start: {\n            line: 350,\n            column: 24\n          },\n          end: {\n            line: 350,\n            column: 25\n          }\n        },\n        loc: {\n          start: {\n            line: 350,\n            column: 31\n          },\n          end: {\n            line: 357,\n            column: 9\n          }\n        },\n        line: 350\n      },\n      \"37\": {\n        name: \"(anonymous_37)\",\n        decl: {\n          start: {\n            line: 360,\n            column: 4\n          },\n          end: {\n            line: 360,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 360,\n            column: 29\n          },\n          end: {\n            line: 381,\n            column: 5\n          }\n        },\n        line: 360\n      },\n      \"38\": {\n        name: \"(anonymous_38)\",\n        decl: {\n          start: {\n            line: 365,\n            column: 18\n          },\n          end: {\n            line: 365,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 365,\n            column: 32\n          },\n          end: {\n            line: 372,\n            column: 13\n          }\n        },\n        line: 365\n      },\n      \"39\": {\n        name: \"(anonymous_39)\",\n        decl: {\n          start: {\n            line: 373,\n            column: 19\n          },\n          end: {\n            line: 373,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 373,\n            column: 30\n          },\n          end: {\n            line: 379,\n            column: 13\n          }\n        },\n        line: 373\n      },\n      \"40\": {\n        name: \"(anonymous_40)\",\n        decl: {\n          start: {\n            line: 382,\n            column: 4\n          },\n          end: {\n            line: 382,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 382,\n            column: 27\n          },\n          end: {\n            line: 403,\n            column: 5\n          }\n        },\n        line: 382\n      },\n      \"41\": {\n        name: \"(anonymous_41)\",\n        decl: {\n          start: {\n            line: 387,\n            column: 18\n          },\n          end: {\n            line: 387,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 387,\n            column: 32\n          },\n          end: {\n            line: 394,\n            column: 13\n          }\n        },\n        line: 387\n      },\n      \"42\": {\n        name: \"(anonymous_42)\",\n        decl: {\n          start: {\n            line: 395,\n            column: 19\n          },\n          end: {\n            line: 395,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 395,\n            column: 30\n          },\n          end: {\n            line: 401,\n            column: 13\n          }\n        },\n        line: 395\n      },\n      \"43\": {\n        name: \"(anonymous_43)\",\n        decl: {\n          start: {\n            line: 405,\n            column: 4\n          },\n          end: {\n            line: 405,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 405,\n            column: 27\n          },\n          end: {\n            line: 419,\n            column: 5\n          }\n        },\n        line: 405\n      },\n      \"44\": {\n        name: \"(anonymous_44)\",\n        decl: {\n          start: {\n            line: 406,\n            column: 55\n          },\n          end: {\n            line: 406,\n            column: 56\n          }\n        },\n        loc: {\n          start: {\n            line: 406,\n            column: 62\n          },\n          end: {\n            line: 418,\n            column: 9\n          }\n        },\n        line: 406\n      },\n      \"45\": {\n        name: \"(anonymous_45)\",\n        decl: {\n          start: {\n            line: 410,\n            column: 41\n          },\n          end: {\n            line: 410,\n            column: 42\n          }\n        },\n        loc: {\n          start: {\n            line: 410,\n            column: 57\n          },\n          end: {\n            line: 416,\n            column: 17\n          }\n        },\n        line: 410\n      },\n      \"46\": {\n        name: \"(anonymous_46)\",\n        decl: {\n          start: {\n            line: 412,\n            column: 67\n          },\n          end: {\n            line: 412,\n            column: 68\n          }\n        },\n        loc: {\n          start: {\n            line: 412,\n            column: 79\n          },\n          end: {\n            line: 414,\n            column: 25\n          }\n        },\n        line: 412\n      },\n      \"47\": {\n        name: \"(anonymous_47)\",\n        decl: {\n          start: {\n            line: 420,\n            column: 4\n          },\n          end: {\n            line: 420,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 420,\n            column: 31\n          },\n          end: {\n            line: 451,\n            column: 5\n          }\n        },\n        line: 420\n      },\n      \"48\": {\n        name: \"(anonymous_48)\",\n        decl: {\n          start: {\n            line: 452,\n            column: 4\n          },\n          end: {\n            line: 452,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 452,\n            column: 35\n          },\n          end: {\n            line: 471,\n            column: 5\n          }\n        },\n        line: 452\n      },\n      \"49\": {\n        name: \"(anonymous_49)\",\n        decl: {\n          start: {\n            line: 473,\n            column: 4\n          },\n          end: {\n            line: 473,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 473,\n            column: 19\n          },\n          end: {\n            line: 489,\n            column: 5\n          }\n        },\n        line: 473\n      },\n      \"50\": {\n        name: \"(anonymous_50)\",\n        decl: {\n          start: {\n            line: 490,\n            column: 4\n          },\n          end: {\n            line: 490,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 490,\n            column: 28\n          },\n          end: {\n            line: 493,\n            column: 5\n          }\n        },\n        line: 490\n      },\n      \"51\": {\n        name: \"(anonymous_51)\",\n        decl: {\n          start: {\n            line: 494,\n            column: 4\n          },\n          end: {\n            line: 494,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 494,\n            column: 20\n          },\n          end: {\n            line: 520,\n            column: 5\n          }\n        },\n        line: 494\n      },\n      \"52\": {\n        name: \"(anonymous_52)\",\n        decl: {\n          start: {\n            line: 497,\n            column: 55\n          },\n          end: {\n            line: 497,\n            column: 56\n          }\n        },\n        loc: {\n          start: {\n            line: 497,\n            column: 62\n          },\n          end: {\n            line: 505,\n            column: 9\n          }\n        },\n        line: 497\n      },\n      \"53\": {\n        name: \"(anonymous_53)\",\n        decl: {\n          start: {\n            line: 509,\n            column: 54\n          },\n          end: {\n            line: 509,\n            column: 55\n          }\n        },\n        loc: {\n          start: {\n            line: 509,\n            column: 61\n          },\n          end: {\n            line: 517,\n            column: 13\n          }\n        },\n        line: 509\n      },\n      \"54\": {\n        name: \"(anonymous_54)\",\n        decl: {\n          start: {\n            line: 522,\n            column: 4\n          },\n          end: {\n            line: 522,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 522,\n            column: 25\n          },\n          end: {\n            line: 534,\n            column: 5\n          }\n        },\n        line: 522\n      },\n      \"55\": {\n        name: \"(anonymous_55)\",\n        decl: {\n          start: {\n            line: 536,\n            column: 4\n          },\n          end: {\n            line: 536,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 536,\n            column: 26\n          },\n          end: {\n            line: 542,\n            column: 5\n          }\n        },\n        line: 536\n      },\n      \"56\": {\n        name: \"(anonymous_56)\",\n        decl: {\n          start: {\n            line: 544,\n            column: 4\n          },\n          end: {\n            line: 544,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 544,\n            column: 30\n          },\n          end: {\n            line: 556,\n            column: 5\n          }\n        },\n        line: 544\n      },\n      \"57\": {\n        name: \"(anonymous_57)\",\n        decl: {\n          start: {\n            line: 558,\n            column: 4\n          },\n          end: {\n            line: 558,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 558,\n            column: 33\n          },\n          end: {\n            line: 565,\n            column: 5\n          }\n        },\n        line: 558\n      },\n      \"58\": {\n        name: \"(anonymous_58)\",\n        decl: {\n          start: {\n            line: 567,\n            column: 4\n          },\n          end: {\n            line: 567,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 567,\n            column: 32\n          },\n          end: {\n            line: 576,\n            column: 5\n          }\n        },\n        line: 567\n      },\n      \"59\": {\n        name: \"(anonymous_59)\",\n        decl: {\n          start: {\n            line: 578,\n            column: 4\n          },\n          end: {\n            line: 578,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 578,\n            column: 25\n          },\n          end: {\n            line: 585,\n            column: 5\n          }\n        },\n        line: 578\n      },\n      \"60\": {\n        name: \"(anonymous_60)\",\n        decl: {\n          start: {\n            line: 586,\n            column: 35\n          },\n          end: {\n            line: 586,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 586,\n            column: 41\n          },\n          end: {\n            line: 592,\n            column: 5\n          }\n        },\n        line: 586\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 103,\n            column: 31\n          },\n          end: {\n            line: 103,\n            column: 62\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 103,\n            column: 31\n          },\n          end: {\n            line: 103,\n            column: 56\n          }\n        }, {\n          start: {\n            line: 103,\n            column: 60\n          },\n          end: {\n            line: 103,\n            column: 62\n          }\n        }],\n        line: 103\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 104,\n            column: 22\n          },\n          end: {\n            line: 104,\n            column: 44\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 104,\n            column: 22\n          },\n          end: {\n            line: 104,\n            column: 38\n          }\n        }, {\n          start: {\n            line: 104,\n            column: 42\n          },\n          end: {\n            line: 104,\n            column: 44\n          }\n        }],\n        line: 104\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 105,\n            column: 21\n          },\n          end: {\n            line: 105,\n            column: 42\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 105,\n            column: 21\n          },\n          end: {\n            line: 105,\n            column: 36\n          }\n        }, {\n          start: {\n            line: 105,\n            column: 40\n          },\n          end: {\n            line: 105,\n            column: 42\n          }\n        }],\n        line: 105\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 107,\n            column: 22\n          },\n          end: {\n            line: 107,\n            column: 50\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 107,\n            column: 22\n          },\n          end: {\n            line: 107,\n            column: 44\n          }\n        }, {\n          start: {\n            line: 107,\n            column: 48\n          },\n          end: {\n            line: 107,\n            column: 50\n          }\n        }],\n        line: 107\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 108,\n            column: 23\n          },\n          end: {\n            line: 108,\n            column: 52\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 108,\n            column: 23\n          },\n          end: {\n            line: 108,\n            column: 46\n          }\n        }, {\n          start: {\n            line: 108,\n            column: 50\n          },\n          end: {\n            line: 108,\n            column: 52\n          }\n        }],\n        line: 108\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 109,\n            column: 25\n          },\n          end: {\n            line: 109,\n            column: 56\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 109,\n            column: 25\n          },\n          end: {\n            line: 109,\n            column: 50\n          }\n        }, {\n          start: {\n            line: 109,\n            column: 54\n          },\n          end: {\n            line: 109,\n            column: 56\n          }\n        }],\n        line: 109\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 110,\n            column: 33\n          },\n          end: {\n            line: 110,\n            column: 72\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 110,\n            column: 33\n          },\n          end: {\n            line: 110,\n            column: 66\n          }\n        }, {\n          start: {\n            line: 110,\n            column: 70\n          },\n          end: {\n            line: 110,\n            column: 72\n          }\n        }],\n        line: 110\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 113,\n            column: 23\n          },\n          end: {\n            line: 113,\n            column: 54\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 113,\n            column: 23\n          },\n          end: {\n            line: 113,\n            column: 48\n          }\n        }, {\n          start: {\n            line: 113,\n            column: 52\n          },\n          end: {\n            line: 113,\n            column: 54\n          }\n        }],\n        line: 113\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 115,\n            column: 25\n          },\n          end: {\n            line: 115,\n            column: 58\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 115,\n            column: 25\n          },\n          end: {\n            line: 115,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 115,\n            column: 56\n          },\n          end: {\n            line: 115,\n            column: 58\n          }\n        }],\n        line: 115\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 116,\n            column: 30\n          },\n          end: {\n            line: 116,\n            column: 68\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 116,\n            column: 30\n          },\n          end: {\n            line: 116,\n            column: 62\n          }\n        }, {\n          start: {\n            line: 116,\n            column: 66\n          },\n          end: {\n            line: 116,\n            column: 68\n          }\n        }],\n        line: 116\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 118,\n            column: 28\n          },\n          end: {\n            line: 118,\n            column: 77\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 118,\n            column: 28\n          },\n          end: {\n            line: 118,\n            column: 71\n          }\n        }, {\n          start: {\n            line: 118,\n            column: 75\n          },\n          end: {\n            line: 118,\n            column: 77\n          }\n        }],\n        line: 118\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 119,\n            column: 26\n          },\n          end: {\n            line: 119,\n            column: 73\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 119,\n            column: 26\n          },\n          end: {\n            line: 119,\n            column: 67\n          }\n        }, {\n          start: {\n            line: 119,\n            column: 71\n          },\n          end: {\n            line: 119,\n            column: 73\n          }\n        }],\n        line: 119\n      },\n      \"12\": {\n        loc: {\n          start: {\n            line: 120,\n            column: 27\n          },\n          end: {\n            line: 120,\n            column: 75\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 120,\n            column: 27\n          },\n          end: {\n            line: 120,\n            column: 69\n          }\n        }, {\n          start: {\n            line: 120,\n            column: 73\n          },\n          end: {\n            line: 120,\n            column: 75\n          }\n        }],\n        line: 120\n      },\n      \"13\": {\n        loc: {\n          start: {\n            line: 121,\n            column: 32\n          },\n          end: {\n            line: 121,\n            column: 85\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 121,\n            column: 32\n          },\n          end: {\n            line: 121,\n            column: 79\n          }\n        }, {\n          start: {\n            line: 121,\n            column: 83\n          },\n          end: {\n            line: 121,\n            column: 85\n          }\n        }],\n        line: 121\n      },\n      \"14\": {\n        loc: {\n          start: {\n            line: 122,\n            column: 29\n          },\n          end: {\n            line: 122,\n            column: 79\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 122,\n            column: 29\n          },\n          end: {\n            line: 122,\n            column: 73\n          }\n        }, {\n          start: {\n            line: 122,\n            column: 77\n          },\n          end: {\n            line: 122,\n            column: 79\n          }\n        }],\n        line: 122\n      },\n      \"15\": {\n        loc: {\n          start: {\n            line: 123,\n            column: 30\n          },\n          end: {\n            line: 123,\n            column: 84\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 123,\n            column: 30\n          },\n          end: {\n            line: 123,\n            column: 75\n          }\n        }, {\n          start: {\n            line: 123,\n            column: 79\n          },\n          end: {\n            line: 123,\n            column: 84\n          }\n        }],\n        line: 123\n      },\n      \"16\": {\n        loc: {\n          start: {\n            line: 128,\n            column: 28\n          },\n          end: {\n            line: 128,\n            column: 73\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 128,\n            column: 28\n          },\n          end: {\n            line: 128,\n            column: 65\n          }\n        }, {\n          start: {\n            line: 128,\n            column: 69\n          },\n          end: {\n            line: 128,\n            column: 73\n          }\n        }],\n        line: 128\n      },\n      \"17\": {\n        loc: {\n          start: {\n            line: 129,\n            column: 29\n          },\n          end: {\n            line: 129,\n            column: 75\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 129,\n            column: 29\n          },\n          end: {\n            line: 129,\n            column: 67\n          }\n        }, {\n          start: {\n            line: 129,\n            column: 71\n          },\n          end: {\n            line: 129,\n            column: 75\n          }\n        }],\n        line: 129\n      },\n      \"18\": {\n        loc: {\n          start: {\n            line: 130,\n            column: 26\n          },\n          end: {\n            line: 130,\n            column: 70\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 130,\n            column: 26\n          },\n          end: {\n            line: 130,\n            column: 61\n          }\n        }, {\n          start: {\n            line: 130,\n            column: 65\n          },\n          end: {\n            line: 130,\n            column: 70\n          }\n        }],\n        line: 130\n      },\n      \"19\": {\n        loc: {\n          start: {\n            line: 134,\n            column: 29\n          },\n          end: {\n            line: 134,\n            column: 67\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 134,\n            column: 29\n          },\n          end: {\n            line: 134,\n            column: 61\n          }\n        }, {\n          start: {\n            line: 134,\n            column: 65\n          },\n          end: {\n            line: 134,\n            column: 67\n          }\n        }],\n        line: 134\n      },\n      \"20\": {\n        loc: {\n          start: {\n            line: 136,\n            column: 28\n          },\n          end: {\n            line: 136,\n            column: 53\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 136,\n            column: 28\n          },\n          end: {\n            line: 136,\n            column: 47\n          }\n        }, {\n          start: {\n            line: 136,\n            column: 51\n          },\n          end: {\n            line: 136,\n            column: 53\n          }\n        }],\n        line: 136\n      },\n      \"21\": {\n        loc: {\n          start: {\n            line: 138,\n            column: 23\n          },\n          end: {\n            line: 138,\n            column: 43\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 138,\n            column: 23\n          },\n          end: {\n            line: 138,\n            column: 37\n          }\n        }, {\n          start: {\n            line: 138,\n            column: 41\n          },\n          end: {\n            line: 138,\n            column: 43\n          }\n        }],\n        line: 138\n      },\n      \"22\": {\n        loc: {\n          start: {\n            line: 140,\n            column: 33\n          },\n          end: {\n            line: 140,\n            column: 63\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 140,\n            column: 33\n          },\n          end: {\n            line: 140,\n            column: 57\n          }\n        }, {\n          start: {\n            line: 140,\n            column: 61\n          },\n          end: {\n            line: 140,\n            column: 63\n          }\n        }],\n        line: 140\n      },\n      \"23\": {\n        loc: {\n          start: {\n            line: 213,\n            column: 27\n          },\n          end: {\n            line: 213,\n            column: 47\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 213,\n            column: 27\n          },\n          end: {\n            line: 213,\n            column: 41\n          }\n        }, {\n          start: {\n            line: 213,\n            column: 45\n          },\n          end: {\n            line: 213,\n            column: 47\n          }\n        }],\n        line: 213\n      },\n      \"24\": {\n        loc: {\n          start: {\n            line: 214,\n            column: 35\n          },\n          end: {\n            line: 214,\n            column: 75\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 214,\n            column: 35\n          },\n          end: {\n            line: 214,\n            column: 57\n          }\n        }, {\n          start: {\n            line: 214,\n            column: 61\n          },\n          end: {\n            line: 214,\n            column: 75\n          }\n        }],\n        line: 214\n      },\n      \"25\": {\n        loc: {\n          start: {\n            line: 243,\n            column: 27\n          },\n          end: {\n            line: 243,\n            column: 52\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 243,\n            column: 27\n          },\n          end: {\n            line: 243,\n            column: 43\n          }\n        }, {\n          start: {\n            line: 243,\n            column: 47\n          },\n          end: {\n            line: 243,\n            column: 52\n          }\n        }],\n        line: 243\n      },\n      \"26\": {\n        loc: {\n          start: {\n            line: 245,\n            column: 27\n          },\n          end: {\n            line: 245,\n            column: 49\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 245,\n            column: 27\n          },\n          end: {\n            line: 245,\n            column: 43\n          }\n        }, {\n          start: {\n            line: 245,\n            column: 47\n          },\n          end: {\n            line: 245,\n            column: 49\n          }\n        }],\n        line: 245\n      },\n      \"27\": {\n        loc: {\n          start: {\n            line: 268,\n            column: 8\n          },\n          end: {\n            line: 298,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 268,\n            column: 8\n          },\n          end: {\n            line: 298,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 268\n      },\n      \"28\": {\n        loc: {\n          start: {\n            line: 280,\n            column: 24\n          },\n          end: {\n            line: 280,\n            column: 46\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 280,\n            column: 24\n          },\n          end: {\n            line: 280,\n            column: 40\n          }\n        }, {\n          start: {\n            line: 280,\n            column: 44\n          },\n          end: {\n            line: 280,\n            column: 46\n          }\n        }],\n        line: 280\n      },\n      \"29\": {\n        loc: {\n          start: {\n            line: 282,\n            column: 34\n          },\n          end: {\n            line: 282,\n            column: 66\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 282,\n            column: 34\n          },\n          end: {\n            line: 282,\n            column: 60\n          }\n        }, {\n          start: {\n            line: 282,\n            column: 64\n          },\n          end: {\n            line: 282,\n            column: 66\n          }\n        }],\n        line: 282\n      },\n      \"30\": {\n        loc: {\n          start: {\n            line: 302,\n            column: 8\n          },\n          end: {\n            line: 307,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 302,\n            column: 8\n          },\n          end: {\n            line: 307,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 305,\n            column: 13\n          },\n          end: {\n            line: 307,\n            column: 9\n          }\n        }],\n        line: 302\n      },\n      \"31\": {\n        loc: {\n          start: {\n            line: 312,\n            column: 8\n          },\n          end: {\n            line: 317,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 312,\n            column: 8\n          },\n          end: {\n            line: 317,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 312\n      },\n      \"32\": {\n        loc: {\n          start: {\n            line: 313,\n            column: 12\n          },\n          end: {\n            line: 316,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 313,\n            column: 12\n          },\n          end: {\n            line: 316,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 313\n      },\n      \"33\": {\n        loc: {\n          start: {\n            line: 323,\n            column: 8\n          },\n          end: {\n            line: 328,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 323,\n            column: 8\n          },\n          end: {\n            line: 328,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 323\n      },\n      \"34\": {\n        loc: {\n          start: {\n            line: 324,\n            column: 12\n          },\n          end: {\n            line: 327,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 324,\n            column: 12\n          },\n          end: {\n            line: 327,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 324\n      },\n      \"35\": {\n        loc: {\n          start: {\n            line: 335,\n            column: 8\n          },\n          end: {\n            line: 338,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 335,\n            column: 8\n          },\n          end: {\n            line: 338,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 335\n      },\n      \"36\": {\n        loc: {\n          start: {\n            line: 340,\n            column: 24\n          },\n          end: {\n            line: 340,\n            column: 79\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 340,\n            column: 45\n          },\n          end: {\n            line: 340,\n            column: 60\n          }\n        }, {\n          start: {\n            line: 340,\n            column: 63\n          },\n          end: {\n            line: 340,\n            column: 79\n          }\n        }],\n        line: 340\n      },\n      \"37\": {\n        loc: {\n          start: {\n            line: 341,\n            column: 8\n          },\n          end: {\n            line: 345,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 341,\n            column: 8\n          },\n          end: {\n            line: 345,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 341\n      },\n      \"38\": {\n        loc: {\n          start: {\n            line: 351,\n            column: 12\n          },\n          end: {\n            line: 356,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 351,\n            column: 12\n          },\n          end: {\n            line: 356,\n            column: 13\n          }\n        }, {\n          start: {\n            line: 354,\n            column: 17\n          },\n          end: {\n            line: 356,\n            column: 13\n          }\n        }],\n        line: 351\n      },\n      \"39\": {\n        loc: {\n          start: {\n            line: 367,\n            column: 16\n          },\n          end: {\n            line: 369,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 367,\n            column: 16\n          },\n          end: {\n            line: 369,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 367\n      },\n      \"40\": {\n        loc: {\n          start: {\n            line: 377,\n            column: 37\n          },\n          end: {\n            line: 377,\n            column: 111\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 377,\n            column: 37\n          },\n          end: {\n            line: 377,\n            column: 57\n          }\n        }, {\n          start: {\n            line: 377,\n            column: 61\n          },\n          end: {\n            line: 377,\n            column: 111\n          }\n        }],\n        line: 377\n      },\n      \"41\": {\n        loc: {\n          start: {\n            line: 389,\n            column: 16\n          },\n          end: {\n            line: 391,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 389,\n            column: 16\n          },\n          end: {\n            line: 391,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 389\n      },\n      \"42\": {\n        loc: {\n          start: {\n            line: 399,\n            column: 37\n          },\n          end: {\n            line: 399,\n            column: 109\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 399,\n            column: 37\n          },\n          end: {\n            line: 399,\n            column: 57\n          }\n        }, {\n          start: {\n            line: 399,\n            column: 61\n          },\n          end: {\n            line: 399,\n            column: 109\n          }\n        }],\n        line: 399\n      },\n      \"43\": {\n        loc: {\n          start: {\n            line: 409,\n            column: 12\n          },\n          end: {\n            line: 417,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 409,\n            column: 12\n          },\n          end: {\n            line: 417,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 409\n      },\n      \"44\": {\n        loc: {\n          start: {\n            line: 411,\n            column: 20\n          },\n          end: {\n            line: 415,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 411,\n            column: 20\n          },\n          end: {\n            line: 415,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 411\n      },\n      \"45\": {\n        loc: {\n          start: {\n            line: 422,\n            column: 8\n          },\n          end: {\n            line: 423,\n            column: 22\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 422,\n            column: 8\n          },\n          end: {\n            line: 423,\n            column: 22\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 422\n      },\n      \"46\": {\n        loc: {\n          start: {\n            line: 422,\n            column: 12\n          },\n          end: {\n            line: 422,\n            column: 39\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 422,\n            column: 12\n          },\n          end: {\n            line: 422,\n            column: 20\n          }\n        }, {\n          start: {\n            line: 422,\n            column: 24\n          },\n          end: {\n            line: 422,\n            column: 39\n          }\n        }],\n        line: 422\n      },\n      \"47\": {\n        loc: {\n          start: {\n            line: 425,\n            column: 8\n          },\n          end: {\n            line: 427,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 425,\n            column: 8\n          },\n          end: {\n            line: 427,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 425\n      },\n      \"48\": {\n        loc: {\n          start: {\n            line: 428,\n            column: 8\n          },\n          end: {\n            line: 430,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 428,\n            column: 8\n          },\n          end: {\n            line: 430,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 428\n      },\n      \"49\": {\n        loc: {\n          start: {\n            line: 431,\n            column: 8\n          },\n          end: {\n            line: 434,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 431,\n            column: 8\n          },\n          end: {\n            line: 434,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 431\n      },\n      \"50\": {\n        loc: {\n          start: {\n            line: 435,\n            column: 8\n          },\n          end: {\n            line: 438,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 435,\n            column: 8\n          },\n          end: {\n            line: 438,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 435\n      },\n      \"51\": {\n        loc: {\n          start: {\n            line: 439,\n            column: 8\n          },\n          end: {\n            line: 441,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 439,\n            column: 8\n          },\n          end: {\n            line: 441,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 439\n      },\n      \"52\": {\n        loc: {\n          start: {\n            line: 442,\n            column: 8\n          },\n          end: {\n            line: 445,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 442,\n            column: 8\n          },\n          end: {\n            line: 445,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 442\n      },\n      \"53\": {\n        loc: {\n          start: {\n            line: 446,\n            column: 8\n          },\n          end: {\n            line: 449,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 446,\n            column: 8\n          },\n          end: {\n            line: 449,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 446\n      },\n      \"54\": {\n        loc: {\n          start: {\n            line: 470,\n            column: 15\n          },\n          end: {\n            line: 470,\n            column: 49\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 470,\n            column: 15\n          },\n          end: {\n            line: 470,\n            column: 36\n          }\n        }, {\n          start: {\n            line: 470,\n            column: 40\n          },\n          end: {\n            line: 470,\n            column: 49\n          }\n        }],\n        line: 470\n      },\n      \"55\": {\n        loc: {\n          start: {\n            line: 474,\n            column: 8\n          },\n          end: {\n            line: 487,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 474,\n            column: 8\n          },\n          end: {\n            line: 487,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 474\n      },\n      \"56\": {\n        loc: {\n          start: {\n            line: 478,\n            column: 12\n          },\n          end: {\n            line: 480,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 478,\n            column: 12\n          },\n          end: {\n            line: 480,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 478\n      },\n      \"57\": {\n        loc: {\n          start: {\n            line: 483,\n            column: 12\n          },\n          end: {\n            line: 485,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 483,\n            column: 12\n          },\n          end: {\n            line: 485,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 483\n      },\n      \"58\": {\n        loc: {\n          start: {\n            line: 492,\n            column: 15\n          },\n          end: {\n            line: 492,\n            column: 65\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 492,\n            column: 42\n          },\n          end: {\n            line: 492,\n            column: 58\n          }\n        }, {\n          start: {\n            line: 492,\n            column: 61\n          },\n          end: {\n            line: 492,\n            column: 65\n          }\n        }],\n        line: 492\n      },\n      \"59\": {\n        loc: {\n          start: {\n            line: 499,\n            column: 12\n          },\n          end: {\n            line: 504,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 499,\n            column: 12\n          },\n          end: {\n            line: 504,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 499\n      },\n      \"60\": {\n        loc: {\n          start: {\n            line: 499,\n            column: 16\n          },\n          end: {\n            line: 499,\n            column: 61\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 499,\n            column: 16\n          },\n          end: {\n            line: 499,\n            column: 23\n          }\n        }, {\n          start: {\n            line: 499,\n            column: 27\n          },\n          end: {\n            line: 499,\n            column: 42\n          }\n        }, {\n          start: {\n            line: 499,\n            column: 46\n          },\n          end: {\n            line: 499,\n            column: 61\n          }\n        }],\n        line: 499\n      },\n      \"61\": {\n        loc: {\n          start: {\n            line: 501,\n            column: 16\n          },\n          end: {\n            line: 503,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 501,\n            column: 16\n          },\n          end: {\n            line: 503,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 501\n      },\n      \"62\": {\n        loc: {\n          start: {\n            line: 508,\n            column: 8\n          },\n          end: {\n            line: 518,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 508,\n            column: 8\n          },\n          end: {\n            line: 518,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 508\n      },\n      \"63\": {\n        loc: {\n          start: {\n            line: 508,\n            column: 12\n          },\n          end: {\n            line: 508,\n            column: 46\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 508,\n            column: 12\n          },\n          end: {\n            line: 508,\n            column: 23\n          }\n        }, {\n          start: {\n            line: 508,\n            column: 27\n          },\n          end: {\n            line: 508,\n            column: 46\n          }\n        }],\n        line: 508\n      },\n      \"64\": {\n        loc: {\n          start: {\n            line: 511,\n            column: 16\n          },\n          end: {\n            line: 516,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 511,\n            column: 16\n          },\n          end: {\n            line: 516,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 511\n      },\n      \"65\": {\n        loc: {\n          start: {\n            line: 511,\n            column: 20\n          },\n          end: {\n            line: 511,\n            column: 65\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 511,\n            column: 20\n          },\n          end: {\n            line: 511,\n            column: 27\n          }\n        }, {\n          start: {\n            line: 511,\n            column: 31\n          },\n          end: {\n            line: 511,\n            column: 46\n          }\n        }, {\n          start: {\n            line: 511,\n            column: 50\n          },\n          end: {\n            line: 511,\n            column: 65\n          }\n        }],\n        line: 511\n      },\n      \"66\": {\n        loc: {\n          start: {\n            line: 513,\n            column: 20\n          },\n          end: {\n            line: 515,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 513,\n            column: 20\n          },\n          end: {\n            line: 515,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 513\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0,\n      \"59\": 0,\n      \"60\": 0,\n      \"61\": 0,\n      \"62\": 0,\n      \"63\": 0,\n      \"64\": 0,\n      \"65\": 0,\n      \"66\": 0,\n      \"67\": 0,\n      \"68\": 0,\n      \"69\": 0,\n      \"70\": 0,\n      \"71\": 0,\n      \"72\": 0,\n      \"73\": 0,\n      \"74\": 0,\n      \"75\": 0,\n      \"76\": 0,\n      \"77\": 0,\n      \"78\": 0,\n      \"79\": 0,\n      \"80\": 0,\n      \"81\": 0,\n      \"82\": 0,\n      \"83\": 0,\n      \"84\": 0,\n      \"85\": 0,\n      \"86\": 0,\n      \"87\": 0,\n      \"88\": 0,\n      \"89\": 0,\n      \"90\": 0,\n      \"91\": 0,\n      \"92\": 0,\n      \"93\": 0,\n      \"94\": 0,\n      \"95\": 0,\n      \"96\": 0,\n      \"97\": 0,\n      \"98\": 0,\n      \"99\": 0,\n      \"100\": 0,\n      \"101\": 0,\n      \"102\": 0,\n      \"103\": 0,\n      \"104\": 0,\n      \"105\": 0,\n      \"106\": 0,\n      \"107\": 0,\n      \"108\": 0,\n      \"109\": 0,\n      \"110\": 0,\n      \"111\": 0,\n      \"112\": 0,\n      \"113\": 0,\n      \"114\": 0,\n      \"115\": 0,\n      \"116\": 0,\n      \"117\": 0,\n      \"118\": 0,\n      \"119\": 0,\n      \"120\": 0,\n      \"121\": 0,\n      \"122\": 0,\n      \"123\": 0,\n      \"124\": 0,\n      \"125\": 0,\n      \"126\": 0,\n      \"127\": 0,\n      \"128\": 0,\n      \"129\": 0,\n      \"130\": 0,\n      \"131\": 0,\n      \"132\": 0,\n      \"133\": 0,\n      \"134\": 0,\n      \"135\": 0,\n      \"136\": 0,\n      \"137\": 0,\n      \"138\": 0,\n      \"139\": 0,\n      \"140\": 0,\n      \"141\": 0,\n      \"142\": 0,\n      \"143\": 0,\n      \"144\": 0,\n      \"145\": 0,\n      \"146\": 0,\n      \"147\": 0,\n      \"148\": 0,\n      \"149\": 0,\n      \"150\": 0,\n      \"151\": 0,\n      \"152\": 0,\n      \"153\": 0,\n      \"154\": 0,\n      \"155\": 0,\n      \"156\": 0,\n      \"157\": 0,\n      \"158\": 0,\n      \"159\": 0,\n      \"160\": 0,\n      \"161\": 0,\n      \"162\": 0,\n      \"163\": 0,\n      \"164\": 0,\n      \"165\": 0,\n      \"166\": 0,\n      \"167\": 0,\n      \"168\": 0,\n      \"169\": 0,\n      \"170\": 0,\n      \"171\": 0,\n      \"172\": 0,\n      \"173\": 0,\n      \"174\": 0,\n      \"175\": 0,\n      \"176\": 0,\n      \"177\": 0,\n      \"178\": 0,\n      \"179\": 0,\n      \"180\": 0,\n      \"181\": 0,\n      \"182\": 0,\n      \"183\": 0,\n      \"184\": 0,\n      \"185\": 0,\n      \"186\": 0,\n      \"187\": 0,\n      \"188\": 0,\n      \"189\": 0,\n      \"190\": 0,\n      \"191\": 0,\n      \"192\": 0,\n      \"193\": 0,\n      \"194\": 0,\n      \"195\": 0,\n      \"196\": 0,\n      \"197\": 0,\n      \"198\": 0,\n      \"199\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0,\n      \"59\": 0,\n      \"60\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0],\n      \"10\": [0, 0],\n      \"11\": [0, 0],\n      \"12\": [0, 0],\n      \"13\": [0, 0],\n      \"14\": [0, 0],\n      \"15\": [0, 0],\n      \"16\": [0, 0],\n      \"17\": [0, 0],\n      \"18\": [0, 0],\n      \"19\": [0, 0],\n      \"20\": [0, 0],\n      \"21\": [0, 0],\n      \"22\": [0, 0],\n      \"23\": [0, 0],\n      \"24\": [0, 0],\n      \"25\": [0, 0],\n      \"26\": [0, 0],\n      \"27\": [0, 0],\n      \"28\": [0, 0],\n      \"29\": [0, 0],\n      \"30\": [0, 0],\n      \"31\": [0, 0],\n      \"32\": [0, 0],\n      \"33\": [0, 0],\n      \"34\": [0, 0],\n      \"35\": [0, 0],\n      \"36\": [0, 0],\n      \"37\": [0, 0],\n      \"38\": [0, 0],\n      \"39\": [0, 0],\n      \"40\": [0, 0],\n      \"41\": [0, 0],\n      \"42\": [0, 0],\n      \"43\": [0, 0],\n      \"44\": [0, 0],\n      \"45\": [0, 0],\n      \"46\": [0, 0],\n      \"47\": [0, 0],\n      \"48\": [0, 0],\n      \"49\": [0, 0],\n      \"50\": [0, 0],\n      \"51\": [0, 0],\n      \"52\": [0, 0],\n      \"53\": [0, 0],\n      \"54\": [0, 0],\n      \"55\": [0, 0],\n      \"56\": [0, 0],\n      \"57\": [0, 0],\n      \"58\": [0, 0],\n      \"59\": [0, 0],\n      \"60\": [0, 0, 0],\n      \"61\": [0, 0],\n      \"62\": [0, 0],\n      \"63\": [0, 0],\n      \"64\": [0, 0],\n      \"65\": [0, 0, 0],\n      \"66\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"profile-edit.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\profile\\\\components\\\\profile-edit\\\\profile-edit.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EAAE,SAAS,EAAqB,MAAM,eAAe,CAAC;AAC7D,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAC/E,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AACzC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AAC1C,OAAO,EAAE,WAAW,EAAE,MAAM,6BAA6B,CAAC;AAE1D,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAChE,OAAO,EAAE,WAAW,EAAE,MAAM,qCAAqC,CAAC;AAQ3D,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAY/B,YACU,WAAwB,EACxB,cAA8B,EAC9B,WAAwB,EACxB,MAAc,EACd,QAAqB;QAJrB,gBAAW,GAAX,WAAW,CAAa;QACxB,mBAAc,GAAd,cAAc,CAAgB;QAC9B,gBAAW,GAAX,WAAW,CAAa;QACxB,WAAM,GAAN,MAAM,CAAQ;QACd,aAAQ,GAAR,QAAQ,CAAa;QAf/B,YAAO,GAAuB,IAAI,CAAC;QACnC,cAAS,GAAG,IAAI,CAAC;QACjB,aAAQ,GAAG,KAAK,CAAC;QACjB,4BAAuB,GAAG,KAAK,CAAC;QAChC,0BAAqB,GAAG,KAAK,CAAC;QAC9B,wBAAmB,GAAkB,IAAI,CAAC;QAC1C,sBAAiB,GAAkB,IAAI,CAAC;QAEhC,aAAQ,GAAG,IAAI,OAAO,EAAQ,CAAC;QASrC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;IACvC,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED,WAAW;QACT,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;IAC3B,CAAC;IAEO,UAAU;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YAC5B,oBAAoB;YACpB,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9D,iBAAiB,EAAE,CAAC,EAAE,CAAC;YACvB,QAAQ,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACzC,OAAO,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAEzC,WAAW;YACX,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;gBAC/B,IAAI,EAAE,CAAC,EAAE,CAAC;gBACV,KAAK,EAAE,CAAC,EAAE,CAAC;gBACX,OAAO,EAAE,CAAC,EAAE,CAAC;gBACb,eAAe,EAAE,CAAC,EAAE,CAAC;aACtB,CAAC;YAEF,sBAAsB;YACtB,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;gBAClC,KAAK,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,KAAK,CAAC;gBAC7B,aAAa,EAAE,CAAC,KAAK,CAAC;gBACtB,OAAO,EAAE,CAAC,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC,EAAE,CAAC;gBAClB,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxC,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;oBACtC,MAAM,EAAE,CAAC,EAAE,CAAC;oBACZ,IAAI,EAAE,CAAC,EAAE,CAAC;oBACV,KAAK,EAAE,CAAC,EAAE,CAAC;oBACX,UAAU,EAAE,CAAC,EAAE,CAAC;oBAChB,OAAO,EAAE,CAAC,EAAE,CAAC;oBACb,QAAQ,EAAE,CAAC,KAAK,CAAC;iBAClB,CAAC;aACH,CAAC;YAEF,mBAAmB;YACnB,QAAQ,EAAE,CAAC,IAAI,CAAC;YAEhB,eAAe;YACf,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;YAEvC,SAAS;YACT,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;YAElC,qBAAqB;YACrB,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;gBACxC,UAAU,EAAE,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC9D,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC/D,QAAQ,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,QAAQ,CAAC;aACvC,CAAC;YAEF,oBAAoB;YACpB,gBAAgB,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;SAC7C,CAAC,CAAC;IACL,CAAC;IAEO,WAAW;QACjB,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE;aACxC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC9B,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE;gBAChB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;gBACvB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;gBAC3B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACzB,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;gBAC/C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;gBACzE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACzB,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAEO,YAAY,CAAC,OAAoB;QACvC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;YAC1B,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,IAAI,EAAE;YAClD,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;YAChC,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;YAC9B,QAAQ,EAAE;gBACR,IAAI,EAAE,OAAO,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE;gBAClC,KAAK,EAAE,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;gBACpC,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE;gBACxC,eAAe,EAAE,OAAO,CAAC,QAAQ,EAAE,eAAe,IAAI,EAAE;aACzD;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;gBACtC,aAAa,EAAE,OAAO,CAAC,WAAW,CAAC,aAAa;gBAChD,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC,OAAO,IAAI,EAAE;gBAC1C,YAAY,EAAE,OAAO,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE;gBACpD,eAAe,EAAE;oBACf,MAAM,EAAE,OAAO,CAAC,WAAW,CAAC,eAAe,EAAE,MAAM,IAAI,EAAE;oBACzD,IAAI,EAAE,OAAO,CAAC,WAAW,CAAC,eAAe,EAAE,IAAI,IAAI,EAAE;oBACrD,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;oBACvD,UAAU,EAAE,OAAO,CAAC,WAAW,CAAC,eAAe,EAAE,UAAU,IAAI,EAAE;oBACjE,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC,eAAe,EAAE,OAAO,IAAI,EAAE;oBAC3D,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC,eAAe,EAAE,QAAQ,IAAI,KAAK;iBACjE;aACF;YACD,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,iBAAiB,EAAE;gBACjB,UAAU,EAAE,OAAO,CAAC,iBAAiB,EAAE,UAAU,IAAI,IAAI;gBACzD,WAAW,EAAE,OAAO,CAAC,iBAAiB,EAAE,WAAW,IAAI,IAAI;gBAC3D,QAAQ,EAAE,OAAO,CAAC,iBAAiB,EAAE,QAAQ,IAAI,KAAK;aACvD;SACF,CAAC,CAAC;QAEH,yBAAyB;QACzB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;QAE7D,wBAAwB;QACxB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;QAE/C,kBAAkB;QAClB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;QAErC,6BAA6B;QAC7B,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED,2BAA2B;IAC3B,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,0BAA0B,CAAc,CAAC;IACvE,CAAC;IAEO,eAAe,CAAC,MAAa;QACnC,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC;QACrC,UAAU,CAAC,KAAK,EAAE,CAAC;QAEnB,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACrB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;gBACrC,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;gBACd,MAAM,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC,QAAQ,CAAC;gBAC3C,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC;gBACvC,QAAQ,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;gBAC1B,SAAS,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC;aAC7B,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC;IAED,cAAc;QACZ,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YACxC,EAAE,EAAE,CAAC,IAAI,CAAC;YACV,MAAM,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;YACjC,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,QAAQ,CAAC;YACrC,QAAQ,EAAE,CAAC,KAAK,CAAC;YACjB,SAAS,EAAE,CAAC,KAAK,CAAC;SACnB,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACrC,CAAC;IAED,iBAAiB,CAAC,KAAa;QAC7B,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAED,0BAA0B;IAC1B,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAc,CAAC;IAC1D,CAAC;IAEO,cAAc,CAAC,KAAY;QACjC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QACpC,UAAU,CAAC,KAAK,EAAE,CAAC;QAEnB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;gBACrC,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBACb,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,QAAQ,CAAC;gBAC9C,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;gBACzE,WAAW,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC;gBAC/B,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC1B,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC;IAED,aAAa;QACX,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YACvC,EAAE,EAAE,CAAC,IAAI,CAAC;YACV,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC,QAAQ,CAAC;YAC3C,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;YACnE,WAAW,EAAE,CAAC,EAAE,CAAC;YACjB,QAAQ,EAAE,CAAC,IAAI,CAAC;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAED,gBAAgB,CAAC,KAAa;QAC5B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;IAED,oBAAoB;IACpB,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAc,CAAC;IACrD,CAAC;IAEO,SAAS,CAAC,MAAsB;QACtC,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;QAC/B,UAAU,CAAC,KAAK,EAAE,CAAC;QAEnB,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACrB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;gBACrC,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;gBACd,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC;gBACvC,QAAQ,EAAE,CAAC,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC;gBAChC,gBAAgB,EAAE,CAAC,KAAK,CAAC,gBAAgB,IAAI,cAAc,EAAE,UAAU,CAAC,QAAQ,CAAC;aAClF,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC;IAED,QAAQ;QACN,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YACxC,EAAE,EAAE,CAAC,IAAI,CAAC;YACV,IAAI,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;YAC/B,QAAQ,EAAE,CAAC,EAAE,CAAC;YACd,gBAAgB,EAAE,CAAC,cAAc,EAAE,UAAU,CAAC,QAAQ,CAAC;SACxD,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC/B,CAAC;IAED,WAAW,CAAC,KAAa;QACvB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED,+BAA+B;IAC/B,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,kBAAkB,CAAc,CAAC;IAC/D,CAAC;IAEO,mBAAmB,CAAC,QAA2B;QACrD,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC3C,YAAY,CAAC,KAAK,EAAE,CAAC;QAErB,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;gBACvC,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;gBAChB,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC;gBACzC,WAAW,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC,QAAQ,CAAC;gBACvD,KAAK,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChE,QAAQ,EAAE,CAAC,OAAO,CAAC,QAAQ,IAAI,KAAK,EAAE,UAAU,CAAC,QAAQ,CAAC;gBAC1D,QAAQ,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAC5B,QAAQ,EAAE,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC;gBAClC,QAAQ,EAAE,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,kBAAkB;aAC1D,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC;IAED,kBAAkB;QAChB,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YAC1C,EAAE,EAAE,CAAC,IAAI,CAAC;YACV,IAAI,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;YAC/B,WAAW,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;YACtC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACpD,QAAQ,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,QAAQ,CAAC;YACtC,QAAQ,EAAE,CAAC,EAAE,CAAC;YACd,QAAQ,EAAE,CAAC,EAAE,CAAC;YACd,QAAQ,EAAE,CAAC,IAAI,CAAC;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC3C,CAAC;IAED,qBAAqB,CAAC,KAAa;QACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,kBAAkB;IAClB,QAAQ;QACN,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;YACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YAErB,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YACzC,MAAM,aAAa,GAAyB;gBAC1C,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,iBAAiB,EAAE,SAAS,CAAC,iBAAiB;gBAC9C,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,WAAW,EAAE,SAAS,CAAC,WAAW;gBAClC,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,MAAM,EAAE,SAAS,CAAC,MAAM,IAAI,EAAE;gBAC9B,iBAAiB,EAAE,SAAS,CAAC,iBAAiB;gBAC9C,gBAAgB,EAAE,SAAS,CAAC,gBAAgB,IAAI,EAAE;aACnD,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,aAAa,CAAC;iBAC7C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBAC9B,SAAS,CAAC;gBACT,IAAI,EAAE,CAAC,cAAc,EAAE,EAAE;oBACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;oBACtB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;oBACjF,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC1D,CAAC;gBACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;oBACf,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;oBACtB,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;oBAChD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,2CAA2C,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC/F,CAAC;aACF,CAAC,CAAC;SACN;QACD,qDAAqD;IACvD,CAAC;IAED,QAAQ;QACN,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;SACvD;aAAM;YACL,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;SACtC;IACH,CAAC;IAED,sBAAsB;IACtB,sBAAsB,CAAC,KAAU;QAC/B,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,IAAI,EAAE;YACR,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE;gBAC3C,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBACzC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAC/B;SACF;QACD,yDAAyD;QACzD,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;IAC1B,CAAC;IAED,oBAAoB,CAAC,KAAU;QAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,IAAI,EAAE;YACR,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;gBACzC,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBACvC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;aAC7B;SACF;QACD,yDAAyD;QACzD,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;IAC1B,CAAC;IAEO,iBAAiB,CAAC,IAAU,EAAE,IAAyB;QAC7D,kBAAkB;QAClB,MAAM,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;QACzF,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACrC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,4DAA4D,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YAC9G,OAAO,KAAK,CAAC;SACd;QAED,oDAAoD;QACpD,MAAM,OAAO,GAAG,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;QACxE,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE;YACvB,MAAM,SAAS,GAAG,OAAO,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;YAC1C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,SAAS,IAAI,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YAC9F,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,kBAAkB,CAAC,IAAU,EAAE,IAAyB;QAC9D,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAChC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAM,EAAE,EAAE;YACzB,IAAI,IAAI,KAAK,SAAS,EAAE;gBACtB,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;aAC5C;iBAAM;gBACL,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;aAC1C;QACH,CAAC,CAAC;QACF,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAEO,kBAAkB,CAAC,IAAU;QACnC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC;aACzC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC9B,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACjB,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;gBACrC,IAAI,IAAI,CAAC,OAAO,EAAE;oBAChB,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,QAAQ,CAAC,GAAG,CAAC;iBAC7C;gBACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAC,6CAA6C;gBAC9E,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YACzF,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;gBACrC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAC,yBAAyB;gBAC1D,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;gBACvD,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,OAAO,IAAI,kDAAkD,CAAC;gBAChG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YAChE,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAEO,gBAAgB,CAAC,IAAU;QACjC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC;aACvC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC9B,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACjB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;gBACnC,IAAI,IAAI,CAAC,OAAO,EAAE;oBAChB,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC;iBAC3C;gBACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,CAAC,6CAA6C;gBAC5E,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,mCAAmC,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YACvF,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;gBACnC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,CAAC,yBAAyB;gBACxD,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;gBACrD,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,OAAO,IAAI,gDAAgD,CAAC;gBAC9F,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YAChE,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAED,kBAAkB;IACV,oBAAoB;QAC1B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACnD,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC1C,OAAO,EAAE,aAAa,EAAE,CAAC;YAEzB,IAAI,OAAO,YAAY,SAAS,EAAE;gBAChC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;oBACtC,IAAI,YAAY,YAAY,SAAS,EAAE;wBACrC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;4BACpD,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,aAAa,EAAE,CAAC;wBAC9C,CAAC,CAAC,CAAC;qBACJ;gBACH,CAAC,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,eAAe,CAAC,SAAiB;QAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;YAAE,OAAO,EAAE,CAAC;QAE3C,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAE7D,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;YAChC,OAAO,GAAG,gBAAgB,cAAc,CAAC;SAC1C;QACD,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC7B,OAAO,oCAAoC,CAAC;SAC7C;QACD,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;YACjC,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC;YAClE,OAAO,GAAG,gBAAgB,qBAAqB,cAAc,aAAa,CAAC;SAC5E;QACD,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;YACjC,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC;YAC7D,OAAO,GAAG,gBAAgB,yBAAyB,SAAS,aAAa,CAAC;SAC3E;QACD,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;YAC/B,OAAO,0BAA0B,CAAC;SACnC;QACD,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC3B,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;YAC3C,OAAO,GAAG,gBAAgB,qBAAqB,QAAQ,EAAE,CAAC;SAC3D;QACD,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC3B,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;YAC3C,OAAO,GAAG,gBAAgB,kBAAkB,QAAQ,EAAE,CAAC;SACxD;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,mBAAmB,CAAC,SAAiB;QAC3C,MAAM,UAAU,GAA8B;YAC5C,WAAW,EAAE,YAAY;YACzB,UAAU,EAAE,WAAW;YACvB,mBAAmB,EAAE,oBAAoB;YACzC,UAAU,EAAE,UAAU;YACtB,SAAS,EAAE,SAAS;YACpB,mBAAmB,EAAE,OAAO;YAC5B,qBAAqB,EAAE,SAAS;YAChC,0BAA0B,EAAE,eAAe;YAC3C,eAAe,EAAE,MAAM;YACvB,gBAAgB,EAAE,OAAO;YACzB,kBAAkB,EAAE,SAAS;YAC7B,0BAA0B,EAAE,kBAAkB;YAC9C,8BAA8B,EAAE,aAAa;YAC7C,+BAA+B,EAAE,cAAc;YAC/C,4BAA4B,EAAE,UAAU;SACzC,CAAC;QACF,OAAO,UAAU,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC;IAC5C,CAAC;IAED,2BAA2B;IAC3B,YAAY;QACV,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;YAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,2CAA2C;YAC3C,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACvD,IAAI,iBAAiB,EAAE;gBACrB,iBAAiB,CAAC,KAAK,EAAE,CAAC;aAC3B;YAED,8BAA8B;YAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACpC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,oCAAoC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;aAC1G;YAED,OAAO,KAAK,CAAC;SACd;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,qBAAqB;QAC3B,MAAM,aAAa,GAAG,QAAQ,CAAC,gBAAgB,CAAC,8GAA8G,CAAC,CAAC;QAChK,OAAO,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAgB,CAAC,CAAC,CAAC,IAAI,CAAC;IAC3E,CAAC;IAEO,aAAa;QACnB,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,yBAAyB;QACzB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACnD,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC1C,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE;gBACjD,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBAC/C,IAAI,YAAY,EAAE;oBAChB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;iBAC3B;aACF;QACH,CAAC,CAAC,CAAC;QAEH,2BAA2B;QAC3B,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAc,CAAC;QACrE,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,EAAE;YACtC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAC9C,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACrC,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE;oBACjD,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,GAAG,EAAE,CAAC,CAAC;oBAChE,IAAI,YAAY,EAAE;wBAChB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;qBAC3B;iBACF;YACH,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,yDAAyD;IACtF,CAAC;IAED,oCAAoC;IACpC,kBAAkB;QAChB,OAAO;YACL,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE;YACxC,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;YACtC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;YACpC,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;YACtC,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE;YACxC,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE;YAC1C,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE;YACxC,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;YACtC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;SACnC,CAAC;IACJ,CAAC;IAED,qBAAqB;IACrB,mBAAmB;QACjB,OAAO;YACL,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;YACpC,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE;YACxC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;SACjC,CAAC;IACJ,CAAC;IAED,yBAAyB;IACzB,uBAAuB;QACrB,OAAO;YACL,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE;YAC1C,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE,iBAAiB,EAAE;YACtD,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE;YAC1C,EAAE,KAAK,EAAE,sBAAsB,EAAE,KAAK,EAAE,sBAAsB,EAAE;YAChE,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,EAAE;YAC5C,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,eAAe,EAAE;YAClD,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,gBAAgB,EAAE;YACpD,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,EAAE;YAC5C,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;SACnC,CAAC;IACJ,CAAC;IAED,4BAA4B;IAC5B,0BAA0B;QACxB,OAAO;YACL,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE;YACxC,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,cAAc,EAAE;YAChD,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE;YACxC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;SACrC,CAAC;IACJ,CAAC;IAED,2BAA2B;IAC3B,yBAAyB;QACvB,OAAO;YACL,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;YACtC,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,cAAc,EAAE;YAChD,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;YACtC,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE;YACxC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;YACpC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;SACnC,CAAC;IACJ,CAAC;IAED,mBAAmB;IACnB,kBAAkB;QAChB,OAAO;YACL,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,qBAAqB,EAAE;YAC9C,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE;YACrC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,EAAE;YAC1C,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,qBAAqB,EAAE;SAC/C,CAAC;IACJ,CAAC;;;;;;;;;AAxoBU,oBAAoB;IALhC,SAAS,CAAC;QACT,QAAQ,EAAE,kBAAkB;QAC5B,8BAA4C;;KAE7C,CAAC;GACW,oBAAoB,CAyoBhC;SAzoBY,oBAAoB\",\n      sourcesContent: [\"import { Component, OnInit, OnDestroy } from '@angular/core';\\r\\nimport { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';\\r\\nimport { Router } from '@angular/router';\\r\\nimport { Subject, takeUntil } from 'rxjs';\\r\\nimport { MatSnackBar } from '@angular/material/snack-bar';\\r\\n\\r\\nimport { ProfileService } from '../../services/profile.service';\\r\\nimport { AuthService } from '../../../auth/services/auth.service';\\r\\nimport { UserProfile, ProfileUpdateRequest, ProfileSkill, ConsultationRates, ServiceOffering } from '../../models/profile.models';\\r\\n\\r\\n@Component({\\r\\n  selector: 'app-profile-edit',\\r\\n  templateUrl: './profile-edit.component.html',\\r\\n  styleUrls: ['./profile-edit.component.css']\\r\\n})\\r\\nexport class ProfileEditComponent implements OnInit, OnDestroy {\\r\\n  profileForm: FormGroup;\\r\\n  profile: UserProfile | null = null;\\r\\n  isLoading = true;\\r\\n  isSaving = false;\\r\\n  isUploadingProfilePhoto = false;\\r\\n  isUploadingCoverPhoto = false;\\r\\n  profilePhotoPreview: string | null = null;\\r\\n  coverPhotoPreview: string | null = null;\\r\\n\\r\\n  private destroy$ = new Subject<void>();\\r\\n\\r\\n  constructor(\\r\\n    private formBuilder: FormBuilder,\\r\\n    private profileService: ProfileService,\\r\\n    private authService: AuthService,\\r\\n    private router: Router,\\r\\n    private snackBar: MatSnackBar\\r\\n  ) {\\r\\n    this.profileForm = this.createForm();\\r\\n  }\\r\\n\\r\\n  ngOnInit(): void {\\r\\n    this.loadProfile();\\r\\n  }\\r\\n\\r\\n  ngOnDestroy(): void {\\r\\n    this.destroy$.next();\\r\\n    this.destroy$.complete();\\r\\n  }\\r\\n\\r\\n  private createForm(): FormGroup {\\r\\n    return this.formBuilder.group({\\r\\n      // Basic Information\\r\\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\\r\\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\\r\\n      professionalTitle: [''],\\r\\n      headline: ['', Validators.maxLength(220)],\\r\\n      summary: ['', Validators.maxLength(2000)],\\r\\n      \\r\\n      // Location\\r\\n      location: this.formBuilder.group({\\r\\n        city: [''],\\r\\n        state: [''],\\r\\n        country: [''],\\r\\n        displayLocation: ['']\\r\\n      }),\\r\\n      \\r\\n      // Contact Information\\r\\n      contactInfo: this.formBuilder.group({\\r\\n        email: ['', Validators.email],\\r\\n        isEmailPublic: [false],\\r\\n        website: [''],\\r\\n        portfolioUrl: [''],\\r\\n        phoneNumbers: this.formBuilder.array([]),\\r\\n        businessAddress: this.formBuilder.group({\\r\\n          street: [''],\\r\\n          city: [''],\\r\\n          state: [''],\\r\\n          postalCode: [''],\\r\\n          country: [''],\\r\\n          isPublic: [false]\\r\\n        })\\r\\n      }),\\r\\n      \\r\\n      // Privacy Settings\\r\\n      isPublic: [true],\\r\\n\\r\\n      // Social Links\\r\\n      socialLinks: this.formBuilder.array([]),\\r\\n\\r\\n      // Skills\\r\\n      skills: this.formBuilder.array([]),\\r\\n\\r\\n      // Consultation Rates\\r\\n      consultationRates: this.formBuilder.group({\\r\\n        hourlyRate: [null, [Validators.min(0), Validators.max(10000)]],\\r\\n        sessionRate: [null, [Validators.min(0), Validators.max(10000)]],\\r\\n        currency: ['BGN', Validators.required]\\r\\n      }),\\r\\n\\r\\n      // Service Offerings\\r\\n      serviceOfferings: this.formBuilder.array([])\\r\\n    });\\r\\n  }\\r\\n\\r\\n  private loadProfile(): void {\\r\\n    this.profileService.getCurrentUserProfile()\\r\\n      .pipe(takeUntil(this.destroy$))\\r\\n      .subscribe({\\r\\n        next: (profile) => {\\r\\n          this.profile = profile;\\r\\n          this.populateForm(profile);\\r\\n          this.isLoading = false;\\r\\n        },\\r\\n        error: (error) => {\\r\\n          console.error('Error loading profile:', error);\\r\\n          this.snackBar.open('Error loading profile', 'Close', { duration: 5000 });\\r\\n          this.router.navigate(['/dashboard']);\\r\\n          this.isLoading = false;\\r\\n        }\\r\\n      });\\r\\n  }\\r\\n\\r\\n  private populateForm(profile: UserProfile): void {\\r\\n    this.profileForm.patchValue({\\r\\n      firstName: profile.firstName,\\r\\n      lastName: profile.lastName,\\r\\n      professionalTitle: profile.professionalTitle || '',\\r\\n      headline: profile.headline || '',\\r\\n      summary: profile.summary || '',\\r\\n      location: {\\r\\n        city: profile.location?.city || '',\\r\\n        state: profile.location?.state || '',\\r\\n        country: profile.location?.country || '',\\r\\n        displayLocation: profile.location?.displayLocation || ''\\r\\n      },\\r\\n      contactInfo: {\\r\\n        email: profile.contactInfo.email || '',\\r\\n        isEmailPublic: profile.contactInfo.isEmailPublic,\\r\\n        website: profile.contactInfo.website || '',\\r\\n        portfolioUrl: profile.contactInfo.portfolioUrl || '',\\r\\n        businessAddress: {\\r\\n          street: profile.contactInfo.businessAddress?.street || '',\\r\\n          city: profile.contactInfo.businessAddress?.city || '',\\r\\n          state: profile.contactInfo.businessAddress?.state || '',\\r\\n          postalCode: profile.contactInfo.businessAddress?.postalCode || '',\\r\\n          country: profile.contactInfo.businessAddress?.country || '',\\r\\n          isPublic: profile.contactInfo.businessAddress?.isPublic || false\\r\\n        }\\r\\n      },\\r\\n      isPublic: profile.isPublic,\\r\\n      consultationRates: {\\r\\n        hourlyRate: profile.consultationRates?.hourlyRate || null,\\r\\n        sessionRate: profile.consultationRates?.sessionRate || null,\\r\\n        currency: profile.consultationRates?.currency || 'BGN'\\r\\n      }\\r\\n    });\\r\\n\\r\\n    // Populate phone numbers\\r\\n    this.setPhoneNumbers(profile.contactInfo.phoneNumbers || []);\\r\\n\\r\\n    // Populate social links\\r\\n    this.setSocialLinks(profile.socialLinks || []);\\r\\n\\r\\n    // Populate skills\\r\\n    this.setSkills(profile.skills || []);\\r\\n\\r\\n    // Populate service offerings\\r\\n    this.setServiceOfferings(profile.serviceOfferings || []);\\r\\n  }\\r\\n\\r\\n  // Phone Numbers Management\\r\\n  get phoneNumbers(): FormArray {\\r\\n    return this.profileForm.get('contactInfo.phoneNumbers') as FormArray;\\r\\n  }\\r\\n\\r\\n  private setPhoneNumbers(phones: any[]): void {\\r\\n    const phoneArray = this.phoneNumbers;\\r\\n    phoneArray.clear();\\r\\n    \\r\\n    phones.forEach(phone => {\\r\\n      phoneArray.push(this.formBuilder.group({\\r\\n        id: [phone.id],\\r\\n        number: [phone.number, Validators.required],\\r\\n        type: [phone.type, Validators.required],\\r\\n        isPublic: [phone.isPublic],\\r\\n        isPrimary: [phone.isPrimary]\\r\\n      }));\\r\\n    });\\r\\n  }\\r\\n\\r\\n  addPhoneNumber(): void {\\r\\n    const phoneGroup = this.formBuilder.group({\\r\\n      id: [null],\\r\\n      number: ['', Validators.required],\\r\\n      type: ['mobile', Validators.required],\\r\\n      isPublic: [false],\\r\\n      isPrimary: [false]\\r\\n    });\\r\\n    \\r\\n    this.phoneNumbers.push(phoneGroup);\\r\\n  }\\r\\n\\r\\n  removePhoneNumber(index: number): void {\\r\\n    this.phoneNumbers.removeAt(index);\\r\\n  }\\r\\n\\r\\n  // Social Links Management\\r\\n  get socialLinks(): FormArray {\\r\\n    return this.profileForm.get('socialLinks') as FormArray;\\r\\n  }\\r\\n\\r\\n  private setSocialLinks(links: any[]): void {\\r\\n    const linksArray = this.socialLinks;\\r\\n    linksArray.clear();\\r\\n    \\r\\n    links.forEach(link => {\\r\\n      linksArray.push(this.formBuilder.group({\\r\\n        id: [link.id],\\r\\n        platform: [link.platform, Validators.required],\\r\\n        url: [link.url, [Validators.required, Validators.pattern('https?://.+')]],\\r\\n        displayName: [link.displayName],\\r\\n        isPublic: [link.isPublic]\\r\\n      }));\\r\\n    });\\r\\n  }\\r\\n\\r\\n  addSocialLink(): void {\\r\\n    const linkGroup = this.formBuilder.group({\\r\\n      id: [null],\\r\\n      platform: ['linkedin', Validators.required],\\r\\n      url: ['', [Validators.required, Validators.pattern('https?://.+')]],\\r\\n      displayName: [''],\\r\\n      isPublic: [true]\\r\\n    });\\r\\n    \\r\\n    this.socialLinks.push(linkGroup);\\r\\n  }\\r\\n\\r\\n  removeSocialLink(index: number): void {\\r\\n    this.socialLinks.removeAt(index);\\r\\n  }\\r\\n\\r\\n  // Skills Management\\r\\n  get skills(): FormArray {\\r\\n    return this.profileForm.get('skills') as FormArray;\\r\\n  }\\r\\n\\r\\n  private setSkills(skills: ProfileSkill[]): void {\\r\\n    const skillArray = this.skills;\\r\\n    skillArray.clear();\\r\\n\\r\\n    skills.forEach(skill => {\\r\\n      skillArray.push(this.formBuilder.group({\\r\\n        id: [skill.id],\\r\\n        name: [skill.name, Validators.required],\\r\\n        category: [skill.category || ''],\\r\\n        proficiencyLevel: [skill.proficiencyLevel || 'intermediate', Validators.required]\\r\\n      }));\\r\\n    });\\r\\n  }\\r\\n\\r\\n  addSkill(): void {\\r\\n    const skillGroup = this.formBuilder.group({\\r\\n      id: [null],\\r\\n      name: ['', Validators.required],\\r\\n      category: [''],\\r\\n      proficiencyLevel: ['intermediate', Validators.required]\\r\\n    });\\r\\n\\r\\n    this.skills.push(skillGroup);\\r\\n  }\\r\\n\\r\\n  removeSkill(index: number): void {\\r\\n    this.skills.removeAt(index);\\r\\n  }\\r\\n\\r\\n  // Service Offerings Management\\r\\n  get serviceOfferings(): FormArray {\\r\\n    return this.profileForm.get('serviceOfferings') as FormArray;\\r\\n  }\\r\\n\\r\\n  private setServiceOfferings(services: ServiceOffering[]): void {\\r\\n    const serviceArray = this.serviceOfferings;\\r\\n    serviceArray.clear();\\r\\n\\r\\n    services.forEach(service => {\\r\\n      serviceArray.push(this.formBuilder.group({\\r\\n        id: [service.id],\\r\\n        name: [service.name, Validators.required],\\r\\n        description: [service.description, Validators.required],\\r\\n        price: [service.price, [Validators.required, Validators.min(0)]],\\r\\n        currency: [service.currency || 'BGN', Validators.required],\\r\\n        duration: [service.duration],\\r\\n        category: [service.category || ''],\\r\\n        isActive: [service.isActive !== false] // default to true\\r\\n      }));\\r\\n    });\\r\\n  }\\r\\n\\r\\n  addServiceOffering(): void {\\r\\n    const serviceGroup = this.formBuilder.group({\\r\\n      id: [null],\\r\\n      name: ['', Validators.required],\\r\\n      description: ['', Validators.required],\\r\\n      price: [0, [Validators.required, Validators.min(0)]],\\r\\n      currency: ['BGN', Validators.required],\\r\\n      duration: [60], // default 60 minutes\\r\\n      category: [''],\\r\\n      isActive: [true]\\r\\n    });\\r\\n\\r\\n    this.serviceOfferings.push(serviceGroup);\\r\\n  }\\r\\n\\r\\n  removeServiceOffering(index: number): void {\\r\\n    this.serviceOfferings.removeAt(index);\\r\\n  }\\r\\n\\r\\n  // Form Submission\\r\\n  onSubmit(): void {\\r\\n    if (this.validateForm()) {\\r\\n      this.isSaving = true;\\r\\n      \\r\\n      const formValue = this.profileForm.value;\\r\\n      const updateRequest: ProfileUpdateRequest = {\\r\\n        firstName: formValue.firstName,\\r\\n        lastName: formValue.lastName,\\r\\n        professionalTitle: formValue.professionalTitle,\\r\\n        headline: formValue.headline,\\r\\n        location: formValue.location,\\r\\n        contactInfo: formValue.contactInfo,\\r\\n        summary: formValue.summary,\\r\\n        isPublic: formValue.isPublic,\\r\\n        skills: formValue.skills || [],\\r\\n        consultationRates: formValue.consultationRates,\\r\\n        serviceOfferings: formValue.serviceOfferings || []\\r\\n      };\\r\\n\\r\\n      this.profileService.updateProfile(updateRequest)\\r\\n        .pipe(takeUntil(this.destroy$))\\r\\n        .subscribe({\\r\\n          next: (updatedProfile) => {\\r\\n            this.isSaving = false;\\r\\n            this.snackBar.open('Profile updated successfully!', 'Close', { duration: 3000 });\\r\\n            this.router.navigate(['/profile', updatedProfile.slug]);\\r\\n          },\\r\\n          error: (error) => {\\r\\n            this.isSaving = false;\\r\\n            console.error('Error updating profile:', error);\\r\\n            this.snackBar.open('Error updating profile. Please try again.', 'Close', { duration: 5000 });\\r\\n          }\\r\\n        });\\r\\n    }\\r\\n    // Validation is now handled in validateForm() method\\r\\n  }\\r\\n\\r\\n  onCancel(): void {\\r\\n    if (this.profile) {\\r\\n      this.router.navigate(['/profile', this.profile.slug]);\\r\\n    } else {\\r\\n      this.router.navigate(['/dashboard']);\\r\\n    }\\r\\n  }\\r\\n\\r\\n  // File Upload Methods\\r\\n  onProfilePhotoSelected(event: any): void {\\r\\n    const file = event.target.files[0];\\r\\n    if (file) {\\r\\n      if (this.validateImageFile(file, 'profile')) {\\r\\n        this.createImagePreview(file, 'profile');\\r\\n        this.uploadProfilePhoto(file);\\r\\n      }\\r\\n    }\\r\\n    // Reset the input to allow selecting the same file again\\r\\n    event.target.value = '';\\r\\n  }\\r\\n\\r\\n  onCoverPhotoSelected(event: any): void {\\r\\n    const file = event.target.files[0];\\r\\n    if (file) {\\r\\n      if (this.validateImageFile(file, 'cover')) {\\r\\n        this.createImagePreview(file, 'cover');\\r\\n        this.uploadCoverPhoto(file);\\r\\n      }\\r\\n    }\\r\\n    // Reset the input to allow selecting the same file again\\r\\n    event.target.value = '';\\r\\n  }\\r\\n\\r\\n  private validateImageFile(file: File, type: 'profile' | 'cover'): boolean {\\r\\n    // Check file type\\r\\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];\\r\\n    if (!allowedTypes.includes(file.type)) {\\r\\n      this.snackBar.open('Please select a valid image file (JPEG, PNG, GIF, or WebP)', 'Close', { duration: 5000 });\\r\\n      return false;\\r\\n    }\\r\\n\\r\\n    // Check file size (5MB for profile, 10MB for cover)\\r\\n    const maxSize = type === 'profile' ? 5 * 1024 * 1024 : 10 * 1024 * 1024;\\r\\n    if (file.size > maxSize) {\\r\\n      const maxSizeMB = maxSize / (1024 * 1024);\\r\\n      this.snackBar.open(`File size must be less than ${maxSizeMB}MB`, 'Close', { duration: 5000 });\\r\\n      return false;\\r\\n    }\\r\\n\\r\\n    return true;\\r\\n  }\\r\\n\\r\\n  private createImagePreview(file: File, type: 'profile' | 'cover'): void {\\r\\n    const reader = new FileReader();\\r\\n    reader.onload = (e: any) => {\\r\\n      if (type === 'profile') {\\r\\n        this.profilePhotoPreview = e.target.result;\\r\\n      } else {\\r\\n        this.coverPhotoPreview = e.target.result;\\r\\n      }\\r\\n    };\\r\\n    reader.readAsDataURL(file);\\r\\n  }\\r\\n\\r\\n  private uploadProfilePhoto(file: File): void {\\r\\n    this.isUploadingProfilePhoto = true;\\r\\n    this.profileService.uploadProfilePhoto(file)\\r\\n      .pipe(takeUntil(this.destroy$))\\r\\n      .subscribe({\\r\\n        next: (response) => {\\r\\n          this.isUploadingProfilePhoto = false;\\r\\n          if (this.profile) {\\r\\n            this.profile.profilePhotoUrl = response.url;\\r\\n          }\\r\\n          this.profilePhotoPreview = null; // Clear preview since we have the actual URL\\r\\n          this.snackBar.open('Profile photo updated successfully!', 'Close', { duration: 3000 });\\r\\n        },\\r\\n        error: (error) => {\\r\\n          this.isUploadingProfilePhoto = false;\\r\\n          this.profilePhotoPreview = null; // Clear preview on error\\r\\n          console.error('Error uploading profile photo:', error);\\r\\n          const errorMessage = error.error?.message || 'Error uploading profile photo. Please try again.';\\r\\n          this.snackBar.open(errorMessage, 'Close', { duration: 5000 });\\r\\n        }\\r\\n      });\\r\\n  }\\r\\n\\r\\n  private uploadCoverPhoto(file: File): void {\\r\\n    this.isUploadingCoverPhoto = true;\\r\\n    this.profileService.uploadCoverPhoto(file)\\r\\n      .pipe(takeUntil(this.destroy$))\\r\\n      .subscribe({\\r\\n        next: (response) => {\\r\\n          this.isUploadingCoverPhoto = false;\\r\\n          if (this.profile) {\\r\\n            this.profile.coverPhotoUrl = response.url;\\r\\n          }\\r\\n          this.coverPhotoPreview = null; // Clear preview since we have the actual URL\\r\\n          this.snackBar.open('Cover photo updated successfully!', 'Close', { duration: 3000 });\\r\\n        },\\r\\n        error: (error) => {\\r\\n          this.isUploadingCoverPhoto = false;\\r\\n          this.coverPhotoPreview = null; // Clear preview on error\\r\\n          console.error('Error uploading cover photo:', error);\\r\\n          const errorMessage = error.error?.message || 'Error uploading cover photo. Please try again.';\\r\\n          this.snackBar.open(errorMessage, 'Close', { duration: 5000 });\\r\\n        }\\r\\n      });\\r\\n  }\\r\\n\\r\\n  // Utility Methods\\r\\n  private markFormGroupTouched(): void {\\r\\n    Object.keys(this.profileForm.controls).forEach(key => {\\r\\n      const control = this.profileForm.get(key);\\r\\n      control?.markAsTouched();\\r\\n      \\r\\n      if (control instanceof FormArray) {\\r\\n        control.controls.forEach(arrayControl => {\\r\\n          if (arrayControl instanceof FormGroup) {\\r\\n            Object.keys(arrayControl.controls).forEach(arrayKey => {\\r\\n              arrayControl.get(arrayKey)?.markAsTouched();\\r\\n            });\\r\\n          }\\r\\n        });\\r\\n      }\\r\\n    });\\r\\n  }\\r\\n\\r\\n  getErrorMessage(fieldName: string): string {\\r\\n    const control = this.profileForm.get(fieldName);\\r\\n    if (!control || !control.errors) return '';\\r\\n\\r\\n    const fieldDisplayName = this.getFieldDisplayName(fieldName);\\r\\n\\r\\n    if (control.hasError('required')) {\\r\\n      return `${fieldDisplayName} is required`;\\r\\n    }\\r\\n    if (control.hasError('email')) {\\r\\n      return 'Please enter a valid email address';\\r\\n    }\\r\\n    if (control.hasError('minlength')) {\\r\\n      const requiredLength = control.errors['minlength'].requiredLength;\\r\\n      return `${fieldDisplayName} must be at least ${requiredLength} characters`;\\r\\n    }\\r\\n    if (control.hasError('maxlength')) {\\r\\n      const maxLength = control.errors['maxlength'].requiredLength;\\r\\n      return `${fieldDisplayName} must be no more than ${maxLength} characters`;\\r\\n    }\\r\\n    if (control.hasError('pattern')) {\\r\\n      return 'Please enter a valid URL';\\r\\n    }\\r\\n    if (control.hasError('min')) {\\r\\n      const minValue = control.errors['min'].min;\\r\\n      return `${fieldDisplayName} must be at least ${minValue}`;\\r\\n    }\\r\\n    if (control.hasError('max')) {\\r\\n      const maxValue = control.errors['max'].max;\\r\\n      return `${fieldDisplayName} cannot exceed ${maxValue}`;\\r\\n    }\\r\\n    return '';\\r\\n  }\\r\\n\\r\\n  private getFieldDisplayName(fieldName: string): string {\\r\\n    const fieldNames: { [key: string]: string } = {\\r\\n      'firstName': 'First Name',\\r\\n      'lastName': 'Last Name',\\r\\n      'professionalTitle': 'Professional Title',\\r\\n      'headline': 'Headline',\\r\\n      'summary': 'Summary',\\r\\n      'contactInfo.email': 'Email',\\r\\n      'contactInfo.website': 'Website',\\r\\n      'contactInfo.portfolioUrl': 'Portfolio URL',\\r\\n      'location.city': 'City',\\r\\n      'location.state': 'State',\\r\\n      'location.country': 'Country',\\r\\n      'location.displayLocation': 'Display Location',\\r\\n      'consultationRates.hourlyRate': 'Hourly Rate',\\r\\n      'consultationRates.sessionRate': 'Session Rate',\\r\\n      'consultationRates.currency': 'Currency'\\r\\n    };\\r\\n    return fieldNames[fieldName] || fieldName;\\r\\n  }\\r\\n\\r\\n  // Enhanced form validation\\r\\n  validateForm(): boolean {\\r\\n    if (this.profileForm.invalid) {\\r\\n      this.markFormGroupTouched();\\r\\n\\r\\n      // Find first invalid field and focus on it\\r\\n      const firstInvalidField = this.findFirstInvalidField();\\r\\n      if (firstInvalidField) {\\r\\n        firstInvalidField.focus();\\r\\n      }\\r\\n\\r\\n      // Show specific error message\\r\\n      const errors = this.getFormErrors();\\r\\n      if (errors.length > 0) {\\r\\n        this.snackBar.open(`Please fix the following errors: ${errors.join(', ')}`, 'Close', { duration: 5000 });\\r\\n      }\\r\\n\\r\\n      return false;\\r\\n    }\\r\\n    return true;\\r\\n  }\\r\\n\\r\\n  private findFirstInvalidField(): HTMLElement | null {\\r\\n    const invalidFields = document.querySelectorAll('.mat-form-field.ng-invalid input, .mat-form-field.ng-invalid textarea, .mat-form-field.ng-invalid mat-select');\\r\\n    return invalidFields.length > 0 ? invalidFields[0] as HTMLElement : null;\\r\\n  }\\r\\n\\r\\n  private getFormErrors(): string[] {\\r\\n    const errors: string[] = [];\\r\\n\\r\\n    // Check main form fields\\r\\n    Object.keys(this.profileForm.controls).forEach(key => {\\r\\n      const control = this.profileForm.get(key);\\r\\n      if (control && control.invalid && control.touched) {\\r\\n        const errorMessage = this.getErrorMessage(key);\\r\\n        if (errorMessage) {\\r\\n          errors.push(errorMessage);\\r\\n        }\\r\\n      }\\r\\n    });\\r\\n\\r\\n    // Check nested form groups\\r\\n    const contactInfo = this.profileForm.get('contactInfo') as FormGroup;\\r\\n    if (contactInfo && contactInfo.invalid) {\\r\\n      Object.keys(contactInfo.controls).forEach(key => {\\r\\n        const control = contactInfo.get(key);\\r\\n        if (control && control.invalid && control.touched) {\\r\\n          const errorMessage = this.getErrorMessage(`contactInfo.${key}`);\\r\\n          if (errorMessage) {\\r\\n            errors.push(errorMessage);\\r\\n          }\\r\\n        }\\r\\n      });\\r\\n    }\\r\\n\\r\\n    return errors.slice(0, 3); // Limit to first 3 errors to avoid overwhelming the user\\r\\n  }\\r\\n\\r\\n  // Platform options for social links\\r\\n  getPlatformOptions() {\\r\\n    return [\\r\\n      { value: 'linkedin', label: 'LinkedIn' },\\r\\n      { value: 'twitter', label: 'Twitter' },\\r\\n      { value: 'github', label: 'GitHub' },\\r\\n      { value: 'behance', label: 'Behance' },\\r\\n      { value: 'dribbble', label: 'Dribbble' },\\r\\n      { value: 'instagram', label: 'Instagram' },\\r\\n      { value: 'facebook', label: 'Facebook' },\\r\\n      { value: 'youtube', label: 'YouTube' },\\r\\n      { value: 'other', label: 'Other' }\\r\\n    ];\\r\\n  }\\r\\n\\r\\n  // Phone type options\\r\\n  getPhoneTypeOptions() {\\r\\n    return [\\r\\n      { value: 'mobile', label: 'Mobile' },\\r\\n      { value: 'business', label: 'Business' },\\r\\n      { value: 'home', label: 'Home' }\\r\\n    ];\\r\\n  }\\r\\n\\r\\n  // Skill category options\\r\\n  getSkillCategoryOptions() {\\r\\n    return [\\r\\n      { value: 'Astrology', label: 'Astrology' },\\r\\n      { value: 'Crystal Healing', label: 'Crystal Healing' },\\r\\n      { value: 'Palmistry', label: 'Palmistry' },\\r\\n      { value: 'Spiritual Counseling', label: 'Spiritual Counseling' },\\r\\n      { value: 'Numerology', label: 'Numerology' },\\r\\n      { value: 'Tarot Reading', label: 'Tarot Reading' },\\r\\n      { value: 'Energy Healing', label: 'Energy Healing' },\\r\\n      { value: 'Meditation', label: 'Meditation' },\\r\\n      { value: 'Other', label: 'Other' }\\r\\n    ];\\r\\n  }\\r\\n\\r\\n  // Proficiency level options\\r\\n  getProficiencyLevelOptions() {\\r\\n    return [\\r\\n      { value: 'beginner', label: 'Beginner' },\\r\\n      { value: 'intermediate', label: 'Intermediate' },\\r\\n      { value: 'advanced', label: 'Advanced' },\\r\\n      { value: 'expert', label: 'Expert' }\\r\\n    ];\\r\\n  }\\r\\n\\r\\n  // Service category options\\r\\n  getServiceCategoryOptions() {\\r\\n    return [\\r\\n      { value: 'Reading', label: 'Reading' },\\r\\n      { value: 'Consultation', label: 'Consultation' },\\r\\n      { value: 'Healing', label: 'Healing' },\\r\\n      { value: 'Workshop', label: 'Workshop' },\\r\\n      { value: 'Course', label: 'Course' },\\r\\n      { value: 'Other', label: 'Other' }\\r\\n    ];\\r\\n  }\\r\\n\\r\\n  // Currency options\\r\\n  getCurrencyOptions() {\\r\\n    return [\\r\\n      { value: 'BGN', label: 'BGN (Bulgarian Lev)' },\\r\\n      { value: 'EUR', label: 'EUR (Euro)' },\\r\\n      { value: 'USD', label: 'USD (US Dollar)' },\\r\\n      { value: 'GBP', label: 'GBP (British Pound)' }\\r\\n    ];\\r\\n  }\\r\\n}\\r\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"bea53bca159f823ab6c2aea6066c6571b4cd9fc8\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_24biu7evmw = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_24biu7evmw();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./profile-edit.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./profile-edit.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { Subject, takeUntil } from 'rxjs';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { ProfileService } from '../../services/profile.service';\nimport { AuthService } from '../../../auth/services/auth.service';\ncov_24biu7evmw().s[0]++;\nlet ProfileEditComponent = class ProfileEditComponent {\n  constructor(formBuilder, profileService, authService, router, snackBar) {\n    cov_24biu7evmw().f[0]++;\n    cov_24biu7evmw().s[1]++;\n    this.formBuilder = formBuilder;\n    cov_24biu7evmw().s[2]++;\n    this.profileService = profileService;\n    cov_24biu7evmw().s[3]++;\n    this.authService = authService;\n    cov_24biu7evmw().s[4]++;\n    this.router = router;\n    cov_24biu7evmw().s[5]++;\n    this.snackBar = snackBar;\n    cov_24biu7evmw().s[6]++;\n    this.profile = null;\n    cov_24biu7evmw().s[7]++;\n    this.isLoading = true;\n    cov_24biu7evmw().s[8]++;\n    this.isSaving = false;\n    cov_24biu7evmw().s[9]++;\n    this.isUploadingProfilePhoto = false;\n    cov_24biu7evmw().s[10]++;\n    this.isUploadingCoverPhoto = false;\n    cov_24biu7evmw().s[11]++;\n    this.profilePhotoPreview = null;\n    cov_24biu7evmw().s[12]++;\n    this.coverPhotoPreview = null;\n    cov_24biu7evmw().s[13]++;\n    this.destroy$ = new Subject();\n    cov_24biu7evmw().s[14]++;\n    this.profileForm = this.createForm();\n  }\n  ngOnInit() {\n    cov_24biu7evmw().f[1]++;\n    cov_24biu7evmw().s[15]++;\n    this.loadProfile();\n  }\n  ngOnDestroy() {\n    cov_24biu7evmw().f[2]++;\n    cov_24biu7evmw().s[16]++;\n    this.destroy$.next();\n    cov_24biu7evmw().s[17]++;\n    this.destroy$.complete();\n  }\n  createForm() {\n    cov_24biu7evmw().f[3]++;\n    cov_24biu7evmw().s[18]++;\n    return this.formBuilder.group({\n      // Basic Information\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      professionalTitle: [''],\n      headline: ['', Validators.maxLength(220)],\n      summary: ['', Validators.maxLength(2000)],\n      // Location\n      location: this.formBuilder.group({\n        city: [''],\n        state: [''],\n        country: [''],\n        displayLocation: ['']\n      }),\n      // Contact Information\n      contactInfo: this.formBuilder.group({\n        email: ['', Validators.email],\n        isEmailPublic: [false],\n        website: [''],\n        portfolioUrl: [''],\n        phoneNumbers: this.formBuilder.array([]),\n        businessAddress: this.formBuilder.group({\n          street: [''],\n          city: [''],\n          state: [''],\n          postalCode: [''],\n          country: [''],\n          isPublic: [false]\n        })\n      }),\n      // Privacy Settings\n      isPublic: [true],\n      // Social Links\n      socialLinks: this.formBuilder.array([]),\n      // Skills\n      skills: this.formBuilder.array([]),\n      // Consultation Rates\n      consultationRates: this.formBuilder.group({\n        hourlyRate: [null, [Validators.min(0), Validators.max(10000)]],\n        sessionRate: [null, [Validators.min(0), Validators.max(10000)]],\n        currency: ['BGN', Validators.required]\n      }),\n      // Service Offerings\n      serviceOfferings: this.formBuilder.array([])\n    });\n  }\n  loadProfile() {\n    cov_24biu7evmw().f[4]++;\n    cov_24biu7evmw().s[19]++;\n    this.profileService.getCurrentUserProfile().pipe(takeUntil(this.destroy$)).subscribe({\n      next: profile => {\n        cov_24biu7evmw().f[5]++;\n        cov_24biu7evmw().s[20]++;\n        this.profile = profile;\n        cov_24biu7evmw().s[21]++;\n        this.populateForm(profile);\n        cov_24biu7evmw().s[22]++;\n        this.isLoading = false;\n      },\n      error: error => {\n        cov_24biu7evmw().f[6]++;\n        cov_24biu7evmw().s[23]++;\n        console.error('Error loading profile:', error);\n        cov_24biu7evmw().s[24]++;\n        this.snackBar.open('Error loading profile', 'Close', {\n          duration: 5000\n        });\n        cov_24biu7evmw().s[25]++;\n        this.router.navigate(['/dashboard']);\n        cov_24biu7evmw().s[26]++;\n        this.isLoading = false;\n      }\n    });\n  }\n  populateForm(profile) {\n    cov_24biu7evmw().f[7]++;\n    cov_24biu7evmw().s[27]++;\n    this.profileForm.patchValue({\n      firstName: profile.firstName,\n      lastName: profile.lastName,\n      professionalTitle: (cov_24biu7evmw().b[0][0]++, profile.professionalTitle) || (cov_24biu7evmw().b[0][1]++, ''),\n      headline: (cov_24biu7evmw().b[1][0]++, profile.headline) || (cov_24biu7evmw().b[1][1]++, ''),\n      summary: (cov_24biu7evmw().b[2][0]++, profile.summary) || (cov_24biu7evmw().b[2][1]++, ''),\n      location: {\n        city: (cov_24biu7evmw().b[3][0]++, profile.location?.city) || (cov_24biu7evmw().b[3][1]++, ''),\n        state: (cov_24biu7evmw().b[4][0]++, profile.location?.state) || (cov_24biu7evmw().b[4][1]++, ''),\n        country: (cov_24biu7evmw().b[5][0]++, profile.location?.country) || (cov_24biu7evmw().b[5][1]++, ''),\n        displayLocation: (cov_24biu7evmw().b[6][0]++, profile.location?.displayLocation) || (cov_24biu7evmw().b[6][1]++, '')\n      },\n      contactInfo: {\n        email: (cov_24biu7evmw().b[7][0]++, profile.contactInfo.email) || (cov_24biu7evmw().b[7][1]++, ''),\n        isEmailPublic: profile.contactInfo.isEmailPublic,\n        website: (cov_24biu7evmw().b[8][0]++, profile.contactInfo.website) || (cov_24biu7evmw().b[8][1]++, ''),\n        portfolioUrl: (cov_24biu7evmw().b[9][0]++, profile.contactInfo.portfolioUrl) || (cov_24biu7evmw().b[9][1]++, ''),\n        businessAddress: {\n          street: (cov_24biu7evmw().b[10][0]++, profile.contactInfo.businessAddress?.street) || (cov_24biu7evmw().b[10][1]++, ''),\n          city: (cov_24biu7evmw().b[11][0]++, profile.contactInfo.businessAddress?.city) || (cov_24biu7evmw().b[11][1]++, ''),\n          state: (cov_24biu7evmw().b[12][0]++, profile.contactInfo.businessAddress?.state) || (cov_24biu7evmw().b[12][1]++, ''),\n          postalCode: (cov_24biu7evmw().b[13][0]++, profile.contactInfo.businessAddress?.postalCode) || (cov_24biu7evmw().b[13][1]++, ''),\n          country: (cov_24biu7evmw().b[14][0]++, profile.contactInfo.businessAddress?.country) || (cov_24biu7evmw().b[14][1]++, ''),\n          isPublic: (cov_24biu7evmw().b[15][0]++, profile.contactInfo.businessAddress?.isPublic) || (cov_24biu7evmw().b[15][1]++, false)\n        }\n      },\n      isPublic: profile.isPublic,\n      consultationRates: {\n        hourlyRate: (cov_24biu7evmw().b[16][0]++, profile.consultationRates?.hourlyRate) || (cov_24biu7evmw().b[16][1]++, null),\n        sessionRate: (cov_24biu7evmw().b[17][0]++, profile.consultationRates?.sessionRate) || (cov_24biu7evmw().b[17][1]++, null),\n        currency: (cov_24biu7evmw().b[18][0]++, profile.consultationRates?.currency) || (cov_24biu7evmw().b[18][1]++, 'BGN')\n      }\n    });\n    // Populate phone numbers\n    cov_24biu7evmw().s[28]++;\n    this.setPhoneNumbers((cov_24biu7evmw().b[19][0]++, profile.contactInfo.phoneNumbers) || (cov_24biu7evmw().b[19][1]++, []));\n    // Populate social links\n    cov_24biu7evmw().s[29]++;\n    this.setSocialLinks((cov_24biu7evmw().b[20][0]++, profile.socialLinks) || (cov_24biu7evmw().b[20][1]++, []));\n    // Populate skills\n    cov_24biu7evmw().s[30]++;\n    this.setSkills((cov_24biu7evmw().b[21][0]++, profile.skills) || (cov_24biu7evmw().b[21][1]++, []));\n    // Populate service offerings\n    cov_24biu7evmw().s[31]++;\n    this.setServiceOfferings((cov_24biu7evmw().b[22][0]++, profile.serviceOfferings) || (cov_24biu7evmw().b[22][1]++, []));\n  }\n  // Phone Numbers Management\n  get phoneNumbers() {\n    cov_24biu7evmw().f[8]++;\n    cov_24biu7evmw().s[32]++;\n    return this.profileForm.get('contactInfo.phoneNumbers');\n  }\n  setPhoneNumbers(phones) {\n    cov_24biu7evmw().f[9]++;\n    const phoneArray = (cov_24biu7evmw().s[33]++, this.phoneNumbers);\n    cov_24biu7evmw().s[34]++;\n    phoneArray.clear();\n    cov_24biu7evmw().s[35]++;\n    phones.forEach(phone => {\n      cov_24biu7evmw().f[10]++;\n      cov_24biu7evmw().s[36]++;\n      phoneArray.push(this.formBuilder.group({\n        id: [phone.id],\n        number: [phone.number, Validators.required],\n        type: [phone.type, Validators.required],\n        isPublic: [phone.isPublic],\n        isPrimary: [phone.isPrimary]\n      }));\n    });\n  }\n  addPhoneNumber() {\n    cov_24biu7evmw().f[11]++;\n    const phoneGroup = (cov_24biu7evmw().s[37]++, this.formBuilder.group({\n      id: [null],\n      number: ['', Validators.required],\n      type: ['mobile', Validators.required],\n      isPublic: [false],\n      isPrimary: [false]\n    }));\n    cov_24biu7evmw().s[38]++;\n    this.phoneNumbers.push(phoneGroup);\n  }\n  removePhoneNumber(index) {\n    cov_24biu7evmw().f[12]++;\n    cov_24biu7evmw().s[39]++;\n    this.phoneNumbers.removeAt(index);\n  }\n  // Social Links Management\n  get socialLinks() {\n    cov_24biu7evmw().f[13]++;\n    cov_24biu7evmw().s[40]++;\n    return this.profileForm.get('socialLinks');\n  }\n  setSocialLinks(links) {\n    cov_24biu7evmw().f[14]++;\n    const linksArray = (cov_24biu7evmw().s[41]++, this.socialLinks);\n    cov_24biu7evmw().s[42]++;\n    linksArray.clear();\n    cov_24biu7evmw().s[43]++;\n    links.forEach(link => {\n      cov_24biu7evmw().f[15]++;\n      cov_24biu7evmw().s[44]++;\n      linksArray.push(this.formBuilder.group({\n        id: [link.id],\n        platform: [link.platform, Validators.required],\n        url: [link.url, [Validators.required, Validators.pattern('https?://.+')]],\n        displayName: [link.displayName],\n        isPublic: [link.isPublic]\n      }));\n    });\n  }\n  addSocialLink() {\n    cov_24biu7evmw().f[16]++;\n    const linkGroup = (cov_24biu7evmw().s[45]++, this.formBuilder.group({\n      id: [null],\n      platform: ['linkedin', Validators.required],\n      url: ['', [Validators.required, Validators.pattern('https?://.+')]],\n      displayName: [''],\n      isPublic: [true]\n    }));\n    cov_24biu7evmw().s[46]++;\n    this.socialLinks.push(linkGroup);\n  }\n  removeSocialLink(index) {\n    cov_24biu7evmw().f[17]++;\n    cov_24biu7evmw().s[47]++;\n    this.socialLinks.removeAt(index);\n  }\n  // Skills Management\n  get skills() {\n    cov_24biu7evmw().f[18]++;\n    cov_24biu7evmw().s[48]++;\n    return this.profileForm.get('skills');\n  }\n  setSkills(skills) {\n    cov_24biu7evmw().f[19]++;\n    const skillArray = (cov_24biu7evmw().s[49]++, this.skills);\n    cov_24biu7evmw().s[50]++;\n    skillArray.clear();\n    cov_24biu7evmw().s[51]++;\n    skills.forEach(skill => {\n      cov_24biu7evmw().f[20]++;\n      cov_24biu7evmw().s[52]++;\n      skillArray.push(this.formBuilder.group({\n        id: [skill.id],\n        name: [skill.name, Validators.required],\n        category: [(cov_24biu7evmw().b[23][0]++, skill.category) || (cov_24biu7evmw().b[23][1]++, '')],\n        proficiencyLevel: [(cov_24biu7evmw().b[24][0]++, skill.proficiencyLevel) || (cov_24biu7evmw().b[24][1]++, 'intermediate'), Validators.required]\n      }));\n    });\n  }\n  addSkill() {\n    cov_24biu7evmw().f[21]++;\n    const skillGroup = (cov_24biu7evmw().s[53]++, this.formBuilder.group({\n      id: [null],\n      name: ['', Validators.required],\n      category: [''],\n      proficiencyLevel: ['intermediate', Validators.required]\n    }));\n    cov_24biu7evmw().s[54]++;\n    this.skills.push(skillGroup);\n  }\n  removeSkill(index) {\n    cov_24biu7evmw().f[22]++;\n    cov_24biu7evmw().s[55]++;\n    this.skills.removeAt(index);\n  }\n  // Service Offerings Management\n  get serviceOfferings() {\n    cov_24biu7evmw().f[23]++;\n    cov_24biu7evmw().s[56]++;\n    return this.profileForm.get('serviceOfferings');\n  }\n  setServiceOfferings(services) {\n    cov_24biu7evmw().f[24]++;\n    const serviceArray = (cov_24biu7evmw().s[57]++, this.serviceOfferings);\n    cov_24biu7evmw().s[58]++;\n    serviceArray.clear();\n    cov_24biu7evmw().s[59]++;\n    services.forEach(service => {\n      cov_24biu7evmw().f[25]++;\n      cov_24biu7evmw().s[60]++;\n      serviceArray.push(this.formBuilder.group({\n        id: [service.id],\n        name: [service.name, Validators.required],\n        description: [service.description, Validators.required],\n        price: [service.price, [Validators.required, Validators.min(0)]],\n        currency: [(cov_24biu7evmw().b[25][0]++, service.currency) || (cov_24biu7evmw().b[25][1]++, 'BGN'), Validators.required],\n        duration: [service.duration],\n        category: [(cov_24biu7evmw().b[26][0]++, service.category) || (cov_24biu7evmw().b[26][1]++, '')],\n        isActive: [service.isActive !== false] // default to true\n      }));\n    });\n  }\n\n  addServiceOffering() {\n    cov_24biu7evmw().f[26]++;\n    const serviceGroup = (cov_24biu7evmw().s[61]++, this.formBuilder.group({\n      id: [null],\n      name: ['', Validators.required],\n      description: ['', Validators.required],\n      price: [0, [Validators.required, Validators.min(0)]],\n      currency: ['BGN', Validators.required],\n      duration: [60],\n      category: [''],\n      isActive: [true]\n    }));\n    cov_24biu7evmw().s[62]++;\n    this.serviceOfferings.push(serviceGroup);\n  }\n  removeServiceOffering(index) {\n    cov_24biu7evmw().f[27]++;\n    cov_24biu7evmw().s[63]++;\n    this.serviceOfferings.removeAt(index);\n  }\n  // Form Submission\n  onSubmit() {\n    cov_24biu7evmw().f[28]++;\n    cov_24biu7evmw().s[64]++;\n    if (this.validateForm()) {\n      cov_24biu7evmw().b[27][0]++;\n      cov_24biu7evmw().s[65]++;\n      this.isSaving = true;\n      const formValue = (cov_24biu7evmw().s[66]++, this.profileForm.value);\n      const updateRequest = (cov_24biu7evmw().s[67]++, {\n        firstName: formValue.firstName,\n        lastName: formValue.lastName,\n        professionalTitle: formValue.professionalTitle,\n        headline: formValue.headline,\n        location: formValue.location,\n        contactInfo: formValue.contactInfo,\n        summary: formValue.summary,\n        isPublic: formValue.isPublic,\n        skills: (cov_24biu7evmw().b[28][0]++, formValue.skills) || (cov_24biu7evmw().b[28][1]++, []),\n        consultationRates: formValue.consultationRates,\n        serviceOfferings: (cov_24biu7evmw().b[29][0]++, formValue.serviceOfferings) || (cov_24biu7evmw().b[29][1]++, [])\n      });\n      cov_24biu7evmw().s[68]++;\n      this.profileService.updateProfile(updateRequest).pipe(takeUntil(this.destroy$)).subscribe({\n        next: updatedProfile => {\n          cov_24biu7evmw().f[29]++;\n          cov_24biu7evmw().s[69]++;\n          this.isSaving = false;\n          cov_24biu7evmw().s[70]++;\n          this.snackBar.open('Profile updated successfully!', 'Close', {\n            duration: 3000\n          });\n          cov_24biu7evmw().s[71]++;\n          this.router.navigate(['/profile', updatedProfile.slug]);\n        },\n        error: error => {\n          cov_24biu7evmw().f[30]++;\n          cov_24biu7evmw().s[72]++;\n          this.isSaving = false;\n          cov_24biu7evmw().s[73]++;\n          console.error('Error updating profile:', error);\n          cov_24biu7evmw().s[74]++;\n          this.snackBar.open('Error updating profile. Please try again.', 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    } else {\n      cov_24biu7evmw().b[27][1]++;\n    }\n    // Validation is now handled in validateForm() method\n  }\n\n  onCancel() {\n    cov_24biu7evmw().f[31]++;\n    cov_24biu7evmw().s[75]++;\n    if (this.profile) {\n      cov_24biu7evmw().b[30][0]++;\n      cov_24biu7evmw().s[76]++;\n      this.router.navigate(['/profile', this.profile.slug]);\n    } else {\n      cov_24biu7evmw().b[30][1]++;\n      cov_24biu7evmw().s[77]++;\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  // File Upload Methods\n  onProfilePhotoSelected(event) {\n    cov_24biu7evmw().f[32]++;\n    const file = (cov_24biu7evmw().s[78]++, event.target.files[0]);\n    cov_24biu7evmw().s[79]++;\n    if (file) {\n      cov_24biu7evmw().b[31][0]++;\n      cov_24biu7evmw().s[80]++;\n      if (this.validateImageFile(file, 'profile')) {\n        cov_24biu7evmw().b[32][0]++;\n        cov_24biu7evmw().s[81]++;\n        this.createImagePreview(file, 'profile');\n        cov_24biu7evmw().s[82]++;\n        this.uploadProfilePhoto(file);\n      } else {\n        cov_24biu7evmw().b[32][1]++;\n      }\n    } else {\n      cov_24biu7evmw().b[31][1]++;\n    }\n    // Reset the input to allow selecting the same file again\n    cov_24biu7evmw().s[83]++;\n    event.target.value = '';\n  }\n  onCoverPhotoSelected(event) {\n    cov_24biu7evmw().f[33]++;\n    const file = (cov_24biu7evmw().s[84]++, event.target.files[0]);\n    cov_24biu7evmw().s[85]++;\n    if (file) {\n      cov_24biu7evmw().b[33][0]++;\n      cov_24biu7evmw().s[86]++;\n      if (this.validateImageFile(file, 'cover')) {\n        cov_24biu7evmw().b[34][0]++;\n        cov_24biu7evmw().s[87]++;\n        this.createImagePreview(file, 'cover');\n        cov_24biu7evmw().s[88]++;\n        this.uploadCoverPhoto(file);\n      } else {\n        cov_24biu7evmw().b[34][1]++;\n      }\n    } else {\n      cov_24biu7evmw().b[33][1]++;\n    }\n    // Reset the input to allow selecting the same file again\n    cov_24biu7evmw().s[89]++;\n    event.target.value = '';\n  }\n  validateImageFile(file, type) {\n    cov_24biu7evmw().f[34]++;\n    // Check file type\n    const allowedTypes = (cov_24biu7evmw().s[90]++, ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']);\n    cov_24biu7evmw().s[91]++;\n    if (!allowedTypes.includes(file.type)) {\n      cov_24biu7evmw().b[35][0]++;\n      cov_24biu7evmw().s[92]++;\n      this.snackBar.open('Please select a valid image file (JPEG, PNG, GIF, or WebP)', 'Close', {\n        duration: 5000\n      });\n      cov_24biu7evmw().s[93]++;\n      return false;\n    } else {\n      cov_24biu7evmw().b[35][1]++;\n    }\n    // Check file size (5MB for profile, 10MB for cover)\n    const maxSize = (cov_24biu7evmw().s[94]++, type === 'profile' ? (cov_24biu7evmw().b[36][0]++, 5 * 1024 * 1024) : (cov_24biu7evmw().b[36][1]++, 10 * 1024 * 1024));\n    cov_24biu7evmw().s[95]++;\n    if (file.size > maxSize) {\n      cov_24biu7evmw().b[37][0]++;\n      const maxSizeMB = (cov_24biu7evmw().s[96]++, maxSize / (1024 * 1024));\n      cov_24biu7evmw().s[97]++;\n      this.snackBar.open(`File size must be less than ${maxSizeMB}MB`, 'Close', {\n        duration: 5000\n      });\n      cov_24biu7evmw().s[98]++;\n      return false;\n    } else {\n      cov_24biu7evmw().b[37][1]++;\n    }\n    cov_24biu7evmw().s[99]++;\n    return true;\n  }\n  createImagePreview(file, type) {\n    cov_24biu7evmw().f[35]++;\n    const reader = (cov_24biu7evmw().s[100]++, new FileReader());\n    cov_24biu7evmw().s[101]++;\n    reader.onload = e => {\n      cov_24biu7evmw().f[36]++;\n      cov_24biu7evmw().s[102]++;\n      if (type === 'profile') {\n        cov_24biu7evmw().b[38][0]++;\n        cov_24biu7evmw().s[103]++;\n        this.profilePhotoPreview = e.target.result;\n      } else {\n        cov_24biu7evmw().b[38][1]++;\n        cov_24biu7evmw().s[104]++;\n        this.coverPhotoPreview = e.target.result;\n      }\n    };\n    cov_24biu7evmw().s[105]++;\n    reader.readAsDataURL(file);\n  }\n  uploadProfilePhoto(file) {\n    cov_24biu7evmw().f[37]++;\n    cov_24biu7evmw().s[106]++;\n    this.isUploadingProfilePhoto = true;\n    cov_24biu7evmw().s[107]++;\n    this.profileService.uploadProfilePhoto(file).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        cov_24biu7evmw().f[38]++;\n        cov_24biu7evmw().s[108]++;\n        this.isUploadingProfilePhoto = false;\n        cov_24biu7evmw().s[109]++;\n        if (this.profile) {\n          cov_24biu7evmw().b[39][0]++;\n          cov_24biu7evmw().s[110]++;\n          this.profile.profilePhotoUrl = response.url;\n        } else {\n          cov_24biu7evmw().b[39][1]++;\n        }\n        cov_24biu7evmw().s[111]++;\n        this.profilePhotoPreview = null; // Clear preview since we have the actual URL\n        cov_24biu7evmw().s[112]++;\n        this.snackBar.open('Profile photo updated successfully!', 'Close', {\n          duration: 3000\n        });\n      },\n      error: error => {\n        cov_24biu7evmw().f[39]++;\n        cov_24biu7evmw().s[113]++;\n        this.isUploadingProfilePhoto = false;\n        cov_24biu7evmw().s[114]++;\n        this.profilePhotoPreview = null; // Clear preview on error\n        cov_24biu7evmw().s[115]++;\n        console.error('Error uploading profile photo:', error);\n        const errorMessage = (cov_24biu7evmw().s[116]++, (cov_24biu7evmw().b[40][0]++, error.error?.message) || (cov_24biu7evmw().b[40][1]++, 'Error uploading profile photo. Please try again.'));\n        cov_24biu7evmw().s[117]++;\n        this.snackBar.open(errorMessage, 'Close', {\n          duration: 5000\n        });\n      }\n    });\n  }\n  uploadCoverPhoto(file) {\n    cov_24biu7evmw().f[40]++;\n    cov_24biu7evmw().s[118]++;\n    this.isUploadingCoverPhoto = true;\n    cov_24biu7evmw().s[119]++;\n    this.profileService.uploadCoverPhoto(file).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        cov_24biu7evmw().f[41]++;\n        cov_24biu7evmw().s[120]++;\n        this.isUploadingCoverPhoto = false;\n        cov_24biu7evmw().s[121]++;\n        if (this.profile) {\n          cov_24biu7evmw().b[41][0]++;\n          cov_24biu7evmw().s[122]++;\n          this.profile.coverPhotoUrl = response.url;\n        } else {\n          cov_24biu7evmw().b[41][1]++;\n        }\n        cov_24biu7evmw().s[123]++;\n        this.coverPhotoPreview = null; // Clear preview since we have the actual URL\n        cov_24biu7evmw().s[124]++;\n        this.snackBar.open('Cover photo updated successfully!', 'Close', {\n          duration: 3000\n        });\n      },\n      error: error => {\n        cov_24biu7evmw().f[42]++;\n        cov_24biu7evmw().s[125]++;\n        this.isUploadingCoverPhoto = false;\n        cov_24biu7evmw().s[126]++;\n        this.coverPhotoPreview = null; // Clear preview on error\n        cov_24biu7evmw().s[127]++;\n        console.error('Error uploading cover photo:', error);\n        const errorMessage = (cov_24biu7evmw().s[128]++, (cov_24biu7evmw().b[42][0]++, error.error?.message) || (cov_24biu7evmw().b[42][1]++, 'Error uploading cover photo. Please try again.'));\n        cov_24biu7evmw().s[129]++;\n        this.snackBar.open(errorMessage, 'Close', {\n          duration: 5000\n        });\n      }\n    });\n  }\n  // Utility Methods\n  markFormGroupTouched() {\n    cov_24biu7evmw().f[43]++;\n    cov_24biu7evmw().s[130]++;\n    Object.keys(this.profileForm.controls).forEach(key => {\n      cov_24biu7evmw().f[44]++;\n      const control = (cov_24biu7evmw().s[131]++, this.profileForm.get(key));\n      cov_24biu7evmw().s[132]++;\n      control?.markAsTouched();\n      cov_24biu7evmw().s[133]++;\n      if (control instanceof FormArray) {\n        cov_24biu7evmw().b[43][0]++;\n        cov_24biu7evmw().s[134]++;\n        control.controls.forEach(arrayControl => {\n          cov_24biu7evmw().f[45]++;\n          cov_24biu7evmw().s[135]++;\n          if (arrayControl instanceof FormGroup) {\n            cov_24biu7evmw().b[44][0]++;\n            cov_24biu7evmw().s[136]++;\n            Object.keys(arrayControl.controls).forEach(arrayKey => {\n              cov_24biu7evmw().f[46]++;\n              cov_24biu7evmw().s[137]++;\n              arrayControl.get(arrayKey)?.markAsTouched();\n            });\n          } else {\n            cov_24biu7evmw().b[44][1]++;\n          }\n        });\n      } else {\n        cov_24biu7evmw().b[43][1]++;\n      }\n    });\n  }\n  getErrorMessage(fieldName) {\n    cov_24biu7evmw().f[47]++;\n    const control = (cov_24biu7evmw().s[138]++, this.profileForm.get(fieldName));\n    cov_24biu7evmw().s[139]++;\n    if ((cov_24biu7evmw().b[46][0]++, !control) || (cov_24biu7evmw().b[46][1]++, !control.errors)) {\n      cov_24biu7evmw().b[45][0]++;\n      cov_24biu7evmw().s[140]++;\n      return '';\n    } else {\n      cov_24biu7evmw().b[45][1]++;\n    }\n    const fieldDisplayName = (cov_24biu7evmw().s[141]++, this.getFieldDisplayName(fieldName));\n    cov_24biu7evmw().s[142]++;\n    if (control.hasError('required')) {\n      cov_24biu7evmw().b[47][0]++;\n      cov_24biu7evmw().s[143]++;\n      return `${fieldDisplayName} is required`;\n    } else {\n      cov_24biu7evmw().b[47][1]++;\n    }\n    cov_24biu7evmw().s[144]++;\n    if (control.hasError('email')) {\n      cov_24biu7evmw().b[48][0]++;\n      cov_24biu7evmw().s[145]++;\n      return 'Please enter a valid email address';\n    } else {\n      cov_24biu7evmw().b[48][1]++;\n    }\n    cov_24biu7evmw().s[146]++;\n    if (control.hasError('minlength')) {\n      cov_24biu7evmw().b[49][0]++;\n      const requiredLength = (cov_24biu7evmw().s[147]++, control.errors['minlength'].requiredLength);\n      cov_24biu7evmw().s[148]++;\n      return `${fieldDisplayName} must be at least ${requiredLength} characters`;\n    } else {\n      cov_24biu7evmw().b[49][1]++;\n    }\n    cov_24biu7evmw().s[149]++;\n    if (control.hasError('maxlength')) {\n      cov_24biu7evmw().b[50][0]++;\n      const maxLength = (cov_24biu7evmw().s[150]++, control.errors['maxlength'].requiredLength);\n      cov_24biu7evmw().s[151]++;\n      return `${fieldDisplayName} must be no more than ${maxLength} characters`;\n    } else {\n      cov_24biu7evmw().b[50][1]++;\n    }\n    cov_24biu7evmw().s[152]++;\n    if (control.hasError('pattern')) {\n      cov_24biu7evmw().b[51][0]++;\n      cov_24biu7evmw().s[153]++;\n      return 'Please enter a valid URL';\n    } else {\n      cov_24biu7evmw().b[51][1]++;\n    }\n    cov_24biu7evmw().s[154]++;\n    if (control.hasError('min')) {\n      cov_24biu7evmw().b[52][0]++;\n      const minValue = (cov_24biu7evmw().s[155]++, control.errors['min'].min);\n      cov_24biu7evmw().s[156]++;\n      return `${fieldDisplayName} must be at least ${minValue}`;\n    } else {\n      cov_24biu7evmw().b[52][1]++;\n    }\n    cov_24biu7evmw().s[157]++;\n    if (control.hasError('max')) {\n      cov_24biu7evmw().b[53][0]++;\n      const maxValue = (cov_24biu7evmw().s[158]++, control.errors['max'].max);\n      cov_24biu7evmw().s[159]++;\n      return `${fieldDisplayName} cannot exceed ${maxValue}`;\n    } else {\n      cov_24biu7evmw().b[53][1]++;\n    }\n    cov_24biu7evmw().s[160]++;\n    return '';\n  }\n  getFieldDisplayName(fieldName) {\n    cov_24biu7evmw().f[48]++;\n    const fieldNames = (cov_24biu7evmw().s[161]++, {\n      'firstName': 'First Name',\n      'lastName': 'Last Name',\n      'professionalTitle': 'Professional Title',\n      'headline': 'Headline',\n      'summary': 'Summary',\n      'contactInfo.email': 'Email',\n      'contactInfo.website': 'Website',\n      'contactInfo.portfolioUrl': 'Portfolio URL',\n      'location.city': 'City',\n      'location.state': 'State',\n      'location.country': 'Country',\n      'location.displayLocation': 'Display Location',\n      'consultationRates.hourlyRate': 'Hourly Rate',\n      'consultationRates.sessionRate': 'Session Rate',\n      'consultationRates.currency': 'Currency'\n    });\n    cov_24biu7evmw().s[162]++;\n    return (cov_24biu7evmw().b[54][0]++, fieldNames[fieldName]) || (cov_24biu7evmw().b[54][1]++, fieldName);\n  }\n  // Enhanced form validation\n  validateForm() {\n    cov_24biu7evmw().f[49]++;\n    cov_24biu7evmw().s[163]++;\n    if (this.profileForm.invalid) {\n      cov_24biu7evmw().b[55][0]++;\n      cov_24biu7evmw().s[164]++;\n      this.markFormGroupTouched();\n      // Find first invalid field and focus on it\n      const firstInvalidField = (cov_24biu7evmw().s[165]++, this.findFirstInvalidField());\n      cov_24biu7evmw().s[166]++;\n      if (firstInvalidField) {\n        cov_24biu7evmw().b[56][0]++;\n        cov_24biu7evmw().s[167]++;\n        firstInvalidField.focus();\n      } else {\n        cov_24biu7evmw().b[56][1]++;\n      }\n      // Show specific error message\n      const errors = (cov_24biu7evmw().s[168]++, this.getFormErrors());\n      cov_24biu7evmw().s[169]++;\n      if (errors.length > 0) {\n        cov_24biu7evmw().b[57][0]++;\n        cov_24biu7evmw().s[170]++;\n        this.snackBar.open(`Please fix the following errors: ${errors.join(', ')}`, 'Close', {\n          duration: 5000\n        });\n      } else {\n        cov_24biu7evmw().b[57][1]++;\n      }\n      cov_24biu7evmw().s[171]++;\n      return false;\n    } else {\n      cov_24biu7evmw().b[55][1]++;\n    }\n    cov_24biu7evmw().s[172]++;\n    return true;\n  }\n  findFirstInvalidField() {\n    cov_24biu7evmw().f[50]++;\n    const invalidFields = (cov_24biu7evmw().s[173]++, document.querySelectorAll('.mat-form-field.ng-invalid input, .mat-form-field.ng-invalid textarea, .mat-form-field.ng-invalid mat-select'));\n    cov_24biu7evmw().s[174]++;\n    return invalidFields.length > 0 ? (cov_24biu7evmw().b[58][0]++, invalidFields[0]) : (cov_24biu7evmw().b[58][1]++, null);\n  }\n  getFormErrors() {\n    cov_24biu7evmw().f[51]++;\n    const errors = (cov_24biu7evmw().s[175]++, []);\n    // Check main form fields\n    cov_24biu7evmw().s[176]++;\n    Object.keys(this.profileForm.controls).forEach(key => {\n      cov_24biu7evmw().f[52]++;\n      const control = (cov_24biu7evmw().s[177]++, this.profileForm.get(key));\n      cov_24biu7evmw().s[178]++;\n      if ((cov_24biu7evmw().b[60][0]++, control) && (cov_24biu7evmw().b[60][1]++, control.invalid) && (cov_24biu7evmw().b[60][2]++, control.touched)) {\n        cov_24biu7evmw().b[59][0]++;\n        const errorMessage = (cov_24biu7evmw().s[179]++, this.getErrorMessage(key));\n        cov_24biu7evmw().s[180]++;\n        if (errorMessage) {\n          cov_24biu7evmw().b[61][0]++;\n          cov_24biu7evmw().s[181]++;\n          errors.push(errorMessage);\n        } else {\n          cov_24biu7evmw().b[61][1]++;\n        }\n      } else {\n        cov_24biu7evmw().b[59][1]++;\n      }\n    });\n    // Check nested form groups\n    const contactInfo = (cov_24biu7evmw().s[182]++, this.profileForm.get('contactInfo'));\n    cov_24biu7evmw().s[183]++;\n    if ((cov_24biu7evmw().b[63][0]++, contactInfo) && (cov_24biu7evmw().b[63][1]++, contactInfo.invalid)) {\n      cov_24biu7evmw().b[62][0]++;\n      cov_24biu7evmw().s[184]++;\n      Object.keys(contactInfo.controls).forEach(key => {\n        cov_24biu7evmw().f[53]++;\n        const control = (cov_24biu7evmw().s[185]++, contactInfo.get(key));\n        cov_24biu7evmw().s[186]++;\n        if ((cov_24biu7evmw().b[65][0]++, control) && (cov_24biu7evmw().b[65][1]++, control.invalid) && (cov_24biu7evmw().b[65][2]++, control.touched)) {\n          cov_24biu7evmw().b[64][0]++;\n          const errorMessage = (cov_24biu7evmw().s[187]++, this.getErrorMessage(`contactInfo.${key}`));\n          cov_24biu7evmw().s[188]++;\n          if (errorMessage) {\n            cov_24biu7evmw().b[66][0]++;\n            cov_24biu7evmw().s[189]++;\n            errors.push(errorMessage);\n          } else {\n            cov_24biu7evmw().b[66][1]++;\n          }\n        } else {\n          cov_24biu7evmw().b[64][1]++;\n        }\n      });\n    } else {\n      cov_24biu7evmw().b[62][1]++;\n    }\n    cov_24biu7evmw().s[190]++;\n    return errors.slice(0, 3); // Limit to first 3 errors to avoid overwhelming the user\n  }\n  // Platform options for social links\n  getPlatformOptions() {\n    cov_24biu7evmw().f[54]++;\n    cov_24biu7evmw().s[191]++;\n    return [{\n      value: 'linkedin',\n      label: 'LinkedIn'\n    }, {\n      value: 'twitter',\n      label: 'Twitter'\n    }, {\n      value: 'github',\n      label: 'GitHub'\n    }, {\n      value: 'behance',\n      label: 'Behance'\n    }, {\n      value: 'dribbble',\n      label: 'Dribbble'\n    }, {\n      value: 'instagram',\n      label: 'Instagram'\n    }, {\n      value: 'facebook',\n      label: 'Facebook'\n    }, {\n      value: 'youtube',\n      label: 'YouTube'\n    }, {\n      value: 'other',\n      label: 'Other'\n    }];\n  }\n  // Phone type options\n  getPhoneTypeOptions() {\n    cov_24biu7evmw().f[55]++;\n    cov_24biu7evmw().s[192]++;\n    return [{\n      value: 'mobile',\n      label: 'Mobile'\n    }, {\n      value: 'business',\n      label: 'Business'\n    }, {\n      value: 'home',\n      label: 'Home'\n    }];\n  }\n  // Skill category options\n  getSkillCategoryOptions() {\n    cov_24biu7evmw().f[56]++;\n    cov_24biu7evmw().s[193]++;\n    return [{\n      value: 'Astrology',\n      label: 'Astrology'\n    }, {\n      value: 'Crystal Healing',\n      label: 'Crystal Healing'\n    }, {\n      value: 'Palmistry',\n      label: 'Palmistry'\n    }, {\n      value: 'Spiritual Counseling',\n      label: 'Spiritual Counseling'\n    }, {\n      value: 'Numerology',\n      label: 'Numerology'\n    }, {\n      value: 'Tarot Reading',\n      label: 'Tarot Reading'\n    }, {\n      value: 'Energy Healing',\n      label: 'Energy Healing'\n    }, {\n      value: 'Meditation',\n      label: 'Meditation'\n    }, {\n      value: 'Other',\n      label: 'Other'\n    }];\n  }\n  // Proficiency level options\n  getProficiencyLevelOptions() {\n    cov_24biu7evmw().f[57]++;\n    cov_24biu7evmw().s[194]++;\n    return [{\n      value: 'beginner',\n      label: 'Beginner'\n    }, {\n      value: 'intermediate',\n      label: 'Intermediate'\n    }, {\n      value: 'advanced',\n      label: 'Advanced'\n    }, {\n      value: 'expert',\n      label: 'Expert'\n    }];\n  }\n  // Service category options\n  getServiceCategoryOptions() {\n    cov_24biu7evmw().f[58]++;\n    cov_24biu7evmw().s[195]++;\n    return [{\n      value: 'Reading',\n      label: 'Reading'\n    }, {\n      value: 'Consultation',\n      label: 'Consultation'\n    }, {\n      value: 'Healing',\n      label: 'Healing'\n    }, {\n      value: 'Workshop',\n      label: 'Workshop'\n    }, {\n      value: 'Course',\n      label: 'Course'\n    }, {\n      value: 'Other',\n      label: 'Other'\n    }];\n  }\n  // Currency options\n  getCurrencyOptions() {\n    cov_24biu7evmw().f[59]++;\n    cov_24biu7evmw().s[196]++;\n    return [{\n      value: 'BGN',\n      label: 'BGN (Bulgarian Lev)'\n    }, {\n      value: 'EUR',\n      label: 'EUR (Euro)'\n    }, {\n      value: 'USD',\n      label: 'USD (US Dollar)'\n    }, {\n      value: 'GBP',\n      label: 'GBP (British Pound)'\n    }];\n  }\n  static {\n    cov_24biu7evmw().s[197]++;\n    this.ctorParameters = () => {\n      cov_24biu7evmw().f[60]++;\n      cov_24biu7evmw().s[198]++;\n      return [{\n        type: FormBuilder\n      }, {\n        type: ProfileService\n      }, {\n        type: AuthService\n      }, {\n        type: Router\n      }, {\n        type: MatSnackBar\n      }];\n    };\n  }\n};\ncov_24biu7evmw().s[199]++;\nProfileEditComponent = __decorate([Component({\n  selector: 'app-profile-edit',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ProfileEditComponent);\nexport { ProfileEditComponent };", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA+BY;IAAA;MAAA;IAAA;EAAA;EAAA;AAAA;AAAA;;;;AA/BZ,SAASA,SAAS,QAA2B,eAAe;AAC5D,SAASC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAC9E,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAASC,WAAW,QAAQ,6BAA6B;AAEzD,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,WAAW,QAAQ,qCAAqC;AAAC;AAQ3D,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAY/BC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAAA;IAAA;IAJrB,gBAAW,GAAXJ,WAAW;IAAa;IACxB,mBAAc,GAAdC,cAAc;IAAgB;IAC9B,gBAAW,GAAXC,WAAW;IAAa;IACxB,WAAM,GAANC,MAAM;IAAQ;IACd,aAAQ,GAARC,QAAQ;IAAa;IAf/B,YAAO,GAAuB,IAAI;IAAC;IACnC,cAAS,GAAG,IAAI;IAAC;IACjB,aAAQ,GAAG,KAAK;IAAC;IACjB,4BAAuB,GAAG,KAAK;IAAC;IAChC,0BAAqB,GAAG,KAAK;IAAC;IAC9B,wBAAmB,GAAkB,IAAI;IAAC;IAC1C,sBAAiB,GAAkB,IAAI;IAAC;IAEhC,aAAQ,GAAG,IAAIX,OAAO,EAAQ;IAAC;IASrC,IAAI,CAACY,WAAW,GAAG,IAAI,CAACC,UAAU,EAAE;EACtC;EAEAC,QAAQ;IAAA;IAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,WAAW;IAAA;IAAA;IACT,IAAI,CAACC,QAAQ,CAACC,IAAI,EAAE;IAAC;IACrB,IAAI,CAACD,QAAQ,CAACE,QAAQ,EAAE;EAC1B;EAEQN,UAAU;IAAA;IAAA;IAChB,OAAO,IAAI,CAACN,WAAW,CAACa,KAAK,CAAC;MAC5B;MACAC,SAAS,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACyB,QAAQ,EAAEzB,UAAU,CAAC0B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC3B,UAAU,CAACyB,QAAQ,EAAEzB,UAAU,CAAC0B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,iBAAiB,EAAE,CAAC,EAAE,CAAC;MACvBC,QAAQ,EAAE,CAAC,EAAE,EAAE7B,UAAU,CAAC8B,SAAS,CAAC,GAAG,CAAC,CAAC;MACzCC,OAAO,EAAE,CAAC,EAAE,EAAE/B,UAAU,CAAC8B,SAAS,CAAC,IAAI,CAAC,CAAC;MAEzC;MACAE,QAAQ,EAAE,IAAI,CAACtB,WAAW,CAACa,KAAK,CAAC;QAC/BU,IAAI,EAAE,CAAC,EAAE,CAAC;QACVC,KAAK,EAAE,CAAC,EAAE,CAAC;QACXC,OAAO,EAAE,CAAC,EAAE,CAAC;QACbC,eAAe,EAAE,CAAC,EAAE;OACrB,CAAC;MAEF;MACAC,WAAW,EAAE,IAAI,CAAC3B,WAAW,CAACa,KAAK,CAAC;QAClCe,KAAK,EAAE,CAAC,EAAE,EAAEtC,UAAU,CAACsC,KAAK,CAAC;QAC7BC,aAAa,EAAE,CAAC,KAAK,CAAC;QACtBC,OAAO,EAAE,CAAC,EAAE,CAAC;QACbC,YAAY,EAAE,CAAC,EAAE,CAAC;QAClBC,YAAY,EAAE,IAAI,CAAChC,WAAW,CAACiC,KAAK,CAAC,EAAE,CAAC;QACxCC,eAAe,EAAE,IAAI,CAAClC,WAAW,CAACa,KAAK,CAAC;UACtCsB,MAAM,EAAE,CAAC,EAAE,CAAC;UACZZ,IAAI,EAAE,CAAC,EAAE,CAAC;UACVC,KAAK,EAAE,CAAC,EAAE,CAAC;UACXY,UAAU,EAAE,CAAC,EAAE,CAAC;UAChBX,OAAO,EAAE,CAAC,EAAE,CAAC;UACbY,QAAQ,EAAE,CAAC,KAAK;SACjB;OACF,CAAC;MAEF;MACAA,QAAQ,EAAE,CAAC,IAAI,CAAC;MAEhB;MACAC,WAAW,EAAE,IAAI,CAACtC,WAAW,CAACiC,KAAK,CAAC,EAAE,CAAC;MAEvC;MACAM,MAAM,EAAE,IAAI,CAACvC,WAAW,CAACiC,KAAK,CAAC,EAAE,CAAC;MAElC;MACAO,iBAAiB,EAAE,IAAI,CAACxC,WAAW,CAACa,KAAK,CAAC;QACxC4B,UAAU,EAAE,CAAC,IAAI,EAAE,CAACnD,UAAU,CAACoD,GAAG,CAAC,CAAC,CAAC,EAAEpD,UAAU,CAACqD,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9DC,WAAW,EAAE,CAAC,IAAI,EAAE,CAACtD,UAAU,CAACoD,GAAG,CAAC,CAAC,CAAC,EAAEpD,UAAU,CAACqD,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAC/DE,QAAQ,EAAE,CAAC,KAAK,EAAEvD,UAAU,CAACyB,QAAQ;OACtC,CAAC;MAEF;MACA+B,gBAAgB,EAAE,IAAI,CAAC9C,WAAW,CAACiC,KAAK,CAAC,EAAE;KAC5C,CAAC;EACJ;EAEQzB,WAAW;IAAA;IAAA;IACjB,IAAI,CAACP,cAAc,CAAC8C,qBAAqB,EAAE,CACxCC,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAC9BuC,SAAS,CAAC;MACTtC,IAAI,EAAGuC,OAAO,IAAI;QAAA;QAAA;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;QAAC;QACvB,IAAI,CAACC,YAAY,CAACD,OAAO,CAAC;QAAC;QAC3B,IAAI,CAACE,SAAS,GAAG,KAAK;MACxB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QAAA;QAAA;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAAC;QAC/C,IAAI,CAACjD,QAAQ,CAACmD,IAAI,CAAC,uBAAuB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAAC;QACzE,IAAI,CAACrD,MAAM,CAACsD,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QAAC;QACrC,IAAI,CAACL,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEQD,YAAY,CAACD,OAAoB;IAAA;IAAA;IACvC,IAAI,CAAC7C,WAAW,CAACqD,UAAU,CAAC;MAC1B5C,SAAS,EAAEoC,OAAO,CAACpC,SAAS;MAC5BG,QAAQ,EAAEiC,OAAO,CAACjC,QAAQ;MAC1BC,iBAAiB,EAAE,oCAAO,CAACA,iBAAiB,kCAAI,EAAE;MAClDC,QAAQ,EAAE,oCAAO,CAACA,QAAQ,kCAAI,EAAE;MAChCE,OAAO,EAAE,oCAAO,CAACA,OAAO,kCAAI,EAAE;MAC9BC,QAAQ,EAAE;QACRC,IAAI,EAAE,oCAAO,CAACD,QAAQ,EAAEC,IAAI,kCAAI,EAAE;QAClCC,KAAK,EAAE,oCAAO,CAACF,QAAQ,EAAEE,KAAK,kCAAI,EAAE;QACpCC,OAAO,EAAE,oCAAO,CAACH,QAAQ,EAAEG,OAAO,kCAAI,EAAE;QACxCC,eAAe,EAAE,oCAAO,CAACJ,QAAQ,EAAEI,eAAe,kCAAI,EAAE;OACzD;MACDC,WAAW,EAAE;QACXC,KAAK,EAAE,oCAAO,CAACD,WAAW,CAACC,KAAK,kCAAI,EAAE;QACtCC,aAAa,EAAEqB,OAAO,CAACvB,WAAW,CAACE,aAAa;QAChDC,OAAO,EAAE,oCAAO,CAACH,WAAW,CAACG,OAAO,kCAAI,EAAE;QAC1CC,YAAY,EAAE,oCAAO,CAACJ,WAAW,CAACI,YAAY,kCAAI,EAAE;QACpDG,eAAe,EAAE;UACfC,MAAM,EAAE,qCAAO,CAACR,WAAW,CAACO,eAAe,EAAEC,MAAM,mCAAI,EAAE;UACzDZ,IAAI,EAAE,qCAAO,CAACI,WAAW,CAACO,eAAe,EAAEX,IAAI,mCAAI,EAAE;UACrDC,KAAK,EAAE,qCAAO,CAACG,WAAW,CAACO,eAAe,EAAEV,KAAK,mCAAI,EAAE;UACvDY,UAAU,EAAE,qCAAO,CAACT,WAAW,CAACO,eAAe,EAAEE,UAAU,mCAAI,EAAE;UACjEX,OAAO,EAAE,qCAAO,CAACE,WAAW,CAACO,eAAe,EAAET,OAAO,mCAAI,EAAE;UAC3DY,QAAQ,EAAE,qCAAO,CAACV,WAAW,CAACO,eAAe,EAAEG,QAAQ,mCAAI,KAAK;;OAEnE;MACDA,QAAQ,EAAEa,OAAO,CAACb,QAAQ;MAC1BG,iBAAiB,EAAE;QACjBC,UAAU,EAAE,qCAAO,CAACD,iBAAiB,EAAEC,UAAU,mCAAI,IAAI;QACzDG,WAAW,EAAE,qCAAO,CAACJ,iBAAiB,EAAEI,WAAW,mCAAI,IAAI;QAC3DC,QAAQ,EAAE,qCAAO,CAACL,iBAAiB,EAAEK,QAAQ,mCAAI,KAAK;;KAEzD,CAAC;IAEF;IAAA;IACA,IAAI,CAACc,eAAe,CAAC,qCAAO,CAAChC,WAAW,CAACK,YAAY,mCAAI,EAAE,EAAC;IAE5D;IAAA;IACA,IAAI,CAAC4B,cAAc,CAAC,qCAAO,CAACtB,WAAW,mCAAI,EAAE,EAAC;IAE9C;IAAA;IACA,IAAI,CAACuB,SAAS,CAAC,qCAAO,CAACtB,MAAM,mCAAI,EAAE,EAAC;IAEpC;IAAA;IACA,IAAI,CAACuB,mBAAmB,CAAC,qCAAO,CAAChB,gBAAgB,mCAAI,EAAE,EAAC;EAC1D;EAEA;EACA,IAAId,YAAY;IAAA;IAAA;IACd,OAAO,IAAI,CAAC3B,WAAW,CAAC0D,GAAG,CAAC,0BAA0B,CAAc;EACtE;EAEQJ,eAAe,CAACK,MAAa;IAAA;IACnC,MAAMC,UAAU,8BAAG,IAAI,CAACjC,YAAY;IAAC;IACrCiC,UAAU,CAACC,KAAK,EAAE;IAAC;IAEnBF,MAAM,CAACG,OAAO,CAACC,KAAK,IAAG;MAAA;MAAA;MACrBH,UAAU,CAACI,IAAI,CAAC,IAAI,CAACrE,WAAW,CAACa,KAAK,CAAC;QACrCyD,EAAE,EAAE,CAACF,KAAK,CAACE,EAAE,CAAC;QACdC,MAAM,EAAE,CAACH,KAAK,CAACG,MAAM,EAAEjF,UAAU,CAACyB,QAAQ,CAAC;QAC3CyD,IAAI,EAAE,CAACJ,KAAK,CAACI,IAAI,EAAElF,UAAU,CAACyB,QAAQ,CAAC;QACvCsB,QAAQ,EAAE,CAAC+B,KAAK,CAAC/B,QAAQ,CAAC;QAC1BoC,SAAS,EAAE,CAACL,KAAK,CAACK,SAAS;OAC5B,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;EAEAC,cAAc;IAAA;IACZ,MAAMC,UAAU,8BAAG,IAAI,CAAC3E,WAAW,CAACa,KAAK,CAAC;MACxCyD,EAAE,EAAE,CAAC,IAAI,CAAC;MACVC,MAAM,EAAE,CAAC,EAAE,EAAEjF,UAAU,CAACyB,QAAQ,CAAC;MACjCyD,IAAI,EAAE,CAAC,QAAQ,EAAElF,UAAU,CAACyB,QAAQ,CAAC;MACrCsB,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBoC,SAAS,EAAE,CAAC,KAAK;KAClB,CAAC;IAAC;IAEH,IAAI,CAACzC,YAAY,CAACqC,IAAI,CAACM,UAAU,CAAC;EACpC;EAEAC,iBAAiB,CAACC,KAAa;IAAA;IAAA;IAC7B,IAAI,CAAC7C,YAAY,CAAC8C,QAAQ,CAACD,KAAK,CAAC;EACnC;EAEA;EACA,IAAIvC,WAAW;IAAA;IAAA;IACb,OAAO,IAAI,CAACjC,WAAW,CAAC0D,GAAG,CAAC,aAAa,CAAc;EACzD;EAEQH,cAAc,CAACmB,KAAY;IAAA;IACjC,MAAMC,UAAU,8BAAG,IAAI,CAAC1C,WAAW;IAAC;IACpC0C,UAAU,CAACd,KAAK,EAAE;IAAC;IAEnBa,KAAK,CAACZ,OAAO,CAACc,IAAI,IAAG;MAAA;MAAA;MACnBD,UAAU,CAACX,IAAI,CAAC,IAAI,CAACrE,WAAW,CAACa,KAAK,CAAC;QACrCyD,EAAE,EAAE,CAACW,IAAI,CAACX,EAAE,CAAC;QACbY,QAAQ,EAAE,CAACD,IAAI,CAACC,QAAQ,EAAE5F,UAAU,CAACyB,QAAQ,CAAC;QAC9CoE,GAAG,EAAE,CAACF,IAAI,CAACE,GAAG,EAAE,CAAC7F,UAAU,CAACyB,QAAQ,EAAEzB,UAAU,CAAC8F,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;QACzEC,WAAW,EAAE,CAACJ,IAAI,CAACI,WAAW,CAAC;QAC/BhD,QAAQ,EAAE,CAAC4C,IAAI,CAAC5C,QAAQ;OACzB,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;EAEAiD,aAAa;IAAA;IACX,MAAMC,SAAS,8BAAG,IAAI,CAACvF,WAAW,CAACa,KAAK,CAAC;MACvCyD,EAAE,EAAE,CAAC,IAAI,CAAC;MACVY,QAAQ,EAAE,CAAC,UAAU,EAAE5F,UAAU,CAACyB,QAAQ,CAAC;MAC3CoE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC7F,UAAU,CAACyB,QAAQ,EAAEzB,UAAU,CAAC8F,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MACnEC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBhD,QAAQ,EAAE,CAAC,IAAI;KAChB,CAAC;IAAC;IAEH,IAAI,CAACC,WAAW,CAAC+B,IAAI,CAACkB,SAAS,CAAC;EAClC;EAEAC,gBAAgB,CAACX,KAAa;IAAA;IAAA;IAC5B,IAAI,CAACvC,WAAW,CAACwC,QAAQ,CAACD,KAAK,CAAC;EAClC;EAEA;EACA,IAAItC,MAAM;IAAA;IAAA;IACR,OAAO,IAAI,CAAClC,WAAW,CAAC0D,GAAG,CAAC,QAAQ,CAAc;EACpD;EAEQF,SAAS,CAACtB,MAAsB;IAAA;IACtC,MAAMkD,UAAU,8BAAG,IAAI,CAAClD,MAAM;IAAC;IAC/BkD,UAAU,CAACvB,KAAK,EAAE;IAAC;IAEnB3B,MAAM,CAAC4B,OAAO,CAACuB,KAAK,IAAG;MAAA;MAAA;MACrBD,UAAU,CAACpB,IAAI,CAAC,IAAI,CAACrE,WAAW,CAACa,KAAK,CAAC;QACrCyD,EAAE,EAAE,CAACoB,KAAK,CAACpB,EAAE,CAAC;QACdqB,IAAI,EAAE,CAACD,KAAK,CAACC,IAAI,EAAErG,UAAU,CAACyB,QAAQ,CAAC;QACvC6E,QAAQ,EAAE,CAAC,mCAAK,CAACA,QAAQ,mCAAI,EAAE,EAAC;QAChCC,gBAAgB,EAAE,CAAC,mCAAK,CAACA,gBAAgB,mCAAI,cAAc,GAAEvG,UAAU,CAACyB,QAAQ;OACjF,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;EAEA+E,QAAQ;IAAA;IACN,MAAMC,UAAU,8BAAG,IAAI,CAAC/F,WAAW,CAACa,KAAK,CAAC;MACxCyD,EAAE,EAAE,CAAC,IAAI,CAAC;MACVqB,IAAI,EAAE,CAAC,EAAE,EAAErG,UAAU,CAACyB,QAAQ,CAAC;MAC/B6E,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,gBAAgB,EAAE,CAAC,cAAc,EAAEvG,UAAU,CAACyB,QAAQ;KACvD,CAAC;IAAC;IAEH,IAAI,CAACwB,MAAM,CAAC8B,IAAI,CAAC0B,UAAU,CAAC;EAC9B;EAEAC,WAAW,CAACnB,KAAa;IAAA;IAAA;IACvB,IAAI,CAACtC,MAAM,CAACuC,QAAQ,CAACD,KAAK,CAAC;EAC7B;EAEA;EACA,IAAI/B,gBAAgB;IAAA;IAAA;IAClB,OAAO,IAAI,CAACzC,WAAW,CAAC0D,GAAG,CAAC,kBAAkB,CAAc;EAC9D;EAEQD,mBAAmB,CAACmC,QAA2B;IAAA;IACrD,MAAMC,YAAY,8BAAG,IAAI,CAACpD,gBAAgB;IAAC;IAC3CoD,YAAY,CAAChC,KAAK,EAAE;IAAC;IAErB+B,QAAQ,CAAC9B,OAAO,CAACgC,OAAO,IAAG;MAAA;MAAA;MACzBD,YAAY,CAAC7B,IAAI,CAAC,IAAI,CAACrE,WAAW,CAACa,KAAK,CAAC;QACvCyD,EAAE,EAAE,CAAC6B,OAAO,CAAC7B,EAAE,CAAC;QAChBqB,IAAI,EAAE,CAACQ,OAAO,CAACR,IAAI,EAAErG,UAAU,CAACyB,QAAQ,CAAC;QACzCqF,WAAW,EAAE,CAACD,OAAO,CAACC,WAAW,EAAE9G,UAAU,CAACyB,QAAQ,CAAC;QACvDsF,KAAK,EAAE,CAACF,OAAO,CAACE,KAAK,EAAE,CAAC/G,UAAU,CAACyB,QAAQ,EAAEzB,UAAU,CAACoD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChEG,QAAQ,EAAE,CAAC,qCAAO,CAACA,QAAQ,mCAAI,KAAK,GAAEvD,UAAU,CAACyB,QAAQ,CAAC;QAC1DyC,QAAQ,EAAE,CAAC2C,OAAO,CAAC3C,QAAQ,CAAC;QAC5BoC,QAAQ,EAAE,CAAC,qCAAO,CAACA,QAAQ,mCAAI,EAAE,EAAC;QAClCU,QAAQ,EAAE,CAACH,OAAO,CAACG,QAAQ,KAAK,KAAK,CAAC,CAAC;OACxC,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;;EAEAC,kBAAkB;IAAA;IAChB,MAAMC,YAAY,8BAAG,IAAI,CAACxG,WAAW,CAACa,KAAK,CAAC;MAC1CyD,EAAE,EAAE,CAAC,IAAI,CAAC;MACVqB,IAAI,EAAE,CAAC,EAAE,EAAErG,UAAU,CAACyB,QAAQ,CAAC;MAC/BqF,WAAW,EAAE,CAAC,EAAE,EAAE9G,UAAU,CAACyB,QAAQ,CAAC;MACtCsF,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC/G,UAAU,CAACyB,QAAQ,EAAEzB,UAAU,CAACoD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACpDG,QAAQ,EAAE,CAAC,KAAK,EAAEvD,UAAU,CAACyB,QAAQ,CAAC;MACtCyC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdoC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdU,QAAQ,EAAE,CAAC,IAAI;KAChB,CAAC;IAAC;IAEH,IAAI,CAACxD,gBAAgB,CAACuB,IAAI,CAACmC,YAAY,CAAC;EAC1C;EAEAC,qBAAqB,CAAC5B,KAAa;IAAA;IAAA;IACjC,IAAI,CAAC/B,gBAAgB,CAACgC,QAAQ,CAACD,KAAK,CAAC;EACvC;EAEA;EACA6B,QAAQ;IAAA;IAAA;IACN,IAAI,IAAI,CAACC,YAAY,EAAE,EAAE;MAAA;MAAA;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;MAEpB,MAAMC,SAAS,8BAAG,IAAI,CAACxG,WAAW,CAACyG,KAAK;MACxC,MAAMC,aAAa,8BAAyB;QAC1CjG,SAAS,EAAE+F,SAAS,CAAC/F,SAAS;QAC9BG,QAAQ,EAAE4F,SAAS,CAAC5F,QAAQ;QAC5BC,iBAAiB,EAAE2F,SAAS,CAAC3F,iBAAiB;QAC9CC,QAAQ,EAAE0F,SAAS,CAAC1F,QAAQ;QAC5BG,QAAQ,EAAEuF,SAAS,CAACvF,QAAQ;QAC5BK,WAAW,EAAEkF,SAAS,CAAClF,WAAW;QAClCN,OAAO,EAAEwF,SAAS,CAACxF,OAAO;QAC1BgB,QAAQ,EAAEwE,SAAS,CAACxE,QAAQ;QAC5BE,MAAM,EAAE,uCAAS,CAACA,MAAM,mCAAI,EAAE;QAC9BC,iBAAiB,EAAEqE,SAAS,CAACrE,iBAAiB;QAC9CM,gBAAgB,EAAE,uCAAS,CAACA,gBAAgB,mCAAI,EAAE;OACnD;MAAC;MAEF,IAAI,CAAC7C,cAAc,CAAC+G,aAAa,CAACD,aAAa,CAAC,CAC7C/D,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAC9BuC,SAAS,CAAC;QACTtC,IAAI,EAAGsG,cAAc,IAAI;UAAA;UAAA;UACvB,IAAI,CAACL,QAAQ,GAAG,KAAK;UAAC;UACtB,IAAI,CAACxG,QAAQ,CAACmD,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UAAC;UACjF,IAAI,CAACrD,MAAM,CAACsD,QAAQ,CAAC,CAAC,UAAU,EAAEwD,cAAc,CAACC,IAAI,CAAC,CAAC;QACzD,CAAC;QACD7D,KAAK,EAAGA,KAAK,IAAI;UAAA;UAAA;UACf,IAAI,CAACuD,QAAQ,GAAG,KAAK;UAAC;UACtBtD,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAAC;UAChD,IAAI,CAACjD,QAAQ,CAACmD,IAAI,CAAC,2CAA2C,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QAC9F;OACD,CAAC;KACL;MAAA;IAAA;IACD;EACF;;EAEA2D,QAAQ;IAAA;IAAA;IACN,IAAI,IAAI,CAACjE,OAAO,EAAE;MAAA;MAAA;MAChB,IAAI,CAAC/C,MAAM,CAACsD,QAAQ,CAAC,CAAC,UAAU,EAAE,IAAI,CAACP,OAAO,CAACgE,IAAI,CAAC,CAAC;KACtD,MAAM;MAAA;MAAA;MACL,IAAI,CAAC/G,MAAM,CAACsD,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEA;EACA2D,sBAAsB,CAACC,KAAU;IAAA;IAC/B,MAAMC,IAAI,8BAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAAC;IACnC,IAAIF,IAAI,EAAE;MAAA;MAAA;MACR,IAAI,IAAI,CAACG,iBAAiB,CAACH,IAAI,EAAE,SAAS,CAAC,EAAE;QAAA;QAAA;QAC3C,IAAI,CAACI,kBAAkB,CAACJ,IAAI,EAAE,SAAS,CAAC;QAAC;QACzC,IAAI,CAACK,kBAAkB,CAACL,IAAI,CAAC;OAC9B;QAAA;MAAA;KACF;MAAA;IAAA;IACD;IAAA;IACAD,KAAK,CAACE,MAAM,CAACT,KAAK,GAAG,EAAE;EACzB;EAEAc,oBAAoB,CAACP,KAAU;IAAA;IAC7B,MAAMC,IAAI,8BAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAAC;IACnC,IAAIF,IAAI,EAAE;MAAA;MAAA;MACR,IAAI,IAAI,CAACG,iBAAiB,CAACH,IAAI,EAAE,OAAO,CAAC,EAAE;QAAA;QAAA;QACzC,IAAI,CAACI,kBAAkB,CAACJ,IAAI,EAAE,OAAO,CAAC;QAAC;QACvC,IAAI,CAACO,gBAAgB,CAACP,IAAI,CAAC;OAC5B;QAAA;MAAA;KACF;MAAA;IAAA;IACD;IAAA;IACAD,KAAK,CAACE,MAAM,CAACT,KAAK,GAAG,EAAE;EACzB;EAEQW,iBAAiB,CAACH,IAAU,EAAE9C,IAAyB;IAAA;IAC7D;IACA,MAAMsD,YAAY,8BAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAAC;IACzF,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACT,IAAI,CAAC9C,IAAI,CAAC,EAAE;MAAA;MAAA;MACrC,IAAI,CAACpE,QAAQ,CAACmD,IAAI,CAAC,4DAA4D,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAAC;MAC9G,OAAO,KAAK;KACb;MAAA;IAAA;IAED;IACA,MAAMwE,OAAO,8BAAGxD,IAAI,KAAK,SAAS,iCAAG,CAAC,GAAG,IAAI,GAAG,IAAI,kCAAG,EAAE,GAAG,IAAI,GAAG,IAAI;IAAC;IACxE,IAAI8C,IAAI,CAACW,IAAI,GAAGD,OAAO,EAAE;MAAA;MACvB,MAAME,SAAS,8BAAGF,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC;MAAC;MAC1C,IAAI,CAAC5H,QAAQ,CAACmD,IAAI,CAAC,+BAA+B2E,SAAS,IAAI,EAAE,OAAO,EAAE;QAAE1E,QAAQ,EAAE;MAAI,CAAE,CAAC;MAAC;MAC9F,OAAO,KAAK;KACb;MAAA;IAAA;IAAA;IAED,OAAO,IAAI;EACb;EAEQkE,kBAAkB,CAACJ,IAAU,EAAE9C,IAAyB;IAAA;IAC9D,MAAM2D,MAAM,+BAAG,IAAIC,UAAU,EAAE;IAAC;IAChCD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;MAAA;MAAA;MACzB,IAAI9D,IAAI,KAAK,SAAS,EAAE;QAAA;QAAA;QACtB,IAAI,CAAC+D,mBAAmB,GAAGD,CAAC,CAACf,MAAM,CAACiB,MAAM;OAC3C,MAAM;QAAA;QAAA;QACL,IAAI,CAACC,iBAAiB,GAAGH,CAAC,CAACf,MAAM,CAACiB,MAAM;;IAE5C,CAAC;IAAC;IACFL,MAAM,CAACO,aAAa,CAACpB,IAAI,CAAC;EAC5B;EAEQK,kBAAkB,CAACL,IAAU;IAAA;IAAA;IACnC,IAAI,CAACqB,uBAAuB,GAAG,IAAI;IAAC;IACpC,IAAI,CAAC1I,cAAc,CAAC0H,kBAAkB,CAACL,IAAI,CAAC,CACzCtE,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAC9BuC,SAAS,CAAC;MACTtC,IAAI,EAAGiI,QAAQ,IAAI;QAAA;QAAA;QACjB,IAAI,CAACD,uBAAuB,GAAG,KAAK;QAAC;QACrC,IAAI,IAAI,CAACzF,OAAO,EAAE;UAAA;UAAA;UAChB,IAAI,CAACA,OAAO,CAAC2F,eAAe,GAAGD,QAAQ,CAACzD,GAAG;SAC5C;UAAA;QAAA;QAAA;QACD,IAAI,CAACoD,mBAAmB,GAAG,IAAI,CAAC,CAAC;QAAA;QACjC,IAAI,CAACnI,QAAQ,CAACmD,IAAI,CAAC,qCAAqC,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACxF,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QAAA;QAAA;QACf,IAAI,CAACsF,uBAAuB,GAAG,KAAK;QAAC;QACrC,IAAI,CAACJ,mBAAmB,GAAG,IAAI,CAAC,CAAC;QAAA;QACjCjF,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAMyF,YAAY,+BAAG,mCAAK,CAACzF,KAAK,EAAE0F,OAAO,mCAAI,kDAAkD;QAAC;QAChG,IAAI,CAAC3I,QAAQ,CAACmD,IAAI,CAACuF,YAAY,EAAE,OAAO,EAAE;UAAEtF,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC/D;KACD,CAAC;EACN;EAEQqE,gBAAgB,CAACP,IAAU;IAAA;IAAA;IACjC,IAAI,CAAC0B,qBAAqB,GAAG,IAAI;IAAC;IAClC,IAAI,CAAC/I,cAAc,CAAC4H,gBAAgB,CAACP,IAAI,CAAC,CACvCtE,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAC9BuC,SAAS,CAAC;MACTtC,IAAI,EAAGiI,QAAQ,IAAI;QAAA;QAAA;QACjB,IAAI,CAACI,qBAAqB,GAAG,KAAK;QAAC;QACnC,IAAI,IAAI,CAAC9F,OAAO,EAAE;UAAA;UAAA;UAChB,IAAI,CAACA,OAAO,CAAC+F,aAAa,GAAGL,QAAQ,CAACzD,GAAG;SAC1C;UAAA;QAAA;QAAA;QACD,IAAI,CAACsD,iBAAiB,GAAG,IAAI,CAAC,CAAC;QAAA;QAC/B,IAAI,CAACrI,QAAQ,CAACmD,IAAI,CAAC,mCAAmC,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACtF,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QAAA;QAAA;QACf,IAAI,CAAC2F,qBAAqB,GAAG,KAAK;QAAC;QACnC,IAAI,CAACP,iBAAiB,GAAG,IAAI,CAAC,CAAC;QAAA;QAC/BnF,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,MAAMyF,YAAY,+BAAG,mCAAK,CAACzF,KAAK,EAAE0F,OAAO,mCAAI,gDAAgD;QAAC;QAC9F,IAAI,CAAC3I,QAAQ,CAACmD,IAAI,CAACuF,YAAY,EAAE,OAAO,EAAE;UAAEtF,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC/D;KACD,CAAC;EACN;EAEA;EACQ0F,oBAAoB;IAAA;IAAA;IAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/I,WAAW,CAACgJ,QAAQ,CAAC,CAAClF,OAAO,CAACmF,GAAG,IAAG;MAAA;MACnD,MAAMC,OAAO,+BAAG,IAAI,CAAClJ,WAAW,CAAC0D,GAAG,CAACuF,GAAG,CAAC;MAAC;MAC1CC,OAAO,EAAEC,aAAa,EAAE;MAAC;MAEzB,IAAID,OAAO,YAAYhK,SAAS,EAAE;QAAA;QAAA;QAChCgK,OAAO,CAACF,QAAQ,CAAClF,OAAO,CAACsF,YAAY,IAAG;UAAA;UAAA;UACtC,IAAIA,YAAY,YAAYpK,SAAS,EAAE;YAAA;YAAA;YACrC8J,MAAM,CAACC,IAAI,CAACK,YAAY,CAACJ,QAAQ,CAAC,CAAClF,OAAO,CAACuF,QAAQ,IAAG;cAAA;cAAA;cACpDD,YAAY,CAAC1F,GAAG,CAAC2F,QAAQ,CAAC,EAAEF,aAAa,EAAE;YAC7C,CAAC,CAAC;WACH;YAAA;UAAA;QACH,CAAC,CAAC;OACH;QAAA;MAAA;IACH,CAAC,CAAC;EACJ;EAEAG,eAAe,CAACC,SAAiB;IAAA;IAC/B,MAAML,OAAO,+BAAG,IAAI,CAAClJ,WAAW,CAAC0D,GAAG,CAAC6F,SAAS,CAAC;IAAC;IAChD,IAAI,+BAACL,OAAO,mCAAI,CAACA,OAAO,CAACM,MAAM,GAAE;MAAA;MAAA;MAAA,OAAO,EAAE;IAAA,CAAC;MAAA;IAAA;IAE3C,MAAMC,gBAAgB,+BAAG,IAAI,CAACC,mBAAmB,CAACH,SAAS,CAAC;IAAC;IAE7D,IAAIL,OAAO,CAACS,QAAQ,CAAC,UAAU,CAAC,EAAE;MAAA;MAAA;MAChC,OAAO,GAAGF,gBAAgB,cAAc;KACzC;MAAA;IAAA;IAAA;IACD,IAAIP,OAAO,CAACS,QAAQ,CAAC,OAAO,CAAC,EAAE;MAAA;MAAA;MAC7B,OAAO,oCAAoC;KAC5C;MAAA;IAAA;IAAA;IACD,IAAIT,OAAO,CAACS,QAAQ,CAAC,WAAW,CAAC,EAAE;MAAA;MACjC,MAAMC,cAAc,+BAAGV,OAAO,CAACM,MAAM,CAAC,WAAW,CAAC,CAACI,cAAc;MAAC;MAClE,OAAO,GAAGH,gBAAgB,qBAAqBG,cAAc,aAAa;KAC3E;MAAA;IAAA;IAAA;IACD,IAAIV,OAAO,CAACS,QAAQ,CAAC,WAAW,CAAC,EAAE;MAAA;MACjC,MAAM5I,SAAS,+BAAGmI,OAAO,CAACM,MAAM,CAAC,WAAW,CAAC,CAACI,cAAc;MAAC;MAC7D,OAAO,GAAGH,gBAAgB,yBAAyB1I,SAAS,aAAa;KAC1E;MAAA;IAAA;IAAA;IACD,IAAImI,OAAO,CAACS,QAAQ,CAAC,SAAS,CAAC,EAAE;MAAA;MAAA;MAC/B,OAAO,0BAA0B;KAClC;MAAA;IAAA;IAAA;IACD,IAAIT,OAAO,CAACS,QAAQ,CAAC,KAAK,CAAC,EAAE;MAAA;MAC3B,MAAME,QAAQ,+BAAGX,OAAO,CAACM,MAAM,CAAC,KAAK,CAAC,CAACnH,GAAG;MAAC;MAC3C,OAAO,GAAGoH,gBAAgB,qBAAqBI,QAAQ,EAAE;KAC1D;MAAA;IAAA;IAAA;IACD,IAAIX,OAAO,CAACS,QAAQ,CAAC,KAAK,CAAC,EAAE;MAAA;MAC3B,MAAMG,QAAQ,+BAAGZ,OAAO,CAACM,MAAM,CAAC,KAAK,CAAC,CAAClH,GAAG;MAAC;MAC3C,OAAO,GAAGmH,gBAAgB,kBAAkBK,QAAQ,EAAE;KACvD;MAAA;IAAA;IAAA;IACD,OAAO,EAAE;EACX;EAEQJ,mBAAmB,CAACH,SAAiB;IAAA;IAC3C,MAAMQ,UAAU,+BAA8B;MAC5C,WAAW,EAAE,YAAY;MACzB,UAAU,EAAE,WAAW;MACvB,mBAAmB,EAAE,oBAAoB;MACzC,UAAU,EAAE,UAAU;MACtB,SAAS,EAAE,SAAS;MACpB,mBAAmB,EAAE,OAAO;MAC5B,qBAAqB,EAAE,SAAS;MAChC,0BAA0B,EAAE,eAAe;MAC3C,eAAe,EAAE,MAAM;MACvB,gBAAgB,EAAE,OAAO;MACzB,kBAAkB,EAAE,SAAS;MAC7B,0BAA0B,EAAE,kBAAkB;MAC9C,8BAA8B,EAAE,aAAa;MAC7C,+BAA+B,EAAE,cAAc;MAC/C,4BAA4B,EAAE;KAC/B;IAAC;IACF,OAAO,wCAAU,CAACR,SAAS,CAAC,mCAAIA,SAAS;EAC3C;EAEA;EACAjD,YAAY;IAAA;IAAA;IACV,IAAI,IAAI,CAACtG,WAAW,CAACgK,OAAO,EAAE;MAAA;MAAA;MAC5B,IAAI,CAACnB,oBAAoB,EAAE;MAE3B;MACA,MAAMoB,iBAAiB,+BAAG,IAAI,CAACC,qBAAqB,EAAE;MAAC;MACvD,IAAID,iBAAiB,EAAE;QAAA;QAAA;QACrBA,iBAAiB,CAACE,KAAK,EAAE;OAC1B;QAAA;MAAA;MAED;MACA,MAAMX,MAAM,+BAAG,IAAI,CAACY,aAAa,EAAE;MAAC;MACpC,IAAIZ,MAAM,CAACa,MAAM,GAAG,CAAC,EAAE;QAAA;QAAA;QACrB,IAAI,CAACtK,QAAQ,CAACmD,IAAI,CAAC,oCAAoCsG,MAAM,CAACc,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE;UAAEnH,QAAQ,EAAE;QAAI,CAAE,CAAC;OACzG;QAAA;MAAA;MAAA;MAED,OAAO,KAAK;KACb;MAAA;IAAA;IAAA;IACD,OAAO,IAAI;EACb;EAEQ+G,qBAAqB;IAAA;IAC3B,MAAMK,aAAa,+BAAGC,QAAQ,CAACC,gBAAgB,CAAC,8GAA8G,CAAC;IAAC;IAChK,OAAOF,aAAa,CAACF,MAAM,GAAG,CAAC,iCAAGE,aAAa,CAAC,CAAC,CAAgB,kCAAG,IAAI;EAC1E;EAEQH,aAAa;IAAA;IACnB,MAAMZ,MAAM,+BAAa,EAAE;IAE3B;IAAA;IACAV,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/I,WAAW,CAACgJ,QAAQ,CAAC,CAAClF,OAAO,CAACmF,GAAG,IAAG;MAAA;MACnD,MAAMC,OAAO,+BAAG,IAAI,CAAClJ,WAAW,CAAC0D,GAAG,CAACuF,GAAG,CAAC;MAAC;MAC1C,IAAI,qCAAO,mCAAIC,OAAO,CAACc,OAAO,mCAAId,OAAO,CAACwB,OAAO,GAAE;QAAA;QACjD,MAAMjC,YAAY,+BAAG,IAAI,CAACa,eAAe,CAACL,GAAG,CAAC;QAAC;QAC/C,IAAIR,YAAY,EAAE;UAAA;UAAA;UAChBe,MAAM,CAACxF,IAAI,CAACyE,YAAY,CAAC;SAC1B;UAAA;QAAA;OACF;QAAA;MAAA;IACH,CAAC,CAAC;IAEF;IACA,MAAMnH,WAAW,+BAAG,IAAI,CAACtB,WAAW,CAAC0D,GAAG,CAAC,aAAa,CAAc;IAAC;IACrE,IAAI,yCAAW,mCAAIpC,WAAW,CAAC0I,OAAO,GAAE;MAAA;MAAA;MACtClB,MAAM,CAACC,IAAI,CAACzH,WAAW,CAAC0H,QAAQ,CAAC,CAAClF,OAAO,CAACmF,GAAG,IAAG;QAAA;QAC9C,MAAMC,OAAO,+BAAG5H,WAAW,CAACoC,GAAG,CAACuF,GAAG,CAAC;QAAC;QACrC,IAAI,qCAAO,mCAAIC,OAAO,CAACc,OAAO,mCAAId,OAAO,CAACwB,OAAO,GAAE;UAAA;UACjD,MAAMjC,YAAY,+BAAG,IAAI,CAACa,eAAe,CAAC,eAAeL,GAAG,EAAE,CAAC;UAAC;UAChE,IAAIR,YAAY,EAAE;YAAA;YAAA;YAChBe,MAAM,CAACxF,IAAI,CAACyE,YAAY,CAAC;WAC1B;YAAA;UAAA;SACF;UAAA;QAAA;MACH,CAAC,CAAC;KACH;MAAA;IAAA;IAAA;IAED,OAAOe,MAAM,CAACmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC7B;EAEA;EACAC,kBAAkB;IAAA;IAAA;IAChB,OAAO,CACL;MAAEnE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,SAAS;MAAEoE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEpE,KAAK,EAAE,QAAQ;MAAEoE,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEpE,KAAK,EAAE,SAAS;MAAEoE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEpE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,WAAW;MAAEoE,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEpE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,SAAS;MAAEoE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEpE,KAAK,EAAE,OAAO;MAAEoE,KAAK,EAAE;IAAO,CAAE,CACnC;EACH;EAEA;EACAC,mBAAmB;IAAA;IAAA;IACjB,OAAO,CACL;MAAErE,KAAK,EAAE,QAAQ;MAAEoE,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEpE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,MAAM;MAAEoE,KAAK,EAAE;IAAM,CAAE,CACjC;EACH;EAEA;EACAE,uBAAuB;IAAA;IAAA;IACrB,OAAO,CACL;MAAEtE,KAAK,EAAE,WAAW;MAAEoE,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEpE,KAAK,EAAE,iBAAiB;MAAEoE,KAAK,EAAE;IAAiB,CAAE,EACtD;MAAEpE,KAAK,EAAE,WAAW;MAAEoE,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEpE,KAAK,EAAE,sBAAsB;MAAEoE,KAAK,EAAE;IAAsB,CAAE,EAChE;MAAEpE,KAAK,EAAE,YAAY;MAAEoE,KAAK,EAAE;IAAY,CAAE,EAC5C;MAAEpE,KAAK,EAAE,eAAe;MAAEoE,KAAK,EAAE;IAAe,CAAE,EAClD;MAAEpE,KAAK,EAAE,gBAAgB;MAAEoE,KAAK,EAAE;IAAgB,CAAE,EACpD;MAAEpE,KAAK,EAAE,YAAY;MAAEoE,KAAK,EAAE;IAAY,CAAE,EAC5C;MAAEpE,KAAK,EAAE,OAAO;MAAEoE,KAAK,EAAE;IAAO,CAAE,CACnC;EACH;EAEA;EACAG,0BAA0B;IAAA;IAAA;IACxB,OAAO,CACL;MAAEvE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,cAAc;MAAEoE,KAAK,EAAE;IAAc,CAAE,EAChD;MAAEpE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,QAAQ;MAAEoE,KAAK,EAAE;IAAQ,CAAE,CACrC;EACH;EAEA;EACAI,yBAAyB;IAAA;IAAA;IACvB,OAAO,CACL;MAAExE,KAAK,EAAE,SAAS;MAAEoE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEpE,KAAK,EAAE,cAAc;MAAEoE,KAAK,EAAE;IAAc,CAAE,EAChD;MAAEpE,KAAK,EAAE,SAAS;MAAEoE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEpE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,QAAQ;MAAEoE,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEpE,KAAK,EAAE,OAAO;MAAEoE,KAAK,EAAE;IAAO,CAAE,CACnC;EACH;EAEA;EACAK,kBAAkB;IAAA;IAAA;IAChB,OAAO,CACL;MAAEzE,KAAK,EAAE,KAAK;MAAEoE,KAAK,EAAE;IAAqB,CAAE,EAC9C;MAAEpE,KAAK,EAAE,KAAK;MAAEoE,KAAK,EAAE;IAAY,CAAE,EACrC;MAAEpE,KAAK,EAAE,KAAK;MAAEoE,KAAK,EAAE;IAAiB,CAAE,EAC1C;MAAEpE,KAAK,EAAE,KAAK;MAAEoE,KAAK,EAAE;IAAqB,CAAE,CAC/C;EACH;;;;;;;;;;;;;;;;;;;;;AAxoBWpL,oBAAoB,eALhCX,SAAS,CAAC;EACTqM,QAAQ,EAAE,kBAAkB;EAC5BC,8BAA4C;;CAE7C,CAAC,GACW3L,oBAAoB,CAyoBhC;SAzoBYA,oBAAoB", "names": ["Component", "FormBuilder", "FormGroup", "Validators", "FormArray", "Router", "Subject", "takeUntil", "MatSnackBar", "ProfileService", "AuthService", "ProfileEditComponent", "constructor", "formBuilder", "profileService", "authService", "router", "snackBar", "profileForm", "createForm", "ngOnInit", "loadProfile", "ngOnDestroy", "destroy$", "next", "complete", "group", "firstName", "required", "<PERSON><PERSON><PERSON><PERSON>", "lastName", "professional<PERSON>itle", "headline", "max<PERSON><PERSON><PERSON>", "summary", "location", "city", "state", "country", "displayLocation", "contactInfo", "email", "isEmailPublic", "website", "portfolioUrl", "phoneNumbers", "array", "businessAddress", "street", "postalCode", "isPublic", "socialLinks", "skills", "consultationRates", "hourlyRate", "min", "max", "sessionRate", "currency", "serviceOfferings", "getCurrentUserProfile", "pipe", "subscribe", "profile", "populateForm", "isLoading", "error", "console", "open", "duration", "navigate", "patchValue", "setPhoneNumbers", "setSocialLinks", "setSkills", "setServiceOfferings", "get", "phones", "phoneArray", "clear", "for<PERSON>ach", "phone", "push", "id", "number", "type", "isPrimary", "addPhoneNumber", "phoneGroup", "removePhoneNumber", "index", "removeAt", "links", "linksArray", "link", "platform", "url", "pattern", "displayName", "addSocialLink", "linkGroup", "removeSocialLink", "skillArray", "skill", "name", "category", "proficiencyLevel", "addSkill", "skillGroup", "removeSkill", "services", "serviceArray", "service", "description", "price", "isActive", "addServiceOffering", "serviceGroup", "removeServiceOffering", "onSubmit", "validateForm", "isSaving", "formValue", "value", "updateRequest", "updateProfile", "updatedProfile", "slug", "onCancel", "onProfilePhotoSelected", "event", "file", "target", "files", "validateImageFile", "createImagePreview", "uploadProfilePhoto", "onCoverPhotoSelected", "uploadCoverPhoto", "allowedTypes", "includes", "maxSize", "size", "maxSizeMB", "reader", "FileReader", "onload", "e", "profilePhotoPreview", "result", "coverPhotoPreview", "readAsDataURL", "isUploadingProfilePhoto", "response", "profilePhotoUrl", "errorMessage", "message", "isUploadingCoverPhoto", "coverPhotoUrl", "markFormGroupTouched", "Object", "keys", "controls", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "arrayControl", "<PERSON><PERSON><PERSON>", "getErrorMessage", "fieldName", "errors", "fieldDisplayName", "getFieldDisplayName", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "minValue", "maxValue", "fieldNames", "invalid", "firstInvalidField", "findFirstInvalidField", "focus", "getFormErrors", "length", "join", "invalidFields", "document", "querySelectorAll", "touched", "slice", "getPlatformOptions", "label", "getPhoneTypeOptions", "getSkillCategoryOptions", "getProficiencyLevelOptions", "getServiceCategoryOptions", "getCurrencyOptions", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\profile-edit\\profile-edit.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\nimport { ProfileService } from '../../services/profile.service';\r\nimport { AuthService } from '../../../auth/services/auth.service';\r\nimport { UserProfile, ProfileUpdateRequest, ProfileSkill, ConsultationRates, ServiceOffering } from '../../models/profile.models';\r\n\r\n@Component({\r\n  selector: 'app-profile-edit',\r\n  templateUrl: './profile-edit.component.html',\r\n  styleUrls: ['./profile-edit.component.css']\r\n})\r\nexport class ProfileEditComponent implements OnInit, OnDestroy {\r\n  profileForm: FormGroup;\r\n  profile: UserProfile | null = null;\r\n  isLoading = true;\r\n  isSaving = false;\r\n  isUploadingProfilePhoto = false;\r\n  isUploadingCoverPhoto = false;\r\n  profilePhotoPreview: string | null = null;\r\n  coverPhotoPreview: string | null = null;\r\n\r\n  private destroy$ = new Subject<void>();\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private profileService: ProfileService,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    this.profileForm = this.createForm();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadProfile();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  private createForm(): FormGroup {\r\n    return this.formBuilder.group({\r\n      // Basic Information\r\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\r\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\r\n      professionalTitle: [''],\r\n      headline: ['', Validators.maxLength(220)],\r\n      summary: ['', Validators.maxLength(2000)],\r\n      \r\n      // Location\r\n      location: this.formBuilder.group({\r\n        city: [''],\r\n        state: [''],\r\n        country: [''],\r\n        displayLocation: ['']\r\n      }),\r\n      \r\n      // Contact Information\r\n      contactInfo: this.formBuilder.group({\r\n        email: ['', Validators.email],\r\n        isEmailPublic: [false],\r\n        website: [''],\r\n        portfolioUrl: [''],\r\n        phoneNumbers: this.formBuilder.array([]),\r\n        businessAddress: this.formBuilder.group({\r\n          street: [''],\r\n          city: [''],\r\n          state: [''],\r\n          postalCode: [''],\r\n          country: [''],\r\n          isPublic: [false]\r\n        })\r\n      }),\r\n      \r\n      // Privacy Settings\r\n      isPublic: [true],\r\n\r\n      // Social Links\r\n      socialLinks: this.formBuilder.array([]),\r\n\r\n      // Skills\r\n      skills: this.formBuilder.array([]),\r\n\r\n      // Consultation Rates\r\n      consultationRates: this.formBuilder.group({\r\n        hourlyRate: [null, [Validators.min(0), Validators.max(10000)]],\r\n        sessionRate: [null, [Validators.min(0), Validators.max(10000)]],\r\n        currency: ['BGN', Validators.required]\r\n      }),\r\n\r\n      // Service Offerings\r\n      serviceOfferings: this.formBuilder.array([])\r\n    });\r\n  }\r\n\r\n  private loadProfile(): void {\r\n    this.profileService.getCurrentUserProfile()\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (profile) => {\r\n          this.profile = profile;\r\n          this.populateForm(profile);\r\n          this.isLoading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading profile:', error);\r\n          this.snackBar.open('Error loading profile', 'Close', { duration: 5000 });\r\n          this.router.navigate(['/dashboard']);\r\n          this.isLoading = false;\r\n        }\r\n      });\r\n  }\r\n\r\n  private populateForm(profile: UserProfile): void {\r\n    this.profileForm.patchValue({\r\n      firstName: profile.firstName,\r\n      lastName: profile.lastName,\r\n      professionalTitle: profile.professionalTitle || '',\r\n      headline: profile.headline || '',\r\n      summary: profile.summary || '',\r\n      location: {\r\n        city: profile.location?.city || '',\r\n        state: profile.location?.state || '',\r\n        country: profile.location?.country || '',\r\n        displayLocation: profile.location?.displayLocation || ''\r\n      },\r\n      contactInfo: {\r\n        email: profile.contactInfo.email || '',\r\n        isEmailPublic: profile.contactInfo.isEmailPublic,\r\n        website: profile.contactInfo.website || '',\r\n        portfolioUrl: profile.contactInfo.portfolioUrl || '',\r\n        businessAddress: {\r\n          street: profile.contactInfo.businessAddress?.street || '',\r\n          city: profile.contactInfo.businessAddress?.city || '',\r\n          state: profile.contactInfo.businessAddress?.state || '',\r\n          postalCode: profile.contactInfo.businessAddress?.postalCode || '',\r\n          country: profile.contactInfo.businessAddress?.country || '',\r\n          isPublic: profile.contactInfo.businessAddress?.isPublic || false\r\n        }\r\n      },\r\n      isPublic: profile.isPublic,\r\n      consultationRates: {\r\n        hourlyRate: profile.consultationRates?.hourlyRate || null,\r\n        sessionRate: profile.consultationRates?.sessionRate || null,\r\n        currency: profile.consultationRates?.currency || 'BGN'\r\n      }\r\n    });\r\n\r\n    // Populate phone numbers\r\n    this.setPhoneNumbers(profile.contactInfo.phoneNumbers || []);\r\n\r\n    // Populate social links\r\n    this.setSocialLinks(profile.socialLinks || []);\r\n\r\n    // Populate skills\r\n    this.setSkills(profile.skills || []);\r\n\r\n    // Populate service offerings\r\n    this.setServiceOfferings(profile.serviceOfferings || []);\r\n  }\r\n\r\n  // Phone Numbers Management\r\n  get phoneNumbers(): FormArray {\r\n    return this.profileForm.get('contactInfo.phoneNumbers') as FormArray;\r\n  }\r\n\r\n  private setPhoneNumbers(phones: any[]): void {\r\n    const phoneArray = this.phoneNumbers;\r\n    phoneArray.clear();\r\n    \r\n    phones.forEach(phone => {\r\n      phoneArray.push(this.formBuilder.group({\r\n        id: [phone.id],\r\n        number: [phone.number, Validators.required],\r\n        type: [phone.type, Validators.required],\r\n        isPublic: [phone.isPublic],\r\n        isPrimary: [phone.isPrimary]\r\n      }));\r\n    });\r\n  }\r\n\r\n  addPhoneNumber(): void {\r\n    const phoneGroup = this.formBuilder.group({\r\n      id: [null],\r\n      number: ['', Validators.required],\r\n      type: ['mobile', Validators.required],\r\n      isPublic: [false],\r\n      isPrimary: [false]\r\n    });\r\n    \r\n    this.phoneNumbers.push(phoneGroup);\r\n  }\r\n\r\n  removePhoneNumber(index: number): void {\r\n    this.phoneNumbers.removeAt(index);\r\n  }\r\n\r\n  // Social Links Management\r\n  get socialLinks(): FormArray {\r\n    return this.profileForm.get('socialLinks') as FormArray;\r\n  }\r\n\r\n  private setSocialLinks(links: any[]): void {\r\n    const linksArray = this.socialLinks;\r\n    linksArray.clear();\r\n    \r\n    links.forEach(link => {\r\n      linksArray.push(this.formBuilder.group({\r\n        id: [link.id],\r\n        platform: [link.platform, Validators.required],\r\n        url: [link.url, [Validators.required, Validators.pattern('https?://.+')]],\r\n        displayName: [link.displayName],\r\n        isPublic: [link.isPublic]\r\n      }));\r\n    });\r\n  }\r\n\r\n  addSocialLink(): void {\r\n    const linkGroup = this.formBuilder.group({\r\n      id: [null],\r\n      platform: ['linkedin', Validators.required],\r\n      url: ['', [Validators.required, Validators.pattern('https?://.+')]],\r\n      displayName: [''],\r\n      isPublic: [true]\r\n    });\r\n    \r\n    this.socialLinks.push(linkGroup);\r\n  }\r\n\r\n  removeSocialLink(index: number): void {\r\n    this.socialLinks.removeAt(index);\r\n  }\r\n\r\n  // Skills Management\r\n  get skills(): FormArray {\r\n    return this.profileForm.get('skills') as FormArray;\r\n  }\r\n\r\n  private setSkills(skills: ProfileSkill[]): void {\r\n    const skillArray = this.skills;\r\n    skillArray.clear();\r\n\r\n    skills.forEach(skill => {\r\n      skillArray.push(this.formBuilder.group({\r\n        id: [skill.id],\r\n        name: [skill.name, Validators.required],\r\n        category: [skill.category || ''],\r\n        proficiencyLevel: [skill.proficiencyLevel || 'intermediate', Validators.required]\r\n      }));\r\n    });\r\n  }\r\n\r\n  addSkill(): void {\r\n    const skillGroup = this.formBuilder.group({\r\n      id: [null],\r\n      name: ['', Validators.required],\r\n      category: [''],\r\n      proficiencyLevel: ['intermediate', Validators.required]\r\n    });\r\n\r\n    this.skills.push(skillGroup);\r\n  }\r\n\r\n  removeSkill(index: number): void {\r\n    this.skills.removeAt(index);\r\n  }\r\n\r\n  // Service Offerings Management\r\n  get serviceOfferings(): FormArray {\r\n    return this.profileForm.get('serviceOfferings') as FormArray;\r\n  }\r\n\r\n  private setServiceOfferings(services: ServiceOffering[]): void {\r\n    const serviceArray = this.serviceOfferings;\r\n    serviceArray.clear();\r\n\r\n    services.forEach(service => {\r\n      serviceArray.push(this.formBuilder.group({\r\n        id: [service.id],\r\n        name: [service.name, Validators.required],\r\n        description: [service.description, Validators.required],\r\n        price: [service.price, [Validators.required, Validators.min(0)]],\r\n        currency: [service.currency || 'BGN', Validators.required],\r\n        duration: [service.duration],\r\n        category: [service.category || ''],\r\n        isActive: [service.isActive !== false] // default to true\r\n      }));\r\n    });\r\n  }\r\n\r\n  addServiceOffering(): void {\r\n    const serviceGroup = this.formBuilder.group({\r\n      id: [null],\r\n      name: ['', Validators.required],\r\n      description: ['', Validators.required],\r\n      price: [0, [Validators.required, Validators.min(0)]],\r\n      currency: ['BGN', Validators.required],\r\n      duration: [60], // default 60 minutes\r\n      category: [''],\r\n      isActive: [true]\r\n    });\r\n\r\n    this.serviceOfferings.push(serviceGroup);\r\n  }\r\n\r\n  removeServiceOffering(index: number): void {\r\n    this.serviceOfferings.removeAt(index);\r\n  }\r\n\r\n  // Form Submission\r\n  onSubmit(): void {\r\n    if (this.validateForm()) {\r\n      this.isSaving = true;\r\n      \r\n      const formValue = this.profileForm.value;\r\n      const updateRequest: ProfileUpdateRequest = {\r\n        firstName: formValue.firstName,\r\n        lastName: formValue.lastName,\r\n        professionalTitle: formValue.professionalTitle,\r\n        headline: formValue.headline,\r\n        location: formValue.location,\r\n        contactInfo: formValue.contactInfo,\r\n        summary: formValue.summary,\r\n        isPublic: formValue.isPublic,\r\n        skills: formValue.skills || [],\r\n        consultationRates: formValue.consultationRates,\r\n        serviceOfferings: formValue.serviceOfferings || []\r\n      };\r\n\r\n      this.profileService.updateProfile(updateRequest)\r\n        .pipe(takeUntil(this.destroy$))\r\n        .subscribe({\r\n          next: (updatedProfile) => {\r\n            this.isSaving = false;\r\n            this.snackBar.open('Profile updated successfully!', 'Close', { duration: 3000 });\r\n            this.router.navigate(['/profile', updatedProfile.slug]);\r\n          },\r\n          error: (error) => {\r\n            this.isSaving = false;\r\n            console.error('Error updating profile:', error);\r\n            this.snackBar.open('Error updating profile. Please try again.', 'Close', { duration: 5000 });\r\n          }\r\n        });\r\n    }\r\n    // Validation is now handled in validateForm() method\r\n  }\r\n\r\n  onCancel(): void {\r\n    if (this.profile) {\r\n      this.router.navigate(['/profile', this.profile.slug]);\r\n    } else {\r\n      this.router.navigate(['/dashboard']);\r\n    }\r\n  }\r\n\r\n  // File Upload Methods\r\n  onProfilePhotoSelected(event: any): void {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      if (this.validateImageFile(file, 'profile')) {\r\n        this.createImagePreview(file, 'profile');\r\n        this.uploadProfilePhoto(file);\r\n      }\r\n    }\r\n    // Reset the input to allow selecting the same file again\r\n    event.target.value = '';\r\n  }\r\n\r\n  onCoverPhotoSelected(event: any): void {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      if (this.validateImageFile(file, 'cover')) {\r\n        this.createImagePreview(file, 'cover');\r\n        this.uploadCoverPhoto(file);\r\n      }\r\n    }\r\n    // Reset the input to allow selecting the same file again\r\n    event.target.value = '';\r\n  }\r\n\r\n  private validateImageFile(file: File, type: 'profile' | 'cover'): boolean {\r\n    // Check file type\r\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];\r\n    if (!allowedTypes.includes(file.type)) {\r\n      this.snackBar.open('Please select a valid image file (JPEG, PNG, GIF, or WebP)', 'Close', { duration: 5000 });\r\n      return false;\r\n    }\r\n\r\n    // Check file size (5MB for profile, 10MB for cover)\r\n    const maxSize = type === 'profile' ? 5 * 1024 * 1024 : 10 * 1024 * 1024;\r\n    if (file.size > maxSize) {\r\n      const maxSizeMB = maxSize / (1024 * 1024);\r\n      this.snackBar.open(`File size must be less than ${maxSizeMB}MB`, 'Close', { duration: 5000 });\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  private createImagePreview(file: File, type: 'profile' | 'cover'): void {\r\n    const reader = new FileReader();\r\n    reader.onload = (e: any) => {\r\n      if (type === 'profile') {\r\n        this.profilePhotoPreview = e.target.result;\r\n      } else {\r\n        this.coverPhotoPreview = e.target.result;\r\n      }\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n\r\n  private uploadProfilePhoto(file: File): void {\r\n    this.isUploadingProfilePhoto = true;\r\n    this.profileService.uploadProfilePhoto(file)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (response) => {\r\n          this.isUploadingProfilePhoto = false;\r\n          if (this.profile) {\r\n            this.profile.profilePhotoUrl = response.url;\r\n          }\r\n          this.profilePhotoPreview = null; // Clear preview since we have the actual URL\r\n          this.snackBar.open('Profile photo updated successfully!', 'Close', { duration: 3000 });\r\n        },\r\n        error: (error) => {\r\n          this.isUploadingProfilePhoto = false;\r\n          this.profilePhotoPreview = null; // Clear preview on error\r\n          console.error('Error uploading profile photo:', error);\r\n          const errorMessage = error.error?.message || 'Error uploading profile photo. Please try again.';\r\n          this.snackBar.open(errorMessage, 'Close', { duration: 5000 });\r\n        }\r\n      });\r\n  }\r\n\r\n  private uploadCoverPhoto(file: File): void {\r\n    this.isUploadingCoverPhoto = true;\r\n    this.profileService.uploadCoverPhoto(file)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (response) => {\r\n          this.isUploadingCoverPhoto = false;\r\n          if (this.profile) {\r\n            this.profile.coverPhotoUrl = response.url;\r\n          }\r\n          this.coverPhotoPreview = null; // Clear preview since we have the actual URL\r\n          this.snackBar.open('Cover photo updated successfully!', 'Close', { duration: 3000 });\r\n        },\r\n        error: (error) => {\r\n          this.isUploadingCoverPhoto = false;\r\n          this.coverPhotoPreview = null; // Clear preview on error\r\n          console.error('Error uploading cover photo:', error);\r\n          const errorMessage = error.error?.message || 'Error uploading cover photo. Please try again.';\r\n          this.snackBar.open(errorMessage, 'Close', { duration: 5000 });\r\n        }\r\n      });\r\n  }\r\n\r\n  // Utility Methods\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.profileForm.controls).forEach(key => {\r\n      const control = this.profileForm.get(key);\r\n      control?.markAsTouched();\r\n      \r\n      if (control instanceof FormArray) {\r\n        control.controls.forEach(arrayControl => {\r\n          if (arrayControl instanceof FormGroup) {\r\n            Object.keys(arrayControl.controls).forEach(arrayKey => {\r\n              arrayControl.get(arrayKey)?.markAsTouched();\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  getErrorMessage(fieldName: string): string {\r\n    const control = this.profileForm.get(fieldName);\r\n    if (!control || !control.errors) return '';\r\n\r\n    const fieldDisplayName = this.getFieldDisplayName(fieldName);\r\n\r\n    if (control.hasError('required')) {\r\n      return `${fieldDisplayName} is required`;\r\n    }\r\n    if (control.hasError('email')) {\r\n      return 'Please enter a valid email address';\r\n    }\r\n    if (control.hasError('minlength')) {\r\n      const requiredLength = control.errors['minlength'].requiredLength;\r\n      return `${fieldDisplayName} must be at least ${requiredLength} characters`;\r\n    }\r\n    if (control.hasError('maxlength')) {\r\n      const maxLength = control.errors['maxlength'].requiredLength;\r\n      return `${fieldDisplayName} must be no more than ${maxLength} characters`;\r\n    }\r\n    if (control.hasError('pattern')) {\r\n      return 'Please enter a valid URL';\r\n    }\r\n    if (control.hasError('min')) {\r\n      const minValue = control.errors['min'].min;\r\n      return `${fieldDisplayName} must be at least ${minValue}`;\r\n    }\r\n    if (control.hasError('max')) {\r\n      const maxValue = control.errors['max'].max;\r\n      return `${fieldDisplayName} cannot exceed ${maxValue}`;\r\n    }\r\n    return '';\r\n  }\r\n\r\n  private getFieldDisplayName(fieldName: string): string {\r\n    const fieldNames: { [key: string]: string } = {\r\n      'firstName': 'First Name',\r\n      'lastName': 'Last Name',\r\n      'professionalTitle': 'Professional Title',\r\n      'headline': 'Headline',\r\n      'summary': 'Summary',\r\n      'contactInfo.email': 'Email',\r\n      'contactInfo.website': 'Website',\r\n      'contactInfo.portfolioUrl': 'Portfolio URL',\r\n      'location.city': 'City',\r\n      'location.state': 'State',\r\n      'location.country': 'Country',\r\n      'location.displayLocation': 'Display Location',\r\n      'consultationRates.hourlyRate': 'Hourly Rate',\r\n      'consultationRates.sessionRate': 'Session Rate',\r\n      'consultationRates.currency': 'Currency'\r\n    };\r\n    return fieldNames[fieldName] || fieldName;\r\n  }\r\n\r\n  // Enhanced form validation\r\n  validateForm(): boolean {\r\n    if (this.profileForm.invalid) {\r\n      this.markFormGroupTouched();\r\n\r\n      // Find first invalid field and focus on it\r\n      const firstInvalidField = this.findFirstInvalidField();\r\n      if (firstInvalidField) {\r\n        firstInvalidField.focus();\r\n      }\r\n\r\n      // Show specific error message\r\n      const errors = this.getFormErrors();\r\n      if (errors.length > 0) {\r\n        this.snackBar.open(`Please fix the following errors: ${errors.join(', ')}`, 'Close', { duration: 5000 });\r\n      }\r\n\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n\r\n  private findFirstInvalidField(): HTMLElement | null {\r\n    const invalidFields = document.querySelectorAll('.mat-form-field.ng-invalid input, .mat-form-field.ng-invalid textarea, .mat-form-field.ng-invalid mat-select');\r\n    return invalidFields.length > 0 ? invalidFields[0] as HTMLElement : null;\r\n  }\r\n\r\n  private getFormErrors(): string[] {\r\n    const errors: string[] = [];\r\n\r\n    // Check main form fields\r\n    Object.keys(this.profileForm.controls).forEach(key => {\r\n      const control = this.profileForm.get(key);\r\n      if (control && control.invalid && control.touched) {\r\n        const errorMessage = this.getErrorMessage(key);\r\n        if (errorMessage) {\r\n          errors.push(errorMessage);\r\n        }\r\n      }\r\n    });\r\n\r\n    // Check nested form groups\r\n    const contactInfo = this.profileForm.get('contactInfo') as FormGroup;\r\n    if (contactInfo && contactInfo.invalid) {\r\n      Object.keys(contactInfo.controls).forEach(key => {\r\n        const control = contactInfo.get(key);\r\n        if (control && control.invalid && control.touched) {\r\n          const errorMessage = this.getErrorMessage(`contactInfo.${key}`);\r\n          if (errorMessage) {\r\n            errors.push(errorMessage);\r\n          }\r\n        }\r\n      });\r\n    }\r\n\r\n    return errors.slice(0, 3); // Limit to first 3 errors to avoid overwhelming the user\r\n  }\r\n\r\n  // Platform options for social links\r\n  getPlatformOptions() {\r\n    return [\r\n      { value: 'linkedin', label: 'LinkedIn' },\r\n      { value: 'twitter', label: 'Twitter' },\r\n      { value: 'github', label: 'GitHub' },\r\n      { value: 'behance', label: 'Behance' },\r\n      { value: 'dribbble', label: 'Dribbble' },\r\n      { value: 'instagram', label: 'Instagram' },\r\n      { value: 'facebook', label: 'Facebook' },\r\n      { value: 'youtube', label: 'YouTube' },\r\n      { value: 'other', label: 'Other' }\r\n    ];\r\n  }\r\n\r\n  // Phone type options\r\n  getPhoneTypeOptions() {\r\n    return [\r\n      { value: 'mobile', label: 'Mobile' },\r\n      { value: 'business', label: 'Business' },\r\n      { value: 'home', label: 'Home' }\r\n    ];\r\n  }\r\n\r\n  // Skill category options\r\n  getSkillCategoryOptions() {\r\n    return [\r\n      { value: 'Astrology', label: 'Astrology' },\r\n      { value: 'Crystal Healing', label: 'Crystal Healing' },\r\n      { value: 'Palmistry', label: 'Palmistry' },\r\n      { value: 'Spiritual Counseling', label: 'Spiritual Counseling' },\r\n      { value: 'Numerology', label: 'Numerology' },\r\n      { value: 'Tarot Reading', label: 'Tarot Reading' },\r\n      { value: 'Energy Healing', label: 'Energy Healing' },\r\n      { value: 'Meditation', label: 'Meditation' },\r\n      { value: 'Other', label: 'Other' }\r\n    ];\r\n  }\r\n\r\n  // Proficiency level options\r\n  getProficiencyLevelOptions() {\r\n    return [\r\n      { value: 'beginner', label: 'Beginner' },\r\n      { value: 'intermediate', label: 'Intermediate' },\r\n      { value: 'advanced', label: 'Advanced' },\r\n      { value: 'expert', label: 'Expert' }\r\n    ];\r\n  }\r\n\r\n  // Service category options\r\n  getServiceCategoryOptions() {\r\n    return [\r\n      { value: 'Reading', label: 'Reading' },\r\n      { value: 'Consultation', label: 'Consultation' },\r\n      { value: 'Healing', label: 'Healing' },\r\n      { value: 'Workshop', label: 'Workshop' },\r\n      { value: 'Course', label: 'Course' },\r\n      { value: 'Other', label: 'Other' }\r\n    ];\r\n  }\r\n\r\n  // Currency options\r\n  getCurrencyOptions() {\r\n    return [\r\n      { value: 'BGN', label: 'BGN (Bulgarian Lev)' },\r\n      { value: 'EUR', label: 'EUR (Euro)' },\r\n      { value: 'USD', label: 'USD (US Dollar)' },\r\n      { value: 'GBP', label: 'GBP (British Pound)' }\r\n    ];\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}