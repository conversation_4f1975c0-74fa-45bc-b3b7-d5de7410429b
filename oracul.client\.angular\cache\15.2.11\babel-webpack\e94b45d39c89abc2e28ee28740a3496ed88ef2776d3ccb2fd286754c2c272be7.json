{"ast": null, "code": "/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵgetDOM, PlatformLocation } from '@angular/common';\nimport { MockPlatformLocation } from '@angular/common/testing';\nimport * as i0 from '@angular/core';\nimport { NgZone, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, APP_ID, NgModule } from '@angular/core';\nimport { ɵBrowserDomAdapter, BrowserModule } from '@angular/platform-browser';\nclass BrowserDetection {\n  get _ua() {\n    if (typeof this._overrideUa === 'string') {\n      return this._overrideUa;\n    }\n    return ɵgetDOM() ? ɵgetDOM().getUserAgent() : '';\n  }\n  static setup() {\n    return new BrowserDetection(null);\n  }\n  constructor(ua) {\n    this._overrideUa = ua;\n  }\n  get isFirefox() {\n    return this._ua.indexOf('Firefox') > -1;\n  }\n  get isAndroid() {\n    return this._ua.indexOf('Mozilla/5.0') > -1 && this._ua.indexOf('Android') > -1 && this._ua.indexOf('AppleWebKit') > -1 && this._ua.indexOf('Chrome') == -1 && this._ua.indexOf('IEMobile') == -1;\n  }\n  get isEdge() {\n    return this._ua.indexOf('Edge') > -1;\n  }\n  get isWebkit() {\n    return this._ua.indexOf('AppleWebKit') > -1 && this._ua.indexOf('Edge') == -1 && this._ua.indexOf('IEMobile') == -1;\n  }\n  get isIOS7() {\n    return (this._ua.indexOf('iPhone OS 7') > -1 || this._ua.indexOf('iPad OS 7') > -1) && this._ua.indexOf('IEMobile') == -1;\n  }\n  get isSlow() {\n    return this.isAndroid || this.isIOS7;\n  }\n  get isChromeDesktop() {\n    return this._ua.indexOf('Chrome') > -1 && this._ua.indexOf('Mobile Safari') == -1 && this._ua.indexOf('Edge') == -1;\n  }\n  // \"Old Chrome\" means Chrome 3X, where there are some discrepancies in the Intl API.\n  // Android 4.4 and 5.X have such browsers by default (respectively 30 and 39).\n  get isOldChrome() {\n    return this._ua.indexOf('Chrome') > -1 && this._ua.indexOf('Chrome/3') > -1 && this._ua.indexOf('Edge') == -1;\n  }\n  get supportsShadowDom() {\n    const testEl = document.createElement('div');\n    return typeof testEl.attachShadow !== 'undefined';\n  }\n}\nconst browserDetection = BrowserDetection.setup();\nfunction dispatchEvent(element, eventType) {\n  const evt = ɵgetDOM().getDefaultDocument().createEvent('Event');\n  evt.initEvent(eventType, true, true);\n  ɵgetDOM().dispatchEvent(element, evt);\n  return evt;\n}\nfunction createMouseEvent(eventType) {\n  const evt = ɵgetDOM().getDefaultDocument().createEvent('MouseEvent');\n  evt.initEvent(eventType, true, true);\n  return evt;\n}\nfunction el(html) {\n  return getContent(createTemplate(html)).firstChild;\n}\nfunction getAttributeMap(element) {\n  const res = new Map();\n  const elAttrs = element.attributes;\n  for (let i = 0; i < elAttrs.length; i++) {\n    const attrib = elAttrs.item(i);\n    res.set(attrib.name, attrib.value);\n  }\n  return res;\n}\nconst _selfClosingTags = ['br', 'hr', 'input'];\nfunction stringifyElement(el /** TODO #9100 */) {\n  let result = '';\n  if (ɵgetDOM().isElementNode(el)) {\n    const tagName = el.tagName.toLowerCase();\n    // Opening tag\n    result += `<${tagName}`;\n    // Attributes in an ordered way\n    const attributeMap = getAttributeMap(el);\n    const sortedKeys = Array.from(attributeMap.keys()).sort();\n    for (const key of sortedKeys) {\n      const lowerCaseKey = key.toLowerCase();\n      let attValue = attributeMap.get(key);\n      if (typeof attValue !== 'string') {\n        result += ` ${lowerCaseKey}`;\n      } else {\n        // Browsers order style rules differently. Order them alphabetically for consistency.\n        if (lowerCaseKey === 'style') {\n          attValue = attValue.split(/; ?/).filter(s => !!s).sort().map(s => `${s};`).join(' ');\n        }\n        result += ` ${lowerCaseKey}=\"${attValue}\"`;\n      }\n    }\n    result += '>';\n    // Children\n    const childrenRoot = templateAwareRoot(el);\n    const children = childrenRoot ? childrenRoot.childNodes : [];\n    for (let j = 0; j < children.length; j++) {\n      result += stringifyElement(children[j]);\n    }\n    // Closing tag\n    if (_selfClosingTags.indexOf(tagName) == -1) {\n      result += `</${tagName}>`;\n    }\n  } else if (isCommentNode(el)) {\n    result += `<!--${el.nodeValue}-->`;\n  } else {\n    result += el.textContent;\n  }\n  return result;\n}\nfunction createNgZone() {\n  return new NgZone({\n    enableLongStackTrace: true,\n    shouldCoalesceEventChangeDetection: false\n  });\n}\nfunction isCommentNode(node) {\n  return node.nodeType === Node.COMMENT_NODE;\n}\nfunction isTextNode(node) {\n  return node.nodeType === Node.TEXT_NODE;\n}\nfunction getContent(node) {\n  if ('content' in node) {\n    return node.content;\n  } else {\n    return node;\n  }\n}\nfunction templateAwareRoot(el) {\n  return ɵgetDOM().isElementNode(el) && el.nodeName === 'TEMPLATE' ? getContent(el) : el;\n}\nfunction setCookie(name, value) {\n  // document.cookie is magical, assigning into it assigns/overrides one cookie value, but does\n  // not clear other cookies.\n  document.cookie = encodeURIComponent(name) + '=' + encodeURIComponent(value);\n}\nfunction hasStyle(element, styleName, styleValue) {\n  const value = element.style[styleName] || '';\n  return styleValue ? value == styleValue : value.length > 0;\n}\nfunction hasClass(element, className) {\n  return element.classList.contains(className);\n}\nfunction sortedClassList(element) {\n  return Array.prototype.slice.call(element.classList, 0).sort();\n}\nfunction createTemplate(html) {\n  const t = ɵgetDOM().getDefaultDocument().createElement('template');\n  t.innerHTML = html;\n  return t;\n}\nfunction childNodesAsList(el) {\n  const childNodes = el.childNodes;\n  const res = [];\n  for (let i = 0; i < childNodes.length; i++) {\n    res[i] = childNodes[i];\n  }\n  return res;\n}\n\n/**\n * Controls whether the `MockPlatformLocation` class should be used\n * as the `PlatformLocation` implementation when the `BrowserTestingModule`\n * is imported.\n *\n * In v16, the value of this flag will be switched to `true` to enable\n * the `MockPlatformLocation` by default.\n */\nconst ENABLE_MOCK_PLATFORM_LOCATION = false;\nfunction initBrowserTests() {\n  ɵBrowserDomAdapter.makeCurrent();\n  BrowserDetection.setup();\n}\nconst _TEST_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_INITIALIZER,\n  useValue: initBrowserTests,\n  multi: true\n}];\n/**\n * Platform for testing\n *\n * @publicApi\n */\nconst platformBrowserTesting = createPlatformFactory(platformCore, 'browserTesting', _TEST_BROWSER_PLATFORM_PROVIDERS);\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserTestingModule {}\nBrowserTestingModule.ɵfac = function BrowserTestingModule_Factory(t) {\n  return new (t || BrowserTestingModule)();\n};\nBrowserTestingModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BrowserTestingModule,\n  exports: [BrowserModule]\n});\nBrowserTestingModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: APP_ID,\n    useValue: 'a'\n  }, {\n    provide: NgZone,\n    useFactory: createNgZone\n  }, ENABLE_MOCK_PLATFORM_LOCATION ? [{\n    provide: PlatformLocation,\n    useClass: MockPlatformLocation\n  }] : []],\n  imports: [BrowserModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserTestingModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: [{\n        provide: APP_ID,\n        useValue: 'a'\n      }, {\n        provide: NgZone,\n        useFactory: createNgZone\n      }, ENABLE_MOCK_PLATFORM_LOCATION ? [{\n        provide: PlatformLocation,\n        useClass: MockPlatformLocation\n      }] : []]\n    }]\n  }], null, null);\n})();\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser/testing package.\n */\n\n/// <reference types=\"jasmine\" />\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserTestingModule, platformBrowserTesting };", "map": {"version": 3, "names": ["ɵgetDOM", "PlatformLocation", "MockPlatformLocation", "i0", "NgZone", "PLATFORM_INITIALIZER", "createPlatformFactory", "platformCore", "APP_ID", "NgModule", "ɵBrowserDomAdapter", "BrowserModule", "BrowserDetection", "_ua", "_overrideUa", "getUserAgent", "setup", "constructor", "ua", "isFirefox", "indexOf", "isAndroid", "isEdge", "isWebkit", "isIOS7", "isSlow", "isChromeDesktop", "isOldChrome", "supportsShadowDom", "testEl", "document", "createElement", "attachShadow", "browserDetection", "dispatchEvent", "element", "eventType", "evt", "getDefaultDocument", "createEvent", "initEvent", "createMouseEvent", "el", "html", "get<PERSON>ontent", "createTemplate", "<PERSON><PERSON><PERSON><PERSON>", "getAttributeMap", "res", "Map", "elAttrs", "attributes", "i", "length", "attrib", "item", "set", "name", "value", "_selfClosingTags", "stringifyElement", "result", "isElementNode", "tagName", "toLowerCase", "attributeMap", "sortedKeys", "Array", "from", "keys", "sort", "key", "lowerCaseKey", "attValue", "get", "split", "filter", "s", "map", "join", "childrenRoot", "templateAwareRoot", "children", "childNodes", "j", "isCommentNode", "nodeValue", "textContent", "createNgZone", "enableLongStackTrace", "shouldCoalesceEventChangeDetection", "node", "nodeType", "Node", "COMMENT_NODE", "isTextNode", "TEXT_NODE", "content", "nodeName", "<PERSON><PERSON><PERSON><PERSON>", "cookie", "encodeURIComponent", "hasStyle", "styleName", "styleValue", "style", "hasClass", "className", "classList", "contains", "sortedClassList", "prototype", "slice", "call", "t", "innerHTML", "childNodesAsList", "ENABLE_MOCK_PLATFORM_LOCATION", "initBrowserTests", "makeCurrent", "_TEST_BROWSER_PLATFORM_PROVIDERS", "provide", "useValue", "multi", "platformBrowserTesting", "BrowserTestingModule", "ɵfac", "ɵmod", "ɵinj", "useFactory", "useClass", "type", "args", "exports", "providers"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/platform-browser/fesm2020/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵgetDOM, PlatformLocation } from '@angular/common';\nimport { MockPlatformLocation } from '@angular/common/testing';\nimport * as i0 from '@angular/core';\nimport { NgZone, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, APP_ID, NgModule } from '@angular/core';\nimport { ɵBrowserDomAdapter, BrowserModule } from '@angular/platform-browser';\n\nclass BrowserDetection {\n    get _ua() {\n        if (typeof this._overrideUa === 'string') {\n            return this._overrideUa;\n        }\n        return ɵgetDOM() ? ɵgetDOM().getUserAgent() : '';\n    }\n    static setup() {\n        return new BrowserDetection(null);\n    }\n    constructor(ua) {\n        this._overrideUa = ua;\n    }\n    get isFirefox() {\n        return this._ua.indexOf('Firefox') > -1;\n    }\n    get isAndroid() {\n        return this._ua.indexOf('Mozilla/5.0') > -1 && this._ua.indexOf('Android') > -1 &&\n            this._ua.indexOf('AppleWebKit') > -1 && this._ua.indexOf('Chrome') == -1 &&\n            this._ua.indexOf('IEMobile') == -1;\n    }\n    get isEdge() {\n        return this._ua.indexOf('Edge') > -1;\n    }\n    get isWebkit() {\n        return this._ua.indexOf('AppleWebKit') > -1 && this._ua.indexOf('Edge') == -1 &&\n            this._ua.indexOf('IEMobile') == -1;\n    }\n    get isIOS7() {\n        return (this._ua.indexOf('iPhone OS 7') > -1 || this._ua.indexOf('iPad OS 7') > -1) &&\n            this._ua.indexOf('IEMobile') == -1;\n    }\n    get isSlow() {\n        return this.isAndroid || this.isIOS7;\n    }\n    get isChromeDesktop() {\n        return this._ua.indexOf('Chrome') > -1 && this._ua.indexOf('Mobile Safari') == -1 &&\n            this._ua.indexOf('Edge') == -1;\n    }\n    // \"Old Chrome\" means Chrome 3X, where there are some discrepancies in the Intl API.\n    // Android 4.4 and 5.X have such browsers by default (respectively 30 and 39).\n    get isOldChrome() {\n        return this._ua.indexOf('Chrome') > -1 && this._ua.indexOf('Chrome/3') > -1 &&\n            this._ua.indexOf('Edge') == -1;\n    }\n    get supportsShadowDom() {\n        const testEl = document.createElement('div');\n        return (typeof testEl.attachShadow !== 'undefined');\n    }\n}\nconst browserDetection = BrowserDetection.setup();\nfunction dispatchEvent(element, eventType) {\n    const evt = ɵgetDOM().getDefaultDocument().createEvent('Event');\n    evt.initEvent(eventType, true, true);\n    ɵgetDOM().dispatchEvent(element, evt);\n    return evt;\n}\nfunction createMouseEvent(eventType) {\n    const evt = ɵgetDOM().getDefaultDocument().createEvent('MouseEvent');\n    evt.initEvent(eventType, true, true);\n    return evt;\n}\nfunction el(html) {\n    return getContent(createTemplate(html)).firstChild;\n}\nfunction getAttributeMap(element) {\n    const res = new Map();\n    const elAttrs = element.attributes;\n    for (let i = 0; i < elAttrs.length; i++) {\n        const attrib = elAttrs.item(i);\n        res.set(attrib.name, attrib.value);\n    }\n    return res;\n}\nconst _selfClosingTags = ['br', 'hr', 'input'];\nfunction stringifyElement(el /** TODO #9100 */) {\n    let result = '';\n    if (ɵgetDOM().isElementNode(el)) {\n        const tagName = el.tagName.toLowerCase();\n        // Opening tag\n        result += `<${tagName}`;\n        // Attributes in an ordered way\n        const attributeMap = getAttributeMap(el);\n        const sortedKeys = Array.from(attributeMap.keys()).sort();\n        for (const key of sortedKeys) {\n            const lowerCaseKey = key.toLowerCase();\n            let attValue = attributeMap.get(key);\n            if (typeof attValue !== 'string') {\n                result += ` ${lowerCaseKey}`;\n            }\n            else {\n                // Browsers order style rules differently. Order them alphabetically for consistency.\n                if (lowerCaseKey === 'style') {\n                    attValue = attValue.split(/; ?/).filter(s => !!s).sort().map(s => `${s};`).join(' ');\n                }\n                result += ` ${lowerCaseKey}=\"${attValue}\"`;\n            }\n        }\n        result += '>';\n        // Children\n        const childrenRoot = templateAwareRoot(el);\n        const children = childrenRoot ? childrenRoot.childNodes : [];\n        for (let j = 0; j < children.length; j++) {\n            result += stringifyElement(children[j]);\n        }\n        // Closing tag\n        if (_selfClosingTags.indexOf(tagName) == -1) {\n            result += `</${tagName}>`;\n        }\n    }\n    else if (isCommentNode(el)) {\n        result += `<!--${el.nodeValue}-->`;\n    }\n    else {\n        result += el.textContent;\n    }\n    return result;\n}\nfunction createNgZone() {\n    return new NgZone({ enableLongStackTrace: true, shouldCoalesceEventChangeDetection: false });\n}\nfunction isCommentNode(node) {\n    return node.nodeType === Node.COMMENT_NODE;\n}\nfunction isTextNode(node) {\n    return node.nodeType === Node.TEXT_NODE;\n}\nfunction getContent(node) {\n    if ('content' in node) {\n        return node.content;\n    }\n    else {\n        return node;\n    }\n}\nfunction templateAwareRoot(el) {\n    return ɵgetDOM().isElementNode(el) && el.nodeName === 'TEMPLATE' ? getContent(el) : el;\n}\nfunction setCookie(name, value) {\n    // document.cookie is magical, assigning into it assigns/overrides one cookie value, but does\n    // not clear other cookies.\n    document.cookie = encodeURIComponent(name) + '=' + encodeURIComponent(value);\n}\nfunction hasStyle(element, styleName, styleValue) {\n    const value = element.style[styleName] || '';\n    return styleValue ? value == styleValue : value.length > 0;\n}\nfunction hasClass(element, className) {\n    return element.classList.contains(className);\n}\nfunction sortedClassList(element) {\n    return Array.prototype.slice.call(element.classList, 0).sort();\n}\nfunction createTemplate(html) {\n    const t = ɵgetDOM().getDefaultDocument().createElement('template');\n    t.innerHTML = html;\n    return t;\n}\nfunction childNodesAsList(el) {\n    const childNodes = el.childNodes;\n    const res = [];\n    for (let i = 0; i < childNodes.length; i++) {\n        res[i] = childNodes[i];\n    }\n    return res;\n}\n\n/**\n * Controls whether the `MockPlatformLocation` class should be used\n * as the `PlatformLocation` implementation when the `BrowserTestingModule`\n * is imported.\n *\n * In v16, the value of this flag will be switched to `true` to enable\n * the `MockPlatformLocation` by default.\n */\nconst ENABLE_MOCK_PLATFORM_LOCATION = false;\n\nfunction initBrowserTests() {\n    ɵBrowserDomAdapter.makeCurrent();\n    BrowserDetection.setup();\n}\nconst _TEST_BROWSER_PLATFORM_PROVIDERS = [{ provide: PLATFORM_INITIALIZER, useValue: initBrowserTests, multi: true }];\n/**\n * Platform for testing\n *\n * @publicApi\n */\nconst platformBrowserTesting = createPlatformFactory(platformCore, 'browserTesting', _TEST_BROWSER_PLATFORM_PROVIDERS);\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserTestingModule {\n}\nBrowserTestingModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nBrowserTestingModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserTestingModule, exports: [BrowserModule] });\nBrowserTestingModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserTestingModule, providers: [\n        { provide: APP_ID, useValue: 'a' },\n        { provide: NgZone, useFactory: createNgZone },\n        (ENABLE_MOCK_PLATFORM_LOCATION ? [{ provide: PlatformLocation, useClass: MockPlatformLocation }] :\n            []),\n    ], imports: [BrowserModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserModule],\n                    providers: [\n                        { provide: APP_ID, useValue: 'a' },\n                        { provide: NgZone, useFactory: createNgZone },\n                        (ENABLE_MOCK_PLATFORM_LOCATION ? [{ provide: PlatformLocation, useClass: MockPlatformLocation }] :\n                            []),\n                    ]\n                }]\n        }] });\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser/testing package.\n */\n\n/// <reference types=\"jasmine\" />\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserTestingModule, platformBrowserTesting };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,OAAO,EAAEC,gBAAgB,QAAQ,iBAAiB;AAC3D,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACnH,SAASC,kBAAkB,EAAEC,aAAa,QAAQ,2BAA2B;AAE7E,MAAMC,gBAAgB,CAAC;EACnB,IAAIC,GAAG,GAAG;IACN,IAAI,OAAO,IAAI,CAACC,WAAW,KAAK,QAAQ,EAAE;MACtC,OAAO,IAAI,CAACA,WAAW;IAC3B;IACA,OAAOd,OAAO,EAAE,GAAGA,OAAO,EAAE,CAACe,YAAY,EAAE,GAAG,EAAE;EACpD;EACA,OAAOC,KAAK,GAAG;IACX,OAAO,IAAIJ,gBAAgB,CAAC,IAAI,CAAC;EACrC;EACAK,WAAW,CAACC,EAAE,EAAE;IACZ,IAAI,CAACJ,WAAW,GAAGI,EAAE;EACzB;EACA,IAAIC,SAAS,GAAG;IACZ,OAAO,IAAI,CAACN,GAAG,CAACO,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;EAC3C;EACA,IAAIC,SAAS,GAAG;IACZ,OAAO,IAAI,CAACR,GAAG,CAACO,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAACP,GAAG,CAACO,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAC3E,IAAI,CAACP,GAAG,CAACO,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAACP,GAAG,CAACO,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IACxE,IAAI,CAACP,GAAG,CAACO,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;EAC1C;EACA,IAAIE,MAAM,GAAG;IACT,OAAO,IAAI,CAACT,GAAG,CAACO,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;EACxC;EACA,IAAIG,QAAQ,GAAG;IACX,OAAO,IAAI,CAACV,GAAG,CAACO,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAACP,GAAG,CAACO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IACzE,IAAI,CAACP,GAAG,CAACO,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;EAC1C;EACA,IAAII,MAAM,GAAG;IACT,OAAO,CAAC,IAAI,CAACX,GAAG,CAACO,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAACP,GAAG,CAACO,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,KAC9E,IAAI,CAACP,GAAG,CAACO,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;EAC1C;EACA,IAAIK,MAAM,GAAG;IACT,OAAO,IAAI,CAACJ,SAAS,IAAI,IAAI,CAACG,MAAM;EACxC;EACA,IAAIE,eAAe,GAAG;IAClB,OAAO,IAAI,CAACb,GAAG,CAACO,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAACP,GAAG,CAACO,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAC7E,IAAI,CAACP,GAAG,CAACO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;EACtC;EACA;EACA;EACA,IAAIO,WAAW,GAAG;IACd,OAAO,IAAI,CAACd,GAAG,CAACO,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAACP,GAAG,CAACO,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IACvE,IAAI,CAACP,GAAG,CAACO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;EACtC;EACA,IAAIQ,iBAAiB,GAAG;IACpB,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5C,OAAQ,OAAOF,MAAM,CAACG,YAAY,KAAK,WAAW;EACtD;AACJ;AACA,MAAMC,gBAAgB,GAAGrB,gBAAgB,CAACI,KAAK,EAAE;AACjD,SAASkB,aAAa,CAACC,OAAO,EAAEC,SAAS,EAAE;EACvC,MAAMC,GAAG,GAAGrC,OAAO,EAAE,CAACsC,kBAAkB,EAAE,CAACC,WAAW,CAAC,OAAO,CAAC;EAC/DF,GAAG,CAACG,SAAS,CAACJ,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC;EACpCpC,OAAO,EAAE,CAACkC,aAAa,CAACC,OAAO,EAAEE,GAAG,CAAC;EACrC,OAAOA,GAAG;AACd;AACA,SAASI,gBAAgB,CAACL,SAAS,EAAE;EACjC,MAAMC,GAAG,GAAGrC,OAAO,EAAE,CAACsC,kBAAkB,EAAE,CAACC,WAAW,CAAC,YAAY,CAAC;EACpEF,GAAG,CAACG,SAAS,CAACJ,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC;EACpC,OAAOC,GAAG;AACd;AACA,SAASK,EAAE,CAACC,IAAI,EAAE;EACd,OAAOC,UAAU,CAACC,cAAc,CAACF,IAAI,CAAC,CAAC,CAACG,UAAU;AACtD;AACA,SAASC,eAAe,CAACZ,OAAO,EAAE;EAC9B,MAAMa,GAAG,GAAG,IAAIC,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGf,OAAO,CAACgB,UAAU;EAClC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAME,MAAM,GAAGJ,OAAO,CAACK,IAAI,CAACH,CAAC,CAAC;IAC9BJ,GAAG,CAACQ,GAAG,CAACF,MAAM,CAACG,IAAI,EAAEH,MAAM,CAACI,KAAK,CAAC;EACtC;EACA,OAAOV,GAAG;AACd;AACA,MAAMW,gBAAgB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC;AAC9C,SAASC,gBAAgB,CAAClB,EAAE,CAAC,mBAAmB;EAC5C,IAAImB,MAAM,GAAG,EAAE;EACf,IAAI7D,OAAO,EAAE,CAAC8D,aAAa,CAACpB,EAAE,CAAC,EAAE;IAC7B,MAAMqB,OAAO,GAAGrB,EAAE,CAACqB,OAAO,CAACC,WAAW,EAAE;IACxC;IACAH,MAAM,IAAK,IAAGE,OAAQ,EAAC;IACvB;IACA,MAAME,YAAY,GAAGlB,eAAe,CAACL,EAAE,CAAC;IACxC,MAAMwB,UAAU,GAAGC,KAAK,CAACC,IAAI,CAACH,YAAY,CAACI,IAAI,EAAE,CAAC,CAACC,IAAI,EAAE;IACzD,KAAK,MAAMC,GAAG,IAAIL,UAAU,EAAE;MAC1B,MAAMM,YAAY,GAAGD,GAAG,CAACP,WAAW,EAAE;MACtC,IAAIS,QAAQ,GAAGR,YAAY,CAACS,GAAG,CAACH,GAAG,CAAC;MACpC,IAAI,OAAOE,QAAQ,KAAK,QAAQ,EAAE;QAC9BZ,MAAM,IAAK,IAAGW,YAAa,EAAC;MAChC,CAAC,MACI;QACD;QACA,IAAIA,YAAY,KAAK,OAAO,EAAE;UAC1BC,QAAQ,GAAGA,QAAQ,CAACE,KAAK,CAAC,KAAK,CAAC,CAACC,MAAM,CAACC,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC,CAACP,IAAI,EAAE,CAACQ,GAAG,CAACD,CAAC,IAAK,GAAEA,CAAE,GAAE,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;QACxF;QACAlB,MAAM,IAAK,IAAGW,YAAa,KAAIC,QAAS,GAAE;MAC9C;IACJ;IACAZ,MAAM,IAAI,GAAG;IACb;IACA,MAAMmB,YAAY,GAAGC,iBAAiB,CAACvC,EAAE,CAAC;IAC1C,MAAMwC,QAAQ,GAAGF,YAAY,GAAGA,YAAY,CAACG,UAAU,GAAG,EAAE;IAC5D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAAC7B,MAAM,EAAE+B,CAAC,EAAE,EAAE;MACtCvB,MAAM,IAAID,gBAAgB,CAACsB,QAAQ,CAACE,CAAC,CAAC,CAAC;IAC3C;IACA;IACA,IAAIzB,gBAAgB,CAACvC,OAAO,CAAC2C,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE;MACzCF,MAAM,IAAK,KAAIE,OAAQ,GAAE;IAC7B;EACJ,CAAC,MACI,IAAIsB,aAAa,CAAC3C,EAAE,CAAC,EAAE;IACxBmB,MAAM,IAAK,OAAMnB,EAAE,CAAC4C,SAAU,KAAI;EACtC,CAAC,MACI;IACDzB,MAAM,IAAInB,EAAE,CAAC6C,WAAW;EAC5B;EACA,OAAO1B,MAAM;AACjB;AACA,SAAS2B,YAAY,GAAG;EACpB,OAAO,IAAIpF,MAAM,CAAC;IAAEqF,oBAAoB,EAAE,IAAI;IAAEC,kCAAkC,EAAE;EAAM,CAAC,CAAC;AAChG;AACA,SAASL,aAAa,CAACM,IAAI,EAAE;EACzB,OAAOA,IAAI,CAACC,QAAQ,KAAKC,IAAI,CAACC,YAAY;AAC9C;AACA,SAASC,UAAU,CAACJ,IAAI,EAAE;EACtB,OAAOA,IAAI,CAACC,QAAQ,KAAKC,IAAI,CAACG,SAAS;AAC3C;AACA,SAASpD,UAAU,CAAC+C,IAAI,EAAE;EACtB,IAAI,SAAS,IAAIA,IAAI,EAAE;IACnB,OAAOA,IAAI,CAACM,OAAO;EACvB,CAAC,MACI;IACD,OAAON,IAAI;EACf;AACJ;AACA,SAASV,iBAAiB,CAACvC,EAAE,EAAE;EAC3B,OAAO1C,OAAO,EAAE,CAAC8D,aAAa,CAACpB,EAAE,CAAC,IAAIA,EAAE,CAACwD,QAAQ,KAAK,UAAU,GAAGtD,UAAU,CAACF,EAAE,CAAC,GAAGA,EAAE;AAC1F;AACA,SAASyD,SAAS,CAAC1C,IAAI,EAAEC,KAAK,EAAE;EAC5B;EACA;EACA5B,QAAQ,CAACsE,MAAM,GAAGC,kBAAkB,CAAC5C,IAAI,CAAC,GAAG,GAAG,GAAG4C,kBAAkB,CAAC3C,KAAK,CAAC;AAChF;AACA,SAAS4C,QAAQ,CAACnE,OAAO,EAAEoE,SAAS,EAAEC,UAAU,EAAE;EAC9C,MAAM9C,KAAK,GAAGvB,OAAO,CAACsE,KAAK,CAACF,SAAS,CAAC,IAAI,EAAE;EAC5C,OAAOC,UAAU,GAAG9C,KAAK,IAAI8C,UAAU,GAAG9C,KAAK,CAACL,MAAM,GAAG,CAAC;AAC9D;AACA,SAASqD,QAAQ,CAACvE,OAAO,EAAEwE,SAAS,EAAE;EAClC,OAAOxE,OAAO,CAACyE,SAAS,CAACC,QAAQ,CAACF,SAAS,CAAC;AAChD;AACA,SAASG,eAAe,CAAC3E,OAAO,EAAE;EAC9B,OAAOgC,KAAK,CAAC4C,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC9E,OAAO,CAACyE,SAAS,EAAE,CAAC,CAAC,CAACtC,IAAI,EAAE;AAClE;AACA,SAASzB,cAAc,CAACF,IAAI,EAAE;EAC1B,MAAMuE,CAAC,GAAGlH,OAAO,EAAE,CAACsC,kBAAkB,EAAE,CAACP,aAAa,CAAC,UAAU,CAAC;EAClEmF,CAAC,CAACC,SAAS,GAAGxE,IAAI;EAClB,OAAOuE,CAAC;AACZ;AACA,SAASE,gBAAgB,CAAC1E,EAAE,EAAE;EAC1B,MAAMyC,UAAU,GAAGzC,EAAE,CAACyC,UAAU;EAChC,MAAMnC,GAAG,GAAG,EAAE;EACd,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+B,UAAU,CAAC9B,MAAM,EAAED,CAAC,EAAE,EAAE;IACxCJ,GAAG,CAACI,CAAC,CAAC,GAAG+B,UAAU,CAAC/B,CAAC,CAAC;EAC1B;EACA,OAAOJ,GAAG;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqE,6BAA6B,GAAG,KAAK;AAE3C,SAASC,gBAAgB,GAAG;EACxB5G,kBAAkB,CAAC6G,WAAW,EAAE;EAChC3G,gBAAgB,CAACI,KAAK,EAAE;AAC5B;AACA,MAAMwG,gCAAgC,GAAG,CAAC;EAAEC,OAAO,EAAEpH,oBAAoB;EAAEqH,QAAQ,EAAEJ,gBAAgB;EAAEK,KAAK,EAAE;AAAK,CAAC,CAAC;AACrH;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAGtH,qBAAqB,CAACC,YAAY,EAAE,gBAAgB,EAAEiH,gCAAgC,CAAC;AACtH;AACA;AACA;AACA;AACA;AACA,MAAMK,oBAAoB,CAAC;AAE3BA,oBAAoB,CAACC,IAAI;EAAA,iBAAyFD,oBAAoB;AAAA,CAAkD;AACxLA,oBAAoB,CAACE,IAAI,kBAD+E5H,EAAE;EAAA,MACS0H,oBAAoB;EAAA,UAAYlH,aAAa;AAAA,EAAI;AACpKkH,oBAAoB,CAACG,IAAI,kBAF+E7H,EAAE;EAAA,WAE0C,CAC5I;IAAEsH,OAAO,EAAEjH,MAAM;IAAEkH,QAAQ,EAAE;EAAI,CAAC,EAClC;IAAED,OAAO,EAAErH,MAAM;IAAE6H,UAAU,EAAEzC;EAAa,CAAC,EAC5C6B,6BAA6B,GAAG,CAAC;IAAEI,OAAO,EAAExH,gBAAgB;IAAEiI,QAAQ,EAAEhI;EAAqB,CAAC,CAAC,GAC5F,EAAE,CACT;EAAA,UAAYS,aAAa;AAAA,EAAI;AAClC;EAAA,mDARwGR,EAAE,mBAQd0H,oBAAoB,EAAc,CAAC;IACnHM,IAAI,EAAE1H,QAAQ;IACd2H,IAAI,EAAE,CAAC;MACCC,OAAO,EAAE,CAAC1H,aAAa,CAAC;MACxB2H,SAAS,EAAE,CACP;QAAEb,OAAO,EAAEjH,MAAM;QAAEkH,QAAQ,EAAE;MAAI,CAAC,EAClC;QAAED,OAAO,EAAErH,MAAM;QAAE6H,UAAU,EAAEzC;MAAa,CAAC,EAC5C6B,6BAA6B,GAAG,CAAC;QAAEI,OAAO,EAAExH,gBAAgB;QAAEiI,QAAQ,EAAEhI;MAAqB,CAAC,CAAC,GAC5F,EAAE;IAEd,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAAS2H,oBAAoB,EAAED,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}