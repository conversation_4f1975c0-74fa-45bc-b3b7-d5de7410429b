export interface UserProfile {
  id: number;
  userId: number;
  username: string;
  slug: string; // URL-friendly identifier
  isPublic: boolean;
  profileCompletionPercentage: number;
  
  // Header Section
  profilePhotoUrl?: string;
  coverPhotoUrl?: string;
  firstName: string;
  lastName: string;
  professionalTitle?: string;
  headline?: string;
  location?: ProfileLocation;
  
  // Contact Information
  contactInfo: ContactInformation;
  
  // Professional Summary
  summary?: string;
  
  // Skills & Expertise
  skills: ProfileSkill[];
  
  // Blog Posts
  blogPosts: BlogPost[];
  
  // Achievements & Certifications
  achievements: Achievement[];
  certifications: Certification[];
  
  // Experience Timeline
  experiences: WorkExperience[];
  
  // Portfolio/Projects
  portfolioItems: PortfolioItem[];
  
  // Social Links
  socialLinks: SocialLink[];

  // Consultation & Services
  consultationRates?: ConsultationRates;
  serviceOfferings?: ServiceOffering[];

  // Analytics
  profileViews: number;
  lastViewedAt?: Date;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

export interface ProfileLocation {
  city?: string;
  state?: string;
  country?: string;
  displayLocation?: string;
}

export interface ContactInformation {
  businessAddress?: BusinessAddress;
  phoneNumbers: PhoneNumber[];
  email?: string;
  isEmailPublic: boolean;
  website?: string;
  portfolioUrl?: string;
}

export interface BusinessAddress {
  street?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  isPublic: boolean;
}

export interface PhoneNumber {
  id?: number;
  number: string;
  type: 'mobile' | 'business' | 'home';
  isPublic: boolean;
  isPrimary: boolean;
}

export interface ProfileSkill {
  id?: number;
  name: string;
  category?: string;
  endorsements: number;
  isEndorsedByCurrentUser?: boolean;
  proficiencyLevel?: 'beginner' | 'intermediate' | 'advanced' | 'expert';
}

export interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  content?: string;
  publishedAt: Date;
  readCount: number;
  tags: string[];
  featuredImageUrl?: string;
  slug: string;
}

export interface Achievement {
  id?: number;
  title: string;
  description: string;
  achievedAt: Date;
  organization?: string;
  imageUrl?: string;
  verificationUrl?: string;
}

export interface Certification {
  id?: number;
  name: string;
  issuingOrganization: string;
  issueDate: Date;
  expirationDate?: Date;
  credentialId?: string;
  credentialUrl?: string;
  imageUrl?: string;
}

export interface WorkExperience {
  id?: number;
  company: string;
  position: string;
  startDate: Date;
  endDate?: Date;
  isCurrent: boolean;
  description: string;
  location?: string;
  companyLogoUrl?: string;
  achievements?: string[];
}

export interface PortfolioItem {
  id?: number;
  title: string;
  description: string;
  imageUrls: string[];
  projectUrl?: string;
  githubUrl?: string;
  technologies: string[];
  completedAt: Date;
  clientName?: string;
  testimonial?: ClientTestimonial;
  category?: string;
}

export interface ClientTestimonial {
  id?: number;
  clientName: string;
  clientTitle?: string;
  clientCompany?: string;
  testimonialText: string;
  rating?: number;
  clientPhotoUrl?: string;
  givenAt: Date;
}

export interface SocialLink {
  id?: number;
  platform: 'linkedin' | 'twitter' | 'github' | 'behance' | 'dribbble' | 'instagram' | 'facebook' | 'youtube' | 'other';
  url: string;
  displayName?: string;
  isPublic: boolean;
}

export interface ProfileAnalytics {
  profileViews: number;
  uniqueVisitors: number;
  viewsThisMonth: number;
  viewsThisWeek: number;
  topReferrers: string[];
  skillEndorsements: number;
  blogPostViews: number;
  contactButtonClicks: number;
}

export interface ConsultationRates {
  hourlyRate?: number;
  sessionRate?: number;
  currency: string;
}

export interface ServiceOffering {
  id?: number;
  name: string;
  description: string;
  price: number;
  currency: string;
  duration?: number; // in minutes
  category?: string;
  isActive: boolean;
}

export interface ProfileUpdateRequest {
  firstName: string;
  lastName: string;
  profilePhotoUrl?: string;
  coverPhotoUrl?: string;
  professionalTitle?: string;
  headline?: string;
  location?: ProfileLocation;
  contactInfo?: Partial<ContactInformation>;
  summary?: string;
  isPublic?: boolean;
  skills?: ProfileSkill[];
  consultationRates?: ConsultationRates;
  serviceOfferings?: ServiceOffering[];
}

export interface SkillEndorsementRequest {
  skillId: number;
  endorsedUserId: number;
}

export interface ProfileViewRequest {
  profileId: number;
  viewerUserId?: number;
  referrer?: string;
  userAgent?: string;
}

export interface ProfileSearchFilters {
  location?: string;
  skills?: string[];
  experience?: string;
  availability?: boolean;
  sortBy?: 'relevance' | 'views' | 'recent' | 'alphabetical';
}

export interface ProfileSearchResult {
  profiles: UserProfile[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  pageSize: number;
}
