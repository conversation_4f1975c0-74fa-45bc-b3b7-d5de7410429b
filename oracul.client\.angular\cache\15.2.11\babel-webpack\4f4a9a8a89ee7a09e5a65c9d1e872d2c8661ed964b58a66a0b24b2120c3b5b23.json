{"ast": null, "code": "function cov_2oge6r1owa() {\n  var path = \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\app.component.ts\";\n  var hash = \"4d14da99f2af51f141a24a3ea99c6f10656faaeb\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\app.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 6,\n          column: 19\n        },\n        end: {\n          line: 18,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 8,\n          column: 8\n        },\n        end: {\n          line: 8,\n          column: 41\n        }\n      },\n      \"2\": {\n        start: {\n          line: 9,\n          column: 8\n        },\n        end: {\n          line: 9,\n          column: 37\n        }\n      },\n      \"3\": {\n        start: {\n          line: 13,\n          column: 8\n        },\n        end: {\n          line: 13,\n          column: 44\n        }\n      },\n      \"4\": {\n        start: {\n          line: 15,\n          column: 13\n        },\n        end: {\n          line: 17,\n          column: 6\n        }\n      },\n      \"5\": {\n        start: {\n          line: 15,\n          column: 41\n        },\n        end: {\n          line: 17,\n          column: 5\n        }\n      },\n      \"6\": {\n        start: {\n          line: 19,\n          column: 0\n        },\n        end: {\n          line: 25,\n          column: 17\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 7,\n            column: 4\n          },\n          end: {\n            line: 7,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 7,\n            column: 30\n          },\n          end: {\n            line: 10,\n            column: 5\n          }\n        },\n        line: 7\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 11,\n            column: 4\n          },\n          end: {\n            line: 11,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 11,\n            column: 15\n          },\n          end: {\n            line: 14,\n            column: 5\n          }\n        },\n        line: 11\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 15,\n            column: 35\n          },\n          end: {\n            line: 15,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 15,\n            column: 41\n          },\n          end: {\n            line: 17,\n            column: 5\n          }\n        },\n        line: 15\n      }\n    },\n    branchMap: {},\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0\n    },\n    b: {},\n    inputSourceMap: {\n      version: 3,\n      file: \"app.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\app.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EAAE,SAAS,EAAU,MAAM,eAAe,CAAC;AAClD,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAOnD,IAAM,YAAY,GAAlB,MAAM,YAAY;IAGvB,YACU,YAA0B;QAA1B,iBAAY,GAAZ,YAAY,CAAc;QAHpC,UAAK,GAAG,eAAe,CAAC;IAIrB,CAAC;IAEJ,QAAQ;QACN,mBAAmB;QACnB,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;IACtC,CAAC;;;;;AAVU,YAAY;IALxB,SAAS,CAAC;QACT,QAAQ,EAAE,UAAU;QACpB,8BAAmC;;KAEpC,CAAC;GACW,YAAY,CAWxB;SAXY,YAAY\",\n      sourcesContent: [\"import { Component, OnInit } from '@angular/core';\\r\\nimport { ThemeService } from './core/theme/theme.service';\\r\\n\\r\\n@Component({\\r\\n  selector: 'app-root',\\r\\n  templateUrl: './app.component.html',\\r\\n  styleUrls: ['./app.component.css']\\r\\n})\\r\\nexport class AppComponent implements OnInit {\\r\\n  title = 'oracul.client';\\r\\n\\r\\n  constructor(\\r\\n    private themeService: ThemeService\\r\\n  ) {}\\r\\n\\r\\n  ngOnInit() {\\r\\n    // Initialize theme\\r\\n    this.themeService.getCurrentTheme();\\r\\n  }\\r\\n}\\r\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"4d14da99f2af51f141a24a3ea99c6f10656faaeb\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_2oge6r1owa = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_2oge6r1owa();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./app.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./app.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { ThemeService } from './core/theme/theme.service';\ncov_2oge6r1owa().s[0]++;\nlet AppComponent = class AppComponent {\n  constructor(themeService) {\n    cov_2oge6r1owa().f[0]++;\n    cov_2oge6r1owa().s[1]++;\n    this.themeService = themeService;\n    cov_2oge6r1owa().s[2]++;\n    this.title = 'oracul.client';\n  }\n  ngOnInit() {\n    cov_2oge6r1owa().f[1]++;\n    cov_2oge6r1owa().s[3]++;\n    // Initialize theme\n    this.themeService.getCurrentTheme();\n  }\n  static {\n    cov_2oge6r1owa().s[4]++;\n    this.ctorParameters = () => {\n      cov_2oge6r1owa().f[2]++;\n      cov_2oge6r1owa().s[5]++;\n      return [{\n        type: ThemeService\n      }];\n    };\n  }\n};\ncov_2oge6r1owa().s[6]++;\nAppComponent = __decorate([Component({\n  selector: 'app-root',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AppComponent);\nexport { AppComponent };", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,4BAA4B;AAAC;AAOnD,IAAMC,YAAY,GAAlB,MAAMA,YAAY;EAGvBC,YACUC,YAA0B;IAAA;IAAA;IAA1B,iBAAY,GAAZA,YAAY;IAAc;IAHpC,UAAK,GAAG,eAAe;EAIpB;EAEHC,QAAQ;IAAA;IAAA;IACN;IACA,IAAI,CAACD,YAAY,CAACE,eAAe,EAAE;EACrC;;;;;;;;;;;;;AAVWJ,YAAY,eALxBF,SAAS,CAAC;EACTO,QAAQ,EAAE,UAAU;EACpBC,8BAAmC;;CAEpC,CAAC,GACWN,YAAY,CAWxB;SAXYA,YAAY", "names": ["Component", "ThemeService", "AppComponent", "constructor", "themeService", "ngOnInit", "getCurrentTheme", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ThemeService } from './core/theme/theme.service';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.css']\r\n})\r\nexport class AppComponent implements OnInit {\r\n  title = 'oracul.client';\r\n\r\n  constructor(\r\n    private themeService: ThemeService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    // Initialize theme\r\n    this.themeService.getCurrentTheme();\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}