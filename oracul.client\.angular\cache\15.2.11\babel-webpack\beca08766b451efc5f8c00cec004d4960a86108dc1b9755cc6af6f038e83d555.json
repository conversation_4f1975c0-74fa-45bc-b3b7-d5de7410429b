{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, Directive, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Inject, Optional, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport * as i1 from '@angular/cdk/a11y';\nimport * as i2 from '@angular/material/core';\nimport { mixinTabIndex, mixinColor, mixinDisableRipple, mixinDisabled, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { CommonModule } from '@angular/common';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token to be used to override the default options for `mat-slide-toggle`. */\nconst _c0 = [\"switch\"];\nconst _c1 = [\"*\"];\nconst MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS = new InjectionToken('mat-slide-toggle-default-options', {\n  providedIn: 'root',\n  factory: () => ({\n    disableToggleValue: false\n  })\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** @docs-private */\nconst MAT_SLIDE_TOGGLE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSlideToggle),\n  multi: true\n};\n/** Change event object emitted by a slide toggle. */\nclass MatSlideToggleChange {\n  constructor( /** The source slide toggle of the event. */\n  source, /** The new `checked` value of the slide toggle. */\n  checked) {\n    this.source = source;\n    this.checked = checked;\n  }\n}\n// Increasing integer for generating unique ids for slide-toggle components.\nlet nextUniqueId = 0;\n// Boilerplate for applying mixins to MatSlideToggle.\n/** @docs-private */\nconst _MatSlideToggleMixinBase = mixinTabIndex(mixinColor(mixinDisableRipple(mixinDisabled(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n}))));\nclass _MatSlideToggleBase extends _MatSlideToggleMixinBase {\n  /** Whether the slide-toggle is required. */\n  get required() {\n    return this._required;\n  }\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n  }\n  /** Whether the slide-toggle element is checked or not. */\n  get checked() {\n    return this._checked;\n  }\n  set checked(value) {\n    this._checked = coerceBooleanProperty(value);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Returns the unique id for the visual hidden input. */\n  get inputId() {\n    return `${this.id || this._uniqueId}-input`;\n  }\n  constructor(elementRef, _focusMonitor, _changeDetectorRef, tabIndex, defaults, animationMode, idPrefix) {\n    super(elementRef);\n    this._focusMonitor = _focusMonitor;\n    this._changeDetectorRef = _changeDetectorRef;\n    this.defaults = defaults;\n    this._onChange = _ => {};\n    this._onTouched = () => {};\n    this._required = false;\n    this._checked = false;\n    /** Name value will be applied to the input element if present. */\n    this.name = null;\n    /** Whether the label should appear after or before the slide-toggle. Defaults to 'after'. */\n    this.labelPosition = 'after';\n    /** Used to set the aria-label attribute on the underlying input element. */\n    this.ariaLabel = null;\n    /** Used to set the aria-labelledby attribute on the underlying input element. */\n    this.ariaLabelledby = null;\n    /** An event will be dispatched each time the slide-toggle changes its value. */\n    this.change = new EventEmitter();\n    /**\n     * An event will be dispatched each time the slide-toggle input is toggled.\n     * This event is always emitted when the user toggles the slide toggle, but this does not mean\n     * the slide toggle's value has changed.\n     */\n    this.toggleChange = new EventEmitter();\n    this.tabIndex = parseInt(tabIndex) || 0;\n    this.color = this.defaultColor = defaults.color || 'accent';\n    this._noopAnimations = animationMode === 'NoopAnimations';\n    this.id = this._uniqueId = `${idPrefix}${++nextUniqueId}`;\n  }\n  ngAfterContentInit() {\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n      if (focusOrigin === 'keyboard' || focusOrigin === 'program') {\n        this._focused = true;\n        this._changeDetectorRef.markForCheck();\n      } else if (!focusOrigin) {\n        // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n        // Angular does not expect events to be raised during change detection, so any state\n        // change (such as a form control's ng-touched) will cause a changed-after-checked error.\n        // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n        // telling the form control it has been touched until the next tick.\n        Promise.resolve().then(() => {\n          this._focused = false;\n          this._onTouched();\n          this._changeDetectorRef.markForCheck();\n        });\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n  }\n  /** Implemented as part of ControlValueAccessor. */\n  writeValue(value) {\n    this.checked = !!value;\n  }\n  /** Implemented as part of ControlValueAccessor. */\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /** Implemented as part of ControlValueAccessor. */\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /** Implemented as a part of ControlValueAccessor. */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Toggles the checked state of the slide-toggle. */\n  toggle() {\n    this.checked = !this.checked;\n    this._onChange(this.checked);\n  }\n  /**\n   * Emits a change event on the `change` output. Also notifies the FormControl about the change.\n   */\n  _emitChangeEvent() {\n    this._onChange(this.checked);\n    this.change.emit(this._createChangeEvent(this.checked));\n  }\n}\n_MatSlideToggleBase.ɵfac = function _MatSlideToggleBase_Factory(t) {\n  i0.ɵɵinvalidFactory();\n};\n_MatSlideToggleBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatSlideToggleBase,\n  inputs: {\n    name: \"name\",\n    id: \"id\",\n    labelPosition: \"labelPosition\",\n    ariaLabel: [\"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n    ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"],\n    required: \"required\",\n    checked: \"checked\"\n  },\n  outputs: {\n    change: \"change\",\n    toggleChange: \"toggleChange\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatSlideToggleBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.FocusMonitor\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined\n    }, {\n      type: undefined\n    }, {\n      type: undefined\n    }, {\n      type: undefined\n    }];\n  }, {\n    name: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    required: [{\n      type: Input\n    }],\n    checked: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    toggleChange: [{\n      type: Output\n    }]\n  });\n})();\nclass MatSlideToggle extends _MatSlideToggleBase {\n  /** Returns the unique id for the visual hidden button. */\n  get buttonId() {\n    return `${this.id || this._uniqueId}-button`;\n  }\n  constructor(elementRef, focusMonitor, changeDetectorRef, tabIndex, defaults, animationMode) {\n    super(elementRef, focusMonitor, changeDetectorRef, tabIndex, defaults, animationMode, 'mat-mdc-slide-toggle-');\n    this._labelId = this._uniqueId + '-label';\n  }\n  /** Method being called whenever the underlying button is clicked. */\n  _handleClick() {\n    this.toggleChange.emit();\n    if (!this.defaults.disableToggleValue) {\n      this.checked = !this.checked;\n      this._onChange(this.checked);\n      this.change.emit(new MatSlideToggleChange(this, this.checked));\n    }\n  }\n  /** Focuses the slide-toggle. */\n  focus() {\n    this._switchElement.nativeElement.focus();\n  }\n  _createChangeEvent(isChecked) {\n    return new MatSlideToggleChange(this, isChecked);\n  }\n  _getAriaLabelledBy() {\n    if (this.ariaLabelledby) {\n      return this.ariaLabelledby;\n    }\n    // Even though we have a `label` element with a `for` pointing to the button, we need the\n    // `aria-labelledby`, because the button gets flagged as not having a label by tools like axe.\n    return this.ariaLabel ? null : this._labelId;\n  }\n}\nMatSlideToggle.ɵfac = function MatSlideToggle_Factory(t) {\n  return new (t || MatSlideToggle)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\nMatSlideToggle.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatSlideToggle,\n  selectors: [[\"mat-slide-toggle\"]],\n  viewQuery: function MatSlideToggle_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._switchElement = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-mdc-slide-toggle\"],\n  hostVars: 11,\n  hostBindings: function MatSlideToggle_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"id\", ctx.id);\n      i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"name\", null)(\"aria-labelledby\", null);\n      i0.ɵɵclassProp(\"mat-mdc-slide-toggle-focused\", ctx._focused)(\"mat-mdc-slide-toggle-checked\", ctx.checked)(\"_mat-animation-noopable\", ctx._noopAnimations);\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    disableRipple: \"disableRipple\",\n    color: \"color\",\n    tabIndex: \"tabIndex\"\n  },\n  exportAs: [\"matSlideToggle\"],\n  features: [i0.ɵɵProvidersFeature([MAT_SLIDE_TOGGLE_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c1,\n  decls: 17,\n  vars: 24,\n  consts: [[1, \"mdc-form-field\"], [\"role\", \"switch\", \"type\", \"button\", 1, \"mdc-switch\", 3, \"tabIndex\", \"disabled\", \"click\"], [\"switch\", \"\"], [1, \"mdc-switch__track\"], [1, \"mdc-switch__handle-track\"], [1, \"mdc-switch__handle\"], [1, \"mdc-switch__shadow\"], [1, \"mdc-elevation-overlay\"], [1, \"mdc-switch__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-slide-toggle-ripple\", \"mat-mdc-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mdc-switch__icons\"], [\"viewBox\", \"0 0 24 24\", 1, \"mdc-switch__icon\", \"mdc-switch__icon--on\"], [\"d\", \"M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z\"], [\"viewBox\", \"0 0 24 24\", 1, \"mdc-switch__icon\", \"mdc-switch__icon--off\"], [\"d\", \"M20 13H4v-2h16v2z\"], [3, \"for\", \"click\"]],\n  template: function MatSlideToggle_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1, 2);\n      i0.ɵɵlistener(\"click\", function MatSlideToggle_Template_button_click_1_listener() {\n        return ctx._handleClick();\n      });\n      i0.ɵɵelement(3, \"div\", 3);\n      i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n      i0.ɵɵelement(7, \"div\", 7);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(8, \"div\", 8);\n      i0.ɵɵelement(9, \"div\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(10, \"div\", 10);\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(11, \"svg\", 11);\n      i0.ɵɵelement(12, \"path\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"svg\", 13);\n      i0.ɵɵelement(14, \"path\", 14);\n      i0.ɵɵelementEnd()()()()();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelementStart(15, \"label\", 15);\n      i0.ɵɵlistener(\"click\", function MatSlideToggle_Template_label_click_15_listener($event) {\n        return $event.stopPropagation();\n      });\n      i0.ɵɵprojection(16);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      const _r0 = i0.ɵɵreference(2);\n      i0.ɵɵclassProp(\"mdc-form-field--align-end\", ctx.labelPosition == \"before\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵclassProp(\"mdc-switch--selected\", ctx.checked)(\"mdc-switch--unselected\", !ctx.checked)(\"mdc-switch--checked\", ctx.checked)(\"mdc-switch--disabled\", ctx.disabled);\n      i0.ɵɵproperty(\"tabIndex\", ctx.tabIndex)(\"disabled\", ctx.disabled);\n      i0.ɵɵattribute(\"id\", ctx.buttonId)(\"name\", ctx.name)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx._getAriaLabelledBy())(\"aria-describedby\", ctx.ariaDescribedby)(\"aria-required\", ctx.required || null)(\"aria-checked\", ctx.checked);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"matRippleTrigger\", _r0)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleCentered\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"for\", ctx.buttonId);\n      i0.ɵɵattribute(\"id\", ctx._labelId);\n    }\n  },\n  dependencies: [i2.MatRipple],\n  styles: [\".mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1);background-color:var(--mdc-elevation-overlay-color, #fff)}.mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative}.mdc-switch[hidden]{display:none}.mdc-switch:disabled{cursor:default;pointer-events:none}.mdc-switch__track{overflow:hidden;position:relative;width:100%}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;width:100%}@media screen and (forced-colors: active){.mdc-switch__track::before,.mdc-switch__track::after{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(-100%)}[dir=rtl] .mdc-switch__track::after,.mdc-switch__track[dir=rtl]::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track[dir=rtl]::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0)}[dir=rtl] .mdc-switch__handle-track,.mdc-switch__handle-track[dir=rtl]{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track,.mdc-switch--selected .mdc-switch__handle-track[dir=rtl]{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto}[dir=rtl] .mdc-switch__handle,.mdc-switch__handle[dir=rtl]{left:auto;right:0}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media screen and (forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-elevation-overlay{bottom:0;left:0;right:0;top:0}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1}.mdc-switch:disabled .mdc-switch__ripple{display:none}.mdc-switch__icons{height:100%;position:relative;width:100%;z-index:1}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle{display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mdc-switch{width:var(--mdc-switch-track-width, 36px)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color, #310077)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color, #310077)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color, #310077)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color, #616161)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color, #212121)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color, #212121)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color, #212121)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color, #424242)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color, var(--mdc-theme-surface, #fff))}.mat-mdc-slide-toggle .mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation, 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation, 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__focus-ring-wrapper,.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle{height:var(--mdc-switch-handle-height, 20px)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__handle::after{opacity:var(--mdc-switch-disabled-handle-opacity, 0.38)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle{border-radius:var(--mdc-switch-handle-shape, 10px)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle{width:var(--mdc-switch-handle-width, 20px)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle-track{width:calc(100% - var(--mdc-switch-handle-width, 20px))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled .mdc-switch__icon{fill:var(--mdc-switch-selected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled .mdc-switch__icon{fill:var(--mdc-switch-unselected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity, 0.38)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size, 18px);height:var(--mdc-switch-selected-icon-size, 18px)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size, 18px);height:var(--mdc-switch-unselected-icon-size, 18px)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-hover-state-layer-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-focus-state-layer-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-pressed-state-layer-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-hover-state-layer-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-focus-state-layer-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-pressed-state-layer-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-selected-hover-state-layer-opacity, 0.04)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-selected-focus-state-layer-opacity, 0.12)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-unselected-hover-state-layer-opacity, 0.04)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-unselected-focus-state-layer-opacity, 0.12)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__ripple{height:var(--mdc-switch-state-layer-size, 48px);width:var(--mdc-switch-state-layer-size, 48px)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__track{height:var(--mdc-switch-track-height, 14px)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity, 0.12)}.mat-mdc-slide-toggle .mdc-switch:enabled .mdc-switch__track::after{background:var(--mdc-switch-selected-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color, #424242)}.mat-mdc-slide-toggle .mdc-switch:enabled .mdc-switch__track::before{background:var(--mdc-switch-unselected-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color, #424242)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__track{border-radius:var(--mdc-switch-track-shape, 7px)}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle .mdc-switch__ripple::after{content:\\\"\\\";opacity:0}.mat-mdc-slide-toggle .mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:opacity 75ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-mdc-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-elevation-overlay,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSlideToggle, [{\n    type: Component,\n    args: [{\n      selector: 'mat-slide-toggle',\n      inputs: ['disabled', 'disableRipple', 'color', 'tabIndex'],\n      host: {\n        'class': 'mat-mdc-slide-toggle',\n        '[id]': 'id',\n        // Needs to be removed since it causes some a11y issues (see #21266).\n        '[attr.tabindex]': 'null',\n        '[attr.aria-label]': 'null',\n        '[attr.name]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[class.mat-mdc-slide-toggle-focused]': '_focused',\n        '[class.mat-mdc-slide-toggle-checked]': 'checked',\n        '[class._mat-animation-noopable]': '_noopAnimations'\n      },\n      exportAs: 'matSlideToggle',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [MAT_SLIDE_TOGGLE_VALUE_ACCESSOR],\n      template: \"<div class=\\\"mdc-form-field\\\"\\n     [class.mdc-form-field--align-end]=\\\"labelPosition == 'before'\\\">\\n  <button\\n    class=\\\"mdc-switch\\\"\\n    role=\\\"switch\\\"\\n    type=\\\"button\\\"\\n    [class.mdc-switch--selected]=\\\"checked\\\"\\n    [class.mdc-switch--unselected]=\\\"!checked\\\"\\n    [class.mdc-switch--checked]=\\\"checked\\\"\\n    [class.mdc-switch--disabled]=\\\"disabled\\\"\\n    [tabIndex]=\\\"tabIndex\\\"\\n    [disabled]=\\\"disabled\\\"\\n    [attr.id]=\\\"buttonId\\\"\\n    [attr.name]=\\\"name\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-labelledby]=\\\"_getAriaLabelledBy()\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n    [attr.aria-required]=\\\"required || null\\\"\\n    [attr.aria-checked]=\\\"checked\\\"\\n    (click)=\\\"_handleClick()\\\"\\n    #switch>\\n    <div class=\\\"mdc-switch__track\\\"></div>\\n    <div class=\\\"mdc-switch__handle-track\\\">\\n      <div class=\\\"mdc-switch__handle\\\">\\n        <div class=\\\"mdc-switch__shadow\\\">\\n          <div class=\\\"mdc-elevation-overlay\\\"></div>\\n        </div>\\n        <div class=\\\"mdc-switch__ripple\\\">\\n          <div class=\\\"mat-mdc-slide-toggle-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n            [matRippleTrigger]=\\\"switch\\\"\\n            [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n            [matRippleCentered]=\\\"true\\\"></div>\\n        </div>\\n        <div class=\\\"mdc-switch__icons\\\">\\n          <svg class=\\\"mdc-switch__icon mdc-switch__icon--on\\\" viewBox=\\\"0 0 24 24\\\">\\n            <path d=\\\"M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z\\\" />\\n          </svg>\\n          <svg class=\\\"mdc-switch__icon mdc-switch__icon--off\\\" viewBox=\\\"0 0 24 24\\\">\\n            <path d=\\\"M20 13H4v-2h16v2z\\\" />\\n          </svg>\\n        </div>\\n      </div>\\n    </div>\\n  </button>\\n\\n  <!--\\n    Clicking on the label will trigger another click event from the button.\\n    Stop propagation here so other listeners further up in the DOM don't execute twice.\\n  -->\\n  <label [for]=\\\"buttonId\\\" [attr.id]=\\\"_labelId\\\" (click)=\\\"$event.stopPropagation()\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\",\n      styles: [\".mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1);background-color:var(--mdc-elevation-overlay-color, #fff)}.mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative}.mdc-switch[hidden]{display:none}.mdc-switch:disabled{cursor:default;pointer-events:none}.mdc-switch__track{overflow:hidden;position:relative;width:100%}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;width:100%}@media screen and (forced-colors: active){.mdc-switch__track::before,.mdc-switch__track::after{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(-100%)}[dir=rtl] .mdc-switch__track::after,.mdc-switch__track[dir=rtl]::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track[dir=rtl]::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0)}[dir=rtl] .mdc-switch__handle-track,.mdc-switch__handle-track[dir=rtl]{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track,.mdc-switch--selected .mdc-switch__handle-track[dir=rtl]{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto}[dir=rtl] .mdc-switch__handle,.mdc-switch__handle[dir=rtl]{left:auto;right:0}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media screen and (forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-elevation-overlay{bottom:0;left:0;right:0;top:0}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1}.mdc-switch:disabled .mdc-switch__ripple{display:none}.mdc-switch__icons{height:100%;position:relative;width:100%;z-index:1}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle{display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mdc-switch{width:var(--mdc-switch-track-width, 36px)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color, #310077)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color, #310077)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color, #310077)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color, #616161)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color, #212121)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color, #212121)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color, #212121)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color, #424242)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color, var(--mdc-theme-surface, #fff))}.mat-mdc-slide-toggle .mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation, 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation, 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__focus-ring-wrapper,.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle{height:var(--mdc-switch-handle-height, 20px)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__handle::after{opacity:var(--mdc-switch-disabled-handle-opacity, 0.38)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle{border-radius:var(--mdc-switch-handle-shape, 10px)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle{width:var(--mdc-switch-handle-width, 20px)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle-track{width:calc(100% - var(--mdc-switch-handle-width, 20px))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled .mdc-switch__icon{fill:var(--mdc-switch-selected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled .mdc-switch__icon{fill:var(--mdc-switch-unselected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity, 0.38)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size, 18px);height:var(--mdc-switch-selected-icon-size, 18px)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size, 18px);height:var(--mdc-switch-unselected-icon-size, 18px)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-hover-state-layer-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-focus-state-layer-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-pressed-state-layer-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-hover-state-layer-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-focus-state-layer-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-pressed-state-layer-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-selected-hover-state-layer-opacity, 0.04)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-selected-focus-state-layer-opacity, 0.12)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-unselected-hover-state-layer-opacity, 0.04)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-unselected-focus-state-layer-opacity, 0.12)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__ripple{height:var(--mdc-switch-state-layer-size, 48px);width:var(--mdc-switch-state-layer-size, 48px)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__track{height:var(--mdc-switch-track-height, 14px)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity, 0.12)}.mat-mdc-slide-toggle .mdc-switch:enabled .mdc-switch__track::after{background:var(--mdc-switch-selected-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color, #424242)}.mat-mdc-slide-toggle .mdc-switch:enabled .mdc-switch__track::before{background:var(--mdc-switch-unselected-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color, #424242)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__track{border-radius:var(--mdc-switch-track-shape, 7px)}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle .mdc-switch__ripple::after{content:\\\"\\\";opacity:0}.mat-mdc-slide-toggle .mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:opacity 75ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-mdc-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-elevation-overlay,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.FocusMonitor\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    _switchElement: [{\n      type: ViewChild,\n      args: ['switch']\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MatSlideToggleRequiredValidator),\n  multi: true\n};\n/**\n * Validator for Material slide-toggle components with the required attribute in a\n * template-driven form. The default validator for required form controls asserts\n * that the control value is not undefined but that is not appropriate for a slide-toggle\n * where the value is always defined.\n *\n * Required slide-toggle form controls are valid when checked.\n */\nclass MatSlideToggleRequiredValidator extends CheckboxRequiredValidator {}\nMatSlideToggleRequiredValidator.ɵfac = /* @__PURE__ */function () {\n  let ɵMatSlideToggleRequiredValidator_BaseFactory;\n  return function MatSlideToggleRequiredValidator_Factory(t) {\n    return (ɵMatSlideToggleRequiredValidator_BaseFactory || (ɵMatSlideToggleRequiredValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MatSlideToggleRequiredValidator)))(t || MatSlideToggleRequiredValidator);\n  };\n}();\nMatSlideToggleRequiredValidator.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatSlideToggleRequiredValidator,\n  selectors: [[\"mat-slide-toggle\", \"required\", \"\", \"formControlName\", \"\"], [\"mat-slide-toggle\", \"required\", \"\", \"formControl\", \"\"], [\"mat-slide-toggle\", \"required\", \"\", \"ngModel\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSlideToggleRequiredValidator, [{\n    type: Directive,\n    args: [{\n      selector: `mat-slide-toggle[required][formControlName],\n             mat-slide-toggle[required][formControl], mat-slide-toggle[required][ngModel]`,\n      providers: [MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** This module is used by both original and MDC-based slide-toggle implementations. */\nclass _MatSlideToggleRequiredValidatorModule {}\n_MatSlideToggleRequiredValidatorModule.ɵfac = function _MatSlideToggleRequiredValidatorModule_Factory(t) {\n  return new (t || _MatSlideToggleRequiredValidatorModule)();\n};\n_MatSlideToggleRequiredValidatorModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatSlideToggleRequiredValidatorModule,\n  declarations: [MatSlideToggleRequiredValidator],\n  exports: [MatSlideToggleRequiredValidator]\n});\n_MatSlideToggleRequiredValidatorModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatSlideToggleRequiredValidatorModule, [{\n    type: NgModule,\n    args: [{\n      exports: [MatSlideToggleRequiredValidator],\n      declarations: [MatSlideToggleRequiredValidator]\n    }]\n  }], null, null);\n})();\nclass MatSlideToggleModule {}\nMatSlideToggleModule.ɵfac = function MatSlideToggleModule_Factory(t) {\n  return new (t || MatSlideToggleModule)();\n};\nMatSlideToggleModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatSlideToggleModule,\n  declarations: [MatSlideToggle],\n  imports: [_MatSlideToggleRequiredValidatorModule, MatCommonModule, MatRippleModule, CommonModule],\n  exports: [_MatSlideToggleRequiredValidatorModule, MatSlideToggle, MatCommonModule]\n});\nMatSlideToggleModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [_MatSlideToggleRequiredValidatorModule, MatCommonModule, MatRippleModule, CommonModule, _MatSlideToggleRequiredValidatorModule, MatCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSlideToggleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [_MatSlideToggleRequiredValidatorModule, MatCommonModule, MatRippleModule, CommonModule],\n      exports: [_MatSlideToggleRequiredValidatorModule, MatSlideToggle, MatCommonModule],\n      declarations: [MatSlideToggle]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS, MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR, MAT_SLIDE_TOGGLE_VALUE_ACCESSOR, MatSlideToggle, MatSlideToggleChange, MatSlideToggleModule, MatSlideToggleRequiredValidator, _MatSlideToggleBase, _MatSlideToggleRequiredValidatorModule };", "map": {"version": 3, "names": ["i0", "InjectionToken", "forwardRef", "EventEmitter", "Directive", "Input", "Output", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Attribute", "Inject", "Optional", "ViewChild", "NgModule", "NG_VALUE_ACCESSOR", "NG_VALIDATORS", "CheckboxRequiredValidator", "ANIMATION_MODULE_TYPE", "i1", "i2", "mixinTabIndex", "mixinColor", "mixinDisableRipple", "mixinDisabled", "MatCommonModule", "MatRippleModule", "coerceBooleanProperty", "CommonModule", "MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS", "providedIn", "factory", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MAT_SLIDE_TOGGLE_VALUE_ACCESSOR", "provide", "useExisting", "MatSlideToggle", "multi", "MatSlideToggleChange", "constructor", "source", "checked", "nextUniqueId", "_MatSlideToggleMixinBase", "_elementRef", "_MatSlideToggleBase", "required", "_required", "value", "_checked", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputId", "id", "_uniqueId", "elementRef", "_focusMonitor", "tabIndex", "defaults", "animationMode", "idPrefix", "_onChange", "_", "_onTouched", "name", "labelPosition", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "change", "toggle<PERSON><PERSON>e", "parseInt", "color", "defaultColor", "_noopAnimations", "ngAfterContentInit", "monitor", "subscribe", "<PERSON><PERSON><PERSON><PERSON>", "_focused", "Promise", "resolve", "then", "ngOnDestroy", "stopMonitoring", "writeValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "disabled", "toggle", "_emitChangeEvent", "emit", "_createChangeEvent", "ɵfac", "ɵdir", "type", "ElementRef", "FocusMonitor", "ChangeDetectorRef", "undefined", "args", "aria<PERSON><PERSON><PERSON><PERSON>", "buttonId", "focusMonitor", "changeDetectorRef", "_labelId", "_handleClick", "focus", "_switchElement", "nativeElement", "isChecked", "_getAriaLabelledBy", "ɵcmp", "<PERSON><PERSON><PERSON><PERSON>", "selector", "inputs", "host", "exportAs", "encapsulation", "None", "changeDetection", "OnPush", "providers", "template", "styles", "decorators", "MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR", "MatSlideToggleRequiredValidator", "_MatSlideToggleRequiredValidatorModule", "ɵmod", "ɵinj", "exports", "declarations", "MatSlideToggleModule", "imports"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/material/fesm2020/slide-toggle.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, Directive, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Inject, Optional, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport * as i1 from '@angular/cdk/a11y';\nimport * as i2 from '@angular/material/core';\nimport { mixinTabIndex, mixinColor, mixinDisableRipple, mixinDisabled, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { CommonModule } from '@angular/common';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token to be used to override the default options for `mat-slide-toggle`. */\nconst MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS = new InjectionToken('mat-slide-toggle-default-options', {\n    providedIn: 'root',\n    factory: () => ({ disableToggleValue: false }),\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** @docs-private */\nconst MAT_SLIDE_TOGGLE_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatSlideToggle),\n    multi: true,\n};\n/** Change event object emitted by a slide toggle. */\nclass MatSlideToggleChange {\n    constructor(\n    /** The source slide toggle of the event. */\n    source, \n    /** The new `checked` value of the slide toggle. */\n    checked) {\n        this.source = source;\n        this.checked = checked;\n    }\n}\n// Increasing integer for generating unique ids for slide-toggle components.\nlet nextUniqueId = 0;\n// Boilerplate for applying mixins to MatSlideToggle.\n/** @docs-private */\nconst _MatSlideToggleMixinBase = mixinTabIndex(mixinColor(mixinDisableRipple(mixinDisabled(class {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n}))));\nclass _MatSlideToggleBase extends _MatSlideToggleMixinBase {\n    /** Whether the slide-toggle is required. */\n    get required() {\n        return this._required;\n    }\n    set required(value) {\n        this._required = coerceBooleanProperty(value);\n    }\n    /** Whether the slide-toggle element is checked or not. */\n    get checked() {\n        return this._checked;\n    }\n    set checked(value) {\n        this._checked = coerceBooleanProperty(value);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Returns the unique id for the visual hidden input. */\n    get inputId() {\n        return `${this.id || this._uniqueId}-input`;\n    }\n    constructor(elementRef, _focusMonitor, _changeDetectorRef, tabIndex, defaults, animationMode, idPrefix) {\n        super(elementRef);\n        this._focusMonitor = _focusMonitor;\n        this._changeDetectorRef = _changeDetectorRef;\n        this.defaults = defaults;\n        this._onChange = (_) => { };\n        this._onTouched = () => { };\n        this._required = false;\n        this._checked = false;\n        /** Name value will be applied to the input element if present. */\n        this.name = null;\n        /** Whether the label should appear after or before the slide-toggle. Defaults to 'after'. */\n        this.labelPosition = 'after';\n        /** Used to set the aria-label attribute on the underlying input element. */\n        this.ariaLabel = null;\n        /** Used to set the aria-labelledby attribute on the underlying input element. */\n        this.ariaLabelledby = null;\n        /** An event will be dispatched each time the slide-toggle changes its value. */\n        this.change = new EventEmitter();\n        /**\n         * An event will be dispatched each time the slide-toggle input is toggled.\n         * This event is always emitted when the user toggles the slide toggle, but this does not mean\n         * the slide toggle's value has changed.\n         */\n        this.toggleChange = new EventEmitter();\n        this.tabIndex = parseInt(tabIndex) || 0;\n        this.color = this.defaultColor = defaults.color || 'accent';\n        this._noopAnimations = animationMode === 'NoopAnimations';\n        this.id = this._uniqueId = `${idPrefix}${++nextUniqueId}`;\n    }\n    ngAfterContentInit() {\n        this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n            if (focusOrigin === 'keyboard' || focusOrigin === 'program') {\n                this._focused = true;\n                this._changeDetectorRef.markForCheck();\n            }\n            else if (!focusOrigin) {\n                // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n                // Angular does not expect events to be raised during change detection, so any state\n                // change (such as a form control's ng-touched) will cause a changed-after-checked error.\n                // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n                // telling the form control it has been touched until the next tick.\n                Promise.resolve().then(() => {\n                    this._focused = false;\n                    this._onTouched();\n                    this._changeDetectorRef.markForCheck();\n                });\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    writeValue(value) {\n        this.checked = !!value;\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    /** Implemented as a part of ControlValueAccessor. */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Toggles the checked state of the slide-toggle. */\n    toggle() {\n        this.checked = !this.checked;\n        this._onChange(this.checked);\n    }\n    /**\n     * Emits a change event on the `change` output. Also notifies the FormControl about the change.\n     */\n    _emitChangeEvent() {\n        this._onChange(this.checked);\n        this.change.emit(this._createChangeEvent(this.checked));\n    }\n}\n_MatSlideToggleBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatSlideToggleBase, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Directive });\n_MatSlideToggleBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatSlideToggleBase, inputs: { name: \"name\", id: \"id\", labelPosition: \"labelPosition\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], required: \"required\", checked: \"checked\" }, outputs: { change: \"change\", toggleChange: \"toggleChange\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatSlideToggleBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.FocusMonitor }, { type: i0.ChangeDetectorRef }, { type: undefined }, { type: undefined }, { type: undefined }, { type: undefined }]; }, propDecorators: { name: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], labelPosition: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], required: [{\n                type: Input\n            }], checked: [{\n                type: Input\n            }], change: [{\n                type: Output\n            }], toggleChange: [{\n                type: Output\n            }] } });\nclass MatSlideToggle extends _MatSlideToggleBase {\n    /** Returns the unique id for the visual hidden button. */\n    get buttonId() {\n        return `${this.id || this._uniqueId}-button`;\n    }\n    constructor(elementRef, focusMonitor, changeDetectorRef, tabIndex, defaults, animationMode) {\n        super(elementRef, focusMonitor, changeDetectorRef, tabIndex, defaults, animationMode, 'mat-mdc-slide-toggle-');\n        this._labelId = this._uniqueId + '-label';\n    }\n    /** Method being called whenever the underlying button is clicked. */\n    _handleClick() {\n        this.toggleChange.emit();\n        if (!this.defaults.disableToggleValue) {\n            this.checked = !this.checked;\n            this._onChange(this.checked);\n            this.change.emit(new MatSlideToggleChange(this, this.checked));\n        }\n    }\n    /** Focuses the slide-toggle. */\n    focus() {\n        this._switchElement.nativeElement.focus();\n    }\n    _createChangeEvent(isChecked) {\n        return new MatSlideToggleChange(this, isChecked);\n    }\n    _getAriaLabelledBy() {\n        if (this.ariaLabelledby) {\n            return this.ariaLabelledby;\n        }\n        // Even though we have a `label` element with a `for` pointing to the button, we need the\n        // `aria-labelledby`, because the button gets flagged as not having a label by tools like axe.\n        return this.ariaLabel ? null : this._labelId;\n    }\n}\nMatSlideToggle.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSlideToggle, deps: [{ token: i0.ElementRef }, { token: i1.FocusMonitor }, { token: i0.ChangeDetectorRef }, { token: 'tabindex', attribute: true }, { token: MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatSlideToggle.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatSlideToggle, selector: \"mat-slide-toggle\", inputs: { disabled: \"disabled\", disableRipple: \"disableRipple\", color: \"color\", tabIndex: \"tabIndex\" }, host: { properties: { \"id\": \"id\", \"attr.tabindex\": \"null\", \"attr.aria-label\": \"null\", \"attr.name\": \"null\", \"attr.aria-labelledby\": \"null\", \"class.mat-mdc-slide-toggle-focused\": \"_focused\", \"class.mat-mdc-slide-toggle-checked\": \"checked\", \"class._mat-animation-noopable\": \"_noopAnimations\" }, classAttribute: \"mat-mdc-slide-toggle\" }, providers: [MAT_SLIDE_TOGGLE_VALUE_ACCESSOR], viewQueries: [{ propertyName: \"_switchElement\", first: true, predicate: [\"switch\"], descendants: true }], exportAs: [\"matSlideToggle\"], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mdc-form-field\\\"\\n     [class.mdc-form-field--align-end]=\\\"labelPosition == 'before'\\\">\\n  <button\\n    class=\\\"mdc-switch\\\"\\n    role=\\\"switch\\\"\\n    type=\\\"button\\\"\\n    [class.mdc-switch--selected]=\\\"checked\\\"\\n    [class.mdc-switch--unselected]=\\\"!checked\\\"\\n    [class.mdc-switch--checked]=\\\"checked\\\"\\n    [class.mdc-switch--disabled]=\\\"disabled\\\"\\n    [tabIndex]=\\\"tabIndex\\\"\\n    [disabled]=\\\"disabled\\\"\\n    [attr.id]=\\\"buttonId\\\"\\n    [attr.name]=\\\"name\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-labelledby]=\\\"_getAriaLabelledBy()\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n    [attr.aria-required]=\\\"required || null\\\"\\n    [attr.aria-checked]=\\\"checked\\\"\\n    (click)=\\\"_handleClick()\\\"\\n    #switch>\\n    <div class=\\\"mdc-switch__track\\\"></div>\\n    <div class=\\\"mdc-switch__handle-track\\\">\\n      <div class=\\\"mdc-switch__handle\\\">\\n        <div class=\\\"mdc-switch__shadow\\\">\\n          <div class=\\\"mdc-elevation-overlay\\\"></div>\\n        </div>\\n        <div class=\\\"mdc-switch__ripple\\\">\\n          <div class=\\\"mat-mdc-slide-toggle-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n            [matRippleTrigger]=\\\"switch\\\"\\n            [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n            [matRippleCentered]=\\\"true\\\"></div>\\n        </div>\\n        <div class=\\\"mdc-switch__icons\\\">\\n          <svg class=\\\"mdc-switch__icon mdc-switch__icon--on\\\" viewBox=\\\"0 0 24 24\\\">\\n            <path d=\\\"M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z\\\" />\\n          </svg>\\n          <svg class=\\\"mdc-switch__icon mdc-switch__icon--off\\\" viewBox=\\\"0 0 24 24\\\">\\n            <path d=\\\"M20 13H4v-2h16v2z\\\" />\\n          </svg>\\n        </div>\\n      </div>\\n    </div>\\n  </button>\\n\\n  <!--\\n    Clicking on the label will trigger another click event from the button.\\n    Stop propagation here so other listeners further up in the DOM don't execute twice.\\n  -->\\n  <label [for]=\\\"buttonId\\\" [attr.id]=\\\"_labelId\\\" (click)=\\\"$event.stopPropagation()\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1);background-color:var(--mdc-elevation-overlay-color, #fff)}.mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative}.mdc-switch[hidden]{display:none}.mdc-switch:disabled{cursor:default;pointer-events:none}.mdc-switch__track{overflow:hidden;position:relative;width:100%}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;width:100%}@media screen and (forced-colors: active){.mdc-switch__track::before,.mdc-switch__track::after{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(-100%)}[dir=rtl] .mdc-switch__track::after,.mdc-switch__track[dir=rtl]::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track[dir=rtl]::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0)}[dir=rtl] .mdc-switch__handle-track,.mdc-switch__handle-track[dir=rtl]{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track,.mdc-switch--selected .mdc-switch__handle-track[dir=rtl]{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto}[dir=rtl] .mdc-switch__handle,.mdc-switch__handle[dir=rtl]{left:auto;right:0}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media screen and (forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-elevation-overlay{bottom:0;left:0;right:0;top:0}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1}.mdc-switch:disabled .mdc-switch__ripple{display:none}.mdc-switch__icons{height:100%;position:relative;width:100%;z-index:1}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle{display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mdc-switch{width:var(--mdc-switch-track-width, 36px)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color, #310077)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color, #310077)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color, #310077)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color, #616161)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color, #212121)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color, #212121)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color, #212121)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color, #424242)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color, var(--mdc-theme-surface, #fff))}.mat-mdc-slide-toggle .mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation, 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation, 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__focus-ring-wrapper,.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle{height:var(--mdc-switch-handle-height, 20px)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__handle::after{opacity:var(--mdc-switch-disabled-handle-opacity, 0.38)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle{border-radius:var(--mdc-switch-handle-shape, 10px)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle{width:var(--mdc-switch-handle-width, 20px)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle-track{width:calc(100% - var(--mdc-switch-handle-width, 20px))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled .mdc-switch__icon{fill:var(--mdc-switch-selected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled .mdc-switch__icon{fill:var(--mdc-switch-unselected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity, 0.38)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size, 18px);height:var(--mdc-switch-selected-icon-size, 18px)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size, 18px);height:var(--mdc-switch-unselected-icon-size, 18px)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-hover-state-layer-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-focus-state-layer-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-pressed-state-layer-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-hover-state-layer-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-focus-state-layer-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-pressed-state-layer-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-selected-hover-state-layer-opacity, 0.04)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-selected-focus-state-layer-opacity, 0.12)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-unselected-hover-state-layer-opacity, 0.04)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-unselected-focus-state-layer-opacity, 0.12)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__ripple{height:var(--mdc-switch-state-layer-size, 48px);width:var(--mdc-switch-state-layer-size, 48px)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__track{height:var(--mdc-switch-track-height, 14px)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity, 0.12)}.mat-mdc-slide-toggle .mdc-switch:enabled .mdc-switch__track::after{background:var(--mdc-switch-selected-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color, #424242)}.mat-mdc-slide-toggle .mdc-switch:enabled .mdc-switch__track::before{background:var(--mdc-switch-unselected-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color, #424242)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__track{border-radius:var(--mdc-switch-track-shape, 7px)}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle .mdc-switch__ripple::after{content:\\\"\\\";opacity:0}.mat-mdc-slide-toggle .mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:opacity 75ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-mdc-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-elevation-overlay,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}\"], dependencies: [{ kind: \"directive\", type: i2.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSlideToggle, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-slide-toggle', inputs: ['disabled', 'disableRipple', 'color', 'tabIndex'], host: {\n                        'class': 'mat-mdc-slide-toggle',\n                        '[id]': 'id',\n                        // Needs to be removed since it causes some a11y issues (see #21266).\n                        '[attr.tabindex]': 'null',\n                        '[attr.aria-label]': 'null',\n                        '[attr.name]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[class.mat-mdc-slide-toggle-focused]': '_focused',\n                        '[class.mat-mdc-slide-toggle-checked]': 'checked',\n                        '[class._mat-animation-noopable]': '_noopAnimations',\n                    }, exportAs: 'matSlideToggle', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [MAT_SLIDE_TOGGLE_VALUE_ACCESSOR], template: \"<div class=\\\"mdc-form-field\\\"\\n     [class.mdc-form-field--align-end]=\\\"labelPosition == 'before'\\\">\\n  <button\\n    class=\\\"mdc-switch\\\"\\n    role=\\\"switch\\\"\\n    type=\\\"button\\\"\\n    [class.mdc-switch--selected]=\\\"checked\\\"\\n    [class.mdc-switch--unselected]=\\\"!checked\\\"\\n    [class.mdc-switch--checked]=\\\"checked\\\"\\n    [class.mdc-switch--disabled]=\\\"disabled\\\"\\n    [tabIndex]=\\\"tabIndex\\\"\\n    [disabled]=\\\"disabled\\\"\\n    [attr.id]=\\\"buttonId\\\"\\n    [attr.name]=\\\"name\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-labelledby]=\\\"_getAriaLabelledBy()\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n    [attr.aria-required]=\\\"required || null\\\"\\n    [attr.aria-checked]=\\\"checked\\\"\\n    (click)=\\\"_handleClick()\\\"\\n    #switch>\\n    <div class=\\\"mdc-switch__track\\\"></div>\\n    <div class=\\\"mdc-switch__handle-track\\\">\\n      <div class=\\\"mdc-switch__handle\\\">\\n        <div class=\\\"mdc-switch__shadow\\\">\\n          <div class=\\\"mdc-elevation-overlay\\\"></div>\\n        </div>\\n        <div class=\\\"mdc-switch__ripple\\\">\\n          <div class=\\\"mat-mdc-slide-toggle-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n            [matRippleTrigger]=\\\"switch\\\"\\n            [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n            [matRippleCentered]=\\\"true\\\"></div>\\n        </div>\\n        <div class=\\\"mdc-switch__icons\\\">\\n          <svg class=\\\"mdc-switch__icon mdc-switch__icon--on\\\" viewBox=\\\"0 0 24 24\\\">\\n            <path d=\\\"M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z\\\" />\\n          </svg>\\n          <svg class=\\\"mdc-switch__icon mdc-switch__icon--off\\\" viewBox=\\\"0 0 24 24\\\">\\n            <path d=\\\"M20 13H4v-2h16v2z\\\" />\\n          </svg>\\n        </div>\\n      </div>\\n    </div>\\n  </button>\\n\\n  <!--\\n    Clicking on the label will trigger another click event from the button.\\n    Stop propagation here so other listeners further up in the DOM don't execute twice.\\n  -->\\n  <label [for]=\\\"buttonId\\\" [attr.id]=\\\"_labelId\\\" (click)=\\\"$event.stopPropagation()\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1);background-color:var(--mdc-elevation-overlay-color, #fff)}.mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative}.mdc-switch[hidden]{display:none}.mdc-switch:disabled{cursor:default;pointer-events:none}.mdc-switch__track{overflow:hidden;position:relative;width:100%}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;width:100%}@media screen and (forced-colors: active){.mdc-switch__track::before,.mdc-switch__track::after{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(-100%)}[dir=rtl] .mdc-switch__track::after,.mdc-switch__track[dir=rtl]::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track[dir=rtl]::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0)}[dir=rtl] .mdc-switch__handle-track,.mdc-switch__handle-track[dir=rtl]{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track,.mdc-switch--selected .mdc-switch__handle-track[dir=rtl]{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto}[dir=rtl] .mdc-switch__handle,.mdc-switch__handle[dir=rtl]{left:auto;right:0}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media screen and (forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-elevation-overlay{bottom:0;left:0;right:0;top:0}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1}.mdc-switch:disabled .mdc-switch__ripple{display:none}.mdc-switch__icons{height:100%;position:relative;width:100%;z-index:1}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle{display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mdc-switch{width:var(--mdc-switch-track-width, 36px)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color, #310077)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color, #310077)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color, #310077)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color, #616161)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color, #212121)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color, #212121)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color, #212121)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color, #424242)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color, var(--mdc-theme-surface, #fff))}.mat-mdc-slide-toggle .mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation, 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation, 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__focus-ring-wrapper,.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle{height:var(--mdc-switch-handle-height, 20px)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__handle::after{opacity:var(--mdc-switch-disabled-handle-opacity, 0.38)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle{border-radius:var(--mdc-switch-handle-shape, 10px)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle{width:var(--mdc-switch-handle-width, 20px)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__handle-track{width:calc(100% - var(--mdc-switch-handle-width, 20px))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled .mdc-switch__icon{fill:var(--mdc-switch-selected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled .mdc-switch__icon{fill:var(--mdc-switch-unselected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity, 0.38)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size, 18px);height:var(--mdc-switch-selected-icon-size, 18px)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size, 18px);height:var(--mdc-switch-unselected-icon-size, 18px)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-hover-state-layer-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-focus-state-layer-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-pressed-state-layer-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-hover-state-layer-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-focus-state-layer-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-pressed-state-layer-color, #424242)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-selected-hover-state-layer-opacity, 0.04)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-selected-focus-state-layer-opacity, 0.12)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-unselected-hover-state-layer-opacity, 0.04)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-unselected-focus-state-layer-opacity, 0.12)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, 0.1)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__ripple{height:var(--mdc-switch-state-layer-size, 48px);width:var(--mdc-switch-state-layer-size, 48px)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__track{height:var(--mdc-switch-track-height, 14px)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity, 0.12)}.mat-mdc-slide-toggle .mdc-switch:enabled .mdc-switch__track::after{background:var(--mdc-switch-selected-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color, #d7bbff)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color, #424242)}.mat-mdc-slide-toggle .mdc-switch:enabled .mdc-switch__track::before{background:var(--mdc-switch-unselected-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color, #e0e0e0)}.mat-mdc-slide-toggle .mdc-switch:disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color, #424242)}.mat-mdc-slide-toggle .mdc-switch .mdc-switch__track{border-radius:var(--mdc-switch-track-shape, 7px)}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle .mdc-switch__ripple::after{content:\\\"\\\";opacity:0}.mat-mdc-slide-toggle .mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:opacity 75ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-mdc-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-elevation-overlay,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.FocusMonitor }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { _switchElement: [{\n                type: ViewChild,\n                args: ['switch']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR = {\n    provide: NG_VALIDATORS,\n    useExisting: forwardRef(() => MatSlideToggleRequiredValidator),\n    multi: true,\n};\n/**\n * Validator for Material slide-toggle components with the required attribute in a\n * template-driven form. The default validator for required form controls asserts\n * that the control value is not undefined but that is not appropriate for a slide-toggle\n * where the value is always defined.\n *\n * Required slide-toggle form controls are valid when checked.\n */\nclass MatSlideToggleRequiredValidator extends CheckboxRequiredValidator {\n}\nMatSlideToggleRequiredValidator.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSlideToggleRequiredValidator, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatSlideToggleRequiredValidator.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatSlideToggleRequiredValidator, selector: \"mat-slide-toggle[required][formControlName],\\n             mat-slide-toggle[required][formControl], mat-slide-toggle[required][ngModel]\", providers: [MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSlideToggleRequiredValidator, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-slide-toggle[required][formControlName],\n             mat-slide-toggle[required][formControl], mat-slide-toggle[required][ngModel]`,\n                    providers: [MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** This module is used by both original and MDC-based slide-toggle implementations. */\nclass _MatSlideToggleRequiredValidatorModule {\n}\n_MatSlideToggleRequiredValidatorModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatSlideToggleRequiredValidatorModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n_MatSlideToggleRequiredValidatorModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatSlideToggleRequiredValidatorModule, declarations: [MatSlideToggleRequiredValidator], exports: [MatSlideToggleRequiredValidator] });\n_MatSlideToggleRequiredValidatorModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatSlideToggleRequiredValidatorModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatSlideToggleRequiredValidatorModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [MatSlideToggleRequiredValidator],\n                    declarations: [MatSlideToggleRequiredValidator],\n                }]\n        }] });\nclass MatSlideToggleModule {\n}\nMatSlideToggleModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSlideToggleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatSlideToggleModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSlideToggleModule, declarations: [MatSlideToggle], imports: [_MatSlideToggleRequiredValidatorModule, MatCommonModule, MatRippleModule, CommonModule], exports: [_MatSlideToggleRequiredValidatorModule, MatSlideToggle, MatCommonModule] });\nMatSlideToggleModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSlideToggleModule, imports: [_MatSlideToggleRequiredValidatorModule, MatCommonModule, MatRippleModule, CommonModule, _MatSlideToggleRequiredValidatorModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSlideToggleModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [_MatSlideToggleRequiredValidatorModule, MatCommonModule, MatRippleModule, CommonModule],\n                    exports: [_MatSlideToggleRequiredValidatorModule, MatSlideToggle, MatCommonModule],\n                    declarations: [MatSlideToggle],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS, MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR, MAT_SLIDE_TOGGLE_VALUE_ACCESSOR, MatSlideToggle, MatSlideToggleChange, MatSlideToggleModule, MatSlideToggleRequiredValidator, _MatSlideToggleBase, _MatSlideToggleRequiredValidatorModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC3M,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,yBAAyB,QAAQ,gBAAgB;AAC5F,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,aAAa,EAAEC,UAAU,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AACvI,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,YAAY,QAAQ,iBAAiB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA,MAAMC,gCAAgC,GAAG,IAAI5B,cAAc,CAAC,kCAAkC,EAAE;EAC5F6B,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAE,OAAO;IAAEC,kBAAkB,EAAE;EAAM,CAAC;AACjD,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,+BAA+B,GAAG;EACpCC,OAAO,EAAEnB,iBAAiB;EAC1BoB,WAAW,EAAEjC,UAAU,CAAC,MAAMkC,cAAc,CAAC;EAC7CC,KAAK,EAAE;AACX,CAAC;AACD;AACA,MAAMC,oBAAoB,CAAC;EACvBC,WAAW,EACX;EACAC,MAAM,EACN;EACAC,OAAO,EAAE;IACL,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;AACJ;AACA;AACA,IAAIC,YAAY,GAAG,CAAC;AACpB;AACA;AACA,MAAMC,wBAAwB,GAAGtB,aAAa,CAACC,UAAU,CAACC,kBAAkB,CAACC,aAAa,CAAC,MAAM;EAC7Fe,WAAW,CAACK,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;AACJ,CAAC,CAAC,CAAC,CAAC,CAAC;AACL,MAAMC,mBAAmB,SAASF,wBAAwB,CAAC;EACvD;EACA,IAAIG,QAAQ,GAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQ,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGpB,qBAAqB,CAACqB,KAAK,CAAC;EACjD;EACA;EACA,IAAIP,OAAO,GAAG;IACV,OAAO,IAAI,CAACQ,QAAQ;EACxB;EACA,IAAIR,OAAO,CAACO,KAAK,EAAE;IACf,IAAI,CAACC,QAAQ,GAAGtB,qBAAqB,CAACqB,KAAK,CAAC;IAC5C,IAAI,CAACE,kBAAkB,CAACC,YAAY,EAAE;EAC1C;EACA;EACA,IAAIC,OAAO,GAAG;IACV,OAAQ,GAAE,IAAI,CAACC,EAAE,IAAI,IAAI,CAACC,SAAU,QAAO;EAC/C;EACAf,WAAW,CAACgB,UAAU,EAAEC,aAAa,EAAEN,kBAAkB,EAAEO,QAAQ,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAE;IACpG,KAAK,CAACL,UAAU,CAAC;IACjB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACN,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACQ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACG,SAAS,GAAIC,CAAC,IAAK,CAAE,CAAC;IAC3B,IAAI,CAACC,UAAU,GAAG,MAAM,CAAE,CAAC;IAC3B,IAAI,CAAChB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACE,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACe,IAAI,GAAG,IAAI;IAChB;IACA,IAAI,CAACC,aAAa,GAAG,OAAO;IAC5B;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACC,MAAM,GAAG,IAAIjE,YAAY,EAAE;IAChC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACkE,YAAY,GAAG,IAAIlE,YAAY,EAAE;IACtC,IAAI,CAACsD,QAAQ,GAAGa,QAAQ,CAACb,QAAQ,CAAC,IAAI,CAAC;IACvC,IAAI,CAACc,KAAK,GAAG,IAAI,CAACC,YAAY,GAAGd,QAAQ,CAACa,KAAK,IAAI,QAAQ;IAC3D,IAAI,CAACE,eAAe,GAAGd,aAAa,KAAK,gBAAgB;IACzD,IAAI,CAACN,EAAE,GAAG,IAAI,CAACC,SAAS,GAAI,GAAEM,QAAS,GAAE,EAAElB,YAAa,EAAC;EAC7D;EACAgC,kBAAkB,GAAG;IACjB,IAAI,CAAClB,aAAa,CAACmB,OAAO,CAAC,IAAI,CAAC/B,WAAW,EAAE,IAAI,CAAC,CAACgC,SAAS,CAACC,WAAW,IAAI;MACxE,IAAIA,WAAW,KAAK,UAAU,IAAIA,WAAW,KAAK,SAAS,EAAE;QACzD,IAAI,CAACC,QAAQ,GAAG,IAAI;QACpB,IAAI,CAAC5B,kBAAkB,CAACC,YAAY,EAAE;MAC1C,CAAC,MACI,IAAI,CAAC0B,WAAW,EAAE;QACnB;QACA;QACA;QACA;QACA;QACAE,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAM;UACzB,IAAI,CAACH,QAAQ,GAAG,KAAK;UACrB,IAAI,CAACf,UAAU,EAAE;UACjB,IAAI,CAACb,kBAAkB,CAACC,YAAY,EAAE;QAC1C,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACA+B,WAAW,GAAG;IACV,IAAI,CAAC1B,aAAa,CAAC2B,cAAc,CAAC,IAAI,CAACvC,WAAW,CAAC;EACvD;EACA;EACAwC,UAAU,CAACpC,KAAK,EAAE;IACd,IAAI,CAACP,OAAO,GAAG,CAAC,CAACO,KAAK;EAC1B;EACA;EACAqC,gBAAgB,CAACC,EAAE,EAAE;IACjB,IAAI,CAACzB,SAAS,GAAGyB,EAAE;EACvB;EACA;EACAC,iBAAiB,CAACD,EAAE,EAAE;IAClB,IAAI,CAACvB,UAAU,GAAGuB,EAAE;EACxB;EACA;EACAE,gBAAgB,CAACC,UAAU,EAAE;IACzB,IAAI,CAACC,QAAQ,GAAGD,UAAU;IAC1B,IAAI,CAACvC,kBAAkB,CAACC,YAAY,EAAE;EAC1C;EACA;EACAwC,MAAM,GAAG;IACL,IAAI,CAAClD,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B,IAAI,CAACoB,SAAS,CAAC,IAAI,CAACpB,OAAO,CAAC;EAChC;EACA;AACJ;AACA;EACImD,gBAAgB,GAAG;IACf,IAAI,CAAC/B,SAAS,CAAC,IAAI,CAACpB,OAAO,CAAC;IAC5B,IAAI,CAAC2B,MAAM,CAACyB,IAAI,CAAC,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACrD,OAAO,CAAC,CAAC;EAC3D;AACJ;AACAI,mBAAmB,CAACkD,IAAI;EAAmF/F,EAAE;AAAA,CAAqF;AAClM6C,mBAAmB,CAACmD,IAAI,kBADmFhG,EAAE;EAAA,MACJ6C,mBAAmB;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA,WADjB7C,EAAE;AAAA,EAC2X;AACxe;EAAA,mDAF2GA,EAAE,mBAEb6C,mBAAmB,EAAc,CAAC;IACtHoD,IAAI,EAAE7F;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE6F,IAAI,EAAEjG,EAAE,CAACkG;IAAW,CAAC,EAAE;MAAED,IAAI,EAAE9E,EAAE,CAACgF;IAAa,CAAC,EAAE;MAAEF,IAAI,EAAEjG,EAAE,CAACoG;IAAkB,CAAC,EAAE;MAAEH,IAAI,EAAEI;IAAU,CAAC,EAAE;MAAEJ,IAAI,EAAEI;IAAU,CAAC,EAAE;MAAEJ,IAAI,EAAEI;IAAU,CAAC,EAAE;MAAEJ,IAAI,EAAEI;IAAU,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAErC,IAAI,EAAE,CAAC;MACvOiC,IAAI,EAAE5F;IACV,CAAC,CAAC;IAAEgD,EAAE,EAAE,CAAC;MACL4C,IAAI,EAAE5F;IACV,CAAC,CAAC;IAAE4D,aAAa,EAAE,CAAC;MAChBgC,IAAI,EAAE5F;IACV,CAAC,CAAC;IAAE6D,SAAS,EAAE,CAAC;MACZ+B,IAAI,EAAE5F,KAAK;MACXiG,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEnC,cAAc,EAAE,CAAC;MACjB8B,IAAI,EAAE5F,KAAK;MACXiG,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEC,eAAe,EAAE,CAAC;MAClBN,IAAI,EAAE5F,KAAK;MACXiG,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAExD,QAAQ,EAAE,CAAC;MACXmD,IAAI,EAAE5F;IACV,CAAC,CAAC;IAAEoC,OAAO,EAAE,CAAC;MACVwD,IAAI,EAAE5F;IACV,CAAC,CAAC;IAAE+D,MAAM,EAAE,CAAC;MACT6B,IAAI,EAAE3F;IACV,CAAC,CAAC;IAAE+D,YAAY,EAAE,CAAC;MACf4B,IAAI,EAAE3F;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM8B,cAAc,SAASS,mBAAmB,CAAC;EAC7C;EACA,IAAI2D,QAAQ,GAAG;IACX,OAAQ,GAAE,IAAI,CAACnD,EAAE,IAAI,IAAI,CAACC,SAAU,SAAQ;EAChD;EACAf,WAAW,CAACgB,UAAU,EAAEkD,YAAY,EAAEC,iBAAiB,EAAEjD,QAAQ,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IACxF,KAAK,CAACJ,UAAU,EAAEkD,YAAY,EAAEC,iBAAiB,EAAEjD,QAAQ,EAAEC,QAAQ,EAAEC,aAAa,EAAE,uBAAuB,CAAC;IAC9G,IAAI,CAACgD,QAAQ,GAAG,IAAI,CAACrD,SAAS,GAAG,QAAQ;EAC7C;EACA;EACAsD,YAAY,GAAG;IACX,IAAI,CAACvC,YAAY,CAACwB,IAAI,EAAE;IACxB,IAAI,CAAC,IAAI,CAACnC,QAAQ,CAAC1B,kBAAkB,EAAE;MACnC,IAAI,CAACS,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;MAC5B,IAAI,CAACoB,SAAS,CAAC,IAAI,CAACpB,OAAO,CAAC;MAC5B,IAAI,CAAC2B,MAAM,CAACyB,IAAI,CAAC,IAAIvD,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAACG,OAAO,CAAC,CAAC;IAClE;EACJ;EACA;EACAoE,KAAK,GAAG;IACJ,IAAI,CAACC,cAAc,CAACC,aAAa,CAACF,KAAK,EAAE;EAC7C;EACAf,kBAAkB,CAACkB,SAAS,EAAE;IAC1B,OAAO,IAAI1E,oBAAoB,CAAC,IAAI,EAAE0E,SAAS,CAAC;EACpD;EACAC,kBAAkB,GAAG;IACjB,IAAI,IAAI,CAAC9C,cAAc,EAAE;MACrB,OAAO,IAAI,CAACA,cAAc;IAC9B;IACA;IACA;IACA,OAAO,IAAI,CAACD,SAAS,GAAG,IAAI,GAAG,IAAI,CAACyC,QAAQ;EAChD;AACJ;AACAvE,cAAc,CAAC2D,IAAI;EAAA,iBAA6F3D,cAAc,EA9DnBpC,EAAE,mBA8DmCA,EAAE,CAACkG,UAAU,GA9DlDlG,EAAE,mBA8D6DmB,EAAE,CAACgF,YAAY,GA9D9EnG,EAAE,mBA8DyFA,EAAE,CAACoG,iBAAiB,GA9D/GpG,EAAE,mBA8D0H,UAAU,GA9DtIA,EAAE,mBA8DkK6B,gCAAgC,GA9DpM7B,EAAE,mBA8D+MkB,qBAAqB;AAAA,CAA4D;AAC7YkB,cAAc,CAAC8E,IAAI,kBA/DwFlH,EAAE;EAAA,MA+DToC,cAAc;EAAA;EAAA;IAAA;MA/DPpC,EAAE;IAAA;IAAA;MAAA;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE,oBA+Dse,CAACiC,+BAA+B,CAAC,GA/DzgBjC,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,4BA+DqyB;MA/DvyBA,EAAE;QAAA,OA+D84C,kBAAc;MAAA,EAAE;MA/Dh6CA,EAAE,uBA+Dy9C;MA/D39CA,EAAE,4BA+DugD;MA/DzgDA,EAAE,uBA+DopD;MA/DtpDA,EAAE,eA+DoqD;MA/DtqDA,EAAE,4BA+DgtD;MA/DltDA,EAAE,uBA+Do8D;MA/Dt8DA,EAAE,eA+Do9D;MA/Dt9DA,EAAE,8BA+D+/D;MA/DjgEA,EAAE,iBA+DslE;MA/DxlEA,EAAE,8BA+DslE;MA/DxlEA,EAAE,0BA+DsrE;MA/DxrEA,EAAE,eA+DwsE;MA/D1sEA,EAAE,8BA+DgyE;MA/DlyEA,EAAE,0BA+D80E;MA/Dh1EA,EAAE,eA+Dg2E;MA/Dl2EA,EAAE,kBA+DwqF;MA/D1qFA,EAAE,gCA+DwqF;MA/D1qFA,EAAE;QAAA,OA+D6oF,wBAAwB;MAAA,EAAE;MA/DzqFA,EAAE,iBA+DusF;MA/DzsFA,EAAE,eA+DmtF;IAAA;IAAA;MAAA,YA/DrtFA,EAAE;MAAFA,EAAE,wEA+DoyB;MA/DtyBA,EAAE,aA+Dk6B;MA/Dp6BA,EAAE,iDA+Dk6B;MA/Dp6BA,EAAE,qCA+D4kC;MA/D9kCA,EAAE,gCA+DqoC;MA/DvoCA,EAAE,aA+Do1D;MA/Dt1DA,EAAE,oCA+Do1D;MA/Dt1DA,EAAE,aA+D2mF;MA/D7mFA,EAAE,gCA+D2mF;MA/D7mFA,EAAE,gCA+DkoF;IAAA;EAAA;EAAA,eAA4qgBoB,EAAE,CAAC+F,SAAS;EAAA;EAAA;EAAA;AAAA,EAA6T;AACpumB;EAAA,mDAhE2GnH,EAAE,mBAgEboC,cAAc,EAAc,CAAC;IACjH6D,IAAI,EAAE1F,SAAS;IACf+F,IAAI,EAAE,CAAC;MAAEc,QAAQ,EAAE,kBAAkB;MAAEC,MAAM,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,OAAO,EAAE,UAAU,CAAC;MAAEC,IAAI,EAAE;QAC7F,OAAO,EAAE,sBAAsB;QAC/B,MAAM,EAAE,IAAI;QACZ;QACA,iBAAiB,EAAE,MAAM;QACzB,mBAAmB,EAAE,MAAM;QAC3B,aAAa,EAAE,MAAM;QACrB,wBAAwB,EAAE,MAAM;QAChC,sCAAsC,EAAE,UAAU;QAClD,sCAAsC,EAAE,SAAS;QACjD,iCAAiC,EAAE;MACvC,CAAC;MAAEC,QAAQ,EAAE,gBAAgB;MAAEC,aAAa,EAAEhH,iBAAiB,CAACiH,IAAI;MAAEC,eAAe,EAAEjH,uBAAuB,CAACkH,MAAM;MAAEC,SAAS,EAAE,CAAC3F,+BAA+B,CAAC;MAAE4F,QAAQ,EAAE,8hEAA8hE;MAAEC,MAAM,EAAE,CAAC,whgBAAwhgB;IAAE,CAAC;EAC/vkB,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE7B,IAAI,EAAEjG,EAAE,CAACkG;IAAW,CAAC,EAAE;MAAED,IAAI,EAAE9E,EAAE,CAACgF;IAAa,CAAC,EAAE;MAAEF,IAAI,EAAEjG,EAAE,CAACoG;IAAkB,CAAC,EAAE;MAAEH,IAAI,EAAEI,SAAS;MAAE0B,UAAU,EAAE,CAAC;QAClJ9B,IAAI,EAAEvF,SAAS;QACf4F,IAAI,EAAE,CAAC,UAAU;MACrB,CAAC;IAAE,CAAC,EAAE;MAAEL,IAAI,EAAEI,SAAS;MAAE0B,UAAU,EAAE,CAAC;QAClC9B,IAAI,EAAEtF,MAAM;QACZ2F,IAAI,EAAE,CAACzE,gCAAgC;MAC3C,CAAC;IAAE,CAAC,EAAE;MAAEoE,IAAI,EAAEI,SAAS;MAAE0B,UAAU,EAAE,CAAC;QAClC9B,IAAI,EAAErF;MACV,CAAC,EAAE;QACCqF,IAAI,EAAEtF,MAAM;QACZ2F,IAAI,EAAE,CAACpF,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE4F,cAAc,EAAE,CAAC;MAC7Cb,IAAI,EAAEpF,SAAS;MACfyF,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0B,mCAAmC,GAAG;EACxC9F,OAAO,EAAElB,aAAa;EACtBmB,WAAW,EAAEjC,UAAU,CAAC,MAAM+H,+BAA+B,CAAC;EAC9D5F,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4F,+BAA+B,SAAShH,yBAAyB,CAAC;AAExEgH,+BAA+B,CAAClC,IAAI;EAAA;EAAA;IAAA,wGApHuE/F,EAAE,uBAoHoBiI,+BAA+B,SAA/BA,+BAA+B;EAAA;AAAA,GAAqD;AACrNA,+BAA+B,CAACjC,IAAI,kBArHuEhG,EAAE;EAAA,MAqHQiI,+BAA+B;EAAA;EAAA,WArHzCjI,EAAE,oBAqHyM,CAACgI,mCAAmC,CAAC,GArHhPhI,EAAE;AAAA,EAqHsR;AACnY;EAAA,mDAtH2GA,EAAE,mBAsHbiI,+BAA+B,EAAc,CAAC;IAClIhC,IAAI,EAAE7F,SAAS;IACfkG,IAAI,EAAE,CAAC;MACCc,QAAQ,EAAG;AAC/B,0FAA0F;MACtEQ,SAAS,EAAE,CAACI,mCAAmC;IACnD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,sCAAsC,CAAC;AAE7CA,sCAAsC,CAACnC,IAAI;EAAA,iBAA6FmC,sCAAsC;AAAA,CAAkD;AAChOA,sCAAsC,CAACC,IAAI,kBA1IgEnI,EAAE;EAAA,MA0I4BkI,sCAAsC;EAAA,eAAiBD,+BAA+B;EAAA,UAAaA,+BAA+B;AAAA,EAAI;AAC/QC,sCAAsC,CAACE,IAAI,kBA3IgEpI,EAAE,qBA2IqE;AAClL;EAAA,mDA5I2GA,EAAE,mBA4IbkI,sCAAsC,EAAc,CAAC;IACzIjC,IAAI,EAAEnF,QAAQ;IACdwF,IAAI,EAAE,CAAC;MACC+B,OAAO,EAAE,CAACJ,+BAA+B,CAAC;MAC1CK,YAAY,EAAE,CAACL,+BAA+B;IAClD,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMM,oBAAoB,CAAC;AAE3BA,oBAAoB,CAACxC,IAAI;EAAA,iBAA6FwC,oBAAoB;AAAA,CAAkD;AAC5LA,oBAAoB,CAACJ,IAAI,kBAtJkFnI,EAAE;EAAA,MAsJUuI,oBAAoB;EAAA,eAAiBnG,cAAc;EAAA,UAAa8F,sCAAsC,EAAEzG,eAAe,EAAEC,eAAe,EAAEE,YAAY;EAAA,UAAasG,sCAAsC,EAAE9F,cAAc,EAAEX,eAAe;AAAA,EAAI;AACrW8G,oBAAoB,CAACH,IAAI,kBAvJkFpI,EAAE;EAAA,UAuJ0CkI,sCAAsC,EAAEzG,eAAe,EAAEC,eAAe,EAAEE,YAAY,EAAEsG,sCAAsC,EAAEzG,eAAe;AAAA,EAAI;AAC1S;EAAA,mDAxJ2GzB,EAAE,mBAwJbuI,oBAAoB,EAAc,CAAC;IACvHtC,IAAI,EAAEnF,QAAQ;IACdwF,IAAI,EAAE,CAAC;MACCkC,OAAO,EAAE,CAACN,sCAAsC,EAAEzG,eAAe,EAAEC,eAAe,EAAEE,YAAY,CAAC;MACjGyG,OAAO,EAAE,CAACH,sCAAsC,EAAE9F,cAAc,EAAEX,eAAe,CAAC;MAClF6G,YAAY,EAAE,CAAClG,cAAc;IACjC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASP,gCAAgC,EAAEmG,mCAAmC,EAAE/F,+BAA+B,EAAEG,cAAc,EAAEE,oBAAoB,EAAEiG,oBAAoB,EAAEN,+BAA+B,EAAEpF,mBAAmB,EAAEqF,sCAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}