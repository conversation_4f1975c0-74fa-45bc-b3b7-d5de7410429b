{"ast": null, "code": "function cov_1tz9cji8kc() {\n  var path = \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\core\\\\theme\\\\theme.config.ts\";\n  var hash = \"5a4f9b2897c7cd702b62c070f19437340b5f0d4b\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\core\\\\theme\\\\theme.config.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 2,\n          column: 29\n        },\n        end: {\n          line: 42,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 44,\n          column: 33\n        },\n        end: {\n          line: 84,\n          column: 1\n        }\n      },\n      \"2\": {\n        start: {\n          line: 86,\n          column: 32\n        },\n        end: {\n          line: 126,\n          column: 1\n        }\n      },\n      \"3\": {\n        start: {\n          line: 128,\n          column: 26\n        },\n        end: {\n          line: 168,\n          column: 1\n        }\n      },\n      \"4\": {\n        start: {\n          line: 170,\n          column: 37\n        },\n        end: {\n          line: 210,\n          column: 1\n        }\n      },\n      \"5\": {\n        start: {\n          line: 211,\n          column: 32\n        },\n        end: {\n          line: 217,\n          column: 1\n        }\n      }\n    },\n    fnMap: {},\n    branchMap: {},\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0\n    },\n    f: {},\n    b: {},\n    inputSourceMap: {\n      version: 3,\n      file: \"theme.config.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\core\\\\theme\\\\theme.config.ts\"],\n      names: [],\n      mappings: \"AA4CA,oCAAoC;AACpC,MAAM,CAAC,MAAM,aAAa,GAAgB;IACxC,IAAI,EAAE,mBAAmB;IACzB,MAAM,EAAE;QACN,OAAO,EAAE,SAAS;QAClB,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,SAAS;QACtB,MAAM,EAAE,SAAS;QACjB,WAAW,EAAE,SAAS;QACtB,UAAU,EAAE,SAAS;QACrB,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,SAAS;QAClB,KAAK,EAAE,SAAS;QAChB,UAAU,EAAE,SAAS;QACrB,OAAO,EAAE,SAAS;QAClB,IAAI,EAAE;YACJ,OAAO,EAAE,qBAAqB;YAC9B,SAAS,EAAE,oBAAoB;YAC/B,QAAQ,EAAE,qBAAqB;YAC/B,IAAI,EAAE,qBAAqB;SAC5B;QACD,QAAQ,EAAE;YACR,OAAO,EAAE,mDAAmD;YAC5D,SAAS,EAAE,mDAAmD;YAC9D,IAAI,EAAE,mDAAmD;SAC1D;QACD,KAAK,EAAE;YACL,MAAM,EAAE;gBACN,UAAU,EAAE,SAAS;gBACrB,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,SAAS;aACjB;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,SAAS;gBACrB,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,SAAS;aACjB;SACF;KACF;CACF,CAAC;AAEF,kCAAkC;AAClC,MAAM,CAAC,MAAM,iBAAiB,GAAgB;IAC5C,IAAI,EAAE,aAAa;IACnB,MAAM,EAAE;QACN,OAAO,EAAE,SAAS;QAClB,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,SAAS;QACtB,MAAM,EAAE,SAAS;QACjB,WAAW,EAAE,SAAS;QACtB,UAAU,EAAE,SAAS;QACrB,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,SAAS;QAClB,KAAK,EAAE,SAAS;QAChB,UAAU,EAAE,SAAS;QACrB,OAAO,EAAE,SAAS;QAClB,IAAI,EAAE;YACJ,OAAO,EAAE,qBAAqB;YAC9B,SAAS,EAAE,oBAAoB;YAC/B,QAAQ,EAAE,qBAAqB;YAC/B,IAAI,EAAE,qBAAqB;SAC5B;QACD,QAAQ,EAAE;YACR,OAAO,EAAE,mDAAmD;YAC5D,SAAS,EAAE,mDAAmD;YAC9D,IAAI,EAAE,mDAAmD;SAC1D;QACD,KAAK,EAAE;YACL,MAAM,EAAE;gBACN,UAAU,EAAE,SAAS;gBACrB,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,SAAS;aACjB;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,SAAS;gBACrB,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,SAAS;aACjB;SACF;KACF;CACF,CAAC;AAEF,qBAAqB;AACrB,MAAM,CAAC,MAAM,gBAAgB,GAAgB;IAC3C,IAAI,EAAE,YAAY;IAClB,MAAM,EAAE;QACN,OAAO,EAAE,SAAS;QAClB,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,SAAS;QACtB,MAAM,EAAE,SAAS;QACjB,WAAW,EAAE,SAAS;QACtB,UAAU,EAAE,SAAS;QACrB,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,SAAS;QAClB,KAAK,EAAE,SAAS;QAChB,UAAU,EAAE,SAAS;QACrB,OAAO,EAAE,SAAS;QAClB,IAAI,EAAE;YACJ,OAAO,EAAE,qBAAqB;YAC9B,SAAS,EAAE,oBAAoB;YAC/B,QAAQ,EAAE,qBAAqB;YAC/B,IAAI,EAAE,qBAAqB;SAC5B;QACD,QAAQ,EAAE;YACR,OAAO,EAAE,mDAAmD;YAC5D,SAAS,EAAE,mDAAmD;YAC9D,IAAI,EAAE,mDAAmD;SAC1D;QACD,KAAK,EAAE;YACL,MAAM,EAAE;gBACN,UAAU,EAAE,SAAS;gBACrB,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,SAAS;aACjB;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,SAAS;gBACrB,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,SAAS;aACjB;SACF;KACF;CACF,CAAC;AAEF,aAAa;AACb,MAAM,CAAC,MAAM,UAAU,GAAgB;IACrC,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE;QACN,OAAO,EAAE,SAAS;QAClB,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,SAAS;QACtB,MAAM,EAAE,SAAS;QACjB,WAAW,EAAE,SAAS;QACtB,UAAU,EAAE,SAAS;QACrB,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,SAAS;QAClB,KAAK,EAAE,SAAS;QAChB,UAAU,EAAE,SAAS;QACrB,OAAO,EAAE,SAAS;QAClB,IAAI,EAAE;YACJ,OAAO,EAAE,2BAA2B;YACpC,SAAS,EAAE,0BAA0B;YACrC,QAAQ,EAAE,2BAA2B;YACrC,IAAI,EAAE,2BAA2B;SAClC;QACD,QAAQ,EAAE;YACR,OAAO,EAAE,mDAAmD;YAC5D,SAAS,EAAE,mDAAmD;YAC9D,IAAI,EAAE,mDAAmD;SAC1D;QACD,KAAK,EAAE;YACL,MAAM,EAAE;gBACN,UAAU,EAAE,SAAS;gBACrB,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,SAAS;aACjB;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,SAAS;gBACrB,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,SAAS;aACjB;SACF;KACF;CACF,CAAC;AAEF,wBAAwB;AACxB,MAAM,CAAC,MAAM,qBAAqB,GAAgB;IAChD,IAAI,EAAE,iBAAiB;IACvB,MAAM,EAAE;QACN,OAAO,EAAE,SAAS;QAClB,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,SAAS;QACtB,MAAM,EAAE,SAAS;QACjB,WAAW,EAAE,SAAS;QACtB,UAAU,EAAE,SAAS;QACrB,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,SAAS;QAClB,KAAK,EAAE,SAAS;QAChB,UAAU,EAAE,SAAS;QACrB,OAAO,EAAE,SAAS;QAClB,IAAI,EAAE;YACJ,OAAO,EAAE,SAAS;YAClB,SAAS,EAAE,SAAS;YACpB,QAAQ,EAAE,wBAAwB;YAClC,IAAI,EAAE,wBAAwB;SAC/B;QACD,QAAQ,EAAE;YACR,OAAO,EAAE,mDAAmD;YAC5D,SAAS,EAAE,mDAAmD;YAC9D,IAAI,EAAE,mDAAmD;SAC1D;QACD,KAAK,EAAE;YACL,MAAM,EAAE;gBACN,UAAU,EAAE,SAAS;gBACrB,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,SAAS;aACjB;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,SAAS;gBACrB,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,SAAS;aACjB;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAkB;IAC7C,qBAAqB;IACrB,aAAa;IACb,iBAAiB;IACjB,gBAAgB;IAChB,UAAU;CACX,CAAC\",\n      sourcesContent: [\"export interface ThemeColors {\\r\\n  primary: string;\\r\\n  primaryLight: string;\\r\\n  primaryDark: string;\\r\\n  accent: string;\\r\\n  accentLight: string;\\r\\n  accentDark: string;\\r\\n  warn: string;\\r\\n  success: string;\\r\\n  error: string;\\r\\n  background: string;\\r\\n  surface: string;\\r\\n  text: {\\r\\n    primary: string;\\r\\n    secondary: string;\\r\\n    disabled: string;\\r\\n    hint: string;\\r\\n  };\\r\\n  gradient: {\\r\\n    primary: string;\\r\\n    secondary: string;\\r\\n    auth: string;\\r\\n  };\\r\\n  oauth: {\\r\\n    google: {\\r\\n      background: string;\\r\\n      border: string;\\r\\n      text: string;\\r\\n      hover: string;\\r\\n    };\\r\\n    facebook: {\\r\\n      background: string;\\r\\n      border: string;\\r\\n      text: string;\\r\\n      hover: string;\\r\\n    };\\r\\n  };\\r\\n}\\r\\n\\r\\nexport interface ThemeConfig {\\r\\n  name: string;\\r\\n  colors: ThemeColors;\\r\\n}\\r\\n\\r\\n// Default Deep Purple & Amber Theme\\r\\nexport const DEFAULT_THEME: ThemeConfig = {\\r\\n  name: 'deep-purple-amber',\\r\\n  colors: {\\r\\n    primary: '#673ab7',\\r\\n    primaryLight: '#9c64e2',\\r\\n    primaryDark: '#320b86',\\r\\n    accent: '#ffc107',\\r\\n    accentLight: '#fff350',\\r\\n    accentDark: '#c79100',\\r\\n    warn: '#f44336',\\r\\n    success: '#4caf50',\\r\\n    error: '#f44336',\\r\\n    background: '#fafafa',\\r\\n    surface: '#ffffff',\\r\\n    text: {\\r\\n      primary: 'rgba(0, 0, 0, 0.87)',\\r\\n      secondary: 'rgba(0, 0, 0, 0.6)',\\r\\n      disabled: 'rgba(0, 0, 0, 0.38)',\\r\\n      hint: 'rgba(0, 0, 0, 0.38)'\\r\\n    },\\r\\n    gradient: {\\r\\n      primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\\r\\n      secondary: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\\r\\n      auth: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\\r\\n    },\\r\\n    oauth: {\\r\\n      google: {\\r\\n        background: '#ffffff',\\r\\n        border: '#dadce0',\\r\\n        text: '#3c4043',\\r\\n        hover: '#f8f9fa'\\r\\n      },\\r\\n      facebook: {\\r\\n        background: '#ffffff',\\r\\n        border: '#1877f2',\\r\\n        text: '#1877f2',\\r\\n        hover: '#f0f2f5'\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n};\\r\\n\\r\\n// Alternative Blue & Orange Theme\\r\\nexport const BLUE_ORANGE_THEME: ThemeConfig = {\\r\\n  name: 'blue-orange',\\r\\n  colors: {\\r\\n    primary: '#2196f3',\\r\\n    primaryLight: '#64b5f6',\\r\\n    primaryDark: '#1976d2',\\r\\n    accent: '#ff9800',\\r\\n    accentLight: '#ffb74d',\\r\\n    accentDark: '#f57c00',\\r\\n    warn: '#f44336',\\r\\n    success: '#4caf50',\\r\\n    error: '#f44336',\\r\\n    background: '#fafafa',\\r\\n    surface: '#ffffff',\\r\\n    text: {\\r\\n      primary: 'rgba(0, 0, 0, 0.87)',\\r\\n      secondary: 'rgba(0, 0, 0, 0.6)',\\r\\n      disabled: 'rgba(0, 0, 0, 0.38)',\\r\\n      hint: 'rgba(0, 0, 0, 0.38)'\\r\\n    },\\r\\n    gradient: {\\r\\n      primary: 'linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)',\\r\\n      secondary: 'linear-gradient(135deg, #ff9800 0%, #ff5722 100%)',\\r\\n      auth: 'linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)'\\r\\n    },\\r\\n    oauth: {\\r\\n      google: {\\r\\n        background: '#ffffff',\\r\\n        border: '#dadce0',\\r\\n        text: '#3c4043',\\r\\n        hover: '#f8f9fa'\\r\\n      },\\r\\n      facebook: {\\r\\n        background: '#ffffff',\\r\\n        border: '#1877f2',\\r\\n        text: '#1877f2',\\r\\n        hover: '#f0f2f5'\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n};\\r\\n\\r\\n// Green & Teal Theme\\r\\nexport const GREEN_TEAL_THEME: ThemeConfig = {\\r\\n  name: 'green-teal',\\r\\n  colors: {\\r\\n    primary: '#4caf50',\\r\\n    primaryLight: '#81c784',\\r\\n    primaryDark: '#388e3c',\\r\\n    accent: '#009688',\\r\\n    accentLight: '#4db6ac',\\r\\n    accentDark: '#00695c',\\r\\n    warn: '#f44336',\\r\\n    success: '#4caf50',\\r\\n    error: '#f44336',\\r\\n    background: '#fafafa',\\r\\n    surface: '#ffffff',\\r\\n    text: {\\r\\n      primary: 'rgba(0, 0, 0, 0.87)',\\r\\n      secondary: 'rgba(0, 0, 0, 0.6)',\\r\\n      disabled: 'rgba(0, 0, 0, 0.38)',\\r\\n      hint: 'rgba(0, 0, 0, 0.38)'\\r\\n    },\\r\\n    gradient: {\\r\\n      primary: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\\r\\n      secondary: 'linear-gradient(135deg, #009688 0%, #4caf50 100%)',\\r\\n      auth: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)'\\r\\n    },\\r\\n    oauth: {\\r\\n      google: {\\r\\n        background: '#ffffff',\\r\\n        border: '#dadce0',\\r\\n        text: '#3c4043',\\r\\n        hover: '#f8f9fa'\\r\\n      },\\r\\n      facebook: {\\r\\n        background: '#ffffff',\\r\\n        border: '#1877f2',\\r\\n        text: '#1877f2',\\r\\n        hover: '#f0f2f5'\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n};\\r\\n\\r\\n// Dark Theme\\r\\nexport const DARK_THEME: ThemeConfig = {\\r\\n  name: 'dark',\\r\\n  colors: {\\r\\n    primary: '#bb86fc',\\r\\n    primaryLight: '#d7b3ff',\\r\\n    primaryDark: '#985eff',\\r\\n    accent: '#03dac6',\\r\\n    accentLight: '#66fff9',\\r\\n    accentDark: '#00a896',\\r\\n    warn: '#cf6679',\\r\\n    success: '#4caf50',\\r\\n    error: '#cf6679',\\r\\n    background: '#121212',\\r\\n    surface: '#1e1e1e',\\r\\n    text: {\\r\\n      primary: 'rgba(255, 255, 255, 0.87)',\\r\\n      secondary: 'rgba(255, 255, 255, 0.6)',\\r\\n      disabled: 'rgba(255, 255, 255, 0.38)',\\r\\n      hint: 'rgba(255, 255, 255, 0.38)'\\r\\n    },\\r\\n    gradient: {\\r\\n      primary: 'linear-gradient(135deg, #bb86fc 0%, #6200ea 100%)',\\r\\n      secondary: 'linear-gradient(135deg, #03dac6 0%, #018786 100%)',\\r\\n      auth: 'linear-gradient(135deg, #bb86fc 0%, #6200ea 100%)'\\r\\n    },\\r\\n    oauth: {\\r\\n      google: {\\r\\n        background: '#2d2d2d',\\r\\n        border: '#5f6368',\\r\\n        text: '#e8eaed',\\r\\n        hover: '#3c4043'\\r\\n      },\\r\\n      facebook: {\\r\\n        background: '#2d2d2d',\\r\\n        border: '#1877f2',\\r\\n        text: '#1877f2',\\r\\n        hover: '#3c4043'\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n};\\r\\n\\r\\n// Mystical Purple Theme\\r\\nexport const MYSTICAL_PURPLE_THEME: ThemeConfig = {\\r\\n  name: 'mystical-purple',\\r\\n  colors: {\\r\\n    primary: '#67455c', // \\u0442\\u044A\\u043C\\u0435\\u043D \\u043F\\u0443\\u0440\\u043F\\u0443\\u0440\\u0435\\u043D\\r\\n    primaryLight: '#a07ba0', // \\u0440\\u043E\\u0437\\u043E\\u0432\\u043E-\\u043B\\u0438\\u043B\\u0430\\u0432\\u043E\\r\\n    primaryDark: '#3f2f4e', // \\u0434\\u044A\\u043B\\u0431\\u043E\\u043A\\u043E \\u043C\\u0438\\u0441\\u0442\\u0438\\u0447\\u043D\\u043E \\u043B\\u0438\\u043B\\u0430\\u0432\\u043E\\r\\n    accent: '#d2a6d0', // \\u043D\\u0435\\u0436\\u0435\\u043D \\u043B\\u0438\\u043B\\u0430\\u0432\\r\\n    accentLight: '#e6dbec', // \\u043C\\u043D\\u043E\\u0433\\u043E \\u0441\\u0432\\u0435\\u0442\\u043B\\u043E \\u043B\\u0438\\u043B\\u0430\\u0432\\u043E\\r\\n    accentDark: '#a07ba0', // \\u0440\\u043E\\u0437\\u043E\\u0432\\u043E-\\u043B\\u0438\\u043B\\u0430\\u0432\\u043E\\r\\n    warn: '#d2869d',\\r\\n    success: '#8fbc8f',\\r\\n    error: '#d2869d',\\r\\n    background: '#e6dbec', // \\u043C\\u043D\\u043E\\u0433\\u043E \\u0441\\u0432\\u0435\\u0442\\u043B\\u043E \\u043B\\u0438\\u043B\\u0430\\u0432\\u043E\\r\\n    surface: '#ffffff',\\r\\n    text: {\\r\\n      primary: '#3f2f4e', // \\u0434\\u044A\\u043B\\u0431\\u043E\\u043A\\u043E \\u043C\\u0438\\u0441\\u0442\\u0438\\u0447\\u043D\\u043E \\u043B\\u0438\\u043B\\u0430\\u0432\\u043E\\r\\n      secondary: '#67455c', // \\u0442\\u044A\\u043C\\u0435\\u043D \\u043F\\u0443\\u0440\\u043F\\u0443\\u0440\\u0435\\u043D\\r\\n      disabled: 'rgba(63, 47, 78, 0.38)',\\r\\n      hint: 'rgba(63, 47, 78, 0.38)'\\r\\n    },\\r\\n    gradient: {\\r\\n      primary: 'linear-gradient(135deg, #d2a6d0 0%, #67455c 100%)',\\r\\n      secondary: 'linear-gradient(135deg, #a07ba0 0%, #3f2f4e 100%)',\\r\\n      auth: 'linear-gradient(135deg, #d2a6d0 0%, #a07ba0 100%)'\\r\\n    },\\r\\n    oauth: {\\r\\n      google: {\\r\\n        background: '#ffffff',\\r\\n        border: '#d2a6d0',\\r\\n        text: '#67455c',\\r\\n        hover: '#e6dbec'\\r\\n      },\\r\\n      facebook: {\\r\\n        background: '#ffffff',\\r\\n        border: '#67455c',\\r\\n        text: '#67455c',\\r\\n        hover: '#e6dbec'\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n};\\r\\n\\r\\nexport const AVAILABLE_THEMES: ThemeConfig[] = [\\r\\n  MYSTICAL_PURPLE_THEME, // Set as first (default) theme\\r\\n  DEFAULT_THEME,\\r\\n  BLUE_ORANGE_THEME,\\r\\n  GREEN_TEAL_THEME,\\r\\n  DARK_THEME\\r\\n];\\r\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"5a4f9b2897c7cd702b62c070f19437340b5f0d4b\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_1tz9cji8kc = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_1tz9cji8kc();\n// Default Deep Purple & Amber Theme\nexport const DEFAULT_THEME = (cov_1tz9cji8kc().s[0]++, {\n  name: 'deep-purple-amber',\n  colors: {\n    primary: '#673ab7',\n    primaryLight: '#9c64e2',\n    primaryDark: '#320b86',\n    accent: '#ffc107',\n    accentLight: '#fff350',\n    accentDark: '#c79100',\n    warn: '#f44336',\n    success: '#4caf50',\n    error: '#f44336',\n    background: '#fafafa',\n    surface: '#ffffff',\n    text: {\n      primary: 'rgba(0, 0, 0, 0.87)',\n      secondary: 'rgba(0, 0, 0, 0.6)',\n      disabled: 'rgba(0, 0, 0, 0.38)',\n      hint: 'rgba(0, 0, 0, 0.38)'\n    },\n    gradient: {\n      primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      secondary: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n      auth: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n    },\n    oauth: {\n      google: {\n        background: '#ffffff',\n        border: '#dadce0',\n        text: '#3c4043',\n        hover: '#f8f9fa'\n      },\n      facebook: {\n        background: '#ffffff',\n        border: '#1877f2',\n        text: '#1877f2',\n        hover: '#f0f2f5'\n      }\n    }\n  }\n});\n// Alternative Blue & Orange Theme\nexport const BLUE_ORANGE_THEME = (cov_1tz9cji8kc().s[1]++, {\n  name: 'blue-orange',\n  colors: {\n    primary: '#2196f3',\n    primaryLight: '#64b5f6',\n    primaryDark: '#1976d2',\n    accent: '#ff9800',\n    accentLight: '#ffb74d',\n    accentDark: '#f57c00',\n    warn: '#f44336',\n    success: '#4caf50',\n    error: '#f44336',\n    background: '#fafafa',\n    surface: '#ffffff',\n    text: {\n      primary: 'rgba(0, 0, 0, 0.87)',\n      secondary: 'rgba(0, 0, 0, 0.6)',\n      disabled: 'rgba(0, 0, 0, 0.38)',\n      hint: 'rgba(0, 0, 0, 0.38)'\n    },\n    gradient: {\n      primary: 'linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)',\n      secondary: 'linear-gradient(135deg, #ff9800 0%, #ff5722 100%)',\n      auth: 'linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)'\n    },\n    oauth: {\n      google: {\n        background: '#ffffff',\n        border: '#dadce0',\n        text: '#3c4043',\n        hover: '#f8f9fa'\n      },\n      facebook: {\n        background: '#ffffff',\n        border: '#1877f2',\n        text: '#1877f2',\n        hover: '#f0f2f5'\n      }\n    }\n  }\n});\n// Green & Teal Theme\nexport const GREEN_TEAL_THEME = (cov_1tz9cji8kc().s[2]++, {\n  name: 'green-teal',\n  colors: {\n    primary: '#4caf50',\n    primaryLight: '#81c784',\n    primaryDark: '#388e3c',\n    accent: '#009688',\n    accentLight: '#4db6ac',\n    accentDark: '#00695c',\n    warn: '#f44336',\n    success: '#4caf50',\n    error: '#f44336',\n    background: '#fafafa',\n    surface: '#ffffff',\n    text: {\n      primary: 'rgba(0, 0, 0, 0.87)',\n      secondary: 'rgba(0, 0, 0, 0.6)',\n      disabled: 'rgba(0, 0, 0, 0.38)',\n      hint: 'rgba(0, 0, 0, 0.38)'\n    },\n    gradient: {\n      primary: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n      secondary: 'linear-gradient(135deg, #009688 0%, #4caf50 100%)',\n      auth: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)'\n    },\n    oauth: {\n      google: {\n        background: '#ffffff',\n        border: '#dadce0',\n        text: '#3c4043',\n        hover: '#f8f9fa'\n      },\n      facebook: {\n        background: '#ffffff',\n        border: '#1877f2',\n        text: '#1877f2',\n        hover: '#f0f2f5'\n      }\n    }\n  }\n});\n// Dark Theme\nexport const DARK_THEME = (cov_1tz9cji8kc().s[3]++, {\n  name: 'dark',\n  colors: {\n    primary: '#bb86fc',\n    primaryLight: '#d7b3ff',\n    primaryDark: '#985eff',\n    accent: '#03dac6',\n    accentLight: '#66fff9',\n    accentDark: '#00a896',\n    warn: '#cf6679',\n    success: '#4caf50',\n    error: '#cf6679',\n    background: '#121212',\n    surface: '#1e1e1e',\n    text: {\n      primary: 'rgba(255, 255, 255, 0.87)',\n      secondary: 'rgba(255, 255, 255, 0.6)',\n      disabled: 'rgba(255, 255, 255, 0.38)',\n      hint: 'rgba(255, 255, 255, 0.38)'\n    },\n    gradient: {\n      primary: 'linear-gradient(135deg, #bb86fc 0%, #6200ea 100%)',\n      secondary: 'linear-gradient(135deg, #03dac6 0%, #018786 100%)',\n      auth: 'linear-gradient(135deg, #bb86fc 0%, #6200ea 100%)'\n    },\n    oauth: {\n      google: {\n        background: '#2d2d2d',\n        border: '#5f6368',\n        text: '#e8eaed',\n        hover: '#3c4043'\n      },\n      facebook: {\n        background: '#2d2d2d',\n        border: '#1877f2',\n        text: '#1877f2',\n        hover: '#3c4043'\n      }\n    }\n  }\n});\n// Mystical Purple Theme\nexport const MYSTICAL_PURPLE_THEME = (cov_1tz9cji8kc().s[4]++, {\n  name: 'mystical-purple',\n  colors: {\n    primary: '#67455c',\n    primaryLight: '#a07ba0',\n    primaryDark: '#3f2f4e',\n    accent: '#d2a6d0',\n    accentLight: '#e6dbec',\n    accentDark: '#a07ba0',\n    warn: '#d2869d',\n    success: '#8fbc8f',\n    error: '#d2869d',\n    background: '#e6dbec',\n    surface: '#ffffff',\n    text: {\n      primary: '#3f2f4e',\n      secondary: '#67455c',\n      disabled: 'rgba(63, 47, 78, 0.38)',\n      hint: 'rgba(63, 47, 78, 0.38)'\n    },\n    gradient: {\n      primary: 'linear-gradient(135deg, #d2a6d0 0%, #67455c 100%)',\n      secondary: 'linear-gradient(135deg, #a07ba0 0%, #3f2f4e 100%)',\n      auth: 'linear-gradient(135deg, #d2a6d0 0%, #a07ba0 100%)'\n    },\n    oauth: {\n      google: {\n        background: '#ffffff',\n        border: '#d2a6d0',\n        text: '#67455c',\n        hover: '#e6dbec'\n      },\n      facebook: {\n        background: '#ffffff',\n        border: '#67455c',\n        text: '#67455c',\n        hover: '#e6dbec'\n      }\n    }\n  }\n});\nexport const AVAILABLE_THEMES = (cov_1tz9cji8kc().s[5]++, [MYSTICAL_PURPLE_THEME, DEFAULT_THEME, BLUE_ORANGE_THEME, GREEN_TEAL_THEME, DARK_THEME]);", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2DQ;IAAA;MAAA;IAAA;EAAA;EAAA;AAAA;AAAA;AAfR;AACA,OAAO,MAAMA,aAAa,6BAAgB;EACxCC,IAAI,EAAE,mBAAmB;EACzBC,MAAM,EAAE;IACNC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,SAAS;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE;MACJX,OAAO,EAAE,qBAAqB;MAC9BY,SAAS,EAAE,oBAAoB;MAC/BC,QAAQ,EAAE,qBAAqB;MAC/BC,IAAI,EAAE;KACP;IACDC,QAAQ,EAAE;MACRf,OAAO,EAAE,mDAAmD;MAC5DY,SAAS,EAAE,mDAAmD;MAC9DI,IAAI,EAAE;KACP;IACDC,KAAK,EAAE;MACLC,MAAM,EAAE;QACNT,UAAU,EAAE,SAAS;QACrBU,MAAM,EAAE,SAAS;QACjBR,IAAI,EAAE,SAAS;QACfS,KAAK,EAAE;OACR;MACDC,QAAQ,EAAE;QACRZ,UAAU,EAAE,SAAS;QACrBU,MAAM,EAAE,SAAS;QACjBR,IAAI,EAAE,SAAS;QACfS,KAAK,EAAE;;;;CAId;AAED;AACA,OAAO,MAAME,iBAAiB,6BAAgB;EAC5CxB,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE;IACNC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,SAAS;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE;MACJX,OAAO,EAAE,qBAAqB;MAC9BY,SAAS,EAAE,oBAAoB;MAC/BC,QAAQ,EAAE,qBAAqB;MAC/BC,IAAI,EAAE;KACP;IACDC,QAAQ,EAAE;MACRf,OAAO,EAAE,mDAAmD;MAC5DY,SAAS,EAAE,mDAAmD;MAC9DI,IAAI,EAAE;KACP;IACDC,KAAK,EAAE;MACLC,MAAM,EAAE;QACNT,UAAU,EAAE,SAAS;QACrBU,MAAM,EAAE,SAAS;QACjBR,IAAI,EAAE,SAAS;QACfS,KAAK,EAAE;OACR;MACDC,QAAQ,EAAE;QACRZ,UAAU,EAAE,SAAS;QACrBU,MAAM,EAAE,SAAS;QACjBR,IAAI,EAAE,SAAS;QACfS,KAAK,EAAE;;;;CAId;AAED;AACA,OAAO,MAAMG,gBAAgB,6BAAgB;EAC3CzB,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE;IACNC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,SAAS;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE;MACJX,OAAO,EAAE,qBAAqB;MAC9BY,SAAS,EAAE,oBAAoB;MAC/BC,QAAQ,EAAE,qBAAqB;MAC/BC,IAAI,EAAE;KACP;IACDC,QAAQ,EAAE;MACRf,OAAO,EAAE,mDAAmD;MAC5DY,SAAS,EAAE,mDAAmD;MAC9DI,IAAI,EAAE;KACP;IACDC,KAAK,EAAE;MACLC,MAAM,EAAE;QACNT,UAAU,EAAE,SAAS;QACrBU,MAAM,EAAE,SAAS;QACjBR,IAAI,EAAE,SAAS;QACfS,KAAK,EAAE;OACR;MACDC,QAAQ,EAAE;QACRZ,UAAU,EAAE,SAAS;QACrBU,MAAM,EAAE,SAAS;QACjBR,IAAI,EAAE,SAAS;QACfS,KAAK,EAAE;;;;CAId;AAED;AACA,OAAO,MAAMI,UAAU,6BAAgB;EACrC1B,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE;IACNC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,SAAS;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE;MACJX,OAAO,EAAE,2BAA2B;MACpCY,SAAS,EAAE,0BAA0B;MACrCC,QAAQ,EAAE,2BAA2B;MACrCC,IAAI,EAAE;KACP;IACDC,QAAQ,EAAE;MACRf,OAAO,EAAE,mDAAmD;MAC5DY,SAAS,EAAE,mDAAmD;MAC9DI,IAAI,EAAE;KACP;IACDC,KAAK,EAAE;MACLC,MAAM,EAAE;QACNT,UAAU,EAAE,SAAS;QACrBU,MAAM,EAAE,SAAS;QACjBR,IAAI,EAAE,SAAS;QACfS,KAAK,EAAE;OACR;MACDC,QAAQ,EAAE;QACRZ,UAAU,EAAE,SAAS;QACrBU,MAAM,EAAE,SAAS;QACjBR,IAAI,EAAE,SAAS;QACfS,KAAK,EAAE;;;;CAId;AAED;AACA,OAAO,MAAMK,qBAAqB,6BAAgB;EAChD3B,IAAI,EAAE,iBAAiB;EACvBC,MAAM,EAAE;IACNC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,SAAS;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE;MACJX,OAAO,EAAE,SAAS;MAClBY,SAAS,EAAE,SAAS;MACpBC,QAAQ,EAAE,wBAAwB;MAClCC,IAAI,EAAE;KACP;IACDC,QAAQ,EAAE;MACRf,OAAO,EAAE,mDAAmD;MAC5DY,SAAS,EAAE,mDAAmD;MAC9DI,IAAI,EAAE;KACP;IACDC,KAAK,EAAE;MACLC,MAAM,EAAE;QACNT,UAAU,EAAE,SAAS;QACrBU,MAAM,EAAE,SAAS;QACjBR,IAAI,EAAE,SAAS;QACfS,KAAK,EAAE;OACR;MACDC,QAAQ,EAAE;QACRZ,UAAU,EAAE,SAAS;QACrBU,MAAM,EAAE,SAAS;QACjBR,IAAI,EAAE,SAAS;QACfS,KAAK,EAAE;;;;CAId;AAED,OAAO,MAAMM,gBAAgB,6BAAkB,CAC7CD,qBAAqB,EACrB5B,aAAa,EACbyB,iBAAiB,EACjBC,gBAAgB,EAChBC,UAAU,CACX", "names": ["DEFAULT_THEME", "name", "colors", "primary", "primaryLight", "primaryDark", "accent", "accentLight", "accentDark", "warn", "success", "error", "background", "surface", "text", "secondary", "disabled", "hint", "gradient", "auth", "o<PERSON>h", "google", "border", "hover", "facebook", "BLUE_ORANGE_THEME", "GREEN_TEAL_THEME", "DARK_THEME", "MYSTICAL_PURPLE_THEME", "AVAILABLE_THEMES"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\core\\theme\\theme.config.ts"], "sourcesContent": ["export interface ThemeColors {\r\n  primary: string;\r\n  primaryLight: string;\r\n  primaryDark: string;\r\n  accent: string;\r\n  accentLight: string;\r\n  accentDark: string;\r\n  warn: string;\r\n  success: string;\r\n  error: string;\r\n  background: string;\r\n  surface: string;\r\n  text: {\r\n    primary: string;\r\n    secondary: string;\r\n    disabled: string;\r\n    hint: string;\r\n  };\r\n  gradient: {\r\n    primary: string;\r\n    secondary: string;\r\n    auth: string;\r\n  };\r\n  oauth: {\r\n    google: {\r\n      background: string;\r\n      border: string;\r\n      text: string;\r\n      hover: string;\r\n    };\r\n    facebook: {\r\n      background: string;\r\n      border: string;\r\n      text: string;\r\n      hover: string;\r\n    };\r\n  };\r\n}\r\n\r\nexport interface ThemeConfig {\r\n  name: string;\r\n  colors: ThemeColors;\r\n}\r\n\r\n// Default Deep Purple & Amber Theme\r\nexport const DEFAULT_THEME: ThemeConfig = {\r\n  name: 'deep-purple-amber',\r\n  colors: {\r\n    primary: '#673ab7',\r\n    primaryLight: '#9c64e2',\r\n    primaryDark: '#320b86',\r\n    accent: '#ffc107',\r\n    accentLight: '#fff350',\r\n    accentDark: '#c79100',\r\n    warn: '#f44336',\r\n    success: '#4caf50',\r\n    error: '#f44336',\r\n    background: '#fafafa',\r\n    surface: '#ffffff',\r\n    text: {\r\n      primary: 'rgba(0, 0, 0, 0.87)',\r\n      secondary: 'rgba(0, 0, 0, 0.6)',\r\n      disabled: 'rgba(0, 0, 0, 0.38)',\r\n      hint: 'rgba(0, 0, 0, 0.38)'\r\n    },\r\n    gradient: {\r\n      primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n      secondary: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\r\n      auth: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\r\n    },\r\n    oauth: {\r\n      google: {\r\n        background: '#ffffff',\r\n        border: '#dadce0',\r\n        text: '#3c4043',\r\n        hover: '#f8f9fa'\r\n      },\r\n      facebook: {\r\n        background: '#ffffff',\r\n        border: '#1877f2',\r\n        text: '#1877f2',\r\n        hover: '#f0f2f5'\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\n// Alternative Blue & Orange Theme\r\nexport const BLUE_ORANGE_THEME: ThemeConfig = {\r\n  name: 'blue-orange',\r\n  colors: {\r\n    primary: '#2196f3',\r\n    primaryLight: '#64b5f6',\r\n    primaryDark: '#1976d2',\r\n    accent: '#ff9800',\r\n    accentLight: '#ffb74d',\r\n    accentDark: '#f57c00',\r\n    warn: '#f44336',\r\n    success: '#4caf50',\r\n    error: '#f44336',\r\n    background: '#fafafa',\r\n    surface: '#ffffff',\r\n    text: {\r\n      primary: 'rgba(0, 0, 0, 0.87)',\r\n      secondary: 'rgba(0, 0, 0, 0.6)',\r\n      disabled: 'rgba(0, 0, 0, 0.38)',\r\n      hint: 'rgba(0, 0, 0, 0.38)'\r\n    },\r\n    gradient: {\r\n      primary: 'linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)',\r\n      secondary: 'linear-gradient(135deg, #ff9800 0%, #ff5722 100%)',\r\n      auth: 'linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)'\r\n    },\r\n    oauth: {\r\n      google: {\r\n        background: '#ffffff',\r\n        border: '#dadce0',\r\n        text: '#3c4043',\r\n        hover: '#f8f9fa'\r\n      },\r\n      facebook: {\r\n        background: '#ffffff',\r\n        border: '#1877f2',\r\n        text: '#1877f2',\r\n        hover: '#f0f2f5'\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\n// Green & Teal Theme\r\nexport const GREEN_TEAL_THEME: ThemeConfig = {\r\n  name: 'green-teal',\r\n  colors: {\r\n    primary: '#4caf50',\r\n    primaryLight: '#81c784',\r\n    primaryDark: '#388e3c',\r\n    accent: '#009688',\r\n    accentLight: '#4db6ac',\r\n    accentDark: '#00695c',\r\n    warn: '#f44336',\r\n    success: '#4caf50',\r\n    error: '#f44336',\r\n    background: '#fafafa',\r\n    surface: '#ffffff',\r\n    text: {\r\n      primary: 'rgba(0, 0, 0, 0.87)',\r\n      secondary: 'rgba(0, 0, 0, 0.6)',\r\n      disabled: 'rgba(0, 0, 0, 0.38)',\r\n      hint: 'rgba(0, 0, 0, 0.38)'\r\n    },\r\n    gradient: {\r\n      primary: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\r\n      secondary: 'linear-gradient(135deg, #009688 0%, #4caf50 100%)',\r\n      auth: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)'\r\n    },\r\n    oauth: {\r\n      google: {\r\n        background: '#ffffff',\r\n        border: '#dadce0',\r\n        text: '#3c4043',\r\n        hover: '#f8f9fa'\r\n      },\r\n      facebook: {\r\n        background: '#ffffff',\r\n        border: '#1877f2',\r\n        text: '#1877f2',\r\n        hover: '#f0f2f5'\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\n// Dark Theme\r\nexport const DARK_THEME: ThemeConfig = {\r\n  name: 'dark',\r\n  colors: {\r\n    primary: '#bb86fc',\r\n    primaryLight: '#d7b3ff',\r\n    primaryDark: '#985eff',\r\n    accent: '#03dac6',\r\n    accentLight: '#66fff9',\r\n    accentDark: '#00a896',\r\n    warn: '#cf6679',\r\n    success: '#4caf50',\r\n    error: '#cf6679',\r\n    background: '#121212',\r\n    surface: '#1e1e1e',\r\n    text: {\r\n      primary: 'rgba(255, 255, 255, 0.87)',\r\n      secondary: 'rgba(255, 255, 255, 0.6)',\r\n      disabled: 'rgba(255, 255, 255, 0.38)',\r\n      hint: 'rgba(255, 255, 255, 0.38)'\r\n    },\r\n    gradient: {\r\n      primary: 'linear-gradient(135deg, #bb86fc 0%, #6200ea 100%)',\r\n      secondary: 'linear-gradient(135deg, #03dac6 0%, #018786 100%)',\r\n      auth: 'linear-gradient(135deg, #bb86fc 0%, #6200ea 100%)'\r\n    },\r\n    oauth: {\r\n      google: {\r\n        background: '#2d2d2d',\r\n        border: '#5f6368',\r\n        text: '#e8eaed',\r\n        hover: '#3c4043'\r\n      },\r\n      facebook: {\r\n        background: '#2d2d2d',\r\n        border: '#1877f2',\r\n        text: '#1877f2',\r\n        hover: '#3c4043'\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\n// Mystical Purple Theme\r\nexport const MYSTICAL_PURPLE_THEME: ThemeConfig = {\r\n  name: 'mystical-purple',\r\n  colors: {\r\n    primary: '#67455c', // тъмен пурпурен\r\n    primaryLight: '#a07ba0', // розово-лилаво\r\n    primaryDark: '#3f2f4e', // дълбоко мистично лилаво\r\n    accent: '#d2a6d0', // нежен лилав\r\n    accentLight: '#e6dbec', // много светло лилаво\r\n    accentDark: '#a07ba0', // розово-лилаво\r\n    warn: '#d2869d',\r\n    success: '#8fbc8f',\r\n    error: '#d2869d',\r\n    background: '#e6dbec', // много светло лилаво\r\n    surface: '#ffffff',\r\n    text: {\r\n      primary: '#3f2f4e', // дълбоко мистично лилаво\r\n      secondary: '#67455c', // тъмен пурпурен\r\n      disabled: 'rgba(63, 47, 78, 0.38)',\r\n      hint: 'rgba(63, 47, 78, 0.38)'\r\n    },\r\n    gradient: {\r\n      primary: 'linear-gradient(135deg, #d2a6d0 0%, #67455c 100%)',\r\n      secondary: 'linear-gradient(135deg, #a07ba0 0%, #3f2f4e 100%)',\r\n      auth: 'linear-gradient(135deg, #d2a6d0 0%, #a07ba0 100%)'\r\n    },\r\n    oauth: {\r\n      google: {\r\n        background: '#ffffff',\r\n        border: '#d2a6d0',\r\n        text: '#67455c',\r\n        hover: '#e6dbec'\r\n      },\r\n      facebook: {\r\n        background: '#ffffff',\r\n        border: '#67455c',\r\n        text: '#67455c',\r\n        hover: '#e6dbec'\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nexport const AVAILABLE_THEMES: ThemeConfig[] = [\r\n  MYSTICAL_PURPLE_THEME, // Set as first (default) theme\r\n  DEFAULT_THEME,\r\n  BLUE_ORANGE_THEME,\r\n  GREEN_TEAL_THEME,\r\n  DARK_THEME\r\n];\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}