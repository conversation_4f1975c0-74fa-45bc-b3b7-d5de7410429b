{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./profile-edit.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./profile-edit.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { Subject, takeUntil } from 'rxjs';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { ProfileService } from '../../services/profile.service';\nimport { AuthService } from '../../../auth/services/auth.service';\nlet ProfileEditComponent = class ProfileEditComponent {\n  constructor(formBuilder, profileService, authService, router, snackBar) {\n    this.formBuilder = formBuilder;\n    this.profileService = profileService;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.profile = null;\n    this.isLoading = true;\n    this.isSaving = false;\n    this.isUploadingProfilePhoto = false;\n    this.isUploadingCoverPhoto = false;\n    this.profilePhotoPreview = null;\n    this.coverPhotoPreview = null;\n    this.destroy$ = new Subject();\n    this.profileForm = this.createForm();\n  }\n  ngOnInit() {\n    this.loadProfile();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  createForm() {\n    return this.formBuilder.group({\n      // Basic Information\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      professionalTitle: [''],\n      headline: ['', Validators.maxLength(220)],\n      summary: ['', Validators.maxLength(2000)],\n      // Location\n      location: this.formBuilder.group({\n        city: [''],\n        state: [''],\n        country: [''],\n        displayLocation: ['']\n      }),\n      // Contact Information\n      contactInfo: this.formBuilder.group({\n        email: ['', Validators.email],\n        isEmailPublic: [false],\n        website: [''],\n        portfolioUrl: [''],\n        phoneNumbers: this.formBuilder.array([]),\n        businessAddress: this.formBuilder.group({\n          street: [''],\n          city: [''],\n          state: [''],\n          postalCode: [''],\n          country: [''],\n          isPublic: [false]\n        })\n      }),\n      // Privacy Settings\n      isPublic: [true],\n      // Social Links\n      socialLinks: this.formBuilder.array([]),\n      // Skills\n      skills: this.formBuilder.array([]),\n      // Consultation Rates\n      consultationRates: this.formBuilder.group({\n        hourlyRate: [null, [Validators.min(0), Validators.max(10000)]],\n        sessionRate: [null, [Validators.min(0), Validators.max(10000)]],\n        currency: ['BGN', Validators.required]\n      }),\n      // Service Offerings\n      serviceOfferings: this.formBuilder.array([])\n    });\n  }\n  loadProfile() {\n    this.profileService.getCurrentUserProfile().pipe(takeUntil(this.destroy$)).subscribe({\n      next: profile => {\n        this.profile = profile;\n        this.populateForm(profile);\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading profile:', error);\n        this.snackBar.open('Error loading profile', 'Close', {\n          duration: 5000\n        });\n        this.router.navigate(['/dashboard']);\n        this.isLoading = false;\n      }\n    });\n  }\n  populateForm(profile) {\n    this.profileForm.patchValue({\n      firstName: profile.firstName,\n      lastName: profile.lastName,\n      professionalTitle: profile.professionalTitle || '',\n      headline: profile.headline || '',\n      summary: profile.summary || '',\n      location: {\n        city: profile.location?.city || '',\n        state: profile.location?.state || '',\n        country: profile.location?.country || '',\n        displayLocation: profile.location?.displayLocation || ''\n      },\n      contactInfo: {\n        email: profile.contactInfo.email || '',\n        isEmailPublic: profile.contactInfo.isEmailPublic,\n        website: profile.contactInfo.website || '',\n        portfolioUrl: profile.contactInfo.portfolioUrl || '',\n        businessAddress: {\n          street: profile.contactInfo.businessAddress?.street || '',\n          city: profile.contactInfo.businessAddress?.city || '',\n          state: profile.contactInfo.businessAddress?.state || '',\n          postalCode: profile.contactInfo.businessAddress?.postalCode || '',\n          country: profile.contactInfo.businessAddress?.country || '',\n          isPublic: profile.contactInfo.businessAddress?.isPublic || false\n        }\n      },\n      isPublic: profile.isPublic,\n      consultationRates: {\n        hourlyRate: profile.consultationRates?.hourlyRate || null,\n        sessionRate: profile.consultationRates?.sessionRate || null,\n        currency: profile.consultationRates?.currency || 'BGN'\n      }\n    });\n    // Populate phone numbers\n    this.setPhoneNumbers(profile.contactInfo.phoneNumbers || []);\n    // Populate social links\n    this.setSocialLinks(profile.socialLinks || []);\n    // Populate skills\n    this.setSkills(profile.skills || []);\n    // Populate service offerings\n    this.setServiceOfferings(profile.serviceOfferings || []);\n  }\n  // Phone Numbers Management\n  get phoneNumbers() {\n    return this.profileForm.get('contactInfo.phoneNumbers');\n  }\n  setPhoneNumbers(phones) {\n    const phoneArray = this.phoneNumbers;\n    phoneArray.clear();\n    phones.forEach(phone => {\n      phoneArray.push(this.formBuilder.group({\n        id: [phone.id],\n        number: [phone.number, Validators.required],\n        type: [phone.type, Validators.required],\n        isPublic: [phone.isPublic],\n        isPrimary: [phone.isPrimary]\n      }));\n    });\n  }\n  addPhoneNumber() {\n    const phoneGroup = this.formBuilder.group({\n      id: [null],\n      number: ['', Validators.required],\n      type: ['mobile', Validators.required],\n      isPublic: [false],\n      isPrimary: [false]\n    });\n    this.phoneNumbers.push(phoneGroup);\n  }\n  removePhoneNumber(index) {\n    this.phoneNumbers.removeAt(index);\n  }\n  // Social Links Management\n  get socialLinks() {\n    return this.profileForm.get('socialLinks');\n  }\n  setSocialLinks(links) {\n    const linksArray = this.socialLinks;\n    linksArray.clear();\n    links.forEach(link => {\n      linksArray.push(this.formBuilder.group({\n        id: [link.id],\n        platform: [link.platform, Validators.required],\n        url: [link.url, [Validators.required, Validators.pattern('https?://.+')]],\n        displayName: [link.displayName],\n        isPublic: [link.isPublic]\n      }));\n    });\n  }\n  addSocialLink() {\n    const linkGroup = this.formBuilder.group({\n      id: [null],\n      platform: ['linkedin', Validators.required],\n      url: ['', [Validators.required, Validators.pattern('https?://.+')]],\n      displayName: [''],\n      isPublic: [true]\n    });\n    this.socialLinks.push(linkGroup);\n  }\n  removeSocialLink(index) {\n    this.socialLinks.removeAt(index);\n  }\n  // Skills Management\n  get skills() {\n    return this.profileForm.get('skills');\n  }\n  setSkills(skills) {\n    const skillArray = this.skills;\n    skillArray.clear();\n    skills.forEach(skill => {\n      skillArray.push(this.formBuilder.group({\n        id: [skill.id],\n        name: [skill.name, Validators.required],\n        category: [skill.category || ''],\n        proficiencyLevel: [skill.proficiencyLevel || 'intermediate', Validators.required]\n      }));\n    });\n  }\n  addSkill() {\n    const skillGroup = this.formBuilder.group({\n      id: [null],\n      name: ['', Validators.required],\n      category: [''],\n      proficiencyLevel: ['intermediate', Validators.required]\n    });\n    this.skills.push(skillGroup);\n  }\n  removeSkill(index) {\n    this.skills.removeAt(index);\n  }\n  // Service Offerings Management\n  get serviceOfferings() {\n    return this.profileForm.get('serviceOfferings');\n  }\n  setServiceOfferings(services) {\n    const serviceArray = this.serviceOfferings;\n    serviceArray.clear();\n    services.forEach(service => {\n      serviceArray.push(this.formBuilder.group({\n        id: [service.id],\n        name: [service.name, Validators.required],\n        description: [service.description, Validators.required],\n        price: [service.price, [Validators.required, Validators.min(0)]],\n        currency: [service.currency || 'BGN', Validators.required],\n        duration: [service.duration],\n        category: [service.category || ''],\n        isActive: [service.isActive !== false] // default to true\n      }));\n    });\n  }\n\n  addServiceOffering() {\n    const serviceGroup = this.formBuilder.group({\n      id: [null],\n      name: ['', Validators.required],\n      description: ['', Validators.required],\n      price: [0, [Validators.required, Validators.min(0)]],\n      currency: ['BGN', Validators.required],\n      duration: [60],\n      category: [''],\n      isActive: [true]\n    });\n    this.serviceOfferings.push(serviceGroup);\n  }\n  removeServiceOffering(index) {\n    this.serviceOfferings.removeAt(index);\n  }\n  // Form Submission\n  onSubmit() {\n    if (this.validateForm()) {\n      this.isSaving = true;\n      const formValue = this.profileForm.value;\n      const updateRequest = {\n        firstName: formValue.firstName,\n        lastName: formValue.lastName,\n        professionalTitle: formValue.professionalTitle,\n        headline: formValue.headline,\n        location: formValue.location,\n        contactInfo: formValue.contactInfo,\n        summary: formValue.summary,\n        isPublic: formValue.isPublic,\n        skills: formValue.skills || [],\n        consultationRates: formValue.consultationRates,\n        serviceOfferings: formValue.serviceOfferings || []\n      };\n      this.profileService.updateProfile(updateRequest).pipe(takeUntil(this.destroy$)).subscribe({\n        next: updatedProfile => {\n          this.isSaving = false;\n          this.snackBar.open('Profile updated successfully!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate(['/profile', updatedProfile.slug]);\n        },\n        error: error => {\n          this.isSaving = false;\n          console.error('Error updating profile:', error);\n          this.snackBar.open('Error updating profile. Please try again.', 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    }\n    // Validation is now handled in validateForm() method\n  }\n\n  onCancel() {\n    if (this.profile) {\n      this.router.navigate(['/profile', this.profile.slug]);\n    } else {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  // File Upload Methods\n  onProfilePhotoSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      if (this.validateImageFile(file, 'profile')) {\n        this.createImagePreview(file, 'profile');\n        this.uploadProfilePhoto(file);\n      }\n    }\n    // Reset the input to allow selecting the same file again\n    event.target.value = '';\n  }\n  onCoverPhotoSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      if (this.validateImageFile(file, 'cover')) {\n        this.createImagePreview(file, 'cover');\n        this.uploadCoverPhoto(file);\n      }\n    }\n    // Reset the input to allow selecting the same file again\n    event.target.value = '';\n  }\n  validateImageFile(file, type) {\n    // Check file type\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      this.snackBar.open('Please select a valid image file (JPEG, PNG, GIF, or WebP)', 'Close', {\n        duration: 5000\n      });\n      return false;\n    }\n    // Check file size (5MB for profile, 10MB for cover)\n    const maxSize = type === 'profile' ? 5 * 1024 * 1024 : 10 * 1024 * 1024;\n    if (file.size > maxSize) {\n      const maxSizeMB = maxSize / (1024 * 1024);\n      this.snackBar.open(`File size must be less than ${maxSizeMB}MB`, 'Close', {\n        duration: 5000\n      });\n      return false;\n    }\n    return true;\n  }\n  createImagePreview(file, type) {\n    const reader = new FileReader();\n    reader.onload = e => {\n      if (type === 'profile') {\n        this.profilePhotoPreview = e.target.result;\n      } else {\n        this.coverPhotoPreview = e.target.result;\n      }\n    };\n    reader.readAsDataURL(file);\n  }\n  uploadProfilePhoto(file) {\n    this.isUploadingProfilePhoto = true;\n    this.profileService.uploadProfilePhoto(file).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        this.isUploadingProfilePhoto = false;\n        if (this.profile) {\n          this.profile.profilePhotoUrl = response.url;\n        }\n        this.profilePhotoPreview = null; // Clear preview since we have the actual URL\n        this.snackBar.open('Profile photo updated successfully!', 'Close', {\n          duration: 3000\n        });\n      },\n      error: error => {\n        this.isUploadingProfilePhoto = false;\n        this.profilePhotoPreview = null; // Clear preview on error\n        console.error('Error uploading profile photo:', error);\n        const errorMessage = error.error?.message || 'Error uploading profile photo. Please try again.';\n        this.snackBar.open(errorMessage, 'Close', {\n          duration: 5000\n        });\n      }\n    });\n  }\n  uploadCoverPhoto(file) {\n    this.isUploadingCoverPhoto = true;\n    this.profileService.uploadCoverPhoto(file).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        this.isUploadingCoverPhoto = false;\n        if (this.profile) {\n          this.profile.coverPhotoUrl = response.url;\n        }\n        this.coverPhotoPreview = null; // Clear preview since we have the actual URL\n        this.snackBar.open('Cover photo updated successfully!', 'Close', {\n          duration: 3000\n        });\n      },\n      error: error => {\n        this.isUploadingCoverPhoto = false;\n        this.coverPhotoPreview = null; // Clear preview on error\n        console.error('Error uploading cover photo:', error);\n        const errorMessage = error.error?.message || 'Error uploading cover photo. Please try again.';\n        this.snackBar.open(errorMessage, 'Close', {\n          duration: 5000\n        });\n      }\n    });\n  }\n  // Utility Methods\n  markFormGroupTouched() {\n    Object.keys(this.profileForm.controls).forEach(key => {\n      const control = this.profileForm.get(key);\n      control?.markAsTouched();\n      if (control instanceof FormArray) {\n        control.controls.forEach(arrayControl => {\n          if (arrayControl instanceof FormGroup) {\n            Object.keys(arrayControl.controls).forEach(arrayKey => {\n              arrayControl.get(arrayKey)?.markAsTouched();\n            });\n          }\n        });\n      }\n    });\n  }\n  getErrorMessage(fieldName) {\n    const control = this.profileForm.get(fieldName);\n    if (!control || !control.errors) return '';\n    const fieldDisplayName = this.getFieldDisplayName(fieldName);\n    if (control.hasError('required')) {\n      return `${fieldDisplayName} is required`;\n    }\n    if (control.hasError('email')) {\n      return 'Please enter a valid email address';\n    }\n    if (control.hasError('minlength')) {\n      const requiredLength = control.errors['minlength'].requiredLength;\n      return `${fieldDisplayName} must be at least ${requiredLength} characters`;\n    }\n    if (control.hasError('maxlength')) {\n      const maxLength = control.errors['maxlength'].requiredLength;\n      return `${fieldDisplayName} must be no more than ${maxLength} characters`;\n    }\n    if (control.hasError('pattern')) {\n      return 'Please enter a valid URL';\n    }\n    if (control.hasError('min')) {\n      const minValue = control.errors['min'].min;\n      return `${fieldDisplayName} must be at least ${minValue}`;\n    }\n    if (control.hasError('max')) {\n      const maxValue = control.errors['max'].max;\n      return `${fieldDisplayName} cannot exceed ${maxValue}`;\n    }\n    return '';\n  }\n  getFieldDisplayName(fieldName) {\n    const fieldNames = {\n      'firstName': 'First Name',\n      'lastName': 'Last Name',\n      'professionalTitle': 'Professional Title',\n      'headline': 'Headline',\n      'summary': 'Summary',\n      'contactInfo.email': 'Email',\n      'contactInfo.website': 'Website',\n      'contactInfo.portfolioUrl': 'Portfolio URL',\n      'location.city': 'City',\n      'location.state': 'State',\n      'location.country': 'Country',\n      'location.displayLocation': 'Display Location',\n      'consultationRates.hourlyRate': 'Hourly Rate',\n      'consultationRates.sessionRate': 'Session Rate',\n      'consultationRates.currency': 'Currency'\n    };\n    return fieldNames[fieldName] || fieldName;\n  }\n  // Enhanced form validation\n  validateForm() {\n    if (this.profileForm.invalid) {\n      this.markFormGroupTouched();\n      // Find first invalid field and focus on it\n      const firstInvalidField = this.findFirstInvalidField();\n      if (firstInvalidField) {\n        firstInvalidField.focus();\n      }\n      // Show specific error message\n      const errors = this.getFormErrors();\n      if (errors.length > 0) {\n        this.snackBar.open(`Please fix the following errors: ${errors.join(', ')}`, 'Close', {\n          duration: 5000\n        });\n      }\n      return false;\n    }\n    return true;\n  }\n  findFirstInvalidField() {\n    const invalidFields = document.querySelectorAll('.mat-form-field.ng-invalid input, .mat-form-field.ng-invalid textarea, .mat-form-field.ng-invalid mat-select');\n    return invalidFields.length > 0 ? invalidFields[0] : null;\n  }\n  getFormErrors() {\n    const errors = [];\n    // Check main form fields\n    Object.keys(this.profileForm.controls).forEach(key => {\n      const control = this.profileForm.get(key);\n      if (control && control.invalid && control.touched) {\n        const errorMessage = this.getErrorMessage(key);\n        if (errorMessage) {\n          errors.push(errorMessage);\n        }\n      }\n    });\n    // Check nested form groups\n    const contactInfo = this.profileForm.get('contactInfo');\n    if (contactInfo && contactInfo.invalid) {\n      Object.keys(contactInfo.controls).forEach(key => {\n        const control = contactInfo.get(key);\n        if (control && control.invalid && control.touched) {\n          const errorMessage = this.getErrorMessage(`contactInfo.${key}`);\n          if (errorMessage) {\n            errors.push(errorMessage);\n          }\n        }\n      });\n    }\n    return errors.slice(0, 3); // Limit to first 3 errors to avoid overwhelming the user\n  }\n  // Platform options for social links\n  getPlatformOptions() {\n    return [{\n      value: 'linkedin',\n      label: 'LinkedIn'\n    }, {\n      value: 'twitter',\n      label: 'Twitter'\n    }, {\n      value: 'github',\n      label: 'GitHub'\n    }, {\n      value: 'behance',\n      label: 'Behance'\n    }, {\n      value: 'dribbble',\n      label: 'Dribbble'\n    }, {\n      value: 'instagram',\n      label: 'Instagram'\n    }, {\n      value: 'facebook',\n      label: 'Facebook'\n    }, {\n      value: 'youtube',\n      label: 'YouTube'\n    }, {\n      value: 'other',\n      label: 'Other'\n    }];\n  }\n  // Phone type options\n  getPhoneTypeOptions() {\n    return [{\n      value: 'mobile',\n      label: 'Mobile'\n    }, {\n      value: 'business',\n      label: 'Business'\n    }, {\n      value: 'home',\n      label: 'Home'\n    }];\n  }\n  // Skill category options\n  getSkillCategoryOptions() {\n    return [{\n      value: 'Astrology',\n      label: 'Astrology'\n    }, {\n      value: 'Crystal Healing',\n      label: 'Crystal Healing'\n    }, {\n      value: 'Palmistry',\n      label: 'Palmistry'\n    }, {\n      value: 'Spiritual Counseling',\n      label: 'Spiritual Counseling'\n    }, {\n      value: 'Numerology',\n      label: 'Numerology'\n    }, {\n      value: 'Tarot Reading',\n      label: 'Tarot Reading'\n    }, {\n      value: 'Energy Healing',\n      label: 'Energy Healing'\n    }, {\n      value: 'Meditation',\n      label: 'Meditation'\n    }, {\n      value: 'Other',\n      label: 'Other'\n    }];\n  }\n  // Proficiency level options\n  getProficiencyLevelOptions() {\n    return [{\n      value: 'beginner',\n      label: 'Beginner'\n    }, {\n      value: 'intermediate',\n      label: 'Intermediate'\n    }, {\n      value: 'advanced',\n      label: 'Advanced'\n    }, {\n      value: 'expert',\n      label: 'Expert'\n    }];\n  }\n  // Service category options\n  getServiceCategoryOptions() {\n    return [{\n      value: 'Reading',\n      label: 'Reading'\n    }, {\n      value: 'Consultation',\n      label: 'Consultation'\n    }, {\n      value: 'Healing',\n      label: 'Healing'\n    }, {\n      value: 'Workshop',\n      label: 'Workshop'\n    }, {\n      value: 'Course',\n      label: 'Course'\n    }, {\n      value: 'Other',\n      label: 'Other'\n    }];\n  }\n  // Currency options\n  getCurrencyOptions() {\n    return [{\n      value: 'BGN',\n      label: 'BGN (Bulgarian Lev)'\n    }, {\n      value: 'EUR',\n      label: 'EUR (Euro)'\n    }, {\n      value: 'USD',\n      label: 'USD (US Dollar)'\n    }, {\n      value: 'GBP',\n      label: 'GBP (British Pound)'\n    }];\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: FormBuilder\n    }, {\n      type: ProfileService\n    }, {\n      type: AuthService\n    }, {\n      type: Router\n    }, {\n      type: MatSnackBar\n    }];\n  }\n};\nProfileEditComponent = __decorate([Component({\n  selector: 'app-profile-edit',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ProfileEditComponent);\nexport { ProfileEditComponent };", "map": {"version": 3, "mappings": ";;;AAAA,SAASA,SAAS,QAA2B,eAAe;AAC5D,SAASC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAC9E,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAASC,WAAW,QAAQ,6BAA6B;AAEzD,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,WAAW,QAAQ,qCAAqC;AAQ1D,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAY/BC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAJrB,gBAAW,GAAXJ,WAAW;IACX,mBAAc,GAAdC,cAAc;IACd,gBAAW,GAAXC,WAAW;IACX,WAAM,GAANC,MAAM;IACN,aAAQ,GAARC,QAAQ;IAflB,YAAO,GAAuB,IAAI;IAClC,cAAS,GAAG,IAAI;IAChB,aAAQ,GAAG,KAAK;IAChB,4BAAuB,GAAG,KAAK;IAC/B,0BAAqB,GAAG,KAAK;IAC7B,wBAAmB,GAAkB,IAAI;IACzC,sBAAiB,GAAkB,IAAI;IAE/B,aAAQ,GAAG,IAAIX,OAAO,EAAQ;IASpC,IAAI,CAACY,WAAW,GAAG,IAAI,CAACC,UAAU,EAAE;EACtC;EAEAC,QAAQ;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,WAAW;IACT,IAAI,CAACC,QAAQ,CAACC,IAAI,EAAE;IACpB,IAAI,CAACD,QAAQ,CAACE,QAAQ,EAAE;EAC1B;EAEQN,UAAU;IAChB,OAAO,IAAI,CAACN,WAAW,CAACa,KAAK,CAAC;MAC5B;MACAC,SAAS,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACyB,QAAQ,EAAEzB,UAAU,CAAC0B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC3B,UAAU,CAACyB,QAAQ,EAAEzB,UAAU,CAAC0B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,iBAAiB,EAAE,CAAC,EAAE,CAAC;MACvBC,QAAQ,EAAE,CAAC,EAAE,EAAE7B,UAAU,CAAC8B,SAAS,CAAC,GAAG,CAAC,CAAC;MACzCC,OAAO,EAAE,CAAC,EAAE,EAAE/B,UAAU,CAAC8B,SAAS,CAAC,IAAI,CAAC,CAAC;MAEzC;MACAE,QAAQ,EAAE,IAAI,CAACtB,WAAW,CAACa,KAAK,CAAC;QAC/BU,IAAI,EAAE,CAAC,EAAE,CAAC;QACVC,KAAK,EAAE,CAAC,EAAE,CAAC;QACXC,OAAO,EAAE,CAAC,EAAE,CAAC;QACbC,eAAe,EAAE,CAAC,EAAE;OACrB,CAAC;MAEF;MACAC,WAAW,EAAE,IAAI,CAAC3B,WAAW,CAACa,KAAK,CAAC;QAClCe,KAAK,EAAE,CAAC,EAAE,EAAEtC,UAAU,CAACsC,KAAK,CAAC;QAC7BC,aAAa,EAAE,CAAC,KAAK,CAAC;QACtBC,OAAO,EAAE,CAAC,EAAE,CAAC;QACbC,YAAY,EAAE,CAAC,EAAE,CAAC;QAClBC,YAAY,EAAE,IAAI,CAAChC,WAAW,CAACiC,KAAK,CAAC,EAAE,CAAC;QACxCC,eAAe,EAAE,IAAI,CAAClC,WAAW,CAACa,KAAK,CAAC;UACtCsB,MAAM,EAAE,CAAC,EAAE,CAAC;UACZZ,IAAI,EAAE,CAAC,EAAE,CAAC;UACVC,KAAK,EAAE,CAAC,EAAE,CAAC;UACXY,UAAU,EAAE,CAAC,EAAE,CAAC;UAChBX,OAAO,EAAE,CAAC,EAAE,CAAC;UACbY,QAAQ,EAAE,CAAC,KAAK;SACjB;OACF,CAAC;MAEF;MACAA,QAAQ,EAAE,CAAC,IAAI,CAAC;MAEhB;MACAC,WAAW,EAAE,IAAI,CAACtC,WAAW,CAACiC,KAAK,CAAC,EAAE,CAAC;MAEvC;MACAM,MAAM,EAAE,IAAI,CAACvC,WAAW,CAACiC,KAAK,CAAC,EAAE,CAAC;MAElC;MACAO,iBAAiB,EAAE,IAAI,CAACxC,WAAW,CAACa,KAAK,CAAC;QACxC4B,UAAU,EAAE,CAAC,IAAI,EAAE,CAACnD,UAAU,CAACoD,GAAG,CAAC,CAAC,CAAC,EAAEpD,UAAU,CAACqD,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9DC,WAAW,EAAE,CAAC,IAAI,EAAE,CAACtD,UAAU,CAACoD,GAAG,CAAC,CAAC,CAAC,EAAEpD,UAAU,CAACqD,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAC/DE,QAAQ,EAAE,CAAC,KAAK,EAAEvD,UAAU,CAACyB,QAAQ;OACtC,CAAC;MAEF;MACA+B,gBAAgB,EAAE,IAAI,CAAC9C,WAAW,CAACiC,KAAK,CAAC,EAAE;KAC5C,CAAC;EACJ;EAEQzB,WAAW;IACjB,IAAI,CAACP,cAAc,CAAC8C,qBAAqB,EAAE,CACxCC,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAC9BuC,SAAS,CAAC;MACTtC,IAAI,EAAGuC,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACC,YAAY,CAACD,OAAO,CAAC;QAC1B,IAAI,CAACE,SAAS,GAAG,KAAK;MACxB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACjD,QAAQ,CAACmD,IAAI,CAAC,uBAAuB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACxE,IAAI,CAACrD,MAAM,CAACsD,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACpC,IAAI,CAACL,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEQD,YAAY,CAACD,OAAoB;IACvC,IAAI,CAAC7C,WAAW,CAACqD,UAAU,CAAC;MAC1B5C,SAAS,EAAEoC,OAAO,CAACpC,SAAS;MAC5BG,QAAQ,EAAEiC,OAAO,CAACjC,QAAQ;MAC1BC,iBAAiB,EAAEgC,OAAO,CAAChC,iBAAiB,IAAI,EAAE;MAClDC,QAAQ,EAAE+B,OAAO,CAAC/B,QAAQ,IAAI,EAAE;MAChCE,OAAO,EAAE6B,OAAO,CAAC7B,OAAO,IAAI,EAAE;MAC9BC,QAAQ,EAAE;QACRC,IAAI,EAAE2B,OAAO,CAAC5B,QAAQ,EAAEC,IAAI,IAAI,EAAE;QAClCC,KAAK,EAAE0B,OAAO,CAAC5B,QAAQ,EAAEE,KAAK,IAAI,EAAE;QACpCC,OAAO,EAAEyB,OAAO,CAAC5B,QAAQ,EAAEG,OAAO,IAAI,EAAE;QACxCC,eAAe,EAAEwB,OAAO,CAAC5B,QAAQ,EAAEI,eAAe,IAAI;OACvD;MACDC,WAAW,EAAE;QACXC,KAAK,EAAEsB,OAAO,CAACvB,WAAW,CAACC,KAAK,IAAI,EAAE;QACtCC,aAAa,EAAEqB,OAAO,CAACvB,WAAW,CAACE,aAAa;QAChDC,OAAO,EAAEoB,OAAO,CAACvB,WAAW,CAACG,OAAO,IAAI,EAAE;QAC1CC,YAAY,EAAEmB,OAAO,CAACvB,WAAW,CAACI,YAAY,IAAI,EAAE;QACpDG,eAAe,EAAE;UACfC,MAAM,EAAEe,OAAO,CAACvB,WAAW,CAACO,eAAe,EAAEC,MAAM,IAAI,EAAE;UACzDZ,IAAI,EAAE2B,OAAO,CAACvB,WAAW,CAACO,eAAe,EAAEX,IAAI,IAAI,EAAE;UACrDC,KAAK,EAAE0B,OAAO,CAACvB,WAAW,CAACO,eAAe,EAAEV,KAAK,IAAI,EAAE;UACvDY,UAAU,EAAEc,OAAO,CAACvB,WAAW,CAACO,eAAe,EAAEE,UAAU,IAAI,EAAE;UACjEX,OAAO,EAAEyB,OAAO,CAACvB,WAAW,CAACO,eAAe,EAAET,OAAO,IAAI,EAAE;UAC3DY,QAAQ,EAAEa,OAAO,CAACvB,WAAW,CAACO,eAAe,EAAEG,QAAQ,IAAI;;OAE9D;MACDA,QAAQ,EAAEa,OAAO,CAACb,QAAQ;MAC1BG,iBAAiB,EAAE;QACjBC,UAAU,EAAES,OAAO,CAACV,iBAAiB,EAAEC,UAAU,IAAI,IAAI;QACzDG,WAAW,EAAEM,OAAO,CAACV,iBAAiB,EAAEI,WAAW,IAAI,IAAI;QAC3DC,QAAQ,EAAEK,OAAO,CAACV,iBAAiB,EAAEK,QAAQ,IAAI;;KAEpD,CAAC;IAEF;IACA,IAAI,CAACc,eAAe,CAACT,OAAO,CAACvB,WAAW,CAACK,YAAY,IAAI,EAAE,CAAC;IAE5D;IACA,IAAI,CAAC4B,cAAc,CAACV,OAAO,CAACZ,WAAW,IAAI,EAAE,CAAC;IAE9C;IACA,IAAI,CAACuB,SAAS,CAACX,OAAO,CAACX,MAAM,IAAI,EAAE,CAAC;IAEpC;IACA,IAAI,CAACuB,mBAAmB,CAACZ,OAAO,CAACJ,gBAAgB,IAAI,EAAE,CAAC;EAC1D;EAEA;EACA,IAAId,YAAY;IACd,OAAO,IAAI,CAAC3B,WAAW,CAAC0D,GAAG,CAAC,0BAA0B,CAAc;EACtE;EAEQJ,eAAe,CAACK,MAAa;IACnC,MAAMC,UAAU,GAAG,IAAI,CAACjC,YAAY;IACpCiC,UAAU,CAACC,KAAK,EAAE;IAElBF,MAAM,CAACG,OAAO,CAACC,KAAK,IAAG;MACrBH,UAAU,CAACI,IAAI,CAAC,IAAI,CAACrE,WAAW,CAACa,KAAK,CAAC;QACrCyD,EAAE,EAAE,CAACF,KAAK,CAACE,EAAE,CAAC;QACdC,MAAM,EAAE,CAACH,KAAK,CAACG,MAAM,EAAEjF,UAAU,CAACyB,QAAQ,CAAC;QAC3CyD,IAAI,EAAE,CAACJ,KAAK,CAACI,IAAI,EAAElF,UAAU,CAACyB,QAAQ,CAAC;QACvCsB,QAAQ,EAAE,CAAC+B,KAAK,CAAC/B,QAAQ,CAAC;QAC1BoC,SAAS,EAAE,CAACL,KAAK,CAACK,SAAS;OAC5B,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;EAEAC,cAAc;IACZ,MAAMC,UAAU,GAAG,IAAI,CAAC3E,WAAW,CAACa,KAAK,CAAC;MACxCyD,EAAE,EAAE,CAAC,IAAI,CAAC;MACVC,MAAM,EAAE,CAAC,EAAE,EAAEjF,UAAU,CAACyB,QAAQ,CAAC;MACjCyD,IAAI,EAAE,CAAC,QAAQ,EAAElF,UAAU,CAACyB,QAAQ,CAAC;MACrCsB,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBoC,SAAS,EAAE,CAAC,KAAK;KAClB,CAAC;IAEF,IAAI,CAACzC,YAAY,CAACqC,IAAI,CAACM,UAAU,CAAC;EACpC;EAEAC,iBAAiB,CAACC,KAAa;IAC7B,IAAI,CAAC7C,YAAY,CAAC8C,QAAQ,CAACD,KAAK,CAAC;EACnC;EAEA;EACA,IAAIvC,WAAW;IACb,OAAO,IAAI,CAACjC,WAAW,CAAC0D,GAAG,CAAC,aAAa,CAAc;EACzD;EAEQH,cAAc,CAACmB,KAAY;IACjC,MAAMC,UAAU,GAAG,IAAI,CAAC1C,WAAW;IACnC0C,UAAU,CAACd,KAAK,EAAE;IAElBa,KAAK,CAACZ,OAAO,CAACc,IAAI,IAAG;MACnBD,UAAU,CAACX,IAAI,CAAC,IAAI,CAACrE,WAAW,CAACa,KAAK,CAAC;QACrCyD,EAAE,EAAE,CAACW,IAAI,CAACX,EAAE,CAAC;QACbY,QAAQ,EAAE,CAACD,IAAI,CAACC,QAAQ,EAAE5F,UAAU,CAACyB,QAAQ,CAAC;QAC9CoE,GAAG,EAAE,CAACF,IAAI,CAACE,GAAG,EAAE,CAAC7F,UAAU,CAACyB,QAAQ,EAAEzB,UAAU,CAAC8F,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;QACzEC,WAAW,EAAE,CAACJ,IAAI,CAACI,WAAW,CAAC;QAC/BhD,QAAQ,EAAE,CAAC4C,IAAI,CAAC5C,QAAQ;OACzB,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;EAEAiD,aAAa;IACX,MAAMC,SAAS,GAAG,IAAI,CAACvF,WAAW,CAACa,KAAK,CAAC;MACvCyD,EAAE,EAAE,CAAC,IAAI,CAAC;MACVY,QAAQ,EAAE,CAAC,UAAU,EAAE5F,UAAU,CAACyB,QAAQ,CAAC;MAC3CoE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC7F,UAAU,CAACyB,QAAQ,EAAEzB,UAAU,CAAC8F,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MACnEC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBhD,QAAQ,EAAE,CAAC,IAAI;KAChB,CAAC;IAEF,IAAI,CAACC,WAAW,CAAC+B,IAAI,CAACkB,SAAS,CAAC;EAClC;EAEAC,gBAAgB,CAACX,KAAa;IAC5B,IAAI,CAACvC,WAAW,CAACwC,QAAQ,CAACD,KAAK,CAAC;EAClC;EAEA;EACA,IAAItC,MAAM;IACR,OAAO,IAAI,CAAClC,WAAW,CAAC0D,GAAG,CAAC,QAAQ,CAAc;EACpD;EAEQF,SAAS,CAACtB,MAAsB;IACtC,MAAMkD,UAAU,GAAG,IAAI,CAAClD,MAAM;IAC9BkD,UAAU,CAACvB,KAAK,EAAE;IAElB3B,MAAM,CAAC4B,OAAO,CAACuB,KAAK,IAAG;MACrBD,UAAU,CAACpB,IAAI,CAAC,IAAI,CAACrE,WAAW,CAACa,KAAK,CAAC;QACrCyD,EAAE,EAAE,CAACoB,KAAK,CAACpB,EAAE,CAAC;QACdqB,IAAI,EAAE,CAACD,KAAK,CAACC,IAAI,EAAErG,UAAU,CAACyB,QAAQ,CAAC;QACvC6E,QAAQ,EAAE,CAACF,KAAK,CAACE,QAAQ,IAAI,EAAE,CAAC;QAChCC,gBAAgB,EAAE,CAACH,KAAK,CAACG,gBAAgB,IAAI,cAAc,EAAEvG,UAAU,CAACyB,QAAQ;OACjF,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;EAEA+E,QAAQ;IACN,MAAMC,UAAU,GAAG,IAAI,CAAC/F,WAAW,CAACa,KAAK,CAAC;MACxCyD,EAAE,EAAE,CAAC,IAAI,CAAC;MACVqB,IAAI,EAAE,CAAC,EAAE,EAAErG,UAAU,CAACyB,QAAQ,CAAC;MAC/B6E,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,gBAAgB,EAAE,CAAC,cAAc,EAAEvG,UAAU,CAACyB,QAAQ;KACvD,CAAC;IAEF,IAAI,CAACwB,MAAM,CAAC8B,IAAI,CAAC0B,UAAU,CAAC;EAC9B;EAEAC,WAAW,CAACnB,KAAa;IACvB,IAAI,CAACtC,MAAM,CAACuC,QAAQ,CAACD,KAAK,CAAC;EAC7B;EAEA;EACA,IAAI/B,gBAAgB;IAClB,OAAO,IAAI,CAACzC,WAAW,CAAC0D,GAAG,CAAC,kBAAkB,CAAc;EAC9D;EAEQD,mBAAmB,CAACmC,QAA2B;IACrD,MAAMC,YAAY,GAAG,IAAI,CAACpD,gBAAgB;IAC1CoD,YAAY,CAAChC,KAAK,EAAE;IAEpB+B,QAAQ,CAAC9B,OAAO,CAACgC,OAAO,IAAG;MACzBD,YAAY,CAAC7B,IAAI,CAAC,IAAI,CAACrE,WAAW,CAACa,KAAK,CAAC;QACvCyD,EAAE,EAAE,CAAC6B,OAAO,CAAC7B,EAAE,CAAC;QAChBqB,IAAI,EAAE,CAACQ,OAAO,CAACR,IAAI,EAAErG,UAAU,CAACyB,QAAQ,CAAC;QACzCqF,WAAW,EAAE,CAACD,OAAO,CAACC,WAAW,EAAE9G,UAAU,CAACyB,QAAQ,CAAC;QACvDsF,KAAK,EAAE,CAACF,OAAO,CAACE,KAAK,EAAE,CAAC/G,UAAU,CAACyB,QAAQ,EAAEzB,UAAU,CAACoD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChEG,QAAQ,EAAE,CAACsD,OAAO,CAACtD,QAAQ,IAAI,KAAK,EAAEvD,UAAU,CAACyB,QAAQ,CAAC;QAC1DyC,QAAQ,EAAE,CAAC2C,OAAO,CAAC3C,QAAQ,CAAC;QAC5BoC,QAAQ,EAAE,CAACO,OAAO,CAACP,QAAQ,IAAI,EAAE,CAAC;QAClCU,QAAQ,EAAE,CAACH,OAAO,CAACG,QAAQ,KAAK,KAAK,CAAC,CAAC;OACxC,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;;EAEAC,kBAAkB;IAChB,MAAMC,YAAY,GAAG,IAAI,CAACxG,WAAW,CAACa,KAAK,CAAC;MAC1CyD,EAAE,EAAE,CAAC,IAAI,CAAC;MACVqB,IAAI,EAAE,CAAC,EAAE,EAAErG,UAAU,CAACyB,QAAQ,CAAC;MAC/BqF,WAAW,EAAE,CAAC,EAAE,EAAE9G,UAAU,CAACyB,QAAQ,CAAC;MACtCsF,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC/G,UAAU,CAACyB,QAAQ,EAAEzB,UAAU,CAACoD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACpDG,QAAQ,EAAE,CAAC,KAAK,EAAEvD,UAAU,CAACyB,QAAQ,CAAC;MACtCyC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdoC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdU,QAAQ,EAAE,CAAC,IAAI;KAChB,CAAC;IAEF,IAAI,CAACxD,gBAAgB,CAACuB,IAAI,CAACmC,YAAY,CAAC;EAC1C;EAEAC,qBAAqB,CAAC5B,KAAa;IACjC,IAAI,CAAC/B,gBAAgB,CAACgC,QAAQ,CAACD,KAAK,CAAC;EACvC;EAEA;EACA6B,QAAQ;IACN,IAAI,IAAI,CAACC,YAAY,EAAE,EAAE;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;MAEpB,MAAMC,SAAS,GAAG,IAAI,CAACxG,WAAW,CAACyG,KAAK;MACxC,MAAMC,aAAa,GAAyB;QAC1CjG,SAAS,EAAE+F,SAAS,CAAC/F,SAAS;QAC9BG,QAAQ,EAAE4F,SAAS,CAAC5F,QAAQ;QAC5BC,iBAAiB,EAAE2F,SAAS,CAAC3F,iBAAiB;QAC9CC,QAAQ,EAAE0F,SAAS,CAAC1F,QAAQ;QAC5BG,QAAQ,EAAEuF,SAAS,CAACvF,QAAQ;QAC5BK,WAAW,EAAEkF,SAAS,CAAClF,WAAW;QAClCN,OAAO,EAAEwF,SAAS,CAACxF,OAAO;QAC1BgB,QAAQ,EAAEwE,SAAS,CAACxE,QAAQ;QAC5BE,MAAM,EAAEsE,SAAS,CAACtE,MAAM,IAAI,EAAE;QAC9BC,iBAAiB,EAAEqE,SAAS,CAACrE,iBAAiB;QAC9CM,gBAAgB,EAAE+D,SAAS,CAAC/D,gBAAgB,IAAI;OACjD;MAED,IAAI,CAAC7C,cAAc,CAAC+G,aAAa,CAACD,aAAa,CAAC,CAC7C/D,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAC9BuC,SAAS,CAAC;QACTtC,IAAI,EAAGsG,cAAc,IAAI;UACvB,IAAI,CAACL,QAAQ,GAAG,KAAK;UACrB,IAAI,CAACxG,QAAQ,CAACmD,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UAChF,IAAI,CAACrD,MAAM,CAACsD,QAAQ,CAAC,CAAC,UAAU,EAAEwD,cAAc,CAACC,IAAI,CAAC,CAAC;QACzD,CAAC;QACD7D,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACuD,QAAQ,GAAG,KAAK;UACrBtD,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,IAAI,CAACjD,QAAQ,CAACmD,IAAI,CAAC,2CAA2C,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QAC9F;OACD,CAAC;;IAEN;EACF;;EAEA2D,QAAQ;IACN,IAAI,IAAI,CAACjE,OAAO,EAAE;MAChB,IAAI,CAAC/C,MAAM,CAACsD,QAAQ,CAAC,CAAC,UAAU,EAAE,IAAI,CAACP,OAAO,CAACgE,IAAI,CAAC,CAAC;KACtD,MAAM;MACL,IAAI,CAAC/G,MAAM,CAACsD,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEA;EACA2D,sBAAsB,CAACC,KAAU;IAC/B,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,IAAI,CAACG,iBAAiB,CAACH,IAAI,EAAE,SAAS,CAAC,EAAE;QAC3C,IAAI,CAACI,kBAAkB,CAACJ,IAAI,EAAE,SAAS,CAAC;QACxC,IAAI,CAACK,kBAAkB,CAACL,IAAI,CAAC;;;IAGjC;IACAD,KAAK,CAACE,MAAM,CAACT,KAAK,GAAG,EAAE;EACzB;EAEAc,oBAAoB,CAACP,KAAU;IAC7B,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,IAAI,CAACG,iBAAiB,CAACH,IAAI,EAAE,OAAO,CAAC,EAAE;QACzC,IAAI,CAACI,kBAAkB,CAACJ,IAAI,EAAE,OAAO,CAAC;QACtC,IAAI,CAACO,gBAAgB,CAACP,IAAI,CAAC;;;IAG/B;IACAD,KAAK,CAACE,MAAM,CAACT,KAAK,GAAG,EAAE;EACzB;EAEQW,iBAAiB,CAACH,IAAU,EAAE9C,IAAyB;IAC7D;IACA,MAAMsD,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IACxF,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACT,IAAI,CAAC9C,IAAI,CAAC,EAAE;MACrC,IAAI,CAACpE,QAAQ,CAACmD,IAAI,CAAC,4DAA4D,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC7G,OAAO,KAAK;;IAGd;IACA,MAAMwE,OAAO,GAAGxD,IAAI,KAAK,SAAS,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;IACvE,IAAI8C,IAAI,CAACW,IAAI,GAAGD,OAAO,EAAE;MACvB,MAAME,SAAS,GAAGF,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC;MACzC,IAAI,CAAC5H,QAAQ,CAACmD,IAAI,CAAC,+BAA+B2E,SAAS,IAAI,EAAE,OAAO,EAAE;QAAE1E,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC7F,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEQkE,kBAAkB,CAACJ,IAAU,EAAE9C,IAAyB;IAC9D,MAAM2D,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;MACzB,IAAI9D,IAAI,KAAK,SAAS,EAAE;QACtB,IAAI,CAAC+D,mBAAmB,GAAGD,CAAC,CAACf,MAAM,CAACiB,MAAM;OAC3C,MAAM;QACL,IAAI,CAACC,iBAAiB,GAAGH,CAAC,CAACf,MAAM,CAACiB,MAAM;;IAE5C,CAAC;IACDL,MAAM,CAACO,aAAa,CAACpB,IAAI,CAAC;EAC5B;EAEQK,kBAAkB,CAACL,IAAU;IACnC,IAAI,CAACqB,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAAC1I,cAAc,CAAC0H,kBAAkB,CAACL,IAAI,CAAC,CACzCtE,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAC9BuC,SAAS,CAAC;MACTtC,IAAI,EAAGiI,QAAQ,IAAI;QACjB,IAAI,CAACD,uBAAuB,GAAG,KAAK;QACpC,IAAI,IAAI,CAACzF,OAAO,EAAE;UAChB,IAAI,CAACA,OAAO,CAAC2F,eAAe,GAAGD,QAAQ,CAACzD,GAAG;;QAE7C,IAAI,CAACoD,mBAAmB,GAAG,IAAI,CAAC,CAAC;QACjC,IAAI,CAACnI,QAAQ,CAACmD,IAAI,CAAC,qCAAqC,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACxF,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACsF,uBAAuB,GAAG,KAAK;QACpC,IAAI,CAACJ,mBAAmB,GAAG,IAAI,CAAC,CAAC;QACjCjF,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAMyF,YAAY,GAAGzF,KAAK,CAACA,KAAK,EAAE0F,OAAO,IAAI,kDAAkD;QAC/F,IAAI,CAAC3I,QAAQ,CAACmD,IAAI,CAACuF,YAAY,EAAE,OAAO,EAAE;UAAEtF,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC/D;KACD,CAAC;EACN;EAEQqE,gBAAgB,CAACP,IAAU;IACjC,IAAI,CAAC0B,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAAC/I,cAAc,CAAC4H,gBAAgB,CAACP,IAAI,CAAC,CACvCtE,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAC9BuC,SAAS,CAAC;MACTtC,IAAI,EAAGiI,QAAQ,IAAI;QACjB,IAAI,CAACI,qBAAqB,GAAG,KAAK;QAClC,IAAI,IAAI,CAAC9F,OAAO,EAAE;UAChB,IAAI,CAACA,OAAO,CAAC+F,aAAa,GAAGL,QAAQ,CAACzD,GAAG;;QAE3C,IAAI,CAACsD,iBAAiB,GAAG,IAAI,CAAC,CAAC;QAC/B,IAAI,CAACrI,QAAQ,CAACmD,IAAI,CAAC,mCAAmC,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACtF,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC2F,qBAAqB,GAAG,KAAK;QAClC,IAAI,CAACP,iBAAiB,GAAG,IAAI,CAAC,CAAC;QAC/BnF,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,MAAMyF,YAAY,GAAGzF,KAAK,CAACA,KAAK,EAAE0F,OAAO,IAAI,gDAAgD;QAC7F,IAAI,CAAC3I,QAAQ,CAACmD,IAAI,CAACuF,YAAY,EAAE,OAAO,EAAE;UAAEtF,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC/D;KACD,CAAC;EACN;EAEA;EACQ0F,oBAAoB;IAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/I,WAAW,CAACgJ,QAAQ,CAAC,CAAClF,OAAO,CAACmF,GAAG,IAAG;MACnD,MAAMC,OAAO,GAAG,IAAI,CAAClJ,WAAW,CAAC0D,GAAG,CAACuF,GAAG,CAAC;MACzCC,OAAO,EAAEC,aAAa,EAAE;MAExB,IAAID,OAAO,YAAYhK,SAAS,EAAE;QAChCgK,OAAO,CAACF,QAAQ,CAAClF,OAAO,CAACsF,YAAY,IAAG;UACtC,IAAIA,YAAY,YAAYpK,SAAS,EAAE;YACrC8J,MAAM,CAACC,IAAI,CAACK,YAAY,CAACJ,QAAQ,CAAC,CAAClF,OAAO,CAACuF,QAAQ,IAAG;cACpDD,YAAY,CAAC1F,GAAG,CAAC2F,QAAQ,CAAC,EAAEF,aAAa,EAAE;YAC7C,CAAC,CAAC;;QAEN,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAG,eAAe,CAACC,SAAiB;IAC/B,MAAML,OAAO,GAAG,IAAI,CAAClJ,WAAW,CAAC0D,GAAG,CAAC6F,SAAS,CAAC;IAC/C,IAAI,CAACL,OAAO,IAAI,CAACA,OAAO,CAACM,MAAM,EAAE,OAAO,EAAE;IAE1C,MAAMC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAACH,SAAS,CAAC;IAE5D,IAAIL,OAAO,CAACS,QAAQ,CAAC,UAAU,CAAC,EAAE;MAChC,OAAO,GAAGF,gBAAgB,cAAc;;IAE1C,IAAIP,OAAO,CAACS,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7B,OAAO,oCAAoC;;IAE7C,IAAIT,OAAO,CAACS,QAAQ,CAAC,WAAW,CAAC,EAAE;MACjC,MAAMC,cAAc,GAAGV,OAAO,CAACM,MAAM,CAAC,WAAW,CAAC,CAACI,cAAc;MACjE,OAAO,GAAGH,gBAAgB,qBAAqBG,cAAc,aAAa;;IAE5E,IAAIV,OAAO,CAACS,QAAQ,CAAC,WAAW,CAAC,EAAE;MACjC,MAAM5I,SAAS,GAAGmI,OAAO,CAACM,MAAM,CAAC,WAAW,CAAC,CAACI,cAAc;MAC5D,OAAO,GAAGH,gBAAgB,yBAAyB1I,SAAS,aAAa;;IAE3E,IAAImI,OAAO,CAACS,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC/B,OAAO,0BAA0B;;IAEnC,IAAIT,OAAO,CAACS,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC3B,MAAME,QAAQ,GAAGX,OAAO,CAACM,MAAM,CAAC,KAAK,CAAC,CAACnH,GAAG;MAC1C,OAAO,GAAGoH,gBAAgB,qBAAqBI,QAAQ,EAAE;;IAE3D,IAAIX,OAAO,CAACS,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC3B,MAAMG,QAAQ,GAAGZ,OAAO,CAACM,MAAM,CAAC,KAAK,CAAC,CAAClH,GAAG;MAC1C,OAAO,GAAGmH,gBAAgB,kBAAkBK,QAAQ,EAAE;;IAExD,OAAO,EAAE;EACX;EAEQJ,mBAAmB,CAACH,SAAiB;IAC3C,MAAMQ,UAAU,GAA8B;MAC5C,WAAW,EAAE,YAAY;MACzB,UAAU,EAAE,WAAW;MACvB,mBAAmB,EAAE,oBAAoB;MACzC,UAAU,EAAE,UAAU;MACtB,SAAS,EAAE,SAAS;MACpB,mBAAmB,EAAE,OAAO;MAC5B,qBAAqB,EAAE,SAAS;MAChC,0BAA0B,EAAE,eAAe;MAC3C,eAAe,EAAE,MAAM;MACvB,gBAAgB,EAAE,OAAO;MACzB,kBAAkB,EAAE,SAAS;MAC7B,0BAA0B,EAAE,kBAAkB;MAC9C,8BAA8B,EAAE,aAAa;MAC7C,+BAA+B,EAAE,cAAc;MAC/C,4BAA4B,EAAE;KAC/B;IACD,OAAOA,UAAU,CAACR,SAAS,CAAC,IAAIA,SAAS;EAC3C;EAEA;EACAjD,YAAY;IACV,IAAI,IAAI,CAACtG,WAAW,CAACgK,OAAO,EAAE;MAC5B,IAAI,CAACnB,oBAAoB,EAAE;MAE3B;MACA,MAAMoB,iBAAiB,GAAG,IAAI,CAACC,qBAAqB,EAAE;MACtD,IAAID,iBAAiB,EAAE;QACrBA,iBAAiB,CAACE,KAAK,EAAE;;MAG3B;MACA,MAAMX,MAAM,GAAG,IAAI,CAACY,aAAa,EAAE;MACnC,IAAIZ,MAAM,CAACa,MAAM,GAAG,CAAC,EAAE;QACrB,IAAI,CAACtK,QAAQ,CAACmD,IAAI,CAAC,oCAAoCsG,MAAM,CAACc,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE;UAAEnH,QAAQ,EAAE;QAAI,CAAE,CAAC;;MAG1G,OAAO,KAAK;;IAEd,OAAO,IAAI;EACb;EAEQ+G,qBAAqB;IAC3B,MAAMK,aAAa,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,8GAA8G,CAAC;IAC/J,OAAOF,aAAa,CAACF,MAAM,GAAG,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAgB,GAAG,IAAI;EAC1E;EAEQH,aAAa;IACnB,MAAMZ,MAAM,GAAa,EAAE;IAE3B;IACAV,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/I,WAAW,CAACgJ,QAAQ,CAAC,CAAClF,OAAO,CAACmF,GAAG,IAAG;MACnD,MAAMC,OAAO,GAAG,IAAI,CAAClJ,WAAW,CAAC0D,GAAG,CAACuF,GAAG,CAAC;MACzC,IAAIC,OAAO,IAAIA,OAAO,CAACc,OAAO,IAAId,OAAO,CAACwB,OAAO,EAAE;QACjD,MAAMjC,YAAY,GAAG,IAAI,CAACa,eAAe,CAACL,GAAG,CAAC;QAC9C,IAAIR,YAAY,EAAE;UAChBe,MAAM,CAACxF,IAAI,CAACyE,YAAY,CAAC;;;IAG/B,CAAC,CAAC;IAEF;IACA,MAAMnH,WAAW,GAAG,IAAI,CAACtB,WAAW,CAAC0D,GAAG,CAAC,aAAa,CAAc;IACpE,IAAIpC,WAAW,IAAIA,WAAW,CAAC0I,OAAO,EAAE;MACtClB,MAAM,CAACC,IAAI,CAACzH,WAAW,CAAC0H,QAAQ,CAAC,CAAClF,OAAO,CAACmF,GAAG,IAAG;QAC9C,MAAMC,OAAO,GAAG5H,WAAW,CAACoC,GAAG,CAACuF,GAAG,CAAC;QACpC,IAAIC,OAAO,IAAIA,OAAO,CAACc,OAAO,IAAId,OAAO,CAACwB,OAAO,EAAE;UACjD,MAAMjC,YAAY,GAAG,IAAI,CAACa,eAAe,CAAC,eAAeL,GAAG,EAAE,CAAC;UAC/D,IAAIR,YAAY,EAAE;YAChBe,MAAM,CAACxF,IAAI,CAACyE,YAAY,CAAC;;;MAG/B,CAAC,CAAC;;IAGJ,OAAOe,MAAM,CAACmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC7B;EAEA;EACAC,kBAAkB;IAChB,OAAO,CACL;MAAEnE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,SAAS;MAAEoE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEpE,KAAK,EAAE,QAAQ;MAAEoE,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEpE,KAAK,EAAE,SAAS;MAAEoE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEpE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,WAAW;MAAEoE,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEpE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,SAAS;MAAEoE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEpE,KAAK,EAAE,OAAO;MAAEoE,KAAK,EAAE;IAAO,CAAE,CACnC;EACH;EAEA;EACAC,mBAAmB;IACjB,OAAO,CACL;MAAErE,KAAK,EAAE,QAAQ;MAAEoE,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEpE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,MAAM;MAAEoE,KAAK,EAAE;IAAM,CAAE,CACjC;EACH;EAEA;EACAE,uBAAuB;IACrB,OAAO,CACL;MAAEtE,KAAK,EAAE,WAAW;MAAEoE,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEpE,KAAK,EAAE,iBAAiB;MAAEoE,KAAK,EAAE;IAAiB,CAAE,EACtD;MAAEpE,KAAK,EAAE,WAAW;MAAEoE,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEpE,KAAK,EAAE,sBAAsB;MAAEoE,KAAK,EAAE;IAAsB,CAAE,EAChE;MAAEpE,KAAK,EAAE,YAAY;MAAEoE,KAAK,EAAE;IAAY,CAAE,EAC5C;MAAEpE,KAAK,EAAE,eAAe;MAAEoE,KAAK,EAAE;IAAe,CAAE,EAClD;MAAEpE,KAAK,EAAE,gBAAgB;MAAEoE,KAAK,EAAE;IAAgB,CAAE,EACpD;MAAEpE,KAAK,EAAE,YAAY;MAAEoE,KAAK,EAAE;IAAY,CAAE,EAC5C;MAAEpE,KAAK,EAAE,OAAO;MAAEoE,KAAK,EAAE;IAAO,CAAE,CACnC;EACH;EAEA;EACAG,0BAA0B;IACxB,OAAO,CACL;MAAEvE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,cAAc;MAAEoE,KAAK,EAAE;IAAc,CAAE,EAChD;MAAEpE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,QAAQ;MAAEoE,KAAK,EAAE;IAAQ,CAAE,CACrC;EACH;EAEA;EACAI,yBAAyB;IACvB,OAAO,CACL;MAAExE,KAAK,EAAE,SAAS;MAAEoE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEpE,KAAK,EAAE,cAAc;MAAEoE,KAAK,EAAE;IAAc,CAAE,EAChD;MAAEpE,KAAK,EAAE,SAAS;MAAEoE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEpE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,QAAQ;MAAEoE,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEpE,KAAK,EAAE,OAAO;MAAEoE,KAAK,EAAE;IAAO,CAAE,CACnC;EACH;EAEA;EACAK,kBAAkB;IAChB,OAAO,CACL;MAAEzE,KAAK,EAAE,KAAK;MAAEoE,KAAK,EAAE;IAAqB,CAAE,EAC9C;MAAEpE,KAAK,EAAE,KAAK;MAAEoE,KAAK,EAAE;IAAY,CAAE,EACrC;MAAEpE,KAAK,EAAE,KAAK;MAAEoE,KAAK,EAAE;IAAiB,CAAE,EAC1C;MAAEpE,KAAK,EAAE,KAAK;MAAEoE,KAAK,EAAE;IAAqB,CAAE,CAC/C;EACH;;;;;;;;;;;;;;;AAxoBWpL,oBAAoB,eALhCX,SAAS,CAAC;EACTqM,QAAQ,EAAE,kBAAkB;EAC5BC,8BAA4C;;CAE7C,CAAC,GACW3L,oBAAoB,CAyoBhC;SAzoBYA,oBAAoB", "names": ["Component", "FormBuilder", "FormGroup", "Validators", "FormArray", "Router", "Subject", "takeUntil", "MatSnackBar", "ProfileService", "AuthService", "ProfileEditComponent", "constructor", "formBuilder", "profileService", "authService", "router", "snackBar", "profileForm", "createForm", "ngOnInit", "loadProfile", "ngOnDestroy", "destroy$", "next", "complete", "group", "firstName", "required", "<PERSON><PERSON><PERSON><PERSON>", "lastName", "professional<PERSON>itle", "headline", "max<PERSON><PERSON><PERSON>", "summary", "location", "city", "state", "country", "displayLocation", "contactInfo", "email", "isEmailPublic", "website", "portfolioUrl", "phoneNumbers", "array", "businessAddress", "street", "postalCode", "isPublic", "socialLinks", "skills", "consultationRates", "hourlyRate", "min", "max", "sessionRate", "currency", "serviceOfferings", "getCurrentUserProfile", "pipe", "subscribe", "profile", "populateForm", "isLoading", "error", "console", "open", "duration", "navigate", "patchValue", "setPhoneNumbers", "setSocialLinks", "setSkills", "setServiceOfferings", "get", "phones", "phoneArray", "clear", "for<PERSON>ach", "phone", "push", "id", "number", "type", "isPrimary", "addPhoneNumber", "phoneGroup", "removePhoneNumber", "index", "removeAt", "links", "linksArray", "link", "platform", "url", "pattern", "displayName", "addSocialLink", "linkGroup", "removeSocialLink", "skillArray", "skill", "name", "category", "proficiencyLevel", "addSkill", "skillGroup", "removeSkill", "services", "serviceArray", "service", "description", "price", "isActive", "addServiceOffering", "serviceGroup", "removeServiceOffering", "onSubmit", "validateForm", "isSaving", "formValue", "value", "updateRequest", "updateProfile", "updatedProfile", "slug", "onCancel", "onProfilePhotoSelected", "event", "file", "target", "files", "validateImageFile", "createImagePreview", "uploadProfilePhoto", "onCoverPhotoSelected", "uploadCoverPhoto", "allowedTypes", "includes", "maxSize", "size", "maxSizeMB", "reader", "FileReader", "onload", "e", "profilePhotoPreview", "result", "coverPhotoPreview", "readAsDataURL", "isUploadingProfilePhoto", "response", "profilePhotoUrl", "errorMessage", "message", "isUploadingCoverPhoto", "coverPhotoUrl", "markFormGroupTouched", "Object", "keys", "controls", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "arrayControl", "<PERSON><PERSON><PERSON>", "getErrorMessage", "fieldName", "errors", "fieldDisplayName", "getFieldDisplayName", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "minValue", "maxValue", "fieldNames", "invalid", "firstInvalidField", "findFirstInvalidField", "focus", "getFormErrors", "length", "join", "invalidFields", "document", "querySelectorAll", "touched", "slice", "getPlatformOptions", "label", "getPhoneTypeOptions", "getSkillCategoryOptions", "getProficiencyLevelOptions", "getServiceCategoryOptions", "getCurrencyOptions", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\profile-edit\\profile-edit.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\nimport { ProfileService } from '../../services/profile.service';\r\nimport { AuthService } from '../../../auth/services/auth.service';\r\nimport { UserProfile, ProfileUpdateRequest, ProfileSkill, ConsultationRates, ServiceOffering } from '../../models/profile.models';\r\n\r\n@Component({\r\n  selector: 'app-profile-edit',\r\n  templateUrl: './profile-edit.component.html',\r\n  styleUrls: ['./profile-edit.component.css']\r\n})\r\nexport class ProfileEditComponent implements OnInit, OnDestroy {\r\n  profileForm: FormGroup;\r\n  profile: UserProfile | null = null;\r\n  isLoading = true;\r\n  isSaving = false;\r\n  isUploadingProfilePhoto = false;\r\n  isUploadingCoverPhoto = false;\r\n  profilePhotoPreview: string | null = null;\r\n  coverPhotoPreview: string | null = null;\r\n\r\n  private destroy$ = new Subject<void>();\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private profileService: ProfileService,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    this.profileForm = this.createForm();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadProfile();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  private createForm(): FormGroup {\r\n    return this.formBuilder.group({\r\n      // Basic Information\r\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\r\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\r\n      professionalTitle: [''],\r\n      headline: ['', Validators.maxLength(220)],\r\n      summary: ['', Validators.maxLength(2000)],\r\n      \r\n      // Location\r\n      location: this.formBuilder.group({\r\n        city: [''],\r\n        state: [''],\r\n        country: [''],\r\n        displayLocation: ['']\r\n      }),\r\n      \r\n      // Contact Information\r\n      contactInfo: this.formBuilder.group({\r\n        email: ['', Validators.email],\r\n        isEmailPublic: [false],\r\n        website: [''],\r\n        portfolioUrl: [''],\r\n        phoneNumbers: this.formBuilder.array([]),\r\n        businessAddress: this.formBuilder.group({\r\n          street: [''],\r\n          city: [''],\r\n          state: [''],\r\n          postalCode: [''],\r\n          country: [''],\r\n          isPublic: [false]\r\n        })\r\n      }),\r\n      \r\n      // Privacy Settings\r\n      isPublic: [true],\r\n\r\n      // Social Links\r\n      socialLinks: this.formBuilder.array([]),\r\n\r\n      // Skills\r\n      skills: this.formBuilder.array([]),\r\n\r\n      // Consultation Rates\r\n      consultationRates: this.formBuilder.group({\r\n        hourlyRate: [null, [Validators.min(0), Validators.max(10000)]],\r\n        sessionRate: [null, [Validators.min(0), Validators.max(10000)]],\r\n        currency: ['BGN', Validators.required]\r\n      }),\r\n\r\n      // Service Offerings\r\n      serviceOfferings: this.formBuilder.array([])\r\n    });\r\n  }\r\n\r\n  private loadProfile(): void {\r\n    this.profileService.getCurrentUserProfile()\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (profile) => {\r\n          this.profile = profile;\r\n          this.populateForm(profile);\r\n          this.isLoading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading profile:', error);\r\n          this.snackBar.open('Error loading profile', 'Close', { duration: 5000 });\r\n          this.router.navigate(['/dashboard']);\r\n          this.isLoading = false;\r\n        }\r\n      });\r\n  }\r\n\r\n  private populateForm(profile: UserProfile): void {\r\n    this.profileForm.patchValue({\r\n      firstName: profile.firstName,\r\n      lastName: profile.lastName,\r\n      professionalTitle: profile.professionalTitle || '',\r\n      headline: profile.headline || '',\r\n      summary: profile.summary || '',\r\n      location: {\r\n        city: profile.location?.city || '',\r\n        state: profile.location?.state || '',\r\n        country: profile.location?.country || '',\r\n        displayLocation: profile.location?.displayLocation || ''\r\n      },\r\n      contactInfo: {\r\n        email: profile.contactInfo.email || '',\r\n        isEmailPublic: profile.contactInfo.isEmailPublic,\r\n        website: profile.contactInfo.website || '',\r\n        portfolioUrl: profile.contactInfo.portfolioUrl || '',\r\n        businessAddress: {\r\n          street: profile.contactInfo.businessAddress?.street || '',\r\n          city: profile.contactInfo.businessAddress?.city || '',\r\n          state: profile.contactInfo.businessAddress?.state || '',\r\n          postalCode: profile.contactInfo.businessAddress?.postalCode || '',\r\n          country: profile.contactInfo.businessAddress?.country || '',\r\n          isPublic: profile.contactInfo.businessAddress?.isPublic || false\r\n        }\r\n      },\r\n      isPublic: profile.isPublic,\r\n      consultationRates: {\r\n        hourlyRate: profile.consultationRates?.hourlyRate || null,\r\n        sessionRate: profile.consultationRates?.sessionRate || null,\r\n        currency: profile.consultationRates?.currency || 'BGN'\r\n      }\r\n    });\r\n\r\n    // Populate phone numbers\r\n    this.setPhoneNumbers(profile.contactInfo.phoneNumbers || []);\r\n\r\n    // Populate social links\r\n    this.setSocialLinks(profile.socialLinks || []);\r\n\r\n    // Populate skills\r\n    this.setSkills(profile.skills || []);\r\n\r\n    // Populate service offerings\r\n    this.setServiceOfferings(profile.serviceOfferings || []);\r\n  }\r\n\r\n  // Phone Numbers Management\r\n  get phoneNumbers(): FormArray {\r\n    return this.profileForm.get('contactInfo.phoneNumbers') as FormArray;\r\n  }\r\n\r\n  private setPhoneNumbers(phones: any[]): void {\r\n    const phoneArray = this.phoneNumbers;\r\n    phoneArray.clear();\r\n    \r\n    phones.forEach(phone => {\r\n      phoneArray.push(this.formBuilder.group({\r\n        id: [phone.id],\r\n        number: [phone.number, Validators.required],\r\n        type: [phone.type, Validators.required],\r\n        isPublic: [phone.isPublic],\r\n        isPrimary: [phone.isPrimary]\r\n      }));\r\n    });\r\n  }\r\n\r\n  addPhoneNumber(): void {\r\n    const phoneGroup = this.formBuilder.group({\r\n      id: [null],\r\n      number: ['', Validators.required],\r\n      type: ['mobile', Validators.required],\r\n      isPublic: [false],\r\n      isPrimary: [false]\r\n    });\r\n    \r\n    this.phoneNumbers.push(phoneGroup);\r\n  }\r\n\r\n  removePhoneNumber(index: number): void {\r\n    this.phoneNumbers.removeAt(index);\r\n  }\r\n\r\n  // Social Links Management\r\n  get socialLinks(): FormArray {\r\n    return this.profileForm.get('socialLinks') as FormArray;\r\n  }\r\n\r\n  private setSocialLinks(links: any[]): void {\r\n    const linksArray = this.socialLinks;\r\n    linksArray.clear();\r\n    \r\n    links.forEach(link => {\r\n      linksArray.push(this.formBuilder.group({\r\n        id: [link.id],\r\n        platform: [link.platform, Validators.required],\r\n        url: [link.url, [Validators.required, Validators.pattern('https?://.+')]],\r\n        displayName: [link.displayName],\r\n        isPublic: [link.isPublic]\r\n      }));\r\n    });\r\n  }\r\n\r\n  addSocialLink(): void {\r\n    const linkGroup = this.formBuilder.group({\r\n      id: [null],\r\n      platform: ['linkedin', Validators.required],\r\n      url: ['', [Validators.required, Validators.pattern('https?://.+')]],\r\n      displayName: [''],\r\n      isPublic: [true]\r\n    });\r\n    \r\n    this.socialLinks.push(linkGroup);\r\n  }\r\n\r\n  removeSocialLink(index: number): void {\r\n    this.socialLinks.removeAt(index);\r\n  }\r\n\r\n  // Skills Management\r\n  get skills(): FormArray {\r\n    return this.profileForm.get('skills') as FormArray;\r\n  }\r\n\r\n  private setSkills(skills: ProfileSkill[]): void {\r\n    const skillArray = this.skills;\r\n    skillArray.clear();\r\n\r\n    skills.forEach(skill => {\r\n      skillArray.push(this.formBuilder.group({\r\n        id: [skill.id],\r\n        name: [skill.name, Validators.required],\r\n        category: [skill.category || ''],\r\n        proficiencyLevel: [skill.proficiencyLevel || 'intermediate', Validators.required]\r\n      }));\r\n    });\r\n  }\r\n\r\n  addSkill(): void {\r\n    const skillGroup = this.formBuilder.group({\r\n      id: [null],\r\n      name: ['', Validators.required],\r\n      category: [''],\r\n      proficiencyLevel: ['intermediate', Validators.required]\r\n    });\r\n\r\n    this.skills.push(skillGroup);\r\n  }\r\n\r\n  removeSkill(index: number): void {\r\n    this.skills.removeAt(index);\r\n  }\r\n\r\n  // Service Offerings Management\r\n  get serviceOfferings(): FormArray {\r\n    return this.profileForm.get('serviceOfferings') as FormArray;\r\n  }\r\n\r\n  private setServiceOfferings(services: ServiceOffering[]): void {\r\n    const serviceArray = this.serviceOfferings;\r\n    serviceArray.clear();\r\n\r\n    services.forEach(service => {\r\n      serviceArray.push(this.formBuilder.group({\r\n        id: [service.id],\r\n        name: [service.name, Validators.required],\r\n        description: [service.description, Validators.required],\r\n        price: [service.price, [Validators.required, Validators.min(0)]],\r\n        currency: [service.currency || 'BGN', Validators.required],\r\n        duration: [service.duration],\r\n        category: [service.category || ''],\r\n        isActive: [service.isActive !== false] // default to true\r\n      }));\r\n    });\r\n  }\r\n\r\n  addServiceOffering(): void {\r\n    const serviceGroup = this.formBuilder.group({\r\n      id: [null],\r\n      name: ['', Validators.required],\r\n      description: ['', Validators.required],\r\n      price: [0, [Validators.required, Validators.min(0)]],\r\n      currency: ['BGN', Validators.required],\r\n      duration: [60], // default 60 minutes\r\n      category: [''],\r\n      isActive: [true]\r\n    });\r\n\r\n    this.serviceOfferings.push(serviceGroup);\r\n  }\r\n\r\n  removeServiceOffering(index: number): void {\r\n    this.serviceOfferings.removeAt(index);\r\n  }\r\n\r\n  // Form Submission\r\n  onSubmit(): void {\r\n    if (this.validateForm()) {\r\n      this.isSaving = true;\r\n      \r\n      const formValue = this.profileForm.value;\r\n      const updateRequest: ProfileUpdateRequest = {\r\n        firstName: formValue.firstName,\r\n        lastName: formValue.lastName,\r\n        professionalTitle: formValue.professionalTitle,\r\n        headline: formValue.headline,\r\n        location: formValue.location,\r\n        contactInfo: formValue.contactInfo,\r\n        summary: formValue.summary,\r\n        isPublic: formValue.isPublic,\r\n        skills: formValue.skills || [],\r\n        consultationRates: formValue.consultationRates,\r\n        serviceOfferings: formValue.serviceOfferings || []\r\n      };\r\n\r\n      this.profileService.updateProfile(updateRequest)\r\n        .pipe(takeUntil(this.destroy$))\r\n        .subscribe({\r\n          next: (updatedProfile) => {\r\n            this.isSaving = false;\r\n            this.snackBar.open('Profile updated successfully!', 'Close', { duration: 3000 });\r\n            this.router.navigate(['/profile', updatedProfile.slug]);\r\n          },\r\n          error: (error) => {\r\n            this.isSaving = false;\r\n            console.error('Error updating profile:', error);\r\n            this.snackBar.open('Error updating profile. Please try again.', 'Close', { duration: 5000 });\r\n          }\r\n        });\r\n    }\r\n    // Validation is now handled in validateForm() method\r\n  }\r\n\r\n  onCancel(): void {\r\n    if (this.profile) {\r\n      this.router.navigate(['/profile', this.profile.slug]);\r\n    } else {\r\n      this.router.navigate(['/dashboard']);\r\n    }\r\n  }\r\n\r\n  // File Upload Methods\r\n  onProfilePhotoSelected(event: any): void {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      if (this.validateImageFile(file, 'profile')) {\r\n        this.createImagePreview(file, 'profile');\r\n        this.uploadProfilePhoto(file);\r\n      }\r\n    }\r\n    // Reset the input to allow selecting the same file again\r\n    event.target.value = '';\r\n  }\r\n\r\n  onCoverPhotoSelected(event: any): void {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      if (this.validateImageFile(file, 'cover')) {\r\n        this.createImagePreview(file, 'cover');\r\n        this.uploadCoverPhoto(file);\r\n      }\r\n    }\r\n    // Reset the input to allow selecting the same file again\r\n    event.target.value = '';\r\n  }\r\n\r\n  private validateImageFile(file: File, type: 'profile' | 'cover'): boolean {\r\n    // Check file type\r\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];\r\n    if (!allowedTypes.includes(file.type)) {\r\n      this.snackBar.open('Please select a valid image file (JPEG, PNG, GIF, or WebP)', 'Close', { duration: 5000 });\r\n      return false;\r\n    }\r\n\r\n    // Check file size (5MB for profile, 10MB for cover)\r\n    const maxSize = type === 'profile' ? 5 * 1024 * 1024 : 10 * 1024 * 1024;\r\n    if (file.size > maxSize) {\r\n      const maxSizeMB = maxSize / (1024 * 1024);\r\n      this.snackBar.open(`File size must be less than ${maxSizeMB}MB`, 'Close', { duration: 5000 });\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  private createImagePreview(file: File, type: 'profile' | 'cover'): void {\r\n    const reader = new FileReader();\r\n    reader.onload = (e: any) => {\r\n      if (type === 'profile') {\r\n        this.profilePhotoPreview = e.target.result;\r\n      } else {\r\n        this.coverPhotoPreview = e.target.result;\r\n      }\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n\r\n  private uploadProfilePhoto(file: File): void {\r\n    this.isUploadingProfilePhoto = true;\r\n    this.profileService.uploadProfilePhoto(file)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (response) => {\r\n          this.isUploadingProfilePhoto = false;\r\n          if (this.profile) {\r\n            this.profile.profilePhotoUrl = response.url;\r\n          }\r\n          this.profilePhotoPreview = null; // Clear preview since we have the actual URL\r\n          this.snackBar.open('Profile photo updated successfully!', 'Close', { duration: 3000 });\r\n        },\r\n        error: (error) => {\r\n          this.isUploadingProfilePhoto = false;\r\n          this.profilePhotoPreview = null; // Clear preview on error\r\n          console.error('Error uploading profile photo:', error);\r\n          const errorMessage = error.error?.message || 'Error uploading profile photo. Please try again.';\r\n          this.snackBar.open(errorMessage, 'Close', { duration: 5000 });\r\n        }\r\n      });\r\n  }\r\n\r\n  private uploadCoverPhoto(file: File): void {\r\n    this.isUploadingCoverPhoto = true;\r\n    this.profileService.uploadCoverPhoto(file)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (response) => {\r\n          this.isUploadingCoverPhoto = false;\r\n          if (this.profile) {\r\n            this.profile.coverPhotoUrl = response.url;\r\n          }\r\n          this.coverPhotoPreview = null; // Clear preview since we have the actual URL\r\n          this.snackBar.open('Cover photo updated successfully!', 'Close', { duration: 3000 });\r\n        },\r\n        error: (error) => {\r\n          this.isUploadingCoverPhoto = false;\r\n          this.coverPhotoPreview = null; // Clear preview on error\r\n          console.error('Error uploading cover photo:', error);\r\n          const errorMessage = error.error?.message || 'Error uploading cover photo. Please try again.';\r\n          this.snackBar.open(errorMessage, 'Close', { duration: 5000 });\r\n        }\r\n      });\r\n  }\r\n\r\n  // Utility Methods\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.profileForm.controls).forEach(key => {\r\n      const control = this.profileForm.get(key);\r\n      control?.markAsTouched();\r\n      \r\n      if (control instanceof FormArray) {\r\n        control.controls.forEach(arrayControl => {\r\n          if (arrayControl instanceof FormGroup) {\r\n            Object.keys(arrayControl.controls).forEach(arrayKey => {\r\n              arrayControl.get(arrayKey)?.markAsTouched();\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  getErrorMessage(fieldName: string): string {\r\n    const control = this.profileForm.get(fieldName);\r\n    if (!control || !control.errors) return '';\r\n\r\n    const fieldDisplayName = this.getFieldDisplayName(fieldName);\r\n\r\n    if (control.hasError('required')) {\r\n      return `${fieldDisplayName} is required`;\r\n    }\r\n    if (control.hasError('email')) {\r\n      return 'Please enter a valid email address';\r\n    }\r\n    if (control.hasError('minlength')) {\r\n      const requiredLength = control.errors['minlength'].requiredLength;\r\n      return `${fieldDisplayName} must be at least ${requiredLength} characters`;\r\n    }\r\n    if (control.hasError('maxlength')) {\r\n      const maxLength = control.errors['maxlength'].requiredLength;\r\n      return `${fieldDisplayName} must be no more than ${maxLength} characters`;\r\n    }\r\n    if (control.hasError('pattern')) {\r\n      return 'Please enter a valid URL';\r\n    }\r\n    if (control.hasError('min')) {\r\n      const minValue = control.errors['min'].min;\r\n      return `${fieldDisplayName} must be at least ${minValue}`;\r\n    }\r\n    if (control.hasError('max')) {\r\n      const maxValue = control.errors['max'].max;\r\n      return `${fieldDisplayName} cannot exceed ${maxValue}`;\r\n    }\r\n    return '';\r\n  }\r\n\r\n  private getFieldDisplayName(fieldName: string): string {\r\n    const fieldNames: { [key: string]: string } = {\r\n      'firstName': 'First Name',\r\n      'lastName': 'Last Name',\r\n      'professionalTitle': 'Professional Title',\r\n      'headline': 'Headline',\r\n      'summary': 'Summary',\r\n      'contactInfo.email': 'Email',\r\n      'contactInfo.website': 'Website',\r\n      'contactInfo.portfolioUrl': 'Portfolio URL',\r\n      'location.city': 'City',\r\n      'location.state': 'State',\r\n      'location.country': 'Country',\r\n      'location.displayLocation': 'Display Location',\r\n      'consultationRates.hourlyRate': 'Hourly Rate',\r\n      'consultationRates.sessionRate': 'Session Rate',\r\n      'consultationRates.currency': 'Currency'\r\n    };\r\n    return fieldNames[fieldName] || fieldName;\r\n  }\r\n\r\n  // Enhanced form validation\r\n  validateForm(): boolean {\r\n    if (this.profileForm.invalid) {\r\n      this.markFormGroupTouched();\r\n\r\n      // Find first invalid field and focus on it\r\n      const firstInvalidField = this.findFirstInvalidField();\r\n      if (firstInvalidField) {\r\n        firstInvalidField.focus();\r\n      }\r\n\r\n      // Show specific error message\r\n      const errors = this.getFormErrors();\r\n      if (errors.length > 0) {\r\n        this.snackBar.open(`Please fix the following errors: ${errors.join(', ')}`, 'Close', { duration: 5000 });\r\n      }\r\n\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n\r\n  private findFirstInvalidField(): HTMLElement | null {\r\n    const invalidFields = document.querySelectorAll('.mat-form-field.ng-invalid input, .mat-form-field.ng-invalid textarea, .mat-form-field.ng-invalid mat-select');\r\n    return invalidFields.length > 0 ? invalidFields[0] as HTMLElement : null;\r\n  }\r\n\r\n  private getFormErrors(): string[] {\r\n    const errors: string[] = [];\r\n\r\n    // Check main form fields\r\n    Object.keys(this.profileForm.controls).forEach(key => {\r\n      const control = this.profileForm.get(key);\r\n      if (control && control.invalid && control.touched) {\r\n        const errorMessage = this.getErrorMessage(key);\r\n        if (errorMessage) {\r\n          errors.push(errorMessage);\r\n        }\r\n      }\r\n    });\r\n\r\n    // Check nested form groups\r\n    const contactInfo = this.profileForm.get('contactInfo') as FormGroup;\r\n    if (contactInfo && contactInfo.invalid) {\r\n      Object.keys(contactInfo.controls).forEach(key => {\r\n        const control = contactInfo.get(key);\r\n        if (control && control.invalid && control.touched) {\r\n          const errorMessage = this.getErrorMessage(`contactInfo.${key}`);\r\n          if (errorMessage) {\r\n            errors.push(errorMessage);\r\n          }\r\n        }\r\n      });\r\n    }\r\n\r\n    return errors.slice(0, 3); // Limit to first 3 errors to avoid overwhelming the user\r\n  }\r\n\r\n  // Platform options for social links\r\n  getPlatformOptions() {\r\n    return [\r\n      { value: 'linkedin', label: 'LinkedIn' },\r\n      { value: 'twitter', label: 'Twitter' },\r\n      { value: 'github', label: 'GitHub' },\r\n      { value: 'behance', label: 'Behance' },\r\n      { value: 'dribbble', label: 'Dribbble' },\r\n      { value: 'instagram', label: 'Instagram' },\r\n      { value: 'facebook', label: 'Facebook' },\r\n      { value: 'youtube', label: 'YouTube' },\r\n      { value: 'other', label: 'Other' }\r\n    ];\r\n  }\r\n\r\n  // Phone type options\r\n  getPhoneTypeOptions() {\r\n    return [\r\n      { value: 'mobile', label: 'Mobile' },\r\n      { value: 'business', label: 'Business' },\r\n      { value: 'home', label: 'Home' }\r\n    ];\r\n  }\r\n\r\n  // Skill category options\r\n  getSkillCategoryOptions() {\r\n    return [\r\n      { value: 'Astrology', label: 'Astrology' },\r\n      { value: 'Crystal Healing', label: 'Crystal Healing' },\r\n      { value: 'Palmistry', label: 'Palmistry' },\r\n      { value: 'Spiritual Counseling', label: 'Spiritual Counseling' },\r\n      { value: 'Numerology', label: 'Numerology' },\r\n      { value: 'Tarot Reading', label: 'Tarot Reading' },\r\n      { value: 'Energy Healing', label: 'Energy Healing' },\r\n      { value: 'Meditation', label: 'Meditation' },\r\n      { value: 'Other', label: 'Other' }\r\n    ];\r\n  }\r\n\r\n  // Proficiency level options\r\n  getProficiencyLevelOptions() {\r\n    return [\r\n      { value: 'beginner', label: 'Beginner' },\r\n      { value: 'intermediate', label: 'Intermediate' },\r\n      { value: 'advanced', label: 'Advanced' },\r\n      { value: 'expert', label: 'Expert' }\r\n    ];\r\n  }\r\n\r\n  // Service category options\r\n  getServiceCategoryOptions() {\r\n    return [\r\n      { value: 'Reading', label: 'Reading' },\r\n      { value: 'Consultation', label: 'Consultation' },\r\n      { value: 'Healing', label: 'Healing' },\r\n      { value: 'Workshop', label: 'Workshop' },\r\n      { value: 'Course', label: 'Course' },\r\n      { value: 'Other', label: 'Other' }\r\n    ];\r\n  }\r\n\r\n  // Currency options\r\n  getCurrencyOptions() {\r\n    return [\r\n      { value: 'BGN', label: 'BGN (Bulgarian Lev)' },\r\n      { value: 'EUR', label: 'EUR (Euro)' },\r\n      { value: 'USD', label: 'USD (US Dollar)' },\r\n      { value: 'GBP', label: 'GBP (British Pound)' }\r\n    ];\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}