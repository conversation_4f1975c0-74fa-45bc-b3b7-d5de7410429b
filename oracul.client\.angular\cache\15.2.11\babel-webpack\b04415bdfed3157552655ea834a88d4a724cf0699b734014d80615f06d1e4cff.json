{"ast": null, "code": "function cov_1b9uogtqsb() {\n  var path = \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\profile\\\\services\\\\profile.service.ts\";\n  var hash = \"990d97d21c90fa7ba44436b44c535bffcb6d409a\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\profile\\\\services\\\\profile.service.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 7,\n          column: 21\n        },\n        end: {\n          line: 331,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 9,\n          column: 8\n        },\n        end: {\n          line: 9,\n          column: 25\n        }\n      },\n      \"2\": {\n        start: {\n          line: 10,\n          column: 8\n        },\n        end: {\n          line: 10,\n          column: 55\n        }\n      },\n      \"3\": {\n        start: {\n          line: 11,\n          column: 8\n        },\n        end: {\n          line: 11,\n          column: 63\n        }\n      },\n      \"4\": {\n        start: {\n          line: 12,\n          column: 8\n        },\n        end: {\n          line: 12,\n          column: 73\n        }\n      },\n      \"5\": {\n        start: {\n          line: 18,\n          column: 22\n        },\n        end: {\n          line: 18,\n          column: 55\n        }\n      },\n      \"6\": {\n        start: {\n          line: 19,\n          column: 22\n        },\n        end: {\n          line: 21,\n          column: 10\n        }\n      },\n      \"7\": {\n        start: {\n          line: 22,\n          column: 8\n        },\n        end: {\n          line: 24,\n          column: 9\n        }\n      },\n      \"8\": {\n        start: {\n          line: 23,\n          column: 12\n        },\n        end: {\n          line: 23,\n          column: 70\n        }\n      },\n      \"9\": {\n        start: {\n          line: 25,\n          column: 8\n        },\n        end: {\n          line: 25,\n          column: 23\n        }\n      },\n      \"10\": {\n        start: {\n          line: 30,\n          column: 20\n        },\n        end: {\n          line: 32,\n          column: 45\n        }\n      },\n      \"11\": {\n        start: {\n          line: 33,\n          column: 8\n        },\n        end: {\n          line: 44,\n          column: 42\n        }\n      },\n      \"12\": {\n        start: {\n          line: 34,\n          column: 34\n        },\n        end: {\n          line: 34,\n          column: 47\n        }\n      },\n      \"13\": {\n        start: {\n          line: 35,\n          column: 12\n        },\n        end: {\n          line: 43,\n          column: 13\n        }\n      },\n      \"14\": {\n        start: {\n          line: 36,\n          column: 16\n        },\n        end: {\n          line: 36,\n          column: 57\n        }\n      },\n      \"15\": {\n        start: {\n          line: 38,\n          column: 16\n        },\n        end: {\n          line: 42,\n          column: 31\n        }\n      },\n      \"16\": {\n        start: {\n          line: 47,\n          column: 8\n        },\n        end: {\n          line: 50,\n          column: 138\n        }\n      },\n      \"17\": {\n        start: {\n          line: 50,\n          column: 34\n        },\n        end: {\n          line: 50,\n          column: 47\n        }\n      },\n      \"18\": {\n        start: {\n          line: 50,\n          column: 65\n        },\n        end: {\n          line: 50,\n          column: 105\n        }\n      },\n      \"19\": {\n        start: {\n          line: 53,\n          column: 30\n        },\n        end: {\n          line: 77,\n          column: 9\n        }\n      },\n      \"20\": {\n        start: {\n          line: 78,\n          column: 8\n        },\n        end: {\n          line: 81,\n          column: 138\n        }\n      },\n      \"21\": {\n        start: {\n          line: 81,\n          column: 34\n        },\n        end: {\n          line: 81,\n          column: 47\n        }\n      },\n      \"22\": {\n        start: {\n          line: 81,\n          column: 65\n        },\n        end: {\n          line: 81,\n          column: 105\n        }\n      },\n      \"23\": {\n        start: {\n          line: 84,\n          column: 30\n        },\n        end: {\n          line: 91,\n          column: 9\n        }\n      },\n      \"24\": {\n        start: {\n          line: 92,\n          column: 8\n        },\n        end: {\n          line: 95,\n          column: 138\n        }\n      },\n      \"25\": {\n        start: {\n          line: 95,\n          column: 34\n        },\n        end: {\n          line: 95,\n          column: 47\n        }\n      },\n      \"26\": {\n        start: {\n          line: 95,\n          column: 65\n        },\n        end: {\n          line: 95,\n          column: 105\n        }\n      },\n      \"27\": {\n        start: {\n          line: 98,\n          column: 8\n        },\n        end: {\n          line: 101,\n          column: 117\n        }\n      },\n      \"28\": {\n        start: {\n          line: 101,\n          column: 28\n        },\n        end: {\n          line: 101,\n          column: 34\n        }\n      },\n      \"29\": {\n        start: {\n          line: 101,\n          column: 47\n        },\n        end: {\n          line: 101,\n          column: 84\n        }\n      },\n      \"30\": {\n        start: {\n          line: 105,\n          column: 30\n        },\n        end: {\n          line: 111,\n          column: 9\n        }\n      },\n      \"31\": {\n        start: {\n          line: 112,\n          column: 8\n        },\n        end: {\n          line: 115,\n          column: 80\n        }\n      },\n      \"32\": {\n        start: {\n          line: 115,\n          column: 34\n        },\n        end: {\n          line: 115,\n          column: 47\n        }\n      },\n      \"33\": {\n        start: {\n          line: 119,\n          column: 30\n        },\n        end: {\n          line: 125,\n          column: 9\n        }\n      },\n      \"34\": {\n        start: {\n          line: 126,\n          column: 8\n        },\n        end: {\n          line: 129,\n          column: 80\n        }\n      },\n      \"35\": {\n        start: {\n          line: 129,\n          column: 34\n        },\n        end: {\n          line: 129,\n          column: 47\n        }\n      },\n      \"36\": {\n        start: {\n          line: 133,\n          column: 8\n        },\n        end: {\n          line: 134,\n          column: 80\n        }\n      },\n      \"37\": {\n        start: {\n          line: 134,\n          column: 34\n        },\n        end: {\n          line: 134,\n          column: 47\n        }\n      },\n      \"38\": {\n        start: {\n          line: 139,\n          column: 25\n        },\n        end: {\n          line: 144,\n          column: 9\n        }\n      },\n      \"39\": {\n        start: {\n          line: 145,\n          column: 8\n        },\n        end: {\n          line: 148,\n          column: 11\n        }\n      },\n      \"40\": {\n        start: {\n          line: 146,\n          column: 12\n        },\n        end: {\n          line: 146,\n          column: 36\n        }\n      },\n      \"41\": {\n        start: {\n          line: 147,\n          column: 12\n        },\n        end: {\n          line: 147,\n          column: 32\n        }\n      },\n      \"42\": {\n        start: {\n          line: 152,\n          column: 8\n        },\n        end: {\n          line: 155,\n          column: 11\n        }\n      },\n      \"43\": {\n        start: {\n          line: 153,\n          column: 12\n        },\n        end: {\n          line: 153,\n          column: 55\n        }\n      },\n      \"44\": {\n        start: {\n          line: 154,\n          column: 12\n        },\n        end: {\n          line: 154,\n          column: 32\n        }\n      },\n      \"45\": {\n        start: {\n          line: 159,\n          column: 8\n        },\n        end: {\n          line: 162,\n          column: 11\n        }\n      },\n      \"46\": {\n        start: {\n          line: 160,\n          column: 12\n        },\n        end: {\n          line: 160,\n          column: 28\n        }\n      },\n      \"47\": {\n        start: {\n          line: 161,\n          column: 12\n        },\n        end: {\n          line: 161,\n          column: 32\n        }\n      },\n      \"48\": {\n        start: {\n          line: 166,\n          column: 8\n        },\n        end: {\n          line: 169,\n          column: 11\n        }\n      },\n      \"49\": {\n        start: {\n          line: 167,\n          column: 12\n        },\n        end: {\n          line: 167,\n          column: 28\n        }\n      },\n      \"50\": {\n        start: {\n          line: 168,\n          column: 12\n        },\n        end: {\n          line: 168,\n          column: 32\n        }\n      },\n      \"51\": {\n        start: {\n          line: 173,\n          column: 8\n        },\n        end: {\n          line: 174,\n          column: 48\n        }\n      },\n      \"52\": {\n        start: {\n          line: 177,\n          column: 8\n        },\n        end: {\n          line: 178,\n          column: 48\n        }\n      },\n      \"53\": {\n        start: {\n          line: 181,\n          column: 8\n        },\n        end: {\n          line: 182,\n          column: 48\n        }\n      },\n      \"54\": {\n        start: {\n          line: 186,\n          column: 8\n        },\n        end: {\n          line: 187,\n          column: 48\n        }\n      },\n      \"55\": {\n        start: {\n          line: 190,\n          column: 8\n        },\n        end: {\n          line: 191,\n          column: 48\n        }\n      },\n      \"56\": {\n        start: {\n          line: 194,\n          column: 8\n        },\n        end: {\n          line: 195,\n          column: 48\n        }\n      },\n      \"57\": {\n        start: {\n          line: 199,\n          column: 8\n        },\n        end: {\n          line: 200,\n          column: 48\n        }\n      },\n      \"58\": {\n        start: {\n          line: 203,\n          column: 8\n        },\n        end: {\n          line: 204,\n          column: 48\n        }\n      },\n      \"59\": {\n        start: {\n          line: 210,\n          column: 8\n        },\n        end: {\n          line: 213,\n          column: 11\n        }\n      },\n      \"60\": {\n        start: {\n          line: 211,\n          column: 12\n        },\n        end: {\n          line: 211,\n          column: 30\n        }\n      },\n      \"61\": {\n        start: {\n          line: 212,\n          column: 12\n        },\n        end: {\n          line: 212,\n          column: 32\n        }\n      },\n      \"62\": {\n        start: {\n          line: 218,\n          column: 30\n        },\n        end: {\n          line: 227,\n          column: 9\n        }\n      },\n      \"63\": {\n        start: {\n          line: 228,\n          column: 8\n        },\n        end: {\n          line: 231,\n          column: 11\n        }\n      },\n      \"64\": {\n        start: {\n          line: 229,\n          column: 12\n        },\n        end: {\n          line: 229,\n          column: 41\n        }\n      },\n      \"65\": {\n        start: {\n          line: 230,\n          column: 12\n        },\n        end: {\n          line: 230,\n          column: 32\n        }\n      },\n      \"66\": {\n        start: {\n          line: 234,\n          column: 8\n        },\n        end: {\n          line: 237,\n          column: 67\n        }\n      },\n      \"67\": {\n        start: {\n          line: 237,\n          column: 28\n        },\n        end: {\n          line: 237,\n          column: 34\n        }\n      },\n      \"68\": {\n        start: {\n          line: 241,\n          column: 25\n        },\n        end: {\n          line: 241,\n          column: 39\n        }\n      },\n      \"69\": {\n        start: {\n          line: 242,\n          column: 8\n        },\n        end: {\n          line: 242,\n          column: 38\n        }\n      },\n      \"70\": {\n        start: {\n          line: 243,\n          column: 22\n        },\n        end: {\n          line: 243,\n          column: 55\n        }\n      },\n      \"71\": {\n        start: {\n          line: 244,\n          column: 22\n        },\n        end: {\n          line: 244,\n          column: 39\n        }\n      },\n      \"72\": {\n        start: {\n          line: 245,\n          column: 8\n        },\n        end: {\n          line: 247,\n          column: 9\n        }\n      },\n      \"73\": {\n        start: {\n          line: 246,\n          column: 12\n        },\n        end: {\n          line: 246,\n          column: 70\n        }\n      },\n      \"74\": {\n        start: {\n          line: 248,\n          column: 8\n        },\n        end: {\n          line: 251,\n          column: 91\n        }\n      },\n      \"75\": {\n        start: {\n          line: 251,\n          column: 35\n        },\n        end: {\n          line: 251,\n          column: 57\n        }\n      },\n      \"76\": {\n        start: {\n          line: 254,\n          column: 25\n        },\n        end: {\n          line: 254,\n          column: 39\n        }\n      },\n      \"77\": {\n        start: {\n          line: 255,\n          column: 8\n        },\n        end: {\n          line: 255,\n          column: 38\n        }\n      },\n      \"78\": {\n        start: {\n          line: 256,\n          column: 22\n        },\n        end: {\n          line: 256,\n          column: 55\n        }\n      },\n      \"79\": {\n        start: {\n          line: 257,\n          column: 22\n        },\n        end: {\n          line: 257,\n          column: 39\n        }\n      },\n      \"80\": {\n        start: {\n          line: 258,\n          column: 8\n        },\n        end: {\n          line: 260,\n          column: 9\n        }\n      },\n      \"81\": {\n        start: {\n          line: 259,\n          column: 12\n        },\n        end: {\n          line: 259,\n          column: 70\n        }\n      },\n      \"82\": {\n        start: {\n          line: 261,\n          column: 8\n        },\n        end: {\n          line: 264,\n          column: 91\n        }\n      },\n      \"83\": {\n        start: {\n          line: 264,\n          column: 35\n        },\n        end: {\n          line: 264,\n          column: 57\n        }\n      },\n      \"84\": {\n        start: {\n          line: 269,\n          column: 21\n        },\n        end: {\n          line: 272,\n          column: 34\n        }\n      },\n      \"85\": {\n        start: {\n          line: 273,\n          column: 8\n        },\n        end: {\n          line: 276,\n          column: 11\n        }\n      },\n      \"86\": {\n        start: {\n          line: 274,\n          column: 12\n        },\n        end: {\n          line: 274,\n          column: 36\n        }\n      },\n      \"87\": {\n        start: {\n          line: 275,\n          column: 12\n        },\n        end: {\n          line: 275,\n          column: 32\n        }\n      },\n      \"88\": {\n        start: {\n          line: 281,\n          column: 8\n        },\n        end: {\n          line: 284,\n          column: 11\n        }\n      },\n      \"89\": {\n        start: {\n          line: 282,\n          column: 12\n        },\n        end: {\n          line: 282,\n          column: 47\n        }\n      },\n      \"90\": {\n        start: {\n          line: 283,\n          column: 12\n        },\n        end: {\n          line: 283,\n          column: 32\n        }\n      },\n      \"91\": {\n        start: {\n          line: 289,\n          column: 31\n        },\n        end: {\n          line: 289,\n          column: 63\n        }\n      },\n      \"92\": {\n        start: {\n          line: 290,\n          column: 8\n        },\n        end: {\n          line: 301,\n          column: 9\n        }\n      },\n      \"93\": {\n        start: {\n          line: 291,\n          column: 30\n        },\n        end: {\n          line: 296,\n          column: 13\n        }\n      },\n      \"94\": {\n        start: {\n          line: 297,\n          column: 12\n        },\n        end: {\n          line: 300,\n          column: 15\n        }\n      },\n      \"95\": {\n        start: {\n          line: 298,\n          column: 16\n        },\n        end: {\n          line: 298,\n          column: 41\n        }\n      },\n      \"96\": {\n        start: {\n          line: 299,\n          column: 16\n        },\n        end: {\n          line: 299,\n          column: 36\n        }\n      },\n      \"97\": {\n        start: {\n          line: 302,\n          column: 8\n        },\n        end: {\n          line: 302,\n          column: 64\n        }\n      },\n      \"98\": {\n        start: {\n          line: 302,\n          column: 32\n        },\n        end: {\n          line: 302,\n          column: 62\n        }\n      },\n      \"99\": {\n        start: {\n          line: 305,\n          column: 8\n        },\n        end: {\n          line: 305,\n          column: 54\n        }\n      },\n      \"100\": {\n        start: {\n          line: 306,\n          column: 27\n        },\n        end: {\n          line: 306,\n          column: 55\n        }\n      },\n      \"101\": {\n        start: {\n          line: 307,\n          column: 8\n        },\n        end: {\n          line: 325,\n          column: 9\n        }\n      },\n      \"102\": {\n        start: {\n          line: 309,\n          column: 12\n        },\n        end: {\n          line: 309,\n          column: 47\n        }\n      },\n      \"103\": {\n        start: {\n          line: 311,\n          column: 13\n        },\n        end: {\n          line: 325,\n          column: 9\n        }\n      },\n      \"104\": {\n        start: {\n          line: 312,\n          column: 12\n        },\n        end: {\n          line: 312,\n          column: 53\n        }\n      },\n      \"105\": {\n        start: {\n          line: 314,\n          column: 13\n        },\n        end: {\n          line: 325,\n          column: 9\n        }\n      },\n      \"106\": {\n        start: {\n          line: 315,\n          column: 12\n        },\n        end: {\n          line: 315,\n          column: 66\n        }\n      },\n      \"107\": {\n        start: {\n          line: 317,\n          column: 13\n        },\n        end: {\n          line: 325,\n          column: 9\n        }\n      },\n      \"108\": {\n        start: {\n          line: 318,\n          column: 12\n        },\n        end: {\n          line: 318,\n          column: 59\n        }\n      },\n      \"109\": {\n        start: {\n          line: 320,\n          column: 13\n        },\n        end: {\n          line: 325,\n          column: 9\n        }\n      },\n      \"110\": {\n        start: {\n          line: 321,\n          column: 12\n        },\n        end: {\n          line: 321,\n          column: 51\n        }\n      },\n      \"111\": {\n        start: {\n          line: 323,\n          column: 13\n        },\n        end: {\n          line: 325,\n          column: 9\n        }\n      },\n      \"112\": {\n        start: {\n          line: 324,\n          column: 12\n        },\n        end: {\n          line: 324,\n          column: 77\n        }\n      },\n      \"113\": {\n        start: {\n          line: 326,\n          column: 8\n        },\n        end: {\n          line: 326,\n          column: 57\n        }\n      },\n      \"114\": {\n        start: {\n          line: 326,\n          column: 32\n        },\n        end: {\n          line: 326,\n          column: 55\n        }\n      },\n      \"115\": {\n        start: {\n          line: 328,\n          column: 13\n        },\n        end: {\n          line: 330,\n          column: 6\n        }\n      },\n      \"116\": {\n        start: {\n          line: 328,\n          column: 41\n        },\n        end: {\n          line: 330,\n          column: 5\n        }\n      },\n      \"117\": {\n        start: {\n          line: 332,\n          column: 0\n        },\n        end: {\n          line: 336,\n          column: 19\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 8,\n            column: 4\n          },\n          end: {\n            line: 8,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 8,\n            column: 22\n          },\n          end: {\n            line: 13,\n            column: 5\n          }\n        },\n        line: 8\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 17,\n            column: 4\n          },\n          end: {\n            line: 17,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 17,\n            column: 21\n          },\n          end: {\n            line: 26,\n            column: 5\n          }\n        },\n        line: 17\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 28,\n            column: 4\n          },\n          end: {\n            line: 28,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 28,\n            column: 27\n          },\n          end: {\n            line: 45,\n            column: 5\n          }\n        },\n        line: 28\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 34,\n            column: 22\n          },\n          end: {\n            line: 34,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 34,\n            column: 34\n          },\n          end: {\n            line: 34,\n            column: 47\n          }\n        },\n        line: 34\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 34,\n            column: 54\n          },\n          end: {\n            line: 34,\n            column: 55\n          }\n        },\n        loc: {\n          start: {\n            line: 34,\n            column: 65\n          },\n          end: {\n            line: 44,\n            column: 9\n          }\n        },\n        line: 34\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 46,\n            column: 4\n          },\n          end: {\n            line: 46,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 46,\n            column: 28\n          },\n          end: {\n            line: 51,\n            column: 5\n          }\n        },\n        line: 46\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 50,\n            column: 22\n          },\n          end: {\n            line: 50,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 50,\n            column: 34\n          },\n          end: {\n            line: 50,\n            column: 47\n          }\n        },\n        line: 50\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 50,\n            column: 54\n          },\n          end: {\n            line: 50,\n            column: 55\n          }\n        },\n        loc: {\n          start: {\n            line: 50,\n            column: 65\n          },\n          end: {\n            line: 50,\n            column: 105\n          }\n        },\n        line: 50\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 52,\n            column: 4\n          },\n          end: {\n            line: 52,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 52,\n            column: 27\n          },\n          end: {\n            line: 82,\n            column: 5\n          }\n        },\n        line: 52\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 81,\n            column: 22\n          },\n          end: {\n            line: 81,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 81,\n            column: 34\n          },\n          end: {\n            line: 81,\n            column: 47\n          }\n        },\n        line: 81\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 81,\n            column: 54\n          },\n          end: {\n            line: 81,\n            column: 55\n          }\n        },\n        loc: {\n          start: {\n            line: 81,\n            column: 65\n          },\n          end: {\n            line: 81,\n            column: 105\n          }\n        },\n        line: 81\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 83,\n            column: 4\n          },\n          end: {\n            line: 83,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 83,\n            column: 31\n          },\n          end: {\n            line: 96,\n            column: 5\n          }\n        },\n        line: 83\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 95,\n            column: 22\n          },\n          end: {\n            line: 95,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 95,\n            column: 34\n          },\n          end: {\n            line: 95,\n            column: 47\n          }\n        },\n        line: 95\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 95,\n            column: 54\n          },\n          end: {\n            line: 95,\n            column: 55\n          }\n        },\n        loc: {\n          start: {\n            line: 95,\n            column: 65\n          },\n          end: {\n            line: 95,\n            column: 105\n          }\n        },\n        line: 95\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 97,\n            column: 4\n          },\n          end: {\n            line: 97,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 97,\n            column: 20\n          },\n          end: {\n            line: 102,\n            column: 5\n          }\n        },\n        line: 97\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 101,\n            column: 22\n          },\n          end: {\n            line: 101,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 101,\n            column: 28\n          },\n          end: {\n            line: 101,\n            column: 34\n          }\n        },\n        line: 101\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 101,\n            column: 41\n          },\n          end: {\n            line: 101,\n            column: 42\n          }\n        },\n        loc: {\n          start: {\n            line: 101,\n            column: 47\n          },\n          end: {\n            line: 101,\n            column: 84\n          }\n        },\n        line: 101\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 104,\n            column: 4\n          },\n          end: {\n            line: 104,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 104,\n            column: 50\n          },\n          end: {\n            line: 116,\n            column: 5\n          }\n        },\n        line: 104\n      },\n      \"18\": {\n        name: \"(anonymous_18)\",\n        decl: {\n          start: {\n            line: 115,\n            column: 22\n          },\n          end: {\n            line: 115,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 115,\n            column: 34\n          },\n          end: {\n            line: 115,\n            column: 47\n          }\n        },\n        line: 115\n      },\n      \"19\": {\n        name: \"(anonymous_19)\",\n        decl: {\n          start: {\n            line: 118,\n            column: 4\n          },\n          end: {\n            line: 118,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 118,\n            column: 62\n          },\n          end: {\n            line: 130,\n            column: 5\n          }\n        },\n        line: 118\n      },\n      \"20\": {\n        name: \"(anonymous_20)\",\n        decl: {\n          start: {\n            line: 129,\n            column: 22\n          },\n          end: {\n            line: 129,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 129,\n            column: 34\n          },\n          end: {\n            line: 129,\n            column: 47\n          }\n        },\n        line: 129\n      },\n      \"21\": {\n        name: \"(anonymous_21)\",\n        decl: {\n          start: {\n            line: 132,\n            column: 4\n          },\n          end: {\n            line: 132,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 132,\n            column: 47\n          },\n          end: {\n            line: 135,\n            column: 5\n          }\n        },\n        line: 132\n      },\n      \"22\": {\n        name: \"(anonymous_22)\",\n        decl: {\n          start: {\n            line: 134,\n            column: 22\n          },\n          end: {\n            line: 134,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 134,\n            column: 34\n          },\n          end: {\n            line: 134,\n            column: 47\n          }\n        },\n        line: 134\n      },\n      \"23\": {\n        name: \"(anonymous_23)\",\n        decl: {\n          start: {\n            line: 137,\n            column: 4\n          },\n          end: {\n            line: 137,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 137,\n            column: 20\n          },\n          end: {\n            line: 149,\n            column: 5\n          }\n        },\n        line: 137\n      },\n      \"24\": {\n        name: \"(anonymous_24)\",\n        decl: {\n          start: {\n            line: 145,\n            column: 30\n          },\n          end: {\n            line: 145,\n            column: 31\n          }\n        },\n        loc: {\n          start: {\n            line: 145,\n            column: 42\n          },\n          end: {\n            line: 148,\n            column: 9\n          }\n        },\n        line: 145\n      },\n      \"25\": {\n        name: \"(anonymous_25)\",\n        decl: {\n          start: {\n            line: 150,\n            column: 4\n          },\n          end: {\n            line: 150,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 150,\n            column: 34\n          },\n          end: {\n            line: 156,\n            column: 5\n          }\n        },\n        line: 150\n      },\n      \"26\": {\n        name: \"(anonymous_26)\",\n        decl: {\n          start: {\n            line: 152,\n            column: 30\n          },\n          end: {\n            line: 152,\n            column: 31\n          }\n        },\n        loc: {\n          start: {\n            line: 152,\n            column: 42\n          },\n          end: {\n            line: 155,\n            column: 9\n          }\n        },\n        line: 152\n      },\n      \"27\": {\n        name: \"(anonymous_27)\",\n        decl: {\n          start: {\n            line: 157,\n            column: 4\n          },\n          end: {\n            line: 157,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 157,\n            column: 25\n          },\n          end: {\n            line: 163,\n            column: 5\n          }\n        },\n        line: 157\n      },\n      \"28\": {\n        name: \"(anonymous_28)\",\n        decl: {\n          start: {\n            line: 159,\n            column: 30\n          },\n          end: {\n            line: 159,\n            column: 31\n          }\n        },\n        loc: {\n          start: {\n            line: 159,\n            column: 42\n          },\n          end: {\n            line: 162,\n            column: 9\n          }\n        },\n        line: 159\n      },\n      \"29\": {\n        name: \"(anonymous_29)\",\n        decl: {\n          start: {\n            line: 164,\n            column: 4\n          },\n          end: {\n            line: 164,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 164,\n            column: 26\n          },\n          end: {\n            line: 170,\n            column: 5\n          }\n        },\n        line: 164\n      },\n      \"30\": {\n        name: \"(anonymous_30)\",\n        decl: {\n          start: {\n            line: 166,\n            column: 30\n          },\n          end: {\n            line: 166,\n            column: 31\n          }\n        },\n        loc: {\n          start: {\n            line: 166,\n            column: 42\n          },\n          end: {\n            line: 169,\n            column: 9\n          }\n        },\n        line: 166\n      },\n      \"31\": {\n        name: \"(anonymous_31)\",\n        decl: {\n          start: {\n            line: 172,\n            column: 4\n          },\n          end: {\n            line: 172,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 172,\n            column: 30\n          },\n          end: {\n            line: 175,\n            column: 5\n          }\n        },\n        line: 172\n      },\n      \"32\": {\n        name: \"(anonymous_32)\",\n        decl: {\n          start: {\n            line: 176,\n            column: 4\n          },\n          end: {\n            line: 176,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 176,\n            column: 44\n          },\n          end: {\n            line: 179,\n            column: 5\n          }\n        },\n        line: 176\n      },\n      \"33\": {\n        name: \"(anonymous_33)\",\n        decl: {\n          start: {\n            line: 180,\n            column: 4\n          },\n          end: {\n            line: 180,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 180,\n            column: 35\n          },\n          end: {\n            line: 183,\n            column: 5\n          }\n        },\n        line: 180\n      },\n      \"34\": {\n        name: \"(anonymous_34)\",\n        decl: {\n          start: {\n            line: 185,\n            column: 4\n          },\n          end: {\n            line: 185,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 185,\n            column: 27\n          },\n          end: {\n            line: 188,\n            column: 5\n          }\n        },\n        line: 185\n      },\n      \"35\": {\n        name: \"(anonymous_35)\",\n        decl: {\n          start: {\n            line: 189,\n            column: 4\n          },\n          end: {\n            line: 189,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 189,\n            column: 41\n          },\n          end: {\n            line: 192,\n            column: 5\n          }\n        },\n        line: 189\n      },\n      \"36\": {\n        name: \"(anonymous_36)\",\n        decl: {\n          start: {\n            line: 193,\n            column: 4\n          },\n          end: {\n            line: 193,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 193,\n            column: 32\n          },\n          end: {\n            line: 196,\n            column: 5\n          }\n        },\n        line: 193\n      },\n      \"37\": {\n        name: \"(anonymous_37)\",\n        decl: {\n          start: {\n            line: 198,\n            column: 4\n          },\n          end: {\n            line: 198,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 198,\n            column: 32\n          },\n          end: {\n            line: 201,\n            column: 5\n          }\n        },\n        line: 198\n      },\n      \"38\": {\n        name: \"(anonymous_38)\",\n        decl: {\n          start: {\n            line: 202,\n            column: 4\n          },\n          end: {\n            line: 202,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 202,\n            column: 36\n          },\n          end: {\n            line: 205,\n            column: 5\n          }\n        },\n        line: 202\n      },\n      \"39\": {\n        name: \"(anonymous_39)\",\n        decl: {\n          start: {\n            line: 207,\n            column: 4\n          },\n          end: {\n            line: 207,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 207,\n            column: 28\n          },\n          end: {\n            line: 214,\n            column: 5\n          }\n        },\n        line: 207\n      },\n      \"40\": {\n        name: \"(anonymous_40)\",\n        decl: {\n          start: {\n            line: 210,\n            column: 30\n          },\n          end: {\n            line: 210,\n            column: 31\n          }\n        },\n        loc: {\n          start: {\n            line: 210,\n            column: 42\n          },\n          end: {\n            line: 213,\n            column: 9\n          }\n        },\n        line: 210\n      },\n      \"41\": {\n        name: \"(anonymous_41)\",\n        decl: {\n          start: {\n            line: 216,\n            column: 4\n          },\n          end: {\n            line: 216,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 216,\n            column: 26\n          },\n          end: {\n            line: 232,\n            column: 5\n          }\n        },\n        line: 216\n      },\n      \"42\": {\n        name: \"(anonymous_42)\",\n        decl: {\n          start: {\n            line: 228,\n            column: 30\n          },\n          end: {\n            line: 228,\n            column: 31\n          }\n        },\n        loc: {\n          start: {\n            line: 228,\n            column: 42\n          },\n          end: {\n            line: 231,\n            column: 9\n          }\n        },\n        line: 228\n      },\n      \"43\": {\n        name: \"(anonymous_43)\",\n        decl: {\n          start: {\n            line: 233,\n            column: 4\n          },\n          end: {\n            line: 233,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 233,\n            column: 31\n          },\n          end: {\n            line: 238,\n            column: 5\n          }\n        },\n        line: 233\n      },\n      \"44\": {\n        name: \"(anonymous_44)\",\n        decl: {\n          start: {\n            line: 237,\n            column: 22\n          },\n          end: {\n            line: 237,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 237,\n            column: 28\n          },\n          end: {\n            line: 237,\n            column: 34\n          }\n        },\n        line: 237\n      },\n      \"45\": {\n        name: \"(anonymous_45)\",\n        decl: {\n          start: {\n            line: 240,\n            column: 4\n          },\n          end: {\n            line: 240,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 240,\n            column: 29\n          },\n          end: {\n            line: 252,\n            column: 5\n          }\n        },\n        line: 240\n      },\n      \"46\": {\n        name: \"(anonymous_46)\",\n        decl: {\n          start: {\n            line: 251,\n            column: 22\n          },\n          end: {\n            line: 251,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 251,\n            column: 35\n          },\n          end: {\n            line: 251,\n            column: 57\n          }\n        },\n        line: 251\n      },\n      \"47\": {\n        name: \"(anonymous_47)\",\n        decl: {\n          start: {\n            line: 253,\n            column: 4\n          },\n          end: {\n            line: 253,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 253,\n            column: 27\n          },\n          end: {\n            line: 265,\n            column: 5\n          }\n        },\n        line: 253\n      },\n      \"48\": {\n        name: \"(anonymous_48)\",\n        decl: {\n          start: {\n            line: 264,\n            column: 22\n          },\n          end: {\n            line: 264,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 264,\n            column: 35\n          },\n          end: {\n            line: 264,\n            column: 57\n          }\n        },\n        line: 264\n      },\n      \"49\": {\n        name: \"(anonymous_49)\",\n        decl: {\n          start: {\n            line: 267,\n            column: 4\n          },\n          end: {\n            line: 267,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 267,\n            column: 45\n          },\n          end: {\n            line: 277,\n            column: 5\n          }\n        },\n        line: 267\n      },\n      \"50\": {\n        name: \"(anonymous_50)\",\n        decl: {\n          start: {\n            line: 273,\n            column: 30\n          },\n          end: {\n            line: 273,\n            column: 31\n          }\n        },\n        loc: {\n          start: {\n            line: 273,\n            column: 42\n          },\n          end: {\n            line: 276,\n            column: 9\n          }\n        },\n        line: 273\n      },\n      \"51\": {\n        name: \"(anonymous_51)\",\n        decl: {\n          start: {\n            line: 278,\n            column: 4\n          },\n          end: {\n            line: 278,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 278,\n            column: 32\n          },\n          end: {\n            line: 285,\n            column: 5\n          }\n        },\n        line: 278\n      },\n      \"52\": {\n        name: \"(anonymous_52)\",\n        decl: {\n          start: {\n            line: 281,\n            column: 30\n          },\n          end: {\n            line: 281,\n            column: 31\n          }\n        },\n        loc: {\n          start: {\n            line: 281,\n            column: 42\n          },\n          end: {\n            line: 284,\n            column: 9\n          }\n        },\n        line: 281\n      },\n      \"53\": {\n        name: \"(anonymous_53)\",\n        decl: {\n          start: {\n            line: 287,\n            column: 4\n          },\n          end: {\n            line: 287,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 287,\n            column: 35\n          },\n          end: {\n            line: 303,\n            column: 5\n          }\n        },\n        line: 287\n      },\n      \"54\": {\n        name: \"(anonymous_54)\",\n        decl: {\n          start: {\n            line: 297,\n            column: 34\n          },\n          end: {\n            line: 297,\n            column: 35\n          }\n        },\n        loc: {\n          start: {\n            line: 297,\n            column: 46\n          },\n          end: {\n            line: 300,\n            column: 13\n          }\n        },\n        line: 297\n      },\n      \"55\": {\n        name: \"(anonymous_55)\",\n        decl: {\n          start: {\n            line: 302,\n            column: 26\n          },\n          end: {\n            line: 302,\n            column: 27\n          }\n        },\n        loc: {\n          start: {\n            line: 302,\n            column: 32\n          },\n          end: {\n            line: 302,\n            column: 62\n          }\n        },\n        line: 302\n      },\n      \"56\": {\n        name: \"(anonymous_56)\",\n        decl: {\n          start: {\n            line: 304,\n            column: 4\n          },\n          end: {\n            line: 304,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 304,\n            column: 23\n          },\n          end: {\n            line: 327,\n            column: 5\n          }\n        },\n        line: 304\n      },\n      \"57\": {\n        name: \"(anonymous_57)\",\n        decl: {\n          start: {\n            line: 326,\n            column: 26\n          },\n          end: {\n            line: 326,\n            column: 27\n          }\n        },\n        loc: {\n          start: {\n            line: 326,\n            column: 32\n          },\n          end: {\n            line: 326,\n            column: 55\n          }\n        },\n        line: 326\n      },\n      \"58\": {\n        name: \"(anonymous_58)\",\n        decl: {\n          start: {\n            line: 328,\n            column: 35\n          },\n          end: {\n            line: 328,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 328,\n            column: 41\n          },\n          end: {\n            line: 330,\n            column: 5\n          }\n        },\n        line: 328\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 22,\n            column: 8\n          },\n          end: {\n            line: 24,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 22,\n            column: 8\n          },\n          end: {\n            line: 24,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 22\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 30,\n            column: 20\n          },\n          end: {\n            line: 32,\n            column: 45\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 31,\n            column: 14\n          },\n          end: {\n            line: 31,\n            column: 50\n          }\n        }, {\n          start: {\n            line: 32,\n            column: 14\n          },\n          end: {\n            line: 32,\n            column: 45\n          }\n        }],\n        line: 30\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 35,\n            column: 12\n          },\n          end: {\n            line: 43,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 35,\n            column: 12\n          },\n          end: {\n            line: 43,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 35\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 57,\n            column: 22\n          },\n          end: {\n            line: 62,\n            column: 25\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 57,\n            column: 41\n          },\n          end: {\n            line: 62,\n            column: 13\n          }\n        }, {\n          start: {\n            line: 62,\n            column: 16\n          },\n          end: {\n            line: 62,\n            column: 25\n          }\n        }],\n        line: 57\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 63,\n            column: 25\n          },\n          end: {\n            line: 76,\n            column: 25\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 63,\n            column: 47\n          },\n          end: {\n            line: 76,\n            column: 13\n          }\n        }, {\n          start: {\n            line: 76,\n            column: 16\n          },\n          end: {\n            line: 76,\n            column: 25\n          }\n        }],\n        line: 63\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 68,\n            column: 33\n          },\n          end: {\n            line: 75,\n            column: 29\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 68,\n            column: 71\n          },\n          end: {\n            line: 75,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 75,\n            column: 20\n          },\n          end: {\n            line: 75,\n            column: 29\n          }\n        }],\n        line: 68\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 90,\n            column: 22\n          },\n          end: {\n            line: 90,\n            column: 50\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 90,\n            column: 22\n          },\n          end: {\n            line: 90,\n            column: 42\n          }\n        }, {\n          start: {\n            line: 90,\n            column: 46\n          },\n          end: {\n            line: 90,\n            column: 50\n          }\n        }],\n        line: 90\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 104,\n            column: 28\n          },\n          end: {\n            line: 104,\n            column: 36\n          }\n        },\n        type: \"default-arg\",\n        locations: [{\n          start: {\n            line: 104,\n            column: 35\n          },\n          end: {\n            line: 104,\n            column: 36\n          }\n        }],\n        line: 104\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 104,\n            column: 38\n          },\n          end: {\n            line: 104,\n            column: 48\n          }\n        },\n        type: \"default-arg\",\n        locations: [{\n          start: {\n            line: 104,\n            column: 46\n          },\n          end: {\n            line: 104,\n            column: 48\n          }\n        }],\n        line: 104\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 107,\n            column: 20\n          },\n          end: {\n            line: 107,\n            column: 40\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 107,\n            column: 20\n          },\n          end: {\n            line: 107,\n            column: 34\n          }\n        }, {\n          start: {\n            line: 107,\n            column: 38\n          },\n          end: {\n            line: 107,\n            column: 40\n          }\n        }],\n        line: 107\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 108,\n            column: 20\n          },\n          end: {\n            line: 108,\n            column: 49\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 108,\n            column: 20\n          },\n          end: {\n            line: 108,\n            column: 34\n          }\n        }, {\n          start: {\n            line: 108,\n            column: 38\n          },\n          end: {\n            line: 108,\n            column: 49\n          }\n        }],\n        line: 108\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 118,\n            column: 37\n          },\n          end: {\n            line: 118,\n            column: 45\n          }\n        },\n        type: \"default-arg\",\n        locations: [{\n          start: {\n            line: 118,\n            column: 44\n          },\n          end: {\n            line: 118,\n            column: 45\n          }\n        }],\n        line: 118\n      },\n      \"12\": {\n        loc: {\n          start: {\n            line: 118,\n            column: 47\n          },\n          end: {\n            line: 118,\n            column: 60\n          }\n        },\n        type: \"default-arg\",\n        locations: [{\n          start: {\n            line: 118,\n            column: 58\n          },\n          end: {\n            line: 118,\n            column: 60\n          }\n        }],\n        line: 118\n      },\n      \"13\": {\n        loc: {\n          start: {\n            line: 132,\n            column: 22\n          },\n          end: {\n            line: 132,\n            column: 30\n          }\n        },\n        type: \"default-arg\",\n        locations: [{\n          start: {\n            line: 132,\n            column: 29\n          },\n          end: {\n            line: 132,\n            column: 30\n          }\n        }],\n        line: 132\n      },\n      \"14\": {\n        loc: {\n          start: {\n            line: 132,\n            column: 32\n          },\n          end: {\n            line: 132,\n            column: 45\n          }\n        },\n        type: \"default-arg\",\n        locations: [{\n          start: {\n            line: 132,\n            column: 43\n          },\n          end: {\n            line: 132,\n            column: 45\n          }\n        }],\n        line: 132\n      },\n      \"15\": {\n        loc: {\n          start: {\n            line: 245,\n            column: 8\n          },\n          end: {\n            line: 247,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 245,\n            column: 8\n          },\n          end: {\n            line: 247,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 245\n      },\n      \"16\": {\n        loc: {\n          start: {\n            line: 258,\n            column: 8\n          },\n          end: {\n            line: 260,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 258,\n            column: 8\n          },\n          end: {\n            line: 260,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 258\n      },\n      \"17\": {\n        loc: {\n          start: {\n            line: 290,\n            column: 8\n          },\n          end: {\n            line: 301,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 290,\n            column: 8\n          },\n          end: {\n            line: 301,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 290\n      },\n      \"18\": {\n        loc: {\n          start: {\n            line: 293,\n            column: 29\n          },\n          end: {\n            line: 293,\n            column: 104\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 293,\n            column: 29\n          },\n          end: {\n            line: 293,\n            column: 51\n          }\n        }, {\n          start: {\n            line: 293,\n            column: 55\n          },\n          end: {\n            line: 293,\n            column: 78\n          }\n        }, {\n          start: {\n            line: 293,\n            column: 82\n          },\n          end: {\n            line: 293,\n            column: 104\n          }\n        }],\n        line: 293\n      },\n      \"19\": {\n        loc: {\n          start: {\n            line: 294,\n            column: 26\n          },\n          end: {\n            line: 294,\n            column: 62\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 294,\n            column: 26\n          },\n          end: {\n            line: 294,\n            column: 56\n          }\n        }, {\n          start: {\n            line: 294,\n            column: 60\n          },\n          end: {\n            line: 294,\n            column: 62\n          }\n        }],\n        line: 294\n      },\n      \"20\": {\n        loc: {\n          start: {\n            line: 307,\n            column: 8\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 307,\n            column: 8\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 311,\n            column: 13\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        }],\n        line: 307\n      },\n      \"21\": {\n        loc: {\n          start: {\n            line: 307,\n            column: 12\n          },\n          end: {\n            line: 307,\n            column: 46\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 307,\n            column: 12\n          },\n          end: {\n            line: 307,\n            column: 23\n          }\n        }, {\n          start: {\n            line: 307,\n            column: 27\n          },\n          end: {\n            line: 307,\n            column: 46\n          }\n        }],\n        line: 307\n      },\n      \"22\": {\n        loc: {\n          start: {\n            line: 311,\n            column: 13\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 311,\n            column: 13\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 314,\n            column: 13\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        }],\n        line: 311\n      },\n      \"23\": {\n        loc: {\n          start: {\n            line: 314,\n            column: 13\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 314,\n            column: 13\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 317,\n            column: 13\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        }],\n        line: 314\n      },\n      \"24\": {\n        loc: {\n          start: {\n            line: 317,\n            column: 13\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 317,\n            column: 13\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 320,\n            column: 13\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        }],\n        line: 317\n      },\n      \"25\": {\n        loc: {\n          start: {\n            line: 320,\n            column: 13\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 320,\n            column: 13\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 323,\n            column: 13\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        }],\n        line: 320\n      },\n      \"26\": {\n        loc: {\n          start: {\n            line: 323,\n            column: 13\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 323,\n            column: 13\n          },\n          end: {\n            line: 325,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 323\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0,\n      \"59\": 0,\n      \"60\": 0,\n      \"61\": 0,\n      \"62\": 0,\n      \"63\": 0,\n      \"64\": 0,\n      \"65\": 0,\n      \"66\": 0,\n      \"67\": 0,\n      \"68\": 0,\n      \"69\": 0,\n      \"70\": 0,\n      \"71\": 0,\n      \"72\": 0,\n      \"73\": 0,\n      \"74\": 0,\n      \"75\": 0,\n      \"76\": 0,\n      \"77\": 0,\n      \"78\": 0,\n      \"79\": 0,\n      \"80\": 0,\n      \"81\": 0,\n      \"82\": 0,\n      \"83\": 0,\n      \"84\": 0,\n      \"85\": 0,\n      \"86\": 0,\n      \"87\": 0,\n      \"88\": 0,\n      \"89\": 0,\n      \"90\": 0,\n      \"91\": 0,\n      \"92\": 0,\n      \"93\": 0,\n      \"94\": 0,\n      \"95\": 0,\n      \"96\": 0,\n      \"97\": 0,\n      \"98\": 0,\n      \"99\": 0,\n      \"100\": 0,\n      \"101\": 0,\n      \"102\": 0,\n      \"103\": 0,\n      \"104\": 0,\n      \"105\": 0,\n      \"106\": 0,\n      \"107\": 0,\n      \"108\": 0,\n      \"109\": 0,\n      \"110\": 0,\n      \"111\": 0,\n      \"112\": 0,\n      \"113\": 0,\n      \"114\": 0,\n      \"115\": 0,\n      \"116\": 0,\n      \"117\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0],\n      \"8\": [0],\n      \"9\": [0, 0],\n      \"10\": [0, 0],\n      \"11\": [0],\n      \"12\": [0],\n      \"13\": [0],\n      \"14\": [0],\n      \"15\": [0, 0],\n      \"16\": [0, 0],\n      \"17\": [0, 0],\n      \"18\": [0, 0, 0],\n      \"19\": [0, 0],\n      \"20\": [0, 0],\n      \"21\": [0, 0],\n      \"22\": [0, 0],\n      \"23\": [0, 0],\n      \"24\": [0, 0],\n      \"25\": [0, 0],\n      \"26\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"profile.service.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\profile\\\\services\\\\profile.service.ts\"],\n      names: [],\n      mappings: \";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,UAAU,EAAc,WAAW,EAAqB,MAAM,sBAAsB,CAAC;AAC9F,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,MAAM,MAAM,CAAC;AAC/D,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AACtD,OAAO,EAAE,WAAW,EAAE,MAAM,mCAAmC,CAAC;AA0BzD,IAAM,cAAc,GAApB,MAAM,cAAc;IAMzB,YAAoB,IAAgB;QAAhB,SAAI,GAAJ,IAAI,CAAY;QALnB,YAAO,GAAG,GAAG,WAAW,CAAC,MAAM,UAAU,CAAC;QAEnD,0BAAqB,GAAG,IAAI,eAAe,CAAqB,IAAI,CAAC,CAAC;QACvE,oBAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,CAAC;IAE5B,CAAC;IAExC;;OAEG;IACK,cAAc;QACpB,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAChD,IAAI,OAAO,GAAG,IAAI,WAAW,CAAC;YAC5B,cAAc,EAAE,kBAAkB;SACnC,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE;YACT,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC,CAAC;SAC3D;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,0BAA0B;IAC1B,UAAU,CAAC,UAAkB;QAC3B,sCAAsC;QACtC,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACnC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,SAAS,UAAU,EAAE;YACtC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,UAAU,EAAE,CAAC;QAEpC,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAA2B,GAAG,CAAC;aAChD,IAAI,CACH,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAK,CAAC,EAC/B,GAAG,CAAC,OAAO,CAAC,EAAE;YACZ,IAAI,OAAO,EAAE;gBACX,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACzC,sBAAsB;gBACtB,IAAI,CAAC,iBAAiB,CAAC;oBACrB,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,SAAS,EAAE,SAAS,CAAC,SAAS;iBAC/B,CAAC,CAAC,SAAS,EAAE,CAAC;aAChB;QACH,CAAC,CAAC,EACF,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;IACN,CAAC;IAED,qBAAqB;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAA2B,GAAG,IAAI,CAAC,OAAO,KAAK,EAAE;YACnE,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE;SAC/B,CAAC;aACC,IAAI,CACH,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAK,CAAC,EAC/B,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EACxD,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;IACN,CAAC;IAED,aAAa,CAAC,OAA6B;QACzC,MAAM,aAAa,GAAyB;YAC1C,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;YAC5C,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC3B,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI;gBAC3B,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK;gBAC7B,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,OAAO;gBACjC,eAAe,EAAE,OAAO,CAAC,QAAQ,CAAC,eAAe;aAClD,CAAC,CAAC,CAAC,SAAS;YACb,WAAW,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;gBACjC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC,KAAK;gBAChC,aAAa,EAAE,OAAO,CAAC,WAAW,CAAC,aAAa;gBAChD,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC,OAAO;gBACpC,YAAY,EAAE,OAAO,CAAC,WAAW,CAAC,YAAY;gBAC9C,eAAe,EAAE,OAAO,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,CAAC;oBACrD,MAAM,EAAE,OAAO,CAAC,WAAW,CAAC,eAAe,CAAC,MAAM;oBAClD,IAAI,EAAE,OAAO,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI;oBAC9C,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC,eAAe,CAAC,KAAK;oBAChD,UAAU,EAAE,OAAO,CAAC,WAAW,CAAC,eAAe,CAAC,UAAU;oBAC1D,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC,eAAe,CAAC,OAAO;oBACpD,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC,eAAe,CAAC,QAAQ;iBACvD,CAAC,CAAC,CAAC,SAAS;aACd,CAAC,CAAC,CAAC,SAAS;SACd,CAAC;QAEF,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAA2B,GAAG,IAAI,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE;YAC/E,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE;SAC/B,CAAC;aACC,IAAI,CACH,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAK,CAAC,EAC/B,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EACxD,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;IACN,CAAC;IAED,aAAa,CAAC,WAAiC;QAC7C,MAAM,aAAa,GAAyB;YAC1C,QAAQ,EAAE,WAAW,CAAC,QAAS;YAC/B,SAAS,EAAE,WAAW,CAAC,SAAU;YACjC,QAAQ,EAAE,WAAW,CAAC,QAAS;YAC/B,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;YAChD,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,QAAQ,EAAE,WAAW,CAAC,QAAQ,IAAI,IAAI;SACvC,CAAC;QAEF,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAA2B,GAAG,IAAI,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE;YAChF,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE;SAC/B,CAAC;aACC,IAAI,CACH,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAK,CAAC,EAC/B,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EACxD,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;IACN,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAuB,GAAG,IAAI,CAAC,OAAO,EAAE,EAAE;YAC/D,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE;SAC/B,CAAC;aACC,IAAI,CACH,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,EACjB,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAChD,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;IACN,CAAC;IAED,iBAAiB;IACjB,cAAc,CAAC,OAA6B,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE;QAChF,MAAM,aAAa,GAAyB;YAC1C,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,EAAE;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,WAAW;YACrC,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,KAAK;SAChB,CAAC;QAEF,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAmC,GAAG,IAAI,CAAC,OAAO,SAAS,EAAE,aAAa,EAAE;YAC/F,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE;SAC/B,CAAC;aACC,IAAI,CACH,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAK,CAAC,EAC/B,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;IACN,CAAC;IAED,0BAA0B;IAC1B,oBAAoB,CAAC,UAAkB,EAAE,OAAe,CAAC,EAAE,WAAmB,EAAE;QAC9E,MAAM,aAAa,GAAyB;YAC1C,UAAU,EAAE,UAAU;YACtB,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,WAAW;YACnB,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,QAAQ;SACnB,CAAC;QAEF,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAmC,GAAG,IAAI,CAAC,OAAO,SAAS,EAAE,aAAa,EAAE;YAC/F,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE;SAC/B,CAAC;aACC,IAAI,CACH,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAK,CAAC,EAC/B,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;IACN,CAAC;IAED,sBAAsB;IACtB,iBAAiB,CAAC,OAAe,CAAC,EAAE,WAAmB,EAAE;QACvD,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAmC,GAAG,IAAI,CAAC,OAAO,gBAAgB,IAAI,aAAa,QAAQ,EAAE,CAAC;aAC/G,IAAI,CACH,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAK,CAAC,EAC/B,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;IACN,CAAC;IAED,iDAAiD;IACjD,QAAQ,CAAC,KAA4E;QACnF,6DAA6D;QAC7D,MAAM,QAAQ,GAAiB;YAC7B,GAAG,KAAK;YACR,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE;YACd,YAAY,EAAE,CAAC;YACf,uBAAuB,EAAE,KAAK;SAC/B,CAAC;QACF,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC/B,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxB,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,WAAW,CAAC,OAAe,EAAE,OAA8B;QACzD,6BAA6B;QAC7B,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC/B,QAAQ,CAAC,IAAI,CAAC,EAAE,GAAG,OAAO,EAAE,EAAE,EAAE,OAAO,EAAkB,CAAC,CAAC;YAC3D,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,WAAW,CAAC,OAAe;QACzB,6BAA6B;QAC7B,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC/B,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChB,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,YAAY,CAAC,OAAgC;QAC3C,6BAA6B;QAC7B,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC/B,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChB,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,wBAAwB;IACxB,aAAa,CAAC,UAAsC;QAClD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAiB,GAAG,IAAI,CAAC,OAAO,iBAAiB,EAAE,UAAU,CAAC;aAChF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,gBAAgB,CAAC,YAAoB,EAAE,OAAgC;QACrE,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAiB,GAAG,IAAI,CAAC,OAAO,mBAAmB,YAAY,EAAE,EAAE,OAAO,CAAC;aAC5F,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,gBAAgB,CAAC,YAAoB;QACnC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAO,GAAG,IAAI,CAAC,OAAO,mBAAmB,YAAY,EAAE,CAAC;aAC5E,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,uBAAuB;IACvB,gBAAgB,CAAC,IAA+B;QAC9C,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAgB,GAAG,IAAI,CAAC,OAAO,eAAe,EAAE,IAAI,CAAC;aACvE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,mBAAmB,CAAC,MAAc,EAAE,OAA+B;QACjE,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAgB,GAAG,IAAI,CAAC,OAAO,iBAAiB,MAAM,EAAE,EAAE,OAAO,CAAC;aACnF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,mBAAmB,CAAC,MAAc;QAChC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAO,GAAG,IAAI,CAAC,OAAO,iBAAiB,MAAM,EAAE,CAAC;aACpE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,gCAAgC;IAChC,cAAc,CAAC,WAAoC;QACjD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAc,GAAG,IAAI,CAAC,OAAO,kBAAkB,EAAE,WAAW,CAAC;aAC/E,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,gBAAgB,CAAC,aAAwC;QACvD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAgB,GAAG,IAAI,CAAC,OAAO,oBAAoB,EAAE,aAAa,CAAC;aACrF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,aAAa;IACb,YAAY,CAAC,SAAiB;QAC5B,+DAA+D;QAC/D,kEAAkE;QAClE,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC/B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAClB,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,YAAY;IACZ,mBAAmB;QACjB,8EAA8E;QAC9E,MAAM,aAAa,GAAqB;YACtC,YAAY,EAAE,CAAC;YACf,cAAc,EAAE,CAAC;YACjB,cAAc,EAAE,CAAC;YACjB,aAAa,EAAE,CAAC;YAChB,YAAY,EAAE,EAAE;YAChB,iBAAiB,EAAE,CAAC;YACpB,aAAa,EAAE,CAAC;YAChB,mBAAmB,EAAE,CAAC;SACvB,CAAC;QACF,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC/B,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC7B,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,iBAAiB,CAAC,OAA2B;QAC3C,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAuB,GAAG,IAAI,CAAC,OAAO,OAAO,EAAE,OAAO,EAAE;YAC3E,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE;SAC/B,CAAC;aACC,IAAI,CACH,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,EACjB,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;IACN,CAAC;IAED,cAAc;IACd,kBAAkB,CAAC,IAAU;QAC3B,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;QAChC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAE9B,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAChD,IAAI,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAChC,IAAI,KAAK,EAAE;YACT,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC,CAAC;SAC3D;QAED,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAsB,GAAG,IAAI,CAAC,OAAO,uBAAuB,EAAE,QAAQ,EAAE;YAC3F,OAAO,EAAE,OAAO;SACjB,CAAC;aACC,IAAI,CACH,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC,IAAK,EAAE,CAAC,CAAC,EAC1C,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;IACN,CAAC;IAED,gBAAgB,CAAC,IAAU;QACzB,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;QAChC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAE9B,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAChD,IAAI,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAChC,IAAI,KAAK,EAAE;YACT,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC,CAAC;SAC3D;QAED,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAsB,GAAG,IAAI,CAAC,OAAO,qBAAqB,EAAE,QAAQ,EAAE;YACzF,OAAO,EAAE,OAAO;SACjB,CAAC;aACC,IAAI,CACH,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC,IAAK,EAAE,CAAC,CAAC,EAC1C,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;IACN,CAAC;IAED,kBAAkB;IAClB,mBAAmB,CAAC,SAAiB,EAAE,QAAgB;QACrD,oCAAoC;QACpC,MAAM,IAAI,GAAG,GAAG,SAAS,CAAC,WAAW,EAAE,IAAI,QAAQ,CAAC,WAAW,EAAE,EAAE;aAChE,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC;aAC3B,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;aACnB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAEzB,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC/B,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;YACxB,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,qBAAqB,CAAC,IAAY;QAChC,0CAA0C;QAC1C,6BAA6B;QAC7B,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC/B,QAAQ,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACnC,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,iBAAiB;IACjB,mBAAmB,CAAC,SAAiB;QACnC,2CAA2C;QAC3C,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC;QACxD,IAAI,cAAc,EAAE;YAClB,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,GAAG,cAAc,CAAC,SAAS,IAAI,cAAc,CAAC,QAAQ,MAAM,cAAc,CAAC,iBAAiB,EAAE;gBACrG,WAAW,EAAE,cAAc,CAAC,OAAO,IAAI,cAAc,CAAC,QAAQ,IAAI,sBAAsB;gBACxF,QAAQ,EAAE,cAAc,CAAC,eAAe,IAAI,EAAE;gBAC9C,GAAG,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,YAAY,cAAc,CAAC,IAAI,EAAE;aAChE,CAAC;YAEF,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;gBAC/B,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACzB,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACtB,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;IAC1D,CAAC;IAEO,WAAW,CAAC,KAAwB;QAC1C,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAE9C,IAAI,YAAY,GAAG,4BAA4B,CAAC;QAEhD,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE;YACtC,oCAAoC;YACpC,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;SACpC;aAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,YAAY,GAAG,yBAAyB,CAAC;SAC1C;aAAM,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE;YAC/B,YAAY,GAAG,sCAAsC,CAAC;SACvD;aAAM,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE;YAC/B,YAAY,GAAG,+BAA+B,CAAC;SAChD;aAAM,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE;YAC/B,YAAY,GAAG,uBAAuB,CAAC;SACxC;aAAM,IAAI,KAAK,CAAC,MAAM,IAAI,GAAG,EAAE;YAC9B,YAAY,GAAG,iDAAiD,CAAC;SAClE;QAED,OAAO,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;IACnD,CAAC;;;;;AAhZU,cAAc;IAH1B,UAAU,CAAC;QACV,UAAU,EAAE,MAAM;KACnB,CAAC;GACW,cAAc,CAiZ1B;SAjZY,cAAc\",\n      sourcesContent: [\"import { Injectable } from '@angular/core';\\r\\nimport { HttpClient, HttpParams, HttpHeaders, HttpErrorResponse } from '@angular/common/http';\\r\\nimport { Observable, BehaviorSubject, throwError } from 'rxjs';\\r\\nimport { map, tap, catchError } from 'rxjs/operators';\\r\\nimport { environment } from '../../../environments/environment';\\r\\nimport {\\r\\n  UserProfile,\\r\\n  ProfileUpdateRequest,\\r\\n  SkillEndorsementRequest,\\r\\n  ProfileViewRequest,\\r\\n  ProfileAnalytics,\\r\\n  ProfileSearchFilters,\\r\\n  ProfileSearchResult,\\r\\n  BlogPost,\\r\\n  Achievement,\\r\\n  Certification,\\r\\n  WorkExperience,\\r\\n  PortfolioItem,\\r\\n  ProfileSkill\\r\\n} from '../models/profile.models';\\r\\nimport {\\r\\n  ApiResponse,\\r\\n  ProfileSearchRequest,\\r\\n  CreateProfileRequest,\\r\\n  UpdateProfileRequest\\r\\n} from '../../shared/models/api-response.model';\\r\\n\\r\\n@Injectable({\\r\\n  providedIn: 'root'\\r\\n})\\r\\nexport class ProfileService {\\r\\n  private readonly API_URL = `${environment.apiUrl}/profile`;\\r\\n\\r\\n  private currentProfileSubject = new BehaviorSubject<UserProfile | null>(null);\\r\\n  public currentProfile$ = this.currentProfileSubject.asObservable();\\r\\n\\r\\n  constructor(private http: HttpClient) {}\\r\\n\\r\\n  /**\\r\\n   * Get HTTP headers with authentication token\\r\\n   */\\r\\n  private getHttpHeaders(): HttpHeaders {\\r\\n    const token = localStorage.getItem('authToken');\\r\\n    let headers = new HttpHeaders({\\r\\n      'Content-Type': 'application/json'\\r\\n    });\\r\\n\\r\\n    if (token) {\\r\\n      headers = headers.set('Authorization', `Bearer ${token}`);\\r\\n    }\\r\\n\\r\\n    return headers;\\r\\n  }\\r\\n\\r\\n  // Profile CRUD Operations\\r\\n  getProfile(identifier: string): Observable<UserProfile> {\\r\\n    // Check if identifier is a slug or ID\\r\\n    const url = isNaN(Number(identifier))\\r\\n      ? `${this.API_URL}/slug/${identifier}`\\r\\n      : `${this.API_URL}/${identifier}`;\\r\\n\\r\\n    return this.http.get<ApiResponse<UserProfile>>(url)\\r\\n      .pipe(\\r\\n        map(response => response.data!),\\r\\n        tap(profile => {\\r\\n          if (profile) {\\r\\n            this.currentProfileSubject.next(profile);\\r\\n            // Record profile view\\r\\n            this.recordProfileView({\\r\\n              profileId: profile.id,\\r\\n              referrer: document.referrer,\\r\\n              userAgent: navigator.userAgent\\r\\n            }).subscribe();\\r\\n          }\\r\\n        }),\\r\\n        catchError(this.handleError)\\r\\n      );\\r\\n  }\\r\\n\\r\\n  getCurrentUserProfile(): Observable<UserProfile> {\\r\\n    return this.http.get<ApiResponse<UserProfile>>(`${this.API_URL}/me`, {\\r\\n      headers: this.getHttpHeaders()\\r\\n    })\\r\\n      .pipe(\\r\\n        map(response => response.data!),\\r\\n        tap(profile => this.currentProfileSubject.next(profile)),\\r\\n        catchError(this.handleError)\\r\\n      );\\r\\n  }\\r\\n\\r\\n  updateProfile(updates: ProfileUpdateRequest): Observable<UserProfile> {\\r\\n    const updateRequest: UpdateProfileRequest = {\\r\\n      professionalTitle: updates.professionalTitle,\\r\\n      headline: updates.headline,\\r\\n      summary: updates.summary,\\r\\n      location: updates.location ? {\\r\\n        city: updates.location.city,\\r\\n        state: updates.location.state,\\r\\n        country: updates.location.country,\\r\\n        displayLocation: updates.location.displayLocation\\r\\n      } : undefined,\\r\\n      contactInfo: updates.contactInfo ? {\\r\\n        email: updates.contactInfo.email,\\r\\n        isEmailPublic: updates.contactInfo.isEmailPublic,\\r\\n        website: updates.contactInfo.website,\\r\\n        portfolioUrl: updates.contactInfo.portfolioUrl,\\r\\n        businessAddress: updates.contactInfo.businessAddress ? {\\r\\n          street: updates.contactInfo.businessAddress.street,\\r\\n          city: updates.contactInfo.businessAddress.city,\\r\\n          state: updates.contactInfo.businessAddress.state,\\r\\n          postalCode: updates.contactInfo.businessAddress.postalCode,\\r\\n          country: updates.contactInfo.businessAddress.country,\\r\\n          isPublic: updates.contactInfo.businessAddress.isPublic\\r\\n        } : undefined\\r\\n      } : undefined\\r\\n    };\\r\\n\\r\\n    return this.http.put<ApiResponse<UserProfile>>(`${this.API_URL}`, updateRequest, {\\r\\n      headers: this.getHttpHeaders()\\r\\n    })\\r\\n      .pipe(\\r\\n        map(response => response.data!),\\r\\n        tap(profile => this.currentProfileSubject.next(profile)),\\r\\n        catchError(this.handleError)\\r\\n      );\\r\\n  }\\r\\n\\r\\n  createProfile(profileData: Partial<UserProfile>): Observable<UserProfile> {\\r\\n    const createRequest: CreateProfileRequest = {\\r\\n      username: profileData.username!,\\r\\n      firstName: profileData.firstName!,\\r\\n      lastName: profileData.lastName!,\\r\\n      professionalTitle: profileData.professionalTitle,\\r\\n      headline: profileData.headline,\\r\\n      isPublic: profileData.isPublic ?? true\\r\\n    };\\r\\n\\r\\n    return this.http.post<ApiResponse<UserProfile>>(`${this.API_URL}`, createRequest, {\\r\\n      headers: this.getHttpHeaders()\\r\\n    })\\r\\n      .pipe(\\r\\n        map(response => response.data!),\\r\\n        tap(profile => this.currentProfileSubject.next(profile)),\\r\\n        catchError(this.handleError)\\r\\n      );\\r\\n  }\\r\\n\\r\\n  deleteProfile(): Observable<void> {\\r\\n    return this.http.delete<ApiResponse<boolean>>(`${this.API_URL}`, {\\r\\n      headers: this.getHttpHeaders()\\r\\n    })\\r\\n      .pipe(\\r\\n        map(() => void 0),\\r\\n        tap(() => this.currentProfileSubject.next(null)),\\r\\n        catchError(this.handleError)\\r\\n      );\\r\\n  }\\r\\n\\r\\n  // Profile Search\\r\\n  searchProfiles(filters: ProfileSearchFilters, page: number = 1, limit: number = 20): Observable<ProfileSearchResult> {\\r\\n    const searchRequest: ProfileSearchRequest = {\\r\\n      location: filters.location,\\r\\n      skills: filters.skills || [],\\r\\n      sortBy: filters.sortBy || 'relevance',\\r\\n      page: page,\\r\\n      pageSize: limit\\r\\n    };\\r\\n\\r\\n    return this.http.post<ApiResponse<ProfileSearchResult>>(`${this.API_URL}/search`, searchRequest, {\\r\\n      headers: this.getHttpHeaders()\\r\\n    })\\r\\n      .pipe(\\r\\n        map(response => response.data!),\\r\\n        catchError(this.handleError)\\r\\n      );\\r\\n  }\\r\\n\\r\\n  // Search profiles by term\\r\\n  searchProfilesByTerm(searchTerm: string, page: number = 1, pageSize: number = 20): Observable<ProfileSearchResult> {\\r\\n    const searchRequest: ProfileSearchRequest = {\\r\\n      searchTerm: searchTerm,\\r\\n      skills: [],\\r\\n      sortBy: 'relevance',\\r\\n      page: page,\\r\\n      pageSize: pageSize\\r\\n    };\\r\\n\\r\\n    return this.http.post<ApiResponse<ProfileSearchResult>>(`${this.API_URL}/search`, searchRequest, {\\r\\n      headers: this.getHttpHeaders()\\r\\n    })\\r\\n      .pipe(\\r\\n        map(response => response.data!),\\r\\n        catchError(this.handleError)\\r\\n      );\\r\\n  }\\r\\n\\r\\n  // Get public profiles\\r\\n  getPublicProfiles(page: number = 1, pageSize: number = 20): Observable<ProfileSearchResult> {\\r\\n    return this.http.get<ApiResponse<ProfileSearchResult>>(`${this.API_URL}/public?page=${page}&pageSize=${pageSize}`)\\r\\n      .pipe(\\r\\n        map(response => response.data!),\\r\\n        catchError(this.handleError)\\r\\n      );\\r\\n  }\\r\\n\\r\\n  // Skills Management (TODO: Implement in backend)\\r\\n  addSkill(skill: Omit<ProfileSkill, 'id' | 'endorsements' | 'isEndorsedByCurrentUser'>): Observable<ProfileSkill> {\\r\\n    // For now, return a mock skill until backend implements this\\r\\n    const newSkill: ProfileSkill = {\\r\\n      ...skill,\\r\\n      id: Date.now(),\\r\\n      endorsements: 0,\\r\\n      isEndorsedByCurrentUser: false\\r\\n    };\\r\\n    return new Observable(observer => {\\r\\n      observer.next(newSkill);\\r\\n      observer.complete();\\r\\n    });\\r\\n  }\\r\\n\\r\\n  updateSkill(skillId: number, updates: Partial<ProfileSkill>): Observable<ProfileSkill> {\\r\\n    // TODO: Implement in backend\\r\\n    return new Observable(observer => {\\r\\n      observer.next({ ...updates, id: skillId } as ProfileSkill);\\r\\n      observer.complete();\\r\\n    });\\r\\n  }\\r\\n\\r\\n  deleteSkill(skillId: number): Observable<void> {\\r\\n    // TODO: Implement in backend\\r\\n    return new Observable(observer => {\\r\\n      observer.next();\\r\\n      observer.complete();\\r\\n    });\\r\\n  }\\r\\n\\r\\n  endorseSkill(request: SkillEndorsementRequest): Observable<void> {\\r\\n    // TODO: Implement in backend\\r\\n    return new Observable(observer => {\\r\\n      observer.next();\\r\\n      observer.complete();\\r\\n    });\\r\\n  }\\r\\n\\r\\n  // Experience Management\\r\\n  addExperience(experience: Omit<WorkExperience, 'id'>): Observable<WorkExperience> {\\r\\n    return this.http.post<WorkExperience>(`${this.API_URL}/me/experiences`, experience)\\r\\n      .pipe(catchError(this.handleError));\\r\\n  }\\r\\n\\r\\n  updateExperience(experienceId: number, updates: Partial<WorkExperience>): Observable<WorkExperience> {\\r\\n    return this.http.put<WorkExperience>(`${this.API_URL}/me/experiences/${experienceId}`, updates)\\r\\n      .pipe(catchError(this.handleError));\\r\\n  }\\r\\n\\r\\n  deleteExperience(experienceId: number): Observable<void> {\\r\\n    return this.http.delete<void>(`${this.API_URL}/me/experiences/${experienceId}`)\\r\\n      .pipe(catchError(this.handleError));\\r\\n  }\\r\\n\\r\\n  // Portfolio Management\\r\\n  addPortfolioItem(item: Omit<PortfolioItem, 'id'>): Observable<PortfolioItem> {\\r\\n    return this.http.post<PortfolioItem>(`${this.API_URL}/me/portfolio`, item)\\r\\n      .pipe(catchError(this.handleError));\\r\\n  }\\r\\n\\r\\n  updatePortfolioItem(itemId: number, updates: Partial<PortfolioItem>): Observable<PortfolioItem> {\\r\\n    return this.http.put<PortfolioItem>(`${this.API_URL}/me/portfolio/${itemId}`, updates)\\r\\n      .pipe(catchError(this.handleError));\\r\\n  }\\r\\n\\r\\n  deletePortfolioItem(itemId: number): Observable<void> {\\r\\n    return this.http.delete<void>(`${this.API_URL}/me/portfolio/${itemId}`)\\r\\n      .pipe(catchError(this.handleError));\\r\\n  }\\r\\n\\r\\n  // Achievements & Certifications\\r\\n  addAchievement(achievement: Omit<Achievement, 'id'>): Observable<Achievement> {\\r\\n    return this.http.post<Achievement>(`${this.API_URL}/me/achievements`, achievement)\\r\\n      .pipe(catchError(this.handleError));\\r\\n  }\\r\\n\\r\\n  addCertification(certification: Omit<Certification, 'id'>): Observable<Certification> {\\r\\n    return this.http.post<Certification>(`${this.API_URL}/me/certifications`, certification)\\r\\n      .pipe(catchError(this.handleError));\\r\\n  }\\r\\n\\r\\n  // Blog Posts\\r\\n  getBlogPosts(profileId: number): Observable<BlogPost[]> {\\r\\n    // Blog posts are included in the profile data from the backend\\r\\n    // Return empty array for now, as they're part of the main profile\\r\\n    return new Observable(observer => {\\r\\n      observer.next([]);\\r\\n      observer.complete();\\r\\n    });\\r\\n  }\\r\\n\\r\\n  // Analytics\\r\\n  getProfileAnalytics(): Observable<ProfileAnalytics> {\\r\\n    // For now, return mock analytics since backend doesn't have this endpoint yet\\r\\n    const mockAnalytics: ProfileAnalytics = {\\r\\n      profileViews: 0,\\r\\n      uniqueVisitors: 0,\\r\\n      viewsThisMonth: 0,\\r\\n      viewsThisWeek: 0,\\r\\n      topReferrers: [],\\r\\n      skillEndorsements: 0,\\r\\n      blogPostViews: 0,\\r\\n      contactButtonClicks: 0\\r\\n    };\\r\\n    return new Observable(observer => {\\r\\n      observer.next(mockAnalytics);\\r\\n      observer.complete();\\r\\n    });\\r\\n  }\\r\\n\\r\\n  recordProfileView(request: ProfileViewRequest): Observable<void> {\\r\\n    return this.http.post<ApiResponse<boolean>>(`${this.API_URL}/view`, request, {\\r\\n      headers: this.getHttpHeaders()\\r\\n    })\\r\\n      .pipe(\\r\\n        map(() => void 0),\\r\\n        catchError(this.handleError)\\r\\n      );\\r\\n  }\\r\\n\\r\\n  // File Upload\\r\\n  uploadProfilePhoto(file: File): Observable<{ url: string }> {\\r\\n    const formData = new FormData();\\r\\n    formData.append('file', file);\\r\\n\\r\\n    const token = localStorage.getItem('authToken');\\r\\n    let headers = new HttpHeaders();\\r\\n    if (token) {\\r\\n      headers = headers.set('Authorization', `Bearer ${token}`);\\r\\n    }\\r\\n\\r\\n    return this.http.post<ApiResponse<string>>(`${this.API_URL}/upload/profile-photo`, formData, {\\r\\n      headers: headers\\r\\n    })\\r\\n      .pipe(\\r\\n        map(response => ({ url: response.data! })),\\r\\n        catchError(this.handleError)\\r\\n      );\\r\\n  }\\r\\n\\r\\n  uploadCoverPhoto(file: File): Observable<{ url: string }> {\\r\\n    const formData = new FormData();\\r\\n    formData.append('file', file);\\r\\n\\r\\n    const token = localStorage.getItem('authToken');\\r\\n    let headers = new HttpHeaders();\\r\\n    if (token) {\\r\\n      headers = headers.set('Authorization', `Bearer ${token}`);\\r\\n    }\\r\\n\\r\\n    return this.http.post<ApiResponse<string>>(`${this.API_URL}/upload/cover-photo`, formData, {\\r\\n      headers: headers\\r\\n    })\\r\\n      .pipe(\\r\\n        map(response => ({ url: response.data! })),\\r\\n        catchError(this.handleError)\\r\\n      );\\r\\n  }\\r\\n\\r\\n  // Utility Methods\\r\\n  generateProfileSlug(firstName: string, lastName: string): Observable<{ slug: string }> {\\r\\n    // Generate slug client-side for now\\r\\n    const slug = `${firstName.toLowerCase()}-${lastName.toLowerCase()}`\\r\\n      .replace(/[^a-z0-9-]/g, '-')\\r\\n      .replace(/-+/g, '-')\\r\\n      .replace(/^-|-$/g, '');\\r\\n\\r\\n    return new Observable(observer => {\\r\\n      observer.next({ slug });\\r\\n      observer.complete();\\r\\n    });\\r\\n  }\\r\\n\\r\\n  checkSlugAvailability(slug: string): Observable<{ available: boolean }> {\\r\\n    // For now, assume all slugs are available\\r\\n    // TODO: Implement in backend\\r\\n    return new Observable(observer => {\\r\\n      observer.next({ available: true });\\r\\n      observer.complete();\\r\\n    });\\r\\n  }\\r\\n\\r\\n  // Social Sharing\\r\\n  getProfileShareData(profileId: number): Observable<{ title: string; description: string; imageUrl: string; url: string }> {\\r\\n    // Generate share data from current profile\\r\\n    const currentProfile = this.currentProfileSubject.value;\\r\\n    if (currentProfile) {\\r\\n      const shareData = {\\r\\n        title: `${currentProfile.firstName} ${currentProfile.lastName} - ${currentProfile.professionalTitle}`,\\r\\n        description: currentProfile.summary || currentProfile.headline || 'Professional profile',\\r\\n        imageUrl: currentProfile.profilePhotoUrl || '',\\r\\n        url: `${window.location.origin}/profile/${currentProfile.slug}`\\r\\n      };\\r\\n\\r\\n      return new Observable(observer => {\\r\\n        observer.next(shareData);\\r\\n        observer.complete();\\r\\n      });\\r\\n    }\\r\\n\\r\\n    return throwError(() => new Error('Profile not found'));\\r\\n  }\\r\\n\\r\\n  private handleError(error: HttpErrorResponse): Observable<never> {\\r\\n    console.error('ProfileService error:', error);\\r\\n\\r\\n    let errorMessage = '\\u0412\\u044A\\u0437\\u043D\\u0438\\u043A\\u043D\\u0430 \\u043D\\u0435\\u043E\\u0447\\u0430\\u043A\\u0432\\u0430\\u043D\\u0430 \\u0433\\u0440\\u0435\\u0448\\u043A\\u0430';\\r\\n\\r\\n    if (error.error && error.error.message) {\\r\\n      // Backend returned an error message\\r\\n      errorMessage = error.error.message;\\r\\n    } else if (error.status === 0) {\\r\\n      errorMessage = '\\u041D\\u044F\\u043C\\u0430 \\u0432\\u0440\\u044A\\u0437\\u043A\\u0430 \\u0441\\u044A\\u0441 \\u0441\\u044A\\u0440\\u0432\\u044A\\u0440\\u0430';\\r\\n    } else if (error.status === 401) {\\r\\n      errorMessage = '\\u041D\\u0435 \\u0441\\u0442\\u0435 \\u0443\\u043F\\u044A\\u043B\\u043D\\u043E\\u043C\\u043E\\u0449\\u0435\\u043D\\u0438 \\u0437\\u0430 \\u0442\\u0430\\u0437\\u0438 \\u043E\\u043F\\u0435\\u0440\\u0430\\u0446\\u0438\\u044F';\\r\\n    } else if (error.status === 403) {\\r\\n      errorMessage = '\\u041D\\u044F\\u043C\\u0430\\u0442\\u0435 \\u043F\\u0440\\u0430\\u0432\\u0430 \\u0437\\u0430 \\u0442\\u0430\\u0437\\u0438 \\u043E\\u043F\\u0435\\u0440\\u0430\\u0446\\u0438\\u044F';\\r\\n    } else if (error.status === 404) {\\r\\n      errorMessage = '\\u041F\\u0440\\u043E\\u0444\\u0438\\u043B\\u044A\\u0442 \\u043D\\u0435 \\u0435 \\u043D\\u0430\\u043C\\u0435\\u0440\\u0435\\u043D';\\r\\n    } else if (error.status >= 500) {\\r\\n      errorMessage = '\\u0421\\u044A\\u0440\\u0432\\u044A\\u0440\\u043D\\u0430 \\u0433\\u0440\\u0435\\u0448\\u043A\\u0430. \\u041C\\u043E\\u043B\\u044F, \\u043E\\u043F\\u0438\\u0442\\u0430\\u0439\\u0442\\u0435 \\u043E\\u0442\\u043D\\u043E\\u0432\\u043E \\u043F\\u043E-\\u043A\\u044A\\u0441\\u043D\\u043E';\\r\\n    }\\r\\n\\r\\n    return throwError(() => new Error(errorMessage));\\r\\n  }\\r\\n}\\r\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"990d97d21c90fa7ba44436b44c535bffcb6d409a\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_1b9uogtqsb = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_1b9uogtqsb();\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, BehaviorSubject, throwError } from 'rxjs';\nimport { map, tap, catchError } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\ncov_1b9uogtqsb().s[0]++;\nlet ProfileService = class ProfileService {\n  constructor(http) {\n    cov_1b9uogtqsb().f[0]++;\n    cov_1b9uogtqsb().s[1]++;\n    this.http = http;\n    cov_1b9uogtqsb().s[2]++;\n    this.API_URL = `${environment.apiUrl}/profile`;\n    cov_1b9uogtqsb().s[3]++;\n    this.currentProfileSubject = new BehaviorSubject(null);\n    cov_1b9uogtqsb().s[4]++;\n    this.currentProfile$ = this.currentProfileSubject.asObservable();\n  }\n  /**\r\n   * Get HTTP headers with authentication token\r\n   */\n  getHttpHeaders() {\n    cov_1b9uogtqsb().f[1]++;\n    const token = (cov_1b9uogtqsb().s[5]++, localStorage.getItem('authToken'));\n    let headers = (cov_1b9uogtqsb().s[6]++, new HttpHeaders({\n      'Content-Type': 'application/json'\n    }));\n    cov_1b9uogtqsb().s[7]++;\n    if (token) {\n      cov_1b9uogtqsb().b[0][0]++;\n      cov_1b9uogtqsb().s[8]++;\n      headers = headers.set('Authorization', `Bearer ${token}`);\n    } else {\n      cov_1b9uogtqsb().b[0][1]++;\n    }\n    cov_1b9uogtqsb().s[9]++;\n    return headers;\n  }\n  // Profile CRUD Operations\n  getProfile(identifier) {\n    cov_1b9uogtqsb().f[2]++;\n    // Check if identifier is a slug or ID\n    const url = (cov_1b9uogtqsb().s[10]++, isNaN(Number(identifier)) ? (cov_1b9uogtqsb().b[1][0]++, `${this.API_URL}/slug/${identifier}`) : (cov_1b9uogtqsb().b[1][1]++, `${this.API_URL}/${identifier}`));\n    cov_1b9uogtqsb().s[11]++;\n    return this.http.get(url).pipe(map(response => {\n      cov_1b9uogtqsb().f[3]++;\n      cov_1b9uogtqsb().s[12]++;\n      return response.data;\n    }), tap(profile => {\n      cov_1b9uogtqsb().f[4]++;\n      cov_1b9uogtqsb().s[13]++;\n      if (profile) {\n        cov_1b9uogtqsb().b[2][0]++;\n        cov_1b9uogtqsb().s[14]++;\n        this.currentProfileSubject.next(profile);\n        // Record profile view\n        cov_1b9uogtqsb().s[15]++;\n        this.recordProfileView({\n          profileId: profile.id,\n          referrer: document.referrer,\n          userAgent: navigator.userAgent\n        }).subscribe();\n      } else {\n        cov_1b9uogtqsb().b[2][1]++;\n      }\n    }), catchError(this.handleError));\n  }\n  getCurrentUserProfile() {\n    cov_1b9uogtqsb().f[5]++;\n    cov_1b9uogtqsb().s[16]++;\n    return this.http.get(`${this.API_URL}/me`, {\n      headers: this.getHttpHeaders()\n    }).pipe(map(response => {\n      cov_1b9uogtqsb().f[6]++;\n      cov_1b9uogtqsb().s[17]++;\n      return response.data;\n    }), tap(profile => {\n      cov_1b9uogtqsb().f[7]++;\n      cov_1b9uogtqsb().s[18]++;\n      return this.currentProfileSubject.next(profile);\n    }), catchError(this.handleError));\n  }\n  updateProfile(updates) {\n    cov_1b9uogtqsb().f[8]++;\n    const updateRequest = (cov_1b9uogtqsb().s[19]++, {\n      professionalTitle: updates.professionalTitle,\n      headline: updates.headline,\n      summary: updates.summary,\n      location: updates.location ? (cov_1b9uogtqsb().b[3][0]++, {\n        city: updates.location.city,\n        state: updates.location.state,\n        country: updates.location.country,\n        displayLocation: updates.location.displayLocation\n      }) : (cov_1b9uogtqsb().b[3][1]++, undefined),\n      contactInfo: updates.contactInfo ? (cov_1b9uogtqsb().b[4][0]++, {\n        email: updates.contactInfo.email,\n        isEmailPublic: updates.contactInfo.isEmailPublic,\n        website: updates.contactInfo.website,\n        portfolioUrl: updates.contactInfo.portfolioUrl,\n        businessAddress: updates.contactInfo.businessAddress ? (cov_1b9uogtqsb().b[5][0]++, {\n          street: updates.contactInfo.businessAddress.street,\n          city: updates.contactInfo.businessAddress.city,\n          state: updates.contactInfo.businessAddress.state,\n          postalCode: updates.contactInfo.businessAddress.postalCode,\n          country: updates.contactInfo.businessAddress.country,\n          isPublic: updates.contactInfo.businessAddress.isPublic\n        }) : (cov_1b9uogtqsb().b[5][1]++, undefined)\n      }) : (cov_1b9uogtqsb().b[4][1]++, undefined)\n    });\n    cov_1b9uogtqsb().s[20]++;\n    return this.http.put(`${this.API_URL}`, updateRequest, {\n      headers: this.getHttpHeaders()\n    }).pipe(map(response => {\n      cov_1b9uogtqsb().f[9]++;\n      cov_1b9uogtqsb().s[21]++;\n      return response.data;\n    }), tap(profile => {\n      cov_1b9uogtqsb().f[10]++;\n      cov_1b9uogtqsb().s[22]++;\n      return this.currentProfileSubject.next(profile);\n    }), catchError(this.handleError));\n  }\n  createProfile(profileData) {\n    cov_1b9uogtqsb().f[11]++;\n    const createRequest = (cov_1b9uogtqsb().s[23]++, {\n      username: profileData.username,\n      firstName: profileData.firstName,\n      lastName: profileData.lastName,\n      professionalTitle: profileData.professionalTitle,\n      headline: profileData.headline,\n      isPublic: (cov_1b9uogtqsb().b[6][0]++, profileData.isPublic) ?? (cov_1b9uogtqsb().b[6][1]++, true)\n    });\n    cov_1b9uogtqsb().s[24]++;\n    return this.http.post(`${this.API_URL}`, createRequest, {\n      headers: this.getHttpHeaders()\n    }).pipe(map(response => {\n      cov_1b9uogtqsb().f[12]++;\n      cov_1b9uogtqsb().s[25]++;\n      return response.data;\n    }), tap(profile => {\n      cov_1b9uogtqsb().f[13]++;\n      cov_1b9uogtqsb().s[26]++;\n      return this.currentProfileSubject.next(profile);\n    }), catchError(this.handleError));\n  }\n  deleteProfile() {\n    cov_1b9uogtqsb().f[14]++;\n    cov_1b9uogtqsb().s[27]++;\n    return this.http.delete(`${this.API_URL}`, {\n      headers: this.getHttpHeaders()\n    }).pipe(map(() => {\n      cov_1b9uogtqsb().f[15]++;\n      cov_1b9uogtqsb().s[28]++;\n      return void 0;\n    }), tap(() => {\n      cov_1b9uogtqsb().f[16]++;\n      cov_1b9uogtqsb().s[29]++;\n      return this.currentProfileSubject.next(null);\n    }), catchError(this.handleError));\n  }\n  // Profile Search\n  searchProfiles(filters, page = (cov_1b9uogtqsb().b[7][0]++, 1), limit = (cov_1b9uogtqsb().b[8][0]++, 20)) {\n    cov_1b9uogtqsb().f[17]++;\n    const searchRequest = (cov_1b9uogtqsb().s[30]++, {\n      location: filters.location,\n      skills: (cov_1b9uogtqsb().b[9][0]++, filters.skills) || (cov_1b9uogtqsb().b[9][1]++, []),\n      sortBy: (cov_1b9uogtqsb().b[10][0]++, filters.sortBy) || (cov_1b9uogtqsb().b[10][1]++, 'relevance'),\n      page: page,\n      pageSize: limit\n    });\n    cov_1b9uogtqsb().s[31]++;\n    return this.http.post(`${this.API_URL}/search`, searchRequest, {\n      headers: this.getHttpHeaders()\n    }).pipe(map(response => {\n      cov_1b9uogtqsb().f[18]++;\n      cov_1b9uogtqsb().s[32]++;\n      return response.data;\n    }), catchError(this.handleError));\n  }\n  // Search profiles by term\n  searchProfilesByTerm(searchTerm, page = (cov_1b9uogtqsb().b[11][0]++, 1), pageSize = (cov_1b9uogtqsb().b[12][0]++, 20)) {\n    cov_1b9uogtqsb().f[19]++;\n    const searchRequest = (cov_1b9uogtqsb().s[33]++, {\n      searchTerm: searchTerm,\n      skills: [],\n      sortBy: 'relevance',\n      page: page,\n      pageSize: pageSize\n    });\n    cov_1b9uogtqsb().s[34]++;\n    return this.http.post(`${this.API_URL}/search`, searchRequest, {\n      headers: this.getHttpHeaders()\n    }).pipe(map(response => {\n      cov_1b9uogtqsb().f[20]++;\n      cov_1b9uogtqsb().s[35]++;\n      return response.data;\n    }), catchError(this.handleError));\n  }\n  // Get public profiles\n  getPublicProfiles(page = (cov_1b9uogtqsb().b[13][0]++, 1), pageSize = (cov_1b9uogtqsb().b[14][0]++, 20)) {\n    cov_1b9uogtqsb().f[21]++;\n    cov_1b9uogtqsb().s[36]++;\n    return this.http.get(`${this.API_URL}/public?page=${page}&pageSize=${pageSize}`).pipe(map(response => {\n      cov_1b9uogtqsb().f[22]++;\n      cov_1b9uogtqsb().s[37]++;\n      return response.data;\n    }), catchError(this.handleError));\n  }\n  // Skills Management (TODO: Implement in backend)\n  addSkill(skill) {\n    cov_1b9uogtqsb().f[23]++;\n    // For now, return a mock skill until backend implements this\n    const newSkill = (cov_1b9uogtqsb().s[38]++, {\n      ...skill,\n      id: Date.now(),\n      endorsements: 0,\n      isEndorsedByCurrentUser: false\n    });\n    cov_1b9uogtqsb().s[39]++;\n    return new Observable(observer => {\n      cov_1b9uogtqsb().f[24]++;\n      cov_1b9uogtqsb().s[40]++;\n      observer.next(newSkill);\n      cov_1b9uogtqsb().s[41]++;\n      observer.complete();\n    });\n  }\n  updateSkill(skillId, updates) {\n    cov_1b9uogtqsb().f[25]++;\n    cov_1b9uogtqsb().s[42]++;\n    // TODO: Implement in backend\n    return new Observable(observer => {\n      cov_1b9uogtqsb().f[26]++;\n      cov_1b9uogtqsb().s[43]++;\n      observer.next({\n        ...updates,\n        id: skillId\n      });\n      cov_1b9uogtqsb().s[44]++;\n      observer.complete();\n    });\n  }\n  deleteSkill(skillId) {\n    cov_1b9uogtqsb().f[27]++;\n    cov_1b9uogtqsb().s[45]++;\n    // TODO: Implement in backend\n    return new Observable(observer => {\n      cov_1b9uogtqsb().f[28]++;\n      cov_1b9uogtqsb().s[46]++;\n      observer.next();\n      cov_1b9uogtqsb().s[47]++;\n      observer.complete();\n    });\n  }\n  endorseSkill(request) {\n    cov_1b9uogtqsb().f[29]++;\n    cov_1b9uogtqsb().s[48]++;\n    // TODO: Implement in backend\n    return new Observable(observer => {\n      cov_1b9uogtqsb().f[30]++;\n      cov_1b9uogtqsb().s[49]++;\n      observer.next();\n      cov_1b9uogtqsb().s[50]++;\n      observer.complete();\n    });\n  }\n  // Experience Management\n  addExperience(experience) {\n    cov_1b9uogtqsb().f[31]++;\n    cov_1b9uogtqsb().s[51]++;\n    return this.http.post(`${this.API_URL}/me/experiences`, experience).pipe(catchError(this.handleError));\n  }\n  updateExperience(experienceId, updates) {\n    cov_1b9uogtqsb().f[32]++;\n    cov_1b9uogtqsb().s[52]++;\n    return this.http.put(`${this.API_URL}/me/experiences/${experienceId}`, updates).pipe(catchError(this.handleError));\n  }\n  deleteExperience(experienceId) {\n    cov_1b9uogtqsb().f[33]++;\n    cov_1b9uogtqsb().s[53]++;\n    return this.http.delete(`${this.API_URL}/me/experiences/${experienceId}`).pipe(catchError(this.handleError));\n  }\n  // Portfolio Management\n  addPortfolioItem(item) {\n    cov_1b9uogtqsb().f[34]++;\n    cov_1b9uogtqsb().s[54]++;\n    return this.http.post(`${this.API_URL}/me/portfolio`, item).pipe(catchError(this.handleError));\n  }\n  updatePortfolioItem(itemId, updates) {\n    cov_1b9uogtqsb().f[35]++;\n    cov_1b9uogtqsb().s[55]++;\n    return this.http.put(`${this.API_URL}/me/portfolio/${itemId}`, updates).pipe(catchError(this.handleError));\n  }\n  deletePortfolioItem(itemId) {\n    cov_1b9uogtqsb().f[36]++;\n    cov_1b9uogtqsb().s[56]++;\n    return this.http.delete(`${this.API_URL}/me/portfolio/${itemId}`).pipe(catchError(this.handleError));\n  }\n  // Achievements & Certifications\n  addAchievement(achievement) {\n    cov_1b9uogtqsb().f[37]++;\n    cov_1b9uogtqsb().s[57]++;\n    return this.http.post(`${this.API_URL}/me/achievements`, achievement).pipe(catchError(this.handleError));\n  }\n  addCertification(certification) {\n    cov_1b9uogtqsb().f[38]++;\n    cov_1b9uogtqsb().s[58]++;\n    return this.http.post(`${this.API_URL}/me/certifications`, certification).pipe(catchError(this.handleError));\n  }\n  // Blog Posts\n  getBlogPosts(profileId) {\n    cov_1b9uogtqsb().f[39]++;\n    cov_1b9uogtqsb().s[59]++;\n    // Blog posts are included in the profile data from the backend\n    // Return empty array for now, as they're part of the main profile\n    return new Observable(observer => {\n      cov_1b9uogtqsb().f[40]++;\n      cov_1b9uogtqsb().s[60]++;\n      observer.next([]);\n      cov_1b9uogtqsb().s[61]++;\n      observer.complete();\n    });\n  }\n  // Analytics\n  getProfileAnalytics() {\n    cov_1b9uogtqsb().f[41]++;\n    // For now, return mock analytics since backend doesn't have this endpoint yet\n    const mockAnalytics = (cov_1b9uogtqsb().s[62]++, {\n      profileViews: 0,\n      uniqueVisitors: 0,\n      viewsThisMonth: 0,\n      viewsThisWeek: 0,\n      topReferrers: [],\n      skillEndorsements: 0,\n      blogPostViews: 0,\n      contactButtonClicks: 0\n    });\n    cov_1b9uogtqsb().s[63]++;\n    return new Observable(observer => {\n      cov_1b9uogtqsb().f[42]++;\n      cov_1b9uogtqsb().s[64]++;\n      observer.next(mockAnalytics);\n      cov_1b9uogtqsb().s[65]++;\n      observer.complete();\n    });\n  }\n  recordProfileView(request) {\n    cov_1b9uogtqsb().f[43]++;\n    cov_1b9uogtqsb().s[66]++;\n    return this.http.post(`${this.API_URL}/view`, request, {\n      headers: this.getHttpHeaders()\n    }).pipe(map(() => {\n      cov_1b9uogtqsb().f[44]++;\n      cov_1b9uogtqsb().s[67]++;\n      return void 0;\n    }), catchError(this.handleError));\n  }\n  // File Upload\n  uploadProfilePhoto(file) {\n    cov_1b9uogtqsb().f[45]++;\n    const formData = (cov_1b9uogtqsb().s[68]++, new FormData());\n    cov_1b9uogtqsb().s[69]++;\n    formData.append('file', file);\n    const token = (cov_1b9uogtqsb().s[70]++, localStorage.getItem('authToken'));\n    let headers = (cov_1b9uogtqsb().s[71]++, new HttpHeaders());\n    cov_1b9uogtqsb().s[72]++;\n    if (token) {\n      cov_1b9uogtqsb().b[15][0]++;\n      cov_1b9uogtqsb().s[73]++;\n      headers = headers.set('Authorization', `Bearer ${token}`);\n    } else {\n      cov_1b9uogtqsb().b[15][1]++;\n    }\n    cov_1b9uogtqsb().s[74]++;\n    return this.http.post(`${this.API_URL}/upload/profile-photo`, formData, {\n      headers: headers\n    }).pipe(map(response => {\n      cov_1b9uogtqsb().f[46]++;\n      cov_1b9uogtqsb().s[75]++;\n      return {\n        url: response.data\n      };\n    }), catchError(this.handleError));\n  }\n  uploadCoverPhoto(file) {\n    cov_1b9uogtqsb().f[47]++;\n    const formData = (cov_1b9uogtqsb().s[76]++, new FormData());\n    cov_1b9uogtqsb().s[77]++;\n    formData.append('file', file);\n    const token = (cov_1b9uogtqsb().s[78]++, localStorage.getItem('authToken'));\n    let headers = (cov_1b9uogtqsb().s[79]++, new HttpHeaders());\n    cov_1b9uogtqsb().s[80]++;\n    if (token) {\n      cov_1b9uogtqsb().b[16][0]++;\n      cov_1b9uogtqsb().s[81]++;\n      headers = headers.set('Authorization', `Bearer ${token}`);\n    } else {\n      cov_1b9uogtqsb().b[16][1]++;\n    }\n    cov_1b9uogtqsb().s[82]++;\n    return this.http.post(`${this.API_URL}/upload/cover-photo`, formData, {\n      headers: headers\n    }).pipe(map(response => {\n      cov_1b9uogtqsb().f[48]++;\n      cov_1b9uogtqsb().s[83]++;\n      return {\n        url: response.data\n      };\n    }), catchError(this.handleError));\n  }\n  // Utility Methods\n  generateProfileSlug(firstName, lastName) {\n    cov_1b9uogtqsb().f[49]++;\n    // Generate slug client-side for now\n    const slug = (cov_1b9uogtqsb().s[84]++, `${firstName.toLowerCase()}-${lastName.toLowerCase()}`.replace(/[^a-z0-9-]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, ''));\n    cov_1b9uogtqsb().s[85]++;\n    return new Observable(observer => {\n      cov_1b9uogtqsb().f[50]++;\n      cov_1b9uogtqsb().s[86]++;\n      observer.next({\n        slug\n      });\n      cov_1b9uogtqsb().s[87]++;\n      observer.complete();\n    });\n  }\n  checkSlugAvailability(slug) {\n    cov_1b9uogtqsb().f[51]++;\n    cov_1b9uogtqsb().s[88]++;\n    // For now, assume all slugs are available\n    // TODO: Implement in backend\n    return new Observable(observer => {\n      cov_1b9uogtqsb().f[52]++;\n      cov_1b9uogtqsb().s[89]++;\n      observer.next({\n        available: true\n      });\n      cov_1b9uogtqsb().s[90]++;\n      observer.complete();\n    });\n  }\n  // Social Sharing\n  getProfileShareData(profileId) {\n    cov_1b9uogtqsb().f[53]++;\n    // Generate share data from current profile\n    const currentProfile = (cov_1b9uogtqsb().s[91]++, this.currentProfileSubject.value);\n    cov_1b9uogtqsb().s[92]++;\n    if (currentProfile) {\n      cov_1b9uogtqsb().b[17][0]++;\n      const shareData = (cov_1b9uogtqsb().s[93]++, {\n        title: `${currentProfile.firstName} ${currentProfile.lastName} - ${currentProfile.professionalTitle}`,\n        description: (cov_1b9uogtqsb().b[18][0]++, currentProfile.summary) || (cov_1b9uogtqsb().b[18][1]++, currentProfile.headline) || (cov_1b9uogtqsb().b[18][2]++, 'Professional profile'),\n        imageUrl: (cov_1b9uogtqsb().b[19][0]++, currentProfile.profilePhotoUrl) || (cov_1b9uogtqsb().b[19][1]++, ''),\n        url: `${window.location.origin}/profile/${currentProfile.slug}`\n      });\n      cov_1b9uogtqsb().s[94]++;\n      return new Observable(observer => {\n        cov_1b9uogtqsb().f[54]++;\n        cov_1b9uogtqsb().s[95]++;\n        observer.next(shareData);\n        cov_1b9uogtqsb().s[96]++;\n        observer.complete();\n      });\n    } else {\n      cov_1b9uogtqsb().b[17][1]++;\n    }\n    cov_1b9uogtqsb().s[97]++;\n    return throwError(() => {\n      cov_1b9uogtqsb().f[55]++;\n      cov_1b9uogtqsb().s[98]++;\n      return new Error('Profile not found');\n    });\n  }\n  handleError(error) {\n    cov_1b9uogtqsb().f[56]++;\n    cov_1b9uogtqsb().s[99]++;\n    console.error('ProfileService error:', error);\n    let errorMessage = (cov_1b9uogtqsb().s[100]++, 'Възникна неочаквана грешка');\n    cov_1b9uogtqsb().s[101]++;\n    if ((cov_1b9uogtqsb().b[21][0]++, error.error) && (cov_1b9uogtqsb().b[21][1]++, error.error.message)) {\n      cov_1b9uogtqsb().b[20][0]++;\n      cov_1b9uogtqsb().s[102]++;\n      // Backend returned an error message\n      errorMessage = error.error.message;\n    } else {\n      cov_1b9uogtqsb().b[20][1]++;\n      cov_1b9uogtqsb().s[103]++;\n      if (error.status === 0) {\n        cov_1b9uogtqsb().b[22][0]++;\n        cov_1b9uogtqsb().s[104]++;\n        errorMessage = 'Няма връзка със сървъра';\n      } else {\n        cov_1b9uogtqsb().b[22][1]++;\n        cov_1b9uogtqsb().s[105]++;\n        if (error.status === 401) {\n          cov_1b9uogtqsb().b[23][0]++;\n          cov_1b9uogtqsb().s[106]++;\n          errorMessage = 'Не сте упълномощени за тази операция';\n        } else {\n          cov_1b9uogtqsb().b[23][1]++;\n          cov_1b9uogtqsb().s[107]++;\n          if (error.status === 403) {\n            cov_1b9uogtqsb().b[24][0]++;\n            cov_1b9uogtqsb().s[108]++;\n            errorMessage = 'Нямате права за тази операция';\n          } else {\n            cov_1b9uogtqsb().b[24][1]++;\n            cov_1b9uogtqsb().s[109]++;\n            if (error.status === 404) {\n              cov_1b9uogtqsb().b[25][0]++;\n              cov_1b9uogtqsb().s[110]++;\n              errorMessage = 'Профилът не е намерен';\n            } else {\n              cov_1b9uogtqsb().b[25][1]++;\n              cov_1b9uogtqsb().s[111]++;\n              if (error.status >= 500) {\n                cov_1b9uogtqsb().b[26][0]++;\n                cov_1b9uogtqsb().s[112]++;\n                errorMessage = 'Сървърна грешка. Моля, опитайте отново по-късно';\n              } else {\n                cov_1b9uogtqsb().b[26][1]++;\n              }\n            }\n          }\n        }\n      }\n    }\n    cov_1b9uogtqsb().s[113]++;\n    return throwError(() => {\n      cov_1b9uogtqsb().f[57]++;\n      cov_1b9uogtqsb().s[114]++;\n      return new Error(errorMessage);\n    });\n  }\n  static {\n    cov_1b9uogtqsb().s[115]++;\n    this.ctorParameters = () => {\n      cov_1b9uogtqsb().f[58]++;\n      cov_1b9uogtqsb().s[116]++;\n      return [{\n        type: HttpClient\n      }];\n    };\n  }\n};\ncov_1b9uogtqsb().s[117]++;\nProfileService = __decorate([Injectable({\n  providedIn: 'root'\n})], ProfileService);\nexport { ProfileService };", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwCK;IAAA;MAAA;IAAA;EAAA;EAAA;AAAA;AAAA;;AAxCL,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,EAAcC,WAAW,QAA2B,sBAAsB;AAC7F,SAASC,UAAU,EAAEC,eAAe,EAAEC,UAAU,QAAQ,MAAM;AAC9D,SAASC,GAAG,EAAEC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;AACrD,SAASC,WAAW,QAAQ,mCAAmC;AAAC;AA0BzD,IAAMC,cAAc,GAApB,MAAMA,cAAc;EAMzBC,YAAoBC,IAAgB;IAAA;IAAA;IAAhB,SAAI,GAAJA,IAAI;IAAY;IALnB,YAAO,GAAG,GAAGH,WAAW,CAACI,MAAM,UAAU;IAAC;IAEnD,0BAAqB,GAAG,IAAIT,eAAe,CAAqB,IAAI,CAAC;IAAC;IACvE,oBAAe,GAAG,IAAI,CAACU,qBAAqB,CAACC,YAAY,EAAE;EAE3B;EAEvC;;;EAGQC,cAAc;IAAA;IACpB,MAAMC,KAAK,6BAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,IAAIC,OAAO,6BAAG,IAAIlB,WAAW,CAAC;MAC5B,cAAc,EAAE;KACjB,CAAC;IAAC;IAEH,IAAIe,KAAK,EAAE;MAAA;MAAA;MACTG,OAAO,GAAGA,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUJ,KAAK,EAAE,CAAC;KAC1D;MAAA;IAAA;IAAA;IAED,OAAOG,OAAO;EAChB;EAEA;EACAE,UAAU,CAACC,UAAkB;IAAA;IAC3B;IACA,MAAMC,GAAG,8BAAGC,KAAK,CAACC,MAAM,CAACH,UAAU,CAAC,CAAC,gCACjC,GAAG,IAAI,CAACI,OAAO,SAASJ,UAAU,EAAE,iCACpC,GAAG,IAAI,CAACI,OAAO,IAAIJ,UAAU,EAAE;IAAC;IAEpC,OAAO,IAAI,CAACX,IAAI,CAACgB,GAAG,CAA2BJ,GAAG,CAAC,CAChDK,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAI;MAAA;MAAA;MAAA,eAAQ,CAACC,IAAK;IAAL,CAAK,CAAC,EAC/BxB,GAAG,CAACyB,OAAO,IAAG;MAAA;MAAA;MACZ,IAAIA,OAAO,EAAE;QAAA;QAAA;QACX,IAAI,CAAClB,qBAAqB,CAACmB,IAAI,CAACD,OAAO,CAAC;QACxC;QAAA;QACA,IAAI,CAACE,iBAAiB,CAAC;UACrBC,SAAS,EAAEH,OAAO,CAACI,EAAE;UACrBC,QAAQ,EAAEC,QAAQ,CAACD,QAAQ;UAC3BE,SAAS,EAAEC,SAAS,CAACD;SACtB,CAAC,CAACE,SAAS,EAAE;OACf;QAAA;MAAA;IACH,CAAC,CAAC,EACFjC,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEAC,qBAAqB;IAAA;IAAA;IACnB,OAAO,IAAI,CAAC/B,IAAI,CAACgB,GAAG,CAA2B,GAAG,IAAI,CAACD,OAAO,KAAK,EAAE;MACnEP,OAAO,EAAE,IAAI,CAACJ,cAAc;KAC7B,CAAC,CACCa,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAI;MAAA;MAAA;MAAA,eAAQ,CAACC,IAAK;IAAL,CAAK,CAAC,EAC/BxB,GAAG,CAACyB,OAAO,IAAI;MAAA;MAAA;MAAA,WAAI,CAAClB,qBAAqB,CAACmB,IAAI,CAACD,OAAO,CAAC;IAAD,CAAC,CAAC,EACxDxB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEAE,aAAa,CAACC,OAA6B;IAAA;IACzC,MAAMC,aAAa,8BAAyB;MAC1CC,iBAAiB,EAAEF,OAAO,CAACE,iBAAiB;MAC5CC,QAAQ,EAAEH,OAAO,CAACG,QAAQ;MAC1BC,OAAO,EAAEJ,OAAO,CAACI,OAAO;MACxBC,QAAQ,EAAEL,OAAO,CAACK,QAAQ,gCAAG;QAC3BC,IAAI,EAAEN,OAAO,CAACK,QAAQ,CAACC,IAAI;QAC3BC,KAAK,EAAEP,OAAO,CAACK,QAAQ,CAACE,KAAK;QAC7BC,OAAO,EAAER,OAAO,CAACK,QAAQ,CAACG,OAAO;QACjCC,eAAe,EAAET,OAAO,CAACK,QAAQ,CAACI;OACnC,iCAAGC,SAAS;MACbC,WAAW,EAAEX,OAAO,CAACW,WAAW,gCAAG;QACjCC,KAAK,EAAEZ,OAAO,CAACW,WAAW,CAACC,KAAK;QAChCC,aAAa,EAAEb,OAAO,CAACW,WAAW,CAACE,aAAa;QAChDC,OAAO,EAAEd,OAAO,CAACW,WAAW,CAACG,OAAO;QACpCC,YAAY,EAAEf,OAAO,CAACW,WAAW,CAACI,YAAY;QAC9CC,eAAe,EAAEhB,OAAO,CAACW,WAAW,CAACK,eAAe,gCAAG;UACrDC,MAAM,EAAEjB,OAAO,CAACW,WAAW,CAACK,eAAe,CAACC,MAAM;UAClDX,IAAI,EAAEN,OAAO,CAACW,WAAW,CAACK,eAAe,CAACV,IAAI;UAC9CC,KAAK,EAAEP,OAAO,CAACW,WAAW,CAACK,eAAe,CAACT,KAAK;UAChDW,UAAU,EAAElB,OAAO,CAACW,WAAW,CAACK,eAAe,CAACE,UAAU;UAC1DV,OAAO,EAAER,OAAO,CAACW,WAAW,CAACK,eAAe,CAACR,OAAO;UACpDW,QAAQ,EAAEnB,OAAO,CAACW,WAAW,CAACK,eAAe,CAACG;SAC/C,iCAAGT,SAAS;OACd,iCAAGA,SAAS;KACd;IAAC;IAEF,OAAO,IAAI,CAAC3C,IAAI,CAACqD,GAAG,CAA2B,GAAG,IAAI,CAACtC,OAAO,EAAE,EAAEmB,aAAa,EAAE;MAC/E1B,OAAO,EAAE,IAAI,CAACJ,cAAc;KAC7B,CAAC,CACCa,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAI;MAAA;MAAA;MAAA,eAAQ,CAACC,IAAK;IAAL,CAAK,CAAC,EAC/BxB,GAAG,CAACyB,OAAO,IAAI;MAAA;MAAA;MAAA,WAAI,CAAClB,qBAAqB,CAACmB,IAAI,CAACD,OAAO,CAAC;IAAD,CAAC,CAAC,EACxDxB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEAwB,aAAa,CAACC,WAAiC;IAAA;IAC7C,MAAMC,aAAa,8BAAyB;MAC1CC,QAAQ,EAAEF,WAAW,CAACE,QAAS;MAC/BC,SAAS,EAAEH,WAAW,CAACG,SAAU;MACjCC,QAAQ,EAAEJ,WAAW,CAACI,QAAS;MAC/BxB,iBAAiB,EAAEoB,WAAW,CAACpB,iBAAiB;MAChDC,QAAQ,EAAEmB,WAAW,CAACnB,QAAQ;MAC9BgB,QAAQ,EAAE,wCAAW,CAACA,QAAQ,kCAAI,IAAI;KACvC;IAAC;IAEF,OAAO,IAAI,CAACpD,IAAI,CAAC4D,IAAI,CAA2B,GAAG,IAAI,CAAC7C,OAAO,EAAE,EAAEyC,aAAa,EAAE;MAChFhD,OAAO,EAAE,IAAI,CAACJ,cAAc;KAC7B,CAAC,CACCa,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAI;MAAA;MAAA;MAAA,eAAQ,CAACC,IAAK;IAAL,CAAK,CAAC,EAC/BxB,GAAG,CAACyB,OAAO,IAAI;MAAA;MAAA;MAAA,WAAI,CAAClB,qBAAqB,CAACmB,IAAI,CAACD,OAAO,CAAC;IAAD,CAAC,CAAC,EACxDxB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEA+B,aAAa;IAAA;IAAA;IACX,OAAO,IAAI,CAAC7D,IAAI,CAAC8D,MAAM,CAAuB,GAAG,IAAI,CAAC/C,OAAO,EAAE,EAAE;MAC/DP,OAAO,EAAE,IAAI,CAACJ,cAAc;KAC7B,CAAC,CACCa,IAAI,CACHvB,GAAG,CAAC,MAAM;MAAA;MAAA;MAAA,YAAK,CAAC;IAAD,CAAC,CAAC,EACjBC,GAAG,CAAC,MAAM;MAAA;MAAA;MAAA,WAAI,CAACO,qBAAqB,CAACmB,IAAI,CAAC,IAAI,CAAC;IAAD,CAAC,CAAC,EAChDzB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEA;EACAiC,cAAc,CAACC,OAA6B,EAAEC,oCAAe,CAAC,GAAEC,qCAAgB,EAAE;IAAA;IAChF,MAAMC,aAAa,8BAAyB;MAC1C7B,QAAQ,EAAE0B,OAAO,CAAC1B,QAAQ;MAC1B8B,MAAM,EAAE,oCAAO,CAACA,MAAM,kCAAI,EAAE;MAC5BC,MAAM,EAAE,qCAAO,CAACA,MAAM,mCAAI,WAAW;MACrCJ,IAAI,EAAEA,IAAI;MACVK,QAAQ,EAAEJ;KACX;IAAC;IAEF,OAAO,IAAI,CAAClE,IAAI,CAAC4D,IAAI,CAAmC,GAAG,IAAI,CAAC7C,OAAO,SAAS,EAAEoD,aAAa,EAAE;MAC/F3D,OAAO,EAAE,IAAI,CAACJ,cAAc;KAC7B,CAAC,CACCa,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAI;MAAA;MAAA;MAAA,eAAQ,CAACC,IAAK;IAAL,CAAK,CAAC,EAC/BvB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEA;EACAyC,oBAAoB,CAACC,UAAkB,EAAEP,qCAAe,CAAC,GAAEK,yCAAmB,EAAE;IAAA;IAC9E,MAAMH,aAAa,8BAAyB;MAC1CK,UAAU,EAAEA,UAAU;MACtBJ,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,WAAW;MACnBJ,IAAI,EAAEA,IAAI;MACVK,QAAQ,EAAEA;KACX;IAAC;IAEF,OAAO,IAAI,CAACtE,IAAI,CAAC4D,IAAI,CAAmC,GAAG,IAAI,CAAC7C,OAAO,SAAS,EAAEoD,aAAa,EAAE;MAC/F3D,OAAO,EAAE,IAAI,CAACJ,cAAc;KAC7B,CAAC,CACCa,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAI;MAAA;MAAA;MAAA,eAAQ,CAACC,IAAK;IAAL,CAAK,CAAC,EAC/BvB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEA;EACA2C,iBAAiB,CAACR,qCAAe,CAAC,GAAEK,yCAAmB,EAAE;IAAA;IAAA;IACvD,OAAO,IAAI,CAACtE,IAAI,CAACgB,GAAG,CAAmC,GAAG,IAAI,CAACD,OAAO,gBAAgBkD,IAAI,aAAaK,QAAQ,EAAE,CAAC,CAC/GrD,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAI;MAAA;MAAA;MAAA,eAAQ,CAACC,IAAK;IAAL,CAAK,CAAC,EAC/BvB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEA;EACA4C,QAAQ,CAACC,KAA4E;IAAA;IACnF;IACA,MAAMC,QAAQ,8BAAiB;MAC7B,GAAGD,KAAK;MACRnD,EAAE,EAAEqD,IAAI,CAACC,GAAG,EAAE;MACdC,YAAY,EAAE,CAAC;MACfC,uBAAuB,EAAE;KAC1B;IAAC;IACF,OAAO,IAAIzF,UAAU,CAAC0F,QAAQ,IAAG;MAAA;MAAA;MAC/BA,QAAQ,CAAC5D,IAAI,CAACuD,QAAQ,CAAC;MAAC;MACxBK,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAC,WAAW,CAACC,OAAe,EAAEnD,OAA8B;IAAA;IAAA;IACzD;IACA,OAAO,IAAI1C,UAAU,CAAC0F,QAAQ,IAAG;MAAA;MAAA;MAC/BA,QAAQ,CAAC5D,IAAI,CAAC;QAAE,GAAGY,OAAO;QAAET,EAAE,EAAE4D;MAAO,CAAkB,CAAC;MAAC;MAC3DH,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAG,WAAW,CAACD,OAAe;IAAA;IAAA;IACzB;IACA,OAAO,IAAI7F,UAAU,CAAC0F,QAAQ,IAAG;MAAA;MAAA;MAC/BA,QAAQ,CAAC5D,IAAI,EAAE;MAAC;MAChB4D,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAI,YAAY,CAACC,OAAgC;IAAA;IAAA;IAC3C;IACA,OAAO,IAAIhG,UAAU,CAAC0F,QAAQ,IAAG;MAAA;MAAA;MAC/BA,QAAQ,CAAC5D,IAAI,EAAE;MAAC;MAChB4D,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEA;EACAM,aAAa,CAACC,UAAsC;IAAA;IAAA;IAClD,OAAO,IAAI,CAACzF,IAAI,CAAC4D,IAAI,CAAiB,GAAG,IAAI,CAAC7C,OAAO,iBAAiB,EAAE0E,UAAU,CAAC,CAChFxE,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEA4D,gBAAgB,CAACC,YAAoB,EAAE1D,OAAgC;IAAA;IAAA;IACrE,OAAO,IAAI,CAACjC,IAAI,CAACqD,GAAG,CAAiB,GAAG,IAAI,CAACtC,OAAO,mBAAmB4E,YAAY,EAAE,EAAE1D,OAAO,CAAC,CAC5FhB,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEA8D,gBAAgB,CAACD,YAAoB;IAAA;IAAA;IACnC,OAAO,IAAI,CAAC3F,IAAI,CAAC8D,MAAM,CAAO,GAAG,IAAI,CAAC/C,OAAO,mBAAmB4E,YAAY,EAAE,CAAC,CAC5E1E,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEA;EACA+D,gBAAgB,CAACC,IAA+B;IAAA;IAAA;IAC9C,OAAO,IAAI,CAAC9F,IAAI,CAAC4D,IAAI,CAAgB,GAAG,IAAI,CAAC7C,OAAO,eAAe,EAAE+E,IAAI,CAAC,CACvE7E,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEAiE,mBAAmB,CAACC,MAAc,EAAE/D,OAA+B;IAAA;IAAA;IACjE,OAAO,IAAI,CAACjC,IAAI,CAACqD,GAAG,CAAgB,GAAG,IAAI,CAACtC,OAAO,iBAAiBiF,MAAM,EAAE,EAAE/D,OAAO,CAAC,CACnFhB,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEAmE,mBAAmB,CAACD,MAAc;IAAA;IAAA;IAChC,OAAO,IAAI,CAAChG,IAAI,CAAC8D,MAAM,CAAO,GAAG,IAAI,CAAC/C,OAAO,iBAAiBiF,MAAM,EAAE,CAAC,CACpE/E,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEA;EACAoE,cAAc,CAACC,WAAoC;IAAA;IAAA;IACjD,OAAO,IAAI,CAACnG,IAAI,CAAC4D,IAAI,CAAc,GAAG,IAAI,CAAC7C,OAAO,kBAAkB,EAAEoF,WAAW,CAAC,CAC/ElF,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEAsE,gBAAgB,CAACC,aAAwC;IAAA;IAAA;IACvD,OAAO,IAAI,CAACrG,IAAI,CAAC4D,IAAI,CAAgB,GAAG,IAAI,CAAC7C,OAAO,oBAAoB,EAAEsF,aAAa,CAAC,CACrFpF,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEA;EACAwE,YAAY,CAAC/E,SAAiB;IAAA;IAAA;IAC5B;IACA;IACA,OAAO,IAAIhC,UAAU,CAAC0F,QAAQ,IAAG;MAAA;MAAA;MAC/BA,QAAQ,CAAC5D,IAAI,CAAC,EAAE,CAAC;MAAC;MAClB4D,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEA;EACAqB,mBAAmB;IAAA;IACjB;IACA,MAAMC,aAAa,8BAAqB;MACtCC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,cAAc,EAAE,CAAC;MACjBC,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE,EAAE;MAChBC,iBAAiB,EAAE,CAAC;MACpBC,aAAa,EAAE,CAAC;MAChBC,mBAAmB,EAAE;KACtB;IAAC;IACF,OAAO,IAAIzH,UAAU,CAAC0F,QAAQ,IAAG;MAAA;MAAA;MAC/BA,QAAQ,CAAC5D,IAAI,CAACmF,aAAa,CAAC;MAAC;MAC7BvB,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEA5D,iBAAiB,CAACiE,OAA2B;IAAA;IAAA;IAC3C,OAAO,IAAI,CAACvF,IAAI,CAAC4D,IAAI,CAAuB,GAAG,IAAI,CAAC7C,OAAO,OAAO,EAAEwE,OAAO,EAAE;MAC3E/E,OAAO,EAAE,IAAI,CAACJ,cAAc;KAC7B,CAAC,CACCa,IAAI,CACHvB,GAAG,CAAC,MAAM;MAAA;MAAA;MAAA,YAAK,CAAC;IAAD,CAAC,CAAC,EACjBE,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEA;EACAmF,kBAAkB,CAACC,IAAU;IAAA;IAC3B,MAAMC,QAAQ,8BAAG,IAAIC,QAAQ,EAAE;IAAC;IAChCD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAE7B,MAAM7G,KAAK,8BAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,IAAIC,OAAO,8BAAG,IAAIlB,WAAW,EAAE;IAAC;IAChC,IAAIe,KAAK,EAAE;MAAA;MAAA;MACTG,OAAO,GAAGA,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUJ,KAAK,EAAE,CAAC;KAC1D;MAAA;IAAA;IAAA;IAED,OAAO,IAAI,CAACL,IAAI,CAAC4D,IAAI,CAAsB,GAAG,IAAI,CAAC7C,OAAO,uBAAuB,EAAEoG,QAAQ,EAAE;MAC3F3G,OAAO,EAAEA;KACV,CAAC,CACCS,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAK;MAAA;MAAA;MAAA;QAAEN,GAAG,EAAEM,QAAQ,CAACC;MAAK,CAAE;IAAF,CAAG,CAAC,EAC1CvB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEAwF,gBAAgB,CAACJ,IAAU;IAAA;IACzB,MAAMC,QAAQ,8BAAG,IAAIC,QAAQ,EAAE;IAAC;IAChCD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAE7B,MAAM7G,KAAK,8BAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,IAAIC,OAAO,8BAAG,IAAIlB,WAAW,EAAE;IAAC;IAChC,IAAIe,KAAK,EAAE;MAAA;MAAA;MACTG,OAAO,GAAGA,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUJ,KAAK,EAAE,CAAC;KAC1D;MAAA;IAAA;IAAA;IAED,OAAO,IAAI,CAACL,IAAI,CAAC4D,IAAI,CAAsB,GAAG,IAAI,CAAC7C,OAAO,qBAAqB,EAAEoG,QAAQ,EAAE;MACzF3G,OAAO,EAAEA;KACV,CAAC,CACCS,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAK;MAAA;MAAA;MAAA;QAAEN,GAAG,EAAEM,QAAQ,CAACC;MAAK,CAAE;IAAF,CAAG,CAAC,EAC1CvB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEA;EACAyF,mBAAmB,CAAC7D,SAAiB,EAAEC,QAAgB;IAAA;IACrD;IACA,MAAM6D,IAAI,8BAAG,GAAG9D,SAAS,CAAC+D,WAAW,EAAE,IAAI9D,QAAQ,CAAC8D,WAAW,EAAE,EAAE,CAChEC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAC3BA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACnBA,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;IAAC;IAEzB,OAAO,IAAInI,UAAU,CAAC0F,QAAQ,IAAG;MAAA;MAAA;MAC/BA,QAAQ,CAAC5D,IAAI,CAAC;QAAEmG;MAAI,CAAE,CAAC;MAAC;MACxBvC,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAyC,qBAAqB,CAACH,IAAY;IAAA;IAAA;IAChC;IACA;IACA,OAAO,IAAIjI,UAAU,CAAC0F,QAAQ,IAAG;MAAA;MAAA;MAC/BA,QAAQ,CAAC5D,IAAI,CAAC;QAAEuG,SAAS,EAAE;MAAI,CAAE,CAAC;MAAC;MACnC3C,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEA;EACA2C,mBAAmB,CAACtG,SAAiB;IAAA;IACnC;IACA,MAAMuG,cAAc,8BAAG,IAAI,CAAC5H,qBAAqB,CAAC6H,KAAK;IAAC;IACxD,IAAID,cAAc,EAAE;MAAA;MAClB,MAAME,SAAS,8BAAG;QAChBC,KAAK,EAAE,GAAGH,cAAc,CAACpE,SAAS,IAAIoE,cAAc,CAACnE,QAAQ,MAAMmE,cAAc,CAAC3F,iBAAiB,EAAE;QACrG+F,WAAW,EAAE,4CAAc,CAAC7F,OAAO,mCAAIyF,cAAc,CAAC1F,QAAQ,mCAAI,sBAAsB;QACxF+F,QAAQ,EAAE,4CAAc,CAACC,eAAe,mCAAI,EAAE;QAC9CxH,GAAG,EAAE,GAAGyH,MAAM,CAAC/F,QAAQ,CAACgG,MAAM,YAAYR,cAAc,CAACN,IAAI;OAC9D;MAAC;MAEF,OAAO,IAAIjI,UAAU,CAAC0F,QAAQ,IAAG;QAAA;QAAA;QAC/BA,QAAQ,CAAC5D,IAAI,CAAC2G,SAAS,CAAC;QAAC;QACzB/C,QAAQ,CAACC,QAAQ,EAAE;MACrB,CAAC,CAAC;KACH;MAAA;IAAA;IAAA;IAED,OAAOzF,UAAU,CAAC,MAAM;MAAA;MAAA;MAAA,WAAI8I,KAAK,CAAC,mBAAmB,CAAC;IAAD,CAAC,CAAC;EACzD;EAEQzG,WAAW,CAAC0G,KAAwB;IAAA;IAAA;IAC1CC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAE7C,IAAIE,YAAY,+BAAG,4BAA4B;IAAC;IAEhD,IAAI,mCAAK,CAACF,KAAK,mCAAIA,KAAK,CAACA,KAAK,CAACG,OAAO,GAAE;MAAA;MAAA;MACtC;MACAD,YAAY,GAAGF,KAAK,CAACA,KAAK,CAACG,OAAO;KACnC,MAAM;MAAA;MAAA;MAAA,IAAIH,KAAK,CAACI,MAAM,KAAK,CAAC,EAAE;QAAA;QAAA;QAC7BF,YAAY,GAAG,yBAAyB;OACzC,MAAM;QAAA;QAAA;QAAA,IAAIF,KAAK,CAACI,MAAM,KAAK,GAAG,EAAE;UAAA;UAAA;UAC/BF,YAAY,GAAG,sCAAsC;SACtD,MAAM;UAAA;UAAA;UAAA,IAAIF,KAAK,CAACI,MAAM,KAAK,GAAG,EAAE;YAAA;YAAA;YAC/BF,YAAY,GAAG,+BAA+B;WAC/C,MAAM;YAAA;YAAA;YAAA,IAAIF,KAAK,CAACI,MAAM,KAAK,GAAG,EAAE;cAAA;cAAA;cAC/BF,YAAY,GAAG,uBAAuB;aACvC,MAAM;cAAA;cAAA;cAAA,IAAIF,KAAK,CAACI,MAAM,IAAI,GAAG,EAAE;gBAAA;gBAAA;gBAC9BF,YAAY,GAAG,iDAAiD;eACjE;gBAAA;cAAA;;;;;;IAAA;IAED,OAAOjJ,UAAU,CAAC,MAAM;MAAA;MAAA;MAAA,WAAI8I,KAAK,CAACG,YAAY,CAAC;IAAD,CAAC,CAAC;EAClD;;;;;;;;;;;;;AAhZW5I,cAAc,eAH1BV,UAAU,CAAC;EACVyJ,UAAU,EAAE;CACb,CAAC,GACW/I,cAAc,CAiZ1B;SAjZYA,cAAc", "names": ["Injectable", "HttpClient", "HttpHeaders", "Observable", "BehaviorSubject", "throwError", "map", "tap", "catchError", "environment", "ProfileService", "constructor", "http", "apiUrl", "currentProfileSubject", "asObservable", "getHttpHeaders", "token", "localStorage", "getItem", "headers", "set", "getProfile", "identifier", "url", "isNaN", "Number", "API_URL", "get", "pipe", "response", "data", "profile", "next", "recordProfileView", "profileId", "id", "referrer", "document", "userAgent", "navigator", "subscribe", "handleError", "getCurrentUserProfile", "updateProfile", "updates", "updateRequest", "professional<PERSON>itle", "headline", "summary", "location", "city", "state", "country", "displayLocation", "undefined", "contactInfo", "email", "isEmailPublic", "website", "portfolioUrl", "businessAddress", "street", "postalCode", "isPublic", "put", "createProfile", "profileData", "createRequest", "username", "firstName", "lastName", "post", "deleteProfile", "delete", "searchProfiles", "filters", "page", "limit", "searchRequest", "skills", "sortBy", "pageSize", "searchProfilesByTerm", "searchTerm", "getPublicProfiles", "addSkill", "skill", "newSkill", "Date", "now", "endorsements", "isEndorsedByCurrentUser", "observer", "complete", "updateSkill", "skillId", "deleteSkill", "endorseSkill", "request", "addExperience", "experience", "updateExperience", "experienceId", "deleteExperience", "addPortfolioItem", "item", "updatePortfolioItem", "itemId", "deletePortfolioItem", "addAchievement", "achievement", "addCertification", "certification", "getBlogPosts", "getProfileAnalytics", "mockAnalytics", "profileViews", "uniqueVisitors", "viewsThisMonth", "viewsThisWeek", "topReferrers", "skillEndorsements", "blogPostViews", "contactButtonClicks", "uploadProfilePhoto", "file", "formData", "FormData", "append", "uploadCoverPhoto", "generateProfileSlug", "slug", "toLowerCase", "replace", "checkSlugAvailability", "available", "getProfileShareData", "currentProfile", "value", "shareData", "title", "description", "imageUrl", "profilePhotoUrl", "window", "origin", "Error", "error", "console", "errorMessage", "message", "status", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\services\\profile.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpParams, HttpHeaders, HttpErrorResponse } from '@angular/common/http';\r\nimport { Observable, BehaviorSubject, throwError } from 'rxjs';\r\nimport { map, tap, catchError } from 'rxjs/operators';\r\nimport { environment } from '../../../environments/environment';\r\nimport {\r\n  UserProfile,\r\n  ProfileUpdateRequest,\r\n  SkillEndorsementRequest,\r\n  ProfileViewRequest,\r\n  ProfileAnalytics,\r\n  ProfileSearchFilters,\r\n  ProfileSearchResult,\r\n  BlogPost,\r\n  Achievement,\r\n  Certification,\r\n  WorkExperience,\r\n  PortfolioItem,\r\n  ProfileSkill\r\n} from '../models/profile.models';\r\nimport {\r\n  ApiResponse,\r\n  ProfileSearchRequest,\r\n  CreateProfileRequest,\r\n  UpdateProfileRequest\r\n} from '../../shared/models/api-response.model';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ProfileService {\r\n  private readonly API_URL = `${environment.apiUrl}/profile`;\r\n\r\n  private currentProfileSubject = new BehaviorSubject<UserProfile | null>(null);\r\n  public currentProfile$ = this.currentProfileSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  /**\r\n   * Get HTTP headers with authentication token\r\n   */\r\n  private getHttpHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('authToken');\r\n    let headers = new HttpHeaders({\r\n      'Content-Type': 'application/json'\r\n    });\r\n\r\n    if (token) {\r\n      headers = headers.set('Authorization', `Bearer ${token}`);\r\n    }\r\n\r\n    return headers;\r\n  }\r\n\r\n  // Profile CRUD Operations\r\n  getProfile(identifier: string): Observable<UserProfile> {\r\n    // Check if identifier is a slug or ID\r\n    const url = isNaN(Number(identifier))\r\n      ? `${this.API_URL}/slug/${identifier}`\r\n      : `${this.API_URL}/${identifier}`;\r\n\r\n    return this.http.get<ApiResponse<UserProfile>>(url)\r\n      .pipe(\r\n        map(response => response.data!),\r\n        tap(profile => {\r\n          if (profile) {\r\n            this.currentProfileSubject.next(profile);\r\n            // Record profile view\r\n            this.recordProfileView({\r\n              profileId: profile.id,\r\n              referrer: document.referrer,\r\n              userAgent: navigator.userAgent\r\n            }).subscribe();\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  getCurrentUserProfile(): Observable<UserProfile> {\r\n    return this.http.get<ApiResponse<UserProfile>>(`${this.API_URL}/me`, {\r\n      headers: this.getHttpHeaders()\r\n    })\r\n      .pipe(\r\n        map(response => response.data!),\r\n        tap(profile => this.currentProfileSubject.next(profile)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  updateProfile(updates: ProfileUpdateRequest): Observable<UserProfile> {\r\n    const updateRequest: UpdateProfileRequest = {\r\n      professionalTitle: updates.professionalTitle,\r\n      headline: updates.headline,\r\n      summary: updates.summary,\r\n      location: updates.location ? {\r\n        city: updates.location.city,\r\n        state: updates.location.state,\r\n        country: updates.location.country,\r\n        displayLocation: updates.location.displayLocation\r\n      } : undefined,\r\n      contactInfo: updates.contactInfo ? {\r\n        email: updates.contactInfo.email,\r\n        isEmailPublic: updates.contactInfo.isEmailPublic,\r\n        website: updates.contactInfo.website,\r\n        portfolioUrl: updates.contactInfo.portfolioUrl,\r\n        businessAddress: updates.contactInfo.businessAddress ? {\r\n          street: updates.contactInfo.businessAddress.street,\r\n          city: updates.contactInfo.businessAddress.city,\r\n          state: updates.contactInfo.businessAddress.state,\r\n          postalCode: updates.contactInfo.businessAddress.postalCode,\r\n          country: updates.contactInfo.businessAddress.country,\r\n          isPublic: updates.contactInfo.businessAddress.isPublic\r\n        } : undefined\r\n      } : undefined\r\n    };\r\n\r\n    return this.http.put<ApiResponse<UserProfile>>(`${this.API_URL}`, updateRequest, {\r\n      headers: this.getHttpHeaders()\r\n    })\r\n      .pipe(\r\n        map(response => response.data!),\r\n        tap(profile => this.currentProfileSubject.next(profile)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  createProfile(profileData: Partial<UserProfile>): Observable<UserProfile> {\r\n    const createRequest: CreateProfileRequest = {\r\n      username: profileData.username!,\r\n      firstName: profileData.firstName!,\r\n      lastName: profileData.lastName!,\r\n      professionalTitle: profileData.professionalTitle,\r\n      headline: profileData.headline,\r\n      isPublic: profileData.isPublic ?? true\r\n    };\r\n\r\n    return this.http.post<ApiResponse<UserProfile>>(`${this.API_URL}`, createRequest, {\r\n      headers: this.getHttpHeaders()\r\n    })\r\n      .pipe(\r\n        map(response => response.data!),\r\n        tap(profile => this.currentProfileSubject.next(profile)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  deleteProfile(): Observable<void> {\r\n    return this.http.delete<ApiResponse<boolean>>(`${this.API_URL}`, {\r\n      headers: this.getHttpHeaders()\r\n    })\r\n      .pipe(\r\n        map(() => void 0),\r\n        tap(() => this.currentProfileSubject.next(null)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Profile Search\r\n  searchProfiles(filters: ProfileSearchFilters, page: number = 1, limit: number = 20): Observable<ProfileSearchResult> {\r\n    const searchRequest: ProfileSearchRequest = {\r\n      location: filters.location,\r\n      skills: filters.skills || [],\r\n      sortBy: filters.sortBy || 'relevance',\r\n      page: page,\r\n      pageSize: limit\r\n    };\r\n\r\n    return this.http.post<ApiResponse<ProfileSearchResult>>(`${this.API_URL}/search`, searchRequest, {\r\n      headers: this.getHttpHeaders()\r\n    })\r\n      .pipe(\r\n        map(response => response.data!),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Search profiles by term\r\n  searchProfilesByTerm(searchTerm: string, page: number = 1, pageSize: number = 20): Observable<ProfileSearchResult> {\r\n    const searchRequest: ProfileSearchRequest = {\r\n      searchTerm: searchTerm,\r\n      skills: [],\r\n      sortBy: 'relevance',\r\n      page: page,\r\n      pageSize: pageSize\r\n    };\r\n\r\n    return this.http.post<ApiResponse<ProfileSearchResult>>(`${this.API_URL}/search`, searchRequest, {\r\n      headers: this.getHttpHeaders()\r\n    })\r\n      .pipe(\r\n        map(response => response.data!),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Get public profiles\r\n  getPublicProfiles(page: number = 1, pageSize: number = 20): Observable<ProfileSearchResult> {\r\n    return this.http.get<ApiResponse<ProfileSearchResult>>(`${this.API_URL}/public?page=${page}&pageSize=${pageSize}`)\r\n      .pipe(\r\n        map(response => response.data!),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Skills Management (TODO: Implement in backend)\r\n  addSkill(skill: Omit<ProfileSkill, 'id' | 'endorsements' | 'isEndorsedByCurrentUser'>): Observable<ProfileSkill> {\r\n    // For now, return a mock skill until backend implements this\r\n    const newSkill: ProfileSkill = {\r\n      ...skill,\r\n      id: Date.now(),\r\n      endorsements: 0,\r\n      isEndorsedByCurrentUser: false\r\n    };\r\n    return new Observable(observer => {\r\n      observer.next(newSkill);\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  updateSkill(skillId: number, updates: Partial<ProfileSkill>): Observable<ProfileSkill> {\r\n    // TODO: Implement in backend\r\n    return new Observable(observer => {\r\n      observer.next({ ...updates, id: skillId } as ProfileSkill);\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  deleteSkill(skillId: number): Observable<void> {\r\n    // TODO: Implement in backend\r\n    return new Observable(observer => {\r\n      observer.next();\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  endorseSkill(request: SkillEndorsementRequest): Observable<void> {\r\n    // TODO: Implement in backend\r\n    return new Observable(observer => {\r\n      observer.next();\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  // Experience Management\r\n  addExperience(experience: Omit<WorkExperience, 'id'>): Observable<WorkExperience> {\r\n    return this.http.post<WorkExperience>(`${this.API_URL}/me/experiences`, experience)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  updateExperience(experienceId: number, updates: Partial<WorkExperience>): Observable<WorkExperience> {\r\n    return this.http.put<WorkExperience>(`${this.API_URL}/me/experiences/${experienceId}`, updates)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  deleteExperience(experienceId: number): Observable<void> {\r\n    return this.http.delete<void>(`${this.API_URL}/me/experiences/${experienceId}`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // Portfolio Management\r\n  addPortfolioItem(item: Omit<PortfolioItem, 'id'>): Observable<PortfolioItem> {\r\n    return this.http.post<PortfolioItem>(`${this.API_URL}/me/portfolio`, item)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  updatePortfolioItem(itemId: number, updates: Partial<PortfolioItem>): Observable<PortfolioItem> {\r\n    return this.http.put<PortfolioItem>(`${this.API_URL}/me/portfolio/${itemId}`, updates)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  deletePortfolioItem(itemId: number): Observable<void> {\r\n    return this.http.delete<void>(`${this.API_URL}/me/portfolio/${itemId}`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // Achievements & Certifications\r\n  addAchievement(achievement: Omit<Achievement, 'id'>): Observable<Achievement> {\r\n    return this.http.post<Achievement>(`${this.API_URL}/me/achievements`, achievement)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  addCertification(certification: Omit<Certification, 'id'>): Observable<Certification> {\r\n    return this.http.post<Certification>(`${this.API_URL}/me/certifications`, certification)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // Blog Posts\r\n  getBlogPosts(profileId: number): Observable<BlogPost[]> {\r\n    // Blog posts are included in the profile data from the backend\r\n    // Return empty array for now, as they're part of the main profile\r\n    return new Observable(observer => {\r\n      observer.next([]);\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  // Analytics\r\n  getProfileAnalytics(): Observable<ProfileAnalytics> {\r\n    // For now, return mock analytics since backend doesn't have this endpoint yet\r\n    const mockAnalytics: ProfileAnalytics = {\r\n      profileViews: 0,\r\n      uniqueVisitors: 0,\r\n      viewsThisMonth: 0,\r\n      viewsThisWeek: 0,\r\n      topReferrers: [],\r\n      skillEndorsements: 0,\r\n      blogPostViews: 0,\r\n      contactButtonClicks: 0\r\n    };\r\n    return new Observable(observer => {\r\n      observer.next(mockAnalytics);\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  recordProfileView(request: ProfileViewRequest): Observable<void> {\r\n    return this.http.post<ApiResponse<boolean>>(`${this.API_URL}/view`, request, {\r\n      headers: this.getHttpHeaders()\r\n    })\r\n      .pipe(\r\n        map(() => void 0),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // File Upload\r\n  uploadProfilePhoto(file: File): Observable<{ url: string }> {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    const token = localStorage.getItem('authToken');\r\n    let headers = new HttpHeaders();\r\n    if (token) {\r\n      headers = headers.set('Authorization', `Bearer ${token}`);\r\n    }\r\n\r\n    return this.http.post<ApiResponse<string>>(`${this.API_URL}/upload/profile-photo`, formData, {\r\n      headers: headers\r\n    })\r\n      .pipe(\r\n        map(response => ({ url: response.data! })),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  uploadCoverPhoto(file: File): Observable<{ url: string }> {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    const token = localStorage.getItem('authToken');\r\n    let headers = new HttpHeaders();\r\n    if (token) {\r\n      headers = headers.set('Authorization', `Bearer ${token}`);\r\n    }\r\n\r\n    return this.http.post<ApiResponse<string>>(`${this.API_URL}/upload/cover-photo`, formData, {\r\n      headers: headers\r\n    })\r\n      .pipe(\r\n        map(response => ({ url: response.data! })),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Utility Methods\r\n  generateProfileSlug(firstName: string, lastName: string): Observable<{ slug: string }> {\r\n    // Generate slug client-side for now\r\n    const slug = `${firstName.toLowerCase()}-${lastName.toLowerCase()}`\r\n      .replace(/[^a-z0-9-]/g, '-')\r\n      .replace(/-+/g, '-')\r\n      .replace(/^-|-$/g, '');\r\n\r\n    return new Observable(observer => {\r\n      observer.next({ slug });\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  checkSlugAvailability(slug: string): Observable<{ available: boolean }> {\r\n    // For now, assume all slugs are available\r\n    // TODO: Implement in backend\r\n    return new Observable(observer => {\r\n      observer.next({ available: true });\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  // Social Sharing\r\n  getProfileShareData(profileId: number): Observable<{ title: string; description: string; imageUrl: string; url: string }> {\r\n    // Generate share data from current profile\r\n    const currentProfile = this.currentProfileSubject.value;\r\n    if (currentProfile) {\r\n      const shareData = {\r\n        title: `${currentProfile.firstName} ${currentProfile.lastName} - ${currentProfile.professionalTitle}`,\r\n        description: currentProfile.summary || currentProfile.headline || 'Professional profile',\r\n        imageUrl: currentProfile.profilePhotoUrl || '',\r\n        url: `${window.location.origin}/profile/${currentProfile.slug}`\r\n      };\r\n\r\n      return new Observable(observer => {\r\n        observer.next(shareData);\r\n        observer.complete();\r\n      });\r\n    }\r\n\r\n    return throwError(() => new Error('Profile not found'));\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse): Observable<never> {\r\n    console.error('ProfileService error:', error);\r\n\r\n    let errorMessage = 'Възникна неочаквана грешка';\r\n\r\n    if (error.error && error.error.message) {\r\n      // Backend returned an error message\r\n      errorMessage = error.error.message;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Няма връзка със сървъра';\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Не сте упълномощени за тази операция';\r\n    } else if (error.status === 403) {\r\n      errorMessage = 'Нямате права за тази операция';\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Профилът не е намерен';\r\n    } else if (error.status >= 500) {\r\n      errorMessage = 'Сървърна грешка. Моля, опитайте отново по-късно';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}