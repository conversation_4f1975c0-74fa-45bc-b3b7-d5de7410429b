{"ast": null, "code": "function cov_1om0jhlgyl() {\n  var path = \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\auth\\\\services\\\\auth.service.ts\";\n  var hash = \"216ec941d909cfefe95c0cff4a769d277b60d5a4\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\auth\\\\services\\\\auth.service.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 8,\n          column: 18\n        },\n        end: {\n          line: 216,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 10,\n          column: 8\n        },\n        end: {\n          line: 10,\n          column: 25\n        }\n      },\n      \"2\": {\n        start: {\n          line: 11,\n          column: 8\n        },\n        end: {\n          line: 11,\n          column: 29\n        }\n      },\n      \"3\": {\n        start: {\n          line: 12,\n          column: 8\n        },\n        end: {\n          line: 12,\n          column: 41\n        }\n      },\n      \"4\": {\n        start: {\n          line: 13,\n          column: 8\n        },\n        end: {\n          line: 13,\n          column: 35\n        }\n      },\n      \"5\": {\n        start: {\n          line: 14,\n          column: 8\n        },\n        end: {\n          line: 14,\n          column: 40\n        }\n      },\n      \"6\": {\n        start: {\n          line: 15,\n          column: 8\n        },\n        end: {\n          line: 15,\n          column: 49\n        }\n      },\n      \"7\": {\n        start: {\n          line: 16,\n          column: 8\n        },\n        end: {\n          line: 16,\n          column: 36\n        }\n      },\n      \"8\": {\n        start: {\n          line: 17,\n          column: 8\n        },\n        end: {\n          line: 17,\n          column: 45\n        }\n      },\n      \"9\": {\n        start: {\n          line: 18,\n          column: 8\n        },\n        end: {\n          line: 18,\n          column: 60\n        }\n      },\n      \"10\": {\n        start: {\n          line: 19,\n          column: 8\n        },\n        end: {\n          line: 19,\n          column: 67\n        }\n      },\n      \"11\": {\n        start: {\n          line: 20,\n          column: 8\n        },\n        end: {\n          line: 20,\n          column: 65\n        }\n      },\n      \"12\": {\n        start: {\n          line: 21,\n          column: 8\n        },\n        end: {\n          line: 21,\n          column: 75\n        }\n      },\n      \"13\": {\n        start: {\n          line: 22,\n          column: 8\n        },\n        end: {\n          line: 22,\n          column: 30\n        }\n      },\n      \"14\": {\n        start: {\n          line: 25,\n          column: 22\n        },\n        end: {\n          line: 25,\n          column: 50\n        }\n      },\n      \"15\": {\n        start: {\n          line: 26,\n          column: 21\n        },\n        end: {\n          line: 26,\n          column: 54\n        }\n      },\n      \"16\": {\n        start: {\n          line: 27,\n          column: 8\n        },\n        end: {\n          line: 48,\n          column: 9\n        }\n      },\n      \"17\": {\n        start: {\n          line: 29,\n          column: 12\n        },\n        end: {\n          line: 47,\n          column: 13\n        }\n      },\n      \"18\": {\n        start: {\n          line: 31,\n          column: 16\n        },\n        end: {\n          line: 41,\n          column: 19\n        }\n      },\n      \"19\": {\n        start: {\n          line: 33,\n          column: 24\n        },\n        end: {\n          line: 33,\n          column: 59\n        }\n      },\n      \"20\": {\n        start: {\n          line: 34,\n          column: 24\n        },\n        end: {\n          line: 34,\n          column: 63\n        }\n      },\n      \"21\": {\n        start: {\n          line: 35,\n          column: 24\n        },\n        end: {\n          line: 35,\n          column: 57\n        }\n      },\n      \"22\": {\n        start: {\n          line: 39,\n          column: 24\n        },\n        end: {\n          line: 39,\n          column: 45\n        }\n      },\n      \"23\": {\n        start: {\n          line: 44,\n          column: 16\n        },\n        end: {\n          line: 44,\n          column: 51\n        }\n      },\n      \"24\": {\n        start: {\n          line: 45,\n          column: 16\n        },\n        end: {\n          line: 45,\n          column: 55\n        }\n      },\n      \"25\": {\n        start: {\n          line: 46,\n          column: 16\n        },\n        end: {\n          line: 46,\n          column: 49\n        }\n      },\n      \"26\": {\n        start: {\n          line: 51,\n          column: 8\n        },\n        end: {\n          line: 65,\n          column: 42\n        }\n      },\n      \"27\": {\n        start: {\n          line: 53,\n          column: 12\n        },\n        end: {\n          line: 64,\n          column: 13\n        }\n      },\n      \"28\": {\n        start: {\n          line: 55,\n          column: 35\n        },\n        end: {\n          line: 55,\n          column: 66\n        }\n      },\n      \"29\": {\n        start: {\n          line: 56,\n          column: 16\n        },\n        end: {\n          line: 56,\n          column: 60\n        }\n      },\n      \"30\": {\n        start: {\n          line: 57,\n          column: 16\n        },\n        end: {\n          line: 57,\n          column: 77\n        }\n      },\n      \"31\": {\n        start: {\n          line: 58,\n          column: 16\n        },\n        end: {\n          line: 60,\n          column: 17\n        }\n      },\n      \"32\": {\n        start: {\n          line: 59,\n          column: 20\n        },\n        end: {\n          line: 59,\n          column: 89\n        }\n      },\n      \"33\": {\n        start: {\n          line: 61,\n          column: 16\n        },\n        end: {\n          line: 61,\n          column: 69\n        }\n      },\n      \"34\": {\n        start: {\n          line: 62,\n          column: 16\n        },\n        end: {\n          line: 62,\n          column: 60\n        }\n      },\n      \"35\": {\n        start: {\n          line: 63,\n          column: 16\n        },\n        end: {\n          line: 63,\n          column: 55\n        }\n      },\n      \"36\": {\n        start: {\n          line: 69,\n          column: 25\n        },\n        end: {\n          line: 69,\n          column: 93\n        }\n      },\n      \"37\": {\n        start: {\n          line: 70,\n          column: 8\n        },\n        end: {\n          line: 84,\n          column: 42\n        }\n      },\n      \"38\": {\n        start: {\n          line: 72,\n          column: 12\n        },\n        end: {\n          line: 83,\n          column: 13\n        }\n      },\n      \"39\": {\n        start: {\n          line: 74,\n          column: 35\n        },\n        end: {\n          line: 74,\n          column: 40\n        }\n      },\n      \"40\": {\n        start: {\n          line: 75,\n          column: 16\n        },\n        end: {\n          line: 75,\n          column: 60\n        }\n      },\n      \"41\": {\n        start: {\n          line: 76,\n          column: 16\n        },\n        end: {\n          line: 76,\n          column: 77\n        }\n      },\n      \"42\": {\n        start: {\n          line: 77,\n          column: 16\n        },\n        end: {\n          line: 79,\n          column: 17\n        }\n      },\n      \"43\": {\n        start: {\n          line: 78,\n          column: 20\n        },\n        end: {\n          line: 78,\n          column: 89\n        }\n      },\n      \"44\": {\n        start: {\n          line: 80,\n          column: 16\n        },\n        end: {\n          line: 80,\n          column: 69\n        }\n      },\n      \"45\": {\n        start: {\n          line: 81,\n          column: 16\n        },\n        end: {\n          line: 81,\n          column: 60\n        }\n      },\n      \"46\": {\n        start: {\n          line: 82,\n          column: 16\n        },\n        end: {\n          line: 82,\n          column: 55\n        }\n      },\n      \"47\": {\n        start: {\n          line: 87,\n          column: 8\n        },\n        end: {\n          line: 87,\n          column: 47\n        }\n      },\n      \"48\": {\n        start: {\n          line: 90,\n          column: 8\n        },\n        end: {\n          line: 97,\n          column: 12\n        }\n      },\n      \"49\": {\n        start: {\n          line: 92,\n          column: 12\n        },\n        end: {\n          line: 92,\n          column: 33\n        }\n      },\n      \"50\": {\n        start: {\n          line: 95,\n          column: 12\n        },\n        end: {\n          line: 95,\n          column: 33\n        }\n      },\n      \"51\": {\n        start: {\n          line: 96,\n          column: 12\n        },\n        end: {\n          line: 96,\n          column: 47\n        }\n      },\n      \"52\": {\n        start: {\n          line: 100,\n          column: 29\n        },\n        end: {\n          line: 100,\n          column: 64\n        }\n      },\n      \"53\": {\n        start: {\n          line: 101,\n          column: 8\n        },\n        end: {\n          line: 103,\n          column: 9\n        }\n      },\n      \"54\": {\n        start: {\n          line: 102,\n          column: 12\n        },\n        end: {\n          line: 102,\n          column: 60\n        }\n      },\n      \"55\": {\n        start: {\n          line: 104,\n          column: 8\n        },\n        end: {\n          line: 120,\n          column: 12\n        }\n      },\n      \"56\": {\n        start: {\n          line: 106,\n          column: 12\n        },\n        end: {\n          line: 116,\n          column: 13\n        }\n      },\n      \"57\": {\n        start: {\n          line: 107,\n          column: 35\n        },\n        end: {\n          line: 107,\n          column: 68\n        }\n      },\n      \"58\": {\n        start: {\n          line: 108,\n          column: 16\n        },\n        end: {\n          line: 108,\n          column: 77\n        }\n      },\n      \"59\": {\n        start: {\n          line: 109,\n          column: 16\n        },\n        end: {\n          line: 111,\n          column: 17\n        }\n      },\n      \"60\": {\n        start: {\n          line: 110,\n          column: 20\n        },\n        end: {\n          line: 110,\n          column: 89\n        }\n      },\n      \"61\": {\n        start: {\n          line: 112,\n          column: 16\n        },\n        end: {\n          line: 115,\n          column: 17\n        }\n      },\n      \"62\": {\n        start: {\n          line: 113,\n          column: 20\n        },\n        end: {\n          line: 113,\n          column: 73\n        }\n      },\n      \"63\": {\n        start: {\n          line: 114,\n          column: 20\n        },\n        end: {\n          line: 114,\n          column: 64\n        }\n      },\n      \"64\": {\n        start: {\n          line: 118,\n          column: 12\n        },\n        end: {\n          line: 118,\n          column: 33\n        }\n      },\n      \"65\": {\n        start: {\n          line: 119,\n          column: 12\n        },\n        end: {\n          line: 119,\n          column: 37\n        }\n      },\n      \"66\": {\n        start: {\n          line: 123,\n          column: 8\n        },\n        end: {\n          line: 128,\n          column: 42\n        }\n      },\n      \"67\": {\n        start: {\n          line: 125,\n          column: 31\n        },\n        end: {\n          line: 125,\n          column: 64\n        }\n      },\n      \"68\": {\n        start: {\n          line: 126,\n          column: 12\n        },\n        end: {\n          line: 126,\n          column: 56\n        }\n      },\n      \"69\": {\n        start: {\n          line: 127,\n          column: 12\n        },\n        end: {\n          line: 127,\n          column: 47\n        }\n      },\n      \"70\": {\n        start: {\n          line: 131,\n          column: 8\n        },\n        end: {\n          line: 132,\n          column: 48\n        }\n      },\n      \"71\": {\n        start: {\n          line: 135,\n          column: 8\n        },\n        end: {\n          line: 136,\n          column: 48\n        }\n      },\n      \"72\": {\n        start: {\n          line: 139,\n          column: 8\n        },\n        end: {\n          line: 140,\n          column: 48\n        }\n      },\n      \"73\": {\n        start: {\n          line: 143,\n          column: 8\n        },\n        end: {\n          line: 157,\n          column: 42\n        }\n      },\n      \"74\": {\n        start: {\n          line: 145,\n          column: 12\n        },\n        end: {\n          line: 156,\n          column: 13\n        }\n      },\n      \"75\": {\n        start: {\n          line: 147,\n          column: 35\n        },\n        end: {\n          line: 147,\n          column: 39\n        }\n      },\n      \"76\": {\n        start: {\n          line: 148,\n          column: 16\n        },\n        end: {\n          line: 148,\n          column: 60\n        }\n      },\n      \"77\": {\n        start: {\n          line: 149,\n          column: 16\n        },\n        end: {\n          line: 149,\n          column: 77\n        }\n      },\n      \"78\": {\n        start: {\n          line: 150,\n          column: 16\n        },\n        end: {\n          line: 152,\n          column: 17\n        }\n      },\n      \"79\": {\n        start: {\n          line: 151,\n          column: 20\n        },\n        end: {\n          line: 151,\n          column: 89\n        }\n      },\n      \"80\": {\n        start: {\n          line: 153,\n          column: 16\n        },\n        end: {\n          line: 153,\n          column: 69\n        }\n      },\n      \"81\": {\n        start: {\n          line: 154,\n          column: 16\n        },\n        end: {\n          line: 154,\n          column: 60\n        }\n      },\n      \"82\": {\n        start: {\n          line: 155,\n          column: 16\n        },\n        end: {\n          line: 155,\n          column: 55\n        }\n      },\n      \"83\": {\n        start: {\n          line: 161,\n          column: 8\n        },\n        end: {\n          line: 161,\n          column: 44\n        }\n      },\n      \"84\": {\n        start: {\n          line: 164,\n          column: 22\n        },\n        end: {\n          line: 164,\n          column: 50\n        }\n      },\n      \"85\": {\n        start: {\n          line: 165,\n          column: 8\n        },\n        end: {\n          line: 176,\n          column: 9\n        }\n      },\n      \"86\": {\n        start: {\n          line: 167,\n          column: 12\n        },\n        end: {\n          line: 175,\n          column: 15\n        }\n      },\n      \"87\": {\n        start: {\n          line: 173,\n          column: 20\n        },\n        end: {\n          line: 173,\n          column: 41\n        }\n      },\n      \"88\": {\n        start: {\n          line: 180,\n          column: 8\n        },\n        end: {\n          line: 182,\n          column: 26\n        }\n      },\n      \"89\": {\n        start: {\n          line: 181,\n          column: 12\n        },\n        end: {\n          line: 181,\n          column: 40\n        }\n      },\n      \"90\": {\n        start: {\n          line: 185,\n          column: 8\n        },\n        end: {\n          line: 185,\n          column: 43\n        }\n      },\n      \"91\": {\n        start: {\n          line: 186,\n          column: 8\n        },\n        end: {\n          line: 186,\n          column: 43\n        }\n      },\n      \"92\": {\n        start: {\n          line: 187,\n          column: 8\n        },\n        end: {\n          line: 187,\n          column: 48\n        }\n      },\n      \"93\": {\n        start: {\n          line: 188,\n          column: 8\n        },\n        end: {\n          line: 188,\n          column: 41\n        }\n      },\n      \"94\": {\n        start: {\n          line: 191,\n          column: 8\n        },\n        end: {\n          line: 191,\n          column: 51\n        }\n      },\n      \"95\": {\n        start: {\n          line: 194,\n          column: 21\n        },\n        end: {\n          line: 194,\n          column: 50\n        }\n      },\n      \"96\": {\n        start: {\n          line: 195,\n          column: 8\n        },\n        end: {\n          line: 195,\n          column: 51\n        }\n      },\n      \"97\": {\n        start: {\n          line: 198,\n          column: 21\n        },\n        end: {\n          line: 198,\n          column: 50\n        }\n      },\n      \"98\": {\n        start: {\n          line: 199,\n          column: 8\n        },\n        end: {\n          line: 199,\n          column: 63\n        }\n      },\n      \"99\": {\n        start: {\n          line: 202,\n          column: 27\n        },\n        end: {\n          line: 202,\n          column: 46\n        }\n      },\n      \"100\": {\n        start: {\n          line: 203,\n          column: 8\n        },\n        end: {\n          line: 208,\n          column: 9\n        }\n      },\n      \"101\": {\n        start: {\n          line: 204,\n          column: 12\n        },\n        end: {\n          line: 204,\n          column: 47\n        }\n      },\n      \"102\": {\n        start: {\n          line: 206,\n          column: 13\n        },\n        end: {\n          line: 208,\n          column: 9\n        }\n      },\n      \"103\": {\n        start: {\n          line: 207,\n          column: 12\n        },\n        end: {\n          line: 207,\n          column: 41\n        }\n      },\n      \"104\": {\n        start: {\n          line: 209,\n          column: 8\n        },\n        end: {\n          line: 209,\n          column: 40\n        }\n      },\n      \"105\": {\n        start: {\n          line: 211,\n          column: 13\n        },\n        end: {\n          line: 215,\n          column: 6\n        }\n      },\n      \"106\": {\n        start: {\n          line: 211,\n          column: 41\n        },\n        end: {\n          line: 215,\n          column: 5\n        }\n      },\n      \"107\": {\n        start: {\n          line: 217,\n          column: 0\n        },\n        end: {\n          line: 221,\n          column: 16\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 9,\n            column: 4\n          },\n          end: {\n            line: 9,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 9,\n            column: 44\n          },\n          end: {\n            line: 23,\n            column: 5\n          }\n        },\n        line: 9\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 24,\n            column: 4\n          },\n          end: {\n            line: 24,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 24,\n            column: 21\n          },\n          end: {\n            line: 49,\n            column: 5\n          }\n        },\n        line: 24\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 32,\n            column: 26\n          },\n          end: {\n            line: 32,\n            column: 27\n          }\n        },\n        loc: {\n          start: {\n            line: 32,\n            column: 32\n          },\n          end: {\n            line: 36,\n            column: 21\n          }\n        },\n        line: 32\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 37,\n            column: 27\n          },\n          end: {\n            line: 37,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 37,\n            column: 33\n          },\n          end: {\n            line: 40,\n            column: 21\n          }\n        },\n        line: 37\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 50,\n            column: 4\n          },\n          end: {\n            line: 50,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 50,\n            column: 23\n          },\n          end: {\n            line: 66,\n            column: 5\n          }\n        },\n        line: 50\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 52,\n            column: 22\n          },\n          end: {\n            line: 52,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 52,\n            column: 34\n          },\n          end: {\n            line: 65,\n            column: 9\n          }\n        },\n        line: 52\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 67,\n            column: 4\n          },\n          end: {\n            line: 67,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 67,\n            column: 23\n          },\n          end: {\n            line: 85,\n            column: 5\n          }\n        },\n        line: 67\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 71,\n            column: 22\n          },\n          end: {\n            line: 71,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 71,\n            column: 34\n          },\n          end: {\n            line: 84,\n            column: 9\n          }\n        },\n        line: 71\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 86,\n            column: 4\n          },\n          end: {\n            line: 86,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 86,\n            column: 35\n          },\n          end: {\n            line: 88,\n            column: 5\n          }\n        },\n        line: 86\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 89,\n            column: 4\n          },\n          end: {\n            line: 89,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 89,\n            column: 13\n          },\n          end: {\n            line: 98,\n            column: 5\n          }\n        },\n        line: 89\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 91,\n            column: 22\n          },\n          end: {\n            line: 91,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 91,\n            column: 28\n          },\n          end: {\n            line: 93,\n            column: 9\n          }\n        },\n        line: 91\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 93,\n            column: 23\n          },\n          end: {\n            line: 93,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 93,\n            column: 29\n          },\n          end: {\n            line: 97,\n            column: 9\n          }\n        },\n        line: 93\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 99,\n            column: 4\n          },\n          end: {\n            line: 99,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 99,\n            column: 19\n          },\n          end: {\n            line: 121,\n            column: 5\n          }\n        },\n        line: 99\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 105,\n            column: 22\n          },\n          end: {\n            line: 105,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 105,\n            column: 34\n          },\n          end: {\n            line: 117,\n            column: 9\n          }\n        },\n        line: 105\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 117,\n            column: 23\n          },\n          end: {\n            line: 117,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 117,\n            column: 32\n          },\n          end: {\n            line: 120,\n            column: 9\n          }\n        },\n        line: 117\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 122,\n            column: 4\n          },\n          end: {\n            line: 122,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 122,\n            column: 21\n          },\n          end: {\n            line: 129,\n            column: 5\n          }\n        },\n        line: 122\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 124,\n            column: 22\n          },\n          end: {\n            line: 124,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 124,\n            column: 30\n          },\n          end: {\n            line: 128,\n            column: 9\n          }\n        },\n        line: 124\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 130,\n            column: 4\n          },\n          end: {\n            line: 130,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 130,\n            column: 28\n          },\n          end: {\n            line: 133,\n            column: 5\n          }\n        },\n        line: 130\n      },\n      \"18\": {\n        name: \"(anonymous_18)\",\n        decl: {\n          start: {\n            line: 134,\n            column: 4\n          },\n          end: {\n            line: 134,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 134,\n            column: 28\n          },\n          end: {\n            line: 137,\n            column: 5\n          }\n        },\n        line: 134\n      },\n      \"19\": {\n        name: \"(anonymous_19)\",\n        decl: {\n          start: {\n            line: 138,\n            column: 4\n          },\n          end: {\n            line: 138,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 138,\n            column: 27\n          },\n          end: {\n            line: 141,\n            column: 5\n          }\n        },\n        line: 138\n      },\n      \"20\": {\n        name: \"(anonymous_20)\",\n        decl: {\n          start: {\n            line: 142,\n            column: 4\n          },\n          end: {\n            line: 142,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 142,\n            column: 28\n          },\n          end: {\n            line: 158,\n            column: 5\n          }\n        },\n        line: 142\n      },\n      \"21\": {\n        name: \"(anonymous_21)\",\n        decl: {\n          start: {\n            line: 144,\n            column: 22\n          },\n          end: {\n            line: 144,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 144,\n            column: 34\n          },\n          end: {\n            line: 157,\n            column: 9\n          }\n        },\n        line: 144\n      },\n      \"22\": {\n        name: \"(anonymous_22)\",\n        decl: {\n          start: {\n            line: 160,\n            column: 4\n          },\n          end: {\n            line: 160,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 160,\n            column: 15\n          },\n          end: {\n            line: 162,\n            column: 5\n          }\n        },\n        line: 160\n      },\n      \"23\": {\n        name: \"(anonymous_23)\",\n        decl: {\n          start: {\n            line: 163,\n            column: 4\n          },\n          end: {\n            line: 163,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 163,\n            column: 27\n          },\n          end: {\n            line: 177,\n            column: 5\n          }\n        },\n        line: 163\n      },\n      \"24\": {\n        name: \"(anonymous_24)\",\n        decl: {\n          start: {\n            line: 168,\n            column: 22\n          },\n          end: {\n            line: 168,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 168,\n            column: 28\n          },\n          end: {\n            line: 170,\n            column: 17\n          }\n        },\n        line: 168\n      },\n      \"25\": {\n        name: \"(anonymous_25)\",\n        decl: {\n          start: {\n            line: 171,\n            column: 23\n          },\n          end: {\n            line: 171,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 171,\n            column: 29\n          },\n          end: {\n            line: 174,\n            column: 17\n          }\n        },\n        line: 171\n      },\n      \"26\": {\n        name: \"(anonymous_26)\",\n        decl: {\n          start: {\n            line: 178,\n            column: 4\n          },\n          end: {\n            line: 178,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 178,\n            column: 32\n          },\n          end: {\n            line: 183,\n            column: 5\n          }\n        },\n        line: 178\n      },\n      \"27\": {\n        name: \"(anonymous_27)\",\n        decl: {\n          start: {\n            line: 180,\n            column: 20\n          },\n          end: {\n            line: 180,\n            column: 21\n          }\n        },\n        loc: {\n          start: {\n            line: 180,\n            column: 26\n          },\n          end: {\n            line: 182,\n            column: 9\n          }\n        },\n        line: 180\n      },\n      \"28\": {\n        name: \"(anonymous_28)\",\n        decl: {\n          start: {\n            line: 184,\n            column: 4\n          },\n          end: {\n            line: 184,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 184,\n            column: 20\n          },\n          end: {\n            line: 189,\n            column: 5\n          }\n        },\n        line: 184\n      },\n      \"29\": {\n        name: \"(anonymous_29)\",\n        decl: {\n          start: {\n            line: 190,\n            column: 4\n          },\n          end: {\n            line: 190,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 190,\n            column: 22\n          },\n          end: {\n            line: 192,\n            column: 5\n          }\n        },\n        line: 190\n      },\n      \"30\": {\n        name: \"(anonymous_30)\",\n        decl: {\n          start: {\n            line: 193,\n            column: 4\n          },\n          end: {\n            line: 193,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 193,\n            column: 18\n          },\n          end: {\n            line: 196,\n            column: 5\n          }\n        },\n        line: 193\n      },\n      \"31\": {\n        name: \"(anonymous_31)\",\n        decl: {\n          start: {\n            line: 197,\n            column: 4\n          },\n          end: {\n            line: 197,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 197,\n            column: 30\n          },\n          end: {\n            line: 200,\n            column: 5\n          }\n        },\n        line: 197\n      },\n      \"32\": {\n        name: \"(anonymous_32)\",\n        decl: {\n          start: {\n            line: 201,\n            column: 4\n          },\n          end: {\n            line: 201,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 201,\n            column: 23\n          },\n          end: {\n            line: 210,\n            column: 5\n          }\n        },\n        line: 201\n      },\n      \"33\": {\n        name: \"(anonymous_33)\",\n        decl: {\n          start: {\n            line: 211,\n            column: 35\n          },\n          end: {\n            line: 211,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 211,\n            column: 41\n          },\n          end: {\n            line: 215,\n            column: 5\n          }\n        },\n        line: 211\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 27,\n            column: 8\n          },\n          end: {\n            line: 48,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 27,\n            column: 8\n          },\n          end: {\n            line: 48,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 27\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 27,\n            column: 12\n          },\n          end: {\n            line: 27,\n            column: 25\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 27,\n            column: 12\n          },\n          end: {\n            line: 27,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 27,\n            column: 21\n          },\n          end: {\n            line: 27,\n            column: 25\n          }\n        }],\n        line: 27\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 29,\n            column: 12\n          },\n          end: {\n            line: 47,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 29,\n            column: 12\n          },\n          end: {\n            line: 47,\n            column: 13\n          }\n        }, {\n          start: {\n            line: 43,\n            column: 17\n          },\n          end: {\n            line: 47,\n            column: 13\n          }\n        }],\n        line: 29\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 53,\n            column: 12\n          },\n          end: {\n            line: 64,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 53,\n            column: 12\n          },\n          end: {\n            line: 64,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 53\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 53,\n            column: 16\n          },\n          end: {\n            line: 53,\n            column: 73\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 53,\n            column: 16\n          },\n          end: {\n            line: 53,\n            column: 32\n          }\n        }, {\n          start: {\n            line: 53,\n            column: 36\n          },\n          end: {\n            line: 53,\n            column: 56\n          }\n        }, {\n          start: {\n            line: 53,\n            column: 60\n          },\n          end: {\n            line: 53,\n            column: 73\n          }\n        }],\n        line: 53\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 55,\n            column: 35\n          },\n          end: {\n            line: 55,\n            column: 66\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 55,\n            column: 35\n          },\n          end: {\n            line: 55,\n            column: 57\n          }\n        }, {\n          start: {\n            line: 55,\n            column: 61\n          },\n          end: {\n            line: 55,\n            column: 66\n          }\n        }],\n        line: 55\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 58,\n            column: 16\n          },\n          end: {\n            line: 60,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 58,\n            column: 16\n          },\n          end: {\n            line: 60,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 58\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 69,\n            column: 25\n          },\n          end: {\n            line: 69,\n            column: 93\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 69,\n            column: 63\n          },\n          end: {\n            line: 69,\n            column: 80\n          }\n        }, {\n          start: {\n            line: 69,\n            column: 83\n          },\n          end: {\n            line: 69,\n            column: 93\n          }\n        }],\n        line: 69\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 72,\n            column: 12\n          },\n          end: {\n            line: 83,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 72,\n            column: 12\n          },\n          end: {\n            line: 83,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 72\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 72,\n            column: 16\n          },\n          end: {\n            line: 72,\n            column: 73\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 72,\n            column: 16\n          },\n          end: {\n            line: 72,\n            column: 32\n          }\n        }, {\n          start: {\n            line: 72,\n            column: 36\n          },\n          end: {\n            line: 72,\n            column: 56\n          }\n        }, {\n          start: {\n            line: 72,\n            column: 60\n          },\n          end: {\n            line: 72,\n            column: 73\n          }\n        }],\n        line: 72\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 77,\n            column: 16\n          },\n          end: {\n            line: 79,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 77,\n            column: 16\n          },\n          end: {\n            line: 79,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 77\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 101,\n            column: 8\n          },\n          end: {\n            line: 103,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 101,\n            column: 8\n          },\n          end: {\n            line: 103,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 101\n      },\n      \"12\": {\n        loc: {\n          start: {\n            line: 106,\n            column: 12\n          },\n          end: {\n            line: 116,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 106,\n            column: 12\n          },\n          end: {\n            line: 116,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 106\n      },\n      \"13\": {\n        loc: {\n          start: {\n            line: 106,\n            column: 16\n          },\n          end: {\n            line: 106,\n            column: 56\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 106,\n            column: 16\n          },\n          end: {\n            line: 106,\n            column: 32\n          }\n        }, {\n          start: {\n            line: 106,\n            column: 36\n          },\n          end: {\n            line: 106,\n            column: 56\n          }\n        }],\n        line: 106\n      },\n      \"14\": {\n        loc: {\n          start: {\n            line: 109,\n            column: 16\n          },\n          end: {\n            line: 111,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 109,\n            column: 16\n          },\n          end: {\n            line: 111,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 109\n      },\n      \"15\": {\n        loc: {\n          start: {\n            line: 112,\n            column: 16\n          },\n          end: {\n            line: 115,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 112,\n            column: 16\n          },\n          end: {\n            line: 115,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 112\n      },\n      \"16\": {\n        loc: {\n          start: {\n            line: 145,\n            column: 12\n          },\n          end: {\n            line: 156,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 145,\n            column: 12\n          },\n          end: {\n            line: 156,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 145\n      },\n      \"17\": {\n        loc: {\n          start: {\n            line: 145,\n            column: 16\n          },\n          end: {\n            line: 145,\n            column: 73\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 145,\n            column: 16\n          },\n          end: {\n            line: 145,\n            column: 32\n          }\n        }, {\n          start: {\n            line: 145,\n            column: 36\n          },\n          end: {\n            line: 145,\n            column: 56\n          }\n        }, {\n          start: {\n            line: 145,\n            column: 60\n          },\n          end: {\n            line: 145,\n            column: 73\n          }\n        }],\n        line: 145\n      },\n      \"18\": {\n        loc: {\n          start: {\n            line: 150,\n            column: 16\n          },\n          end: {\n            line: 152,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 150,\n            column: 16\n          },\n          end: {\n            line: 152,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 150\n      },\n      \"19\": {\n        loc: {\n          start: {\n            line: 165,\n            column: 8\n          },\n          end: {\n            line: 176,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 165,\n            column: 8\n          },\n          end: {\n            line: 176,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 165\n      },\n      \"20\": {\n        loc: {\n          start: {\n            line: 165,\n            column: 12\n          },\n          end: {\n            line: 165,\n            column: 60\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 165,\n            column: 12\n          },\n          end: {\n            line: 165,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 165,\n            column: 21\n          },\n          end: {\n            line: 165,\n            column: 60\n          }\n        }],\n        line: 165\n      },\n      \"21\": {\n        loc: {\n          start: {\n            line: 195,\n            column: 15\n          },\n          end: {\n            line: 195,\n            column: 50\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 195,\n            column: 15\n          },\n          end: {\n            line: 195,\n            column: 41\n          }\n        }, {\n          start: {\n            line: 195,\n            column: 45\n          },\n          end: {\n            line: 195,\n            column: 50\n          }\n        }],\n        line: 195\n      },\n      \"22\": {\n        loc: {\n          start: {\n            line: 199,\n            column: 15\n          },\n          end: {\n            line: 199,\n            column: 62\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 199,\n            column: 15\n          },\n          end: {\n            line: 199,\n            column: 53\n          }\n        }, {\n          start: {\n            line: 199,\n            column: 57\n          },\n          end: {\n            line: 199,\n            column: 62\n          }\n        }],\n        line: 199\n      },\n      \"23\": {\n        loc: {\n          start: {\n            line: 203,\n            column: 8\n          },\n          end: {\n            line: 208,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 203,\n            column: 8\n          },\n          end: {\n            line: 208,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 206,\n            column: 13\n          },\n          end: {\n            line: 208,\n            column: 9\n          }\n        }],\n        line: 203\n      },\n      \"24\": {\n        loc: {\n          start: {\n            line: 206,\n            column: 13\n          },\n          end: {\n            line: 208,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 206,\n            column: 13\n          },\n          end: {\n            line: 208,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 206\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0,\n      \"59\": 0,\n      \"60\": 0,\n      \"61\": 0,\n      \"62\": 0,\n      \"63\": 0,\n      \"64\": 0,\n      \"65\": 0,\n      \"66\": 0,\n      \"67\": 0,\n      \"68\": 0,\n      \"69\": 0,\n      \"70\": 0,\n      \"71\": 0,\n      \"72\": 0,\n      \"73\": 0,\n      \"74\": 0,\n      \"75\": 0,\n      \"76\": 0,\n      \"77\": 0,\n      \"78\": 0,\n      \"79\": 0,\n      \"80\": 0,\n      \"81\": 0,\n      \"82\": 0,\n      \"83\": 0,\n      \"84\": 0,\n      \"85\": 0,\n      \"86\": 0,\n      \"87\": 0,\n      \"88\": 0,\n      \"89\": 0,\n      \"90\": 0,\n      \"91\": 0,\n      \"92\": 0,\n      \"93\": 0,\n      \"94\": 0,\n      \"95\": 0,\n      \"96\": 0,\n      \"97\": 0,\n      \"98\": 0,\n      \"99\": 0,\n      \"100\": 0,\n      \"101\": 0,\n      \"102\": 0,\n      \"103\": 0,\n      \"104\": 0,\n      \"105\": 0,\n      \"106\": 0,\n      \"107\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0, 0],\n      \"10\": [0, 0],\n      \"11\": [0, 0],\n      \"12\": [0, 0],\n      \"13\": [0, 0],\n      \"14\": [0, 0],\n      \"15\": [0, 0],\n      \"16\": [0, 0],\n      \"17\": [0, 0, 0],\n      \"18\": [0, 0],\n      \"19\": [0, 0],\n      \"20\": [0, 0],\n      \"21\": [0, 0],\n      \"22\": [0, 0],\n      \"23\": [0, 0],\n      \"24\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"auth.service.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\auth\\\\services\\\\auth.service.ts\"],\n      names: [],\n      mappings: \";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,UAAU,EAAe,MAAM,sBAAsB,CAAC;AAC/D,OAAO,EAAE,eAAe,EAAc,UAAU,EAAE,MAAM,MAAM,CAAC;AAC/D,OAAO,EAAO,UAAU,EAAE,GAAG,EAAE,MAAM,gBAAgB,CAAC;AACtD,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AACzC,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAiBxC,IAAM,WAAW,GAAjB,MAAM,WAAW;IAatB,YACU,IAAgB,EAChB,MAAc,EACd,YAA0B;QAF1B,SAAI,GAAJ,IAAI,CAAY;QAChB,WAAM,GAAN,MAAM,CAAQ;QACd,iBAAY,GAAZ,YAAY,CAAc;QAfnB,YAAO,GAAG,WAAW,CAAC;QACtB,cAAS,GAAG,cAAc,CAAC;QAC3B,sBAAiB,GAAG,eAAe,CAAC;QACpC,aAAQ,GAAG,WAAW,CAAC;QACvB,oBAAe,GAAG,aAAa,CAAC;QAEzC,uBAAkB,GAAG,IAAI,eAAe,CAAkB,IAAI,CAAC,CAAC;QACjE,iBAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;QAErD,2BAAsB,GAAG,IAAI,eAAe,CAAU,KAAK,CAAC,CAAC;QAC9D,qBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,CAAC;QAOnE,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAEO,cAAc;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;QAE/C,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,4BAA4B;YAC5B,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;gBAC3C,2BAA2B;gBAC3B,IAAI,CAAC,YAAY,EAAE,CAAC,SAAS,CAAC;oBAC5B,IAAI,EAAE,GAAG,EAAE;wBACT,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACnC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACvC,IAAI,CAAC,yBAAyB,EAAE,CAAC;oBACnC,CAAC;oBACD,KAAK,EAAE,GAAG,EAAE;wBACV,kCAAkC;wBAClC,IAAI,CAAC,aAAa,EAAE,CAAC;oBACvB,CAAC;iBACF,CAAC,CAAC;aACJ;iBAAM;gBACL,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvC,IAAI,CAAC,yBAAyB,EAAE,CAAC;aAClC;SACF;IACH,CAAC;IAED,KAAK,CAAC,WAAyB;QAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAe,GAAG,IAAI,CAAC,OAAO,QAAQ,EAAE,WAAW,CAAC;aACtE,IAAI,CACH,GAAG,CAAC,QAAQ,CAAC,EAAE;YACb,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,IAAI,EAAE;gBAC7D,+BAA+B;gBAC/B,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,IAAI,KAAK,CAAC;gBACnD,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;gBAE5C,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;gBAC7D,IAAI,QAAQ,CAAC,YAAY,EAAE;oBACzB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;iBACtE;gBACD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;gBACrD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC5C,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACxC;QACH,CAAC,CAAC,EACF,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;IACN,CAAC;IAED,QAAQ,CAAC,QAAiD;QACxD,wCAAwC;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,UAAU,CAAC;QAEtF,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAe,GAAG,IAAI,CAAC,OAAO,IAAI,QAAQ,EAAE,EAAE,QAAQ,CAAC;aACzE,IAAI,CACH,GAAG,CAAC,QAAQ,CAAC,EAAE;YACb,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,IAAI,EAAE;gBAC7D,mDAAmD;gBACnD,MAAM,UAAU,GAAG,KAAK,CAAC;gBACzB,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;gBAE5C,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;gBAC7D,IAAI,QAAQ,CAAC,YAAY,EAAE;oBACzB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;iBACtE;gBACD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;gBACrD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC5C,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACxC;QACH,CAAC,CAAC,EACF,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;IACN,CAAC;IAEO,oBAAoB,CAAC,QAAiD;QAC5E,OAAO,mBAAmB,IAAI,QAAQ,CAAC;IACzC,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,SAAS,EAAE,EAAE,CAAC;aAChD,IAAI,CACH,GAAG,CAAC,GAAG,EAAE;YACP,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC,CAAC,EACF,UAAU,CAAC,GAAG,EAAE;YACd,mDAAmD;YACnD,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,OAAO,UAAU,CAAC,eAAe,CAAC,CAAC;QACrC,CAAC,CAAC,CACH,CAAC;IACN,CAAC;IAED,YAAY;QACV,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QACzD,IAAI,CAAC,YAAY,EAAE;YACjB,OAAO,UAAU,CAAC,4BAA4B,CAAC,CAAC;SACjD;QAED,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAe,GAAG,IAAI,CAAC,OAAO,gBAAgB,EAAE,EAAE,YAAY,EAAE,CAAC;aACnF,IAAI,CACH,GAAG,CAAC,QAAQ,CAAC,EAAE;YACb,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,WAAW,EAAE;gBAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;gBACrD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;gBAC7D,IAAI,QAAQ,CAAC,YAAY,EAAE;oBACzB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;iBACtE;gBACD,IAAI,QAAQ,CAAC,IAAI,EAAE;oBACjB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;oBACrD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;iBAC7C;aACF;QACH,CAAC,CAAC,EACF,UAAU,CAAC,KAAK,CAAC,EAAE;YACjB,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC,CAAC,CACH,CAAC;IACN,CAAC;IAED,cAAc;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAW,GAAG,IAAI,CAAC,OAAO,KAAK,CAAC;aACjD,IAAI,CACH,GAAG,CAAC,IAAI,CAAC,EAAE;YACT,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;YACrD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAC5C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,EACF,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;IACN,CAAC;IAED,cAAc,CAAC,OAA8B;QAC3C,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAmB,GAAG,IAAI,CAAC,OAAO,kBAAkB,EAAE,OAAO,CAAC;aAChF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,cAAc,CAAC,OAA8B;QAC3C,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAmB,GAAG,IAAI,CAAC,OAAO,kBAAkB,EAAE,OAAO,CAAC;aAChF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,aAAa,CAAC,OAA6B;QACzC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAmB,GAAG,IAAI,CAAC,OAAO,iBAAiB,EAAE,OAAO,CAAC;aAC/E,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,cAAc,CAAC,OAA0B;QACvC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAe,GAAG,IAAI,CAAC,OAAO,cAAc,EAAE,OAAO,CAAC;aACxE,IAAI,CACH,GAAG,CAAC,QAAQ,CAAC,EAAE;YACb,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,IAAI,EAAE;gBAC7D,yDAAyD;gBACzD,MAAM,UAAU,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;gBAE5C,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;gBAC7D,IAAI,QAAQ,CAAC,YAAY,EAAE;oBACzB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;iBACtE;gBACD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;gBACrD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC5C,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACxC;QACH,CAAC,CAAC,EACF,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;IACN,CAAC;IAED,+CAA+C;IAC/C,QAAQ;QACN,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;IACtC,CAAC;IAED,oBAAoB;QAClB,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC3C,IAAI,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;YACpD,2BAA2B;YAC3B,IAAI,CAAC,YAAY,EAAE,CAAC,SAAS,CAAC;gBAC5B,IAAI,EAAE,GAAG,EAAE;oBACT,+BAA+B;gBACjC,CAAC;gBACD,KAAK,EAAE,GAAG,EAAE;oBACV,8BAA8B;oBAC9B,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,CAAC;aACF,CAAC,CAAC;SACJ;IACH,CAAC;IAED,yBAAyB;QACvB,yCAAyC;QACzC,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9B,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACpB,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;QACnC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;IACnC,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;IAC7C,CAAC;IAID,OAAO,CAAC,IAAY;QAClB,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;QAC3C,OAAO,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC;IAC7C,CAAC;IAED,aAAa,CAAC,UAAkB;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;QAC3C,OAAO,IAAI,EAAE,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC;IACzD,CAAC;IAEO,WAAW,CAAC,KAAU;QAC5B,IAAI,YAAY,GAAG,mBAAmB,CAAC;QAEvC,IAAI,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE;YACxB,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;SACpC;aAAM,IAAI,KAAK,CAAC,OAAO,EAAE;YACxB,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;SAC9B;QAED,OAAO,UAAU,CAAC,YAAY,CAAC,CAAC;IAClC,CAAC;;;;;;;AA3PU,WAAW;IAHvB,UAAU,CAAC;QACV,UAAU,EAAE,MAAM;KACnB,CAAC;GACW,WAAW,CA4PvB;SA5PY,WAAW\",\n      sourcesContent: [\"import { Injectable } from '@angular/core';\\r\\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\\r\\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\\r\\nimport { map, catchError, tap } from 'rxjs/operators';\\r\\nimport { Router } from '@angular/router';\\r\\nimport { TokenService } from './token.service';\\r\\nimport {\\r\\n  LoginRequest,\\r\\n  RegisterRequest,\\r\\n  OracleRegisterRequest,\\r\\n  AuthResponse,\\r\\n  UserInfo,\\r\\n  ChangePasswordRequest,\\r\\n  ForgotPasswordRequest,\\r\\n  ResetPasswordRequest,\\r\\n  ApiResponse,\\r\\n  OAuthLoginRequest\\r\\n} from '../models/auth.models';\\r\\n\\r\\n@Injectable({\\r\\n  providedIn: 'root'\\r\\n})\\r\\nexport class AuthService {\\r\\n  private readonly API_URL = '/api/auth';\\r\\n  private readonly TOKEN_KEY = 'access_token';\\r\\n  private readonly REFRESH_TOKEN_KEY = 'refresh_token';\\r\\n  private readonly USER_KEY = 'user_info';\\r\\n  private readonly REMEMBER_ME_KEY = 'remember_me';\\r\\n\\r\\n  private currentUserSubject = new BehaviorSubject<UserInfo | null>(null);\\r\\n  public currentUser$ = this.currentUserSubject.asObservable();\\r\\n\\r\\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);\\r\\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\\r\\n\\r\\n  constructor(\\r\\n    private http: HttpClient,\\r\\n    private router: Router,\\r\\n    private tokenService: TokenService\\r\\n  ) {\\r\\n    this.initializeAuth();\\r\\n  }\\r\\n\\r\\n  private initializeAuth(): void {\\r\\n    const token = this.tokenService.getToken();\\r\\n    const user = this.tokenService.getStoredUser();\\r\\n\\r\\n    if (token && user) {\\r\\n      // Check if token is expired\\r\\n      if (this.tokenService.isTokenExpired(token)) {\\r\\n        // Try to refresh the token\\r\\n        this.refreshToken().subscribe({\\r\\n          next: () => {\\r\\n            this.currentUserSubject.next(user);\\r\\n            this.isAuthenticatedSubject.next(true);\\r\\n            this.startTokenExpirationCheck();\\r\\n          },\\r\\n          error: () => {\\r\\n            // Refresh failed, clear auth data\\r\\n            this.clearAuthData();\\r\\n          }\\r\\n        });\\r\\n      } else {\\r\\n        this.currentUserSubject.next(user);\\r\\n        this.isAuthenticatedSubject.next(true);\\r\\n        this.startTokenExpirationCheck();\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n\\r\\n  login(credentials: LoginRequest): Observable<AuthResponse> {\\r\\n    return this.http.post<AuthResponse>(`${this.API_URL}/login`, credentials)\\r\\n      .pipe(\\r\\n        tap(response => {\\r\\n          if (response.success && response.accessToken && response.user) {\\r\\n            // Store remember me preference\\r\\n            const rememberMe = credentials.rememberMe || false;\\r\\n            this.tokenService.setRememberMe(rememberMe);\\r\\n\\r\\n            this.tokenService.setToken(response.accessToken, rememberMe);\\r\\n            if (response.refreshToken) {\\r\\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\\r\\n            }\\r\\n            this.tokenService.setUser(response.user, rememberMe);\\r\\n            this.currentUserSubject.next(response.user);\\r\\n            this.isAuthenticatedSubject.next(true);\\r\\n          }\\r\\n        }),\\r\\n        catchError(this.handleError)\\r\\n      );\\r\\n  }\\r\\n\\r\\n  register(userData: RegisterRequest | OracleRegisterRequest): Observable<AuthResponse> {\\r\\n    // Determine endpoint based on data type\\r\\n    const endpoint = this.isOracleRegistration(userData) ? 'register-oracle' : 'register';\\r\\n\\r\\n    return this.http.post<AuthResponse>(`${this.API_URL}/${endpoint}`, userData)\\r\\n      .pipe(\\r\\n        tap(response => {\\r\\n          if (response.success && response.accessToken && response.user) {\\r\\n            // For registration, default to remember me = false\\r\\n            const rememberMe = false;\\r\\n            this.tokenService.setRememberMe(rememberMe);\\r\\n\\r\\n            this.tokenService.setToken(response.accessToken, rememberMe);\\r\\n            if (response.refreshToken) {\\r\\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\\r\\n            }\\r\\n            this.tokenService.setUser(response.user, rememberMe);\\r\\n            this.currentUserSubject.next(response.user);\\r\\n            this.isAuthenticatedSubject.next(true);\\r\\n          }\\r\\n        }),\\r\\n        catchError(this.handleError)\\r\\n      );\\r\\n  }\\r\\n\\r\\n  private isOracleRegistration(userData: RegisterRequest | OracleRegisterRequest): userData is OracleRegisterRequest {\\r\\n    return 'professionalTitle' in userData;\\r\\n  }\\r\\n\\r\\n  logout(): Observable<any> {\\r\\n    return this.http.post(`${this.API_URL}/logout`, {})\\r\\n      .pipe(\\r\\n        tap(() => {\\r\\n          this.clearAuthData();\\r\\n        }),\\r\\n        catchError(() => {\\r\\n          // Even if logout fails on server, clear local data\\r\\n          this.clearAuthData();\\r\\n          return throwError('Logout failed');\\r\\n        })\\r\\n      );\\r\\n  }\\r\\n\\r\\n  refreshToken(): Observable<AuthResponse> {\\r\\n    const refreshToken = this.tokenService.getRefreshToken();\\r\\n    if (!refreshToken) {\\r\\n      return throwError('No refresh token available');\\r\\n    }\\r\\n\\r\\n    return this.http.post<AuthResponse>(`${this.API_URL}/refresh-token`, { refreshToken })\\r\\n      .pipe(\\r\\n        tap(response => {\\r\\n          if (response.success && response.accessToken) {\\r\\n            const rememberMe = this.tokenService.getRememberMe();\\r\\n            this.tokenService.setToken(response.accessToken, rememberMe);\\r\\n            if (response.refreshToken) {\\r\\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\\r\\n            }\\r\\n            if (response.user) {\\r\\n              this.tokenService.setUser(response.user, rememberMe);\\r\\n              this.currentUserSubject.next(response.user);\\r\\n            }\\r\\n          }\\r\\n        }),\\r\\n        catchError(error => {\\r\\n          this.clearAuthData();\\r\\n          return throwError(error);\\r\\n        })\\r\\n      );\\r\\n  }\\r\\n\\r\\n  getCurrentUser(): Observable<UserInfo> {\\r\\n    return this.http.get<UserInfo>(`${this.API_URL}/me`)\\r\\n      .pipe(\\r\\n        tap(user => {\\r\\n          const rememberMe = this.tokenService.getRememberMe();\\r\\n          this.tokenService.setUser(user, rememberMe);\\r\\n          this.currentUserSubject.next(user);\\r\\n        }),\\r\\n        catchError(this.handleError)\\r\\n      );\\r\\n  }\\r\\n\\r\\n  changePassword(request: ChangePasswordRequest): Observable<ApiResponse<any>> {\\r\\n    return this.http.post<ApiResponse<any>>(`${this.API_URL}/change-password`, request)\\r\\n      .pipe(catchError(this.handleError));\\r\\n  }\\r\\n\\r\\n  forgotPassword(request: ForgotPasswordRequest): Observable<ApiResponse<any>> {\\r\\n    return this.http.post<ApiResponse<any>>(`${this.API_URL}/forgot-password`, request)\\r\\n      .pipe(catchError(this.handleError));\\r\\n  }\\r\\n\\r\\n  resetPassword(request: ResetPasswordRequest): Observable<ApiResponse<any>> {\\r\\n    return this.http.post<ApiResponse<any>>(`${this.API_URL}/reset-password`, request)\\r\\n      .pipe(catchError(this.handleError));\\r\\n  }\\r\\n\\r\\n  loginWithOAuth(request: OAuthLoginRequest): Observable<AuthResponse> {\\r\\n    return this.http.post<AuthResponse>(`${this.API_URL}/oauth-login`, request)\\r\\n      .pipe(\\r\\n        tap(response => {\\r\\n          if (response.success && response.accessToken && response.user) {\\r\\n            // For OAuth, default to remember me = true for better UX\\r\\n            const rememberMe = true;\\r\\n            this.tokenService.setRememberMe(rememberMe);\\r\\n\\r\\n            this.tokenService.setToken(response.accessToken, rememberMe);\\r\\n            if (response.refreshToken) {\\r\\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\\r\\n            }\\r\\n            this.tokenService.setUser(response.user, rememberMe);\\r\\n            this.currentUserSubject.next(response.user);\\r\\n            this.isAuthenticatedSubject.next(true);\\r\\n          }\\r\\n        }),\\r\\n        catchError(this.handleError)\\r\\n      );\\r\\n  }\\r\\n\\r\\n  // Token management - delegated to TokenService\\r\\n  getToken(): string | null {\\r\\n    return this.tokenService.getToken();\\r\\n  }\\r\\n\\r\\n  checkTokenExpiration(): void {\\r\\n    const token = this.tokenService.getToken();\\r\\n    if (token && this.tokenService.isTokenExpired(token)) {\\r\\n      // Try to refresh the token\\r\\n      this.refreshToken().subscribe({\\r\\n        next: () => {\\r\\n          // Token refreshed successfully\\r\\n        },\\r\\n        error: () => {\\r\\n          // Refresh failed, logout user\\r\\n          this.clearAuthData();\\r\\n        }\\r\\n      });\\r\\n    }\\r\\n  }\\r\\n\\r\\n  startTokenExpirationCheck(): void {\\r\\n    // Check token expiration every 5 minutes\\r\\n    setInterval(() => {\\r\\n      this.checkTokenExpiration();\\r\\n    }, 5 * 60 * 1000);\\r\\n  }\\r\\n\\r\\n  private clearAuthData(): void {\\r\\n    this.tokenService.clearAllTokens();\\r\\n    this.currentUserSubject.next(null);\\r\\n    this.isAuthenticatedSubject.next(false);\\r\\n    this.router.navigate(['/login']);\\r\\n  }\\r\\n\\r\\n  isAuthenticated(): boolean {\\r\\n    return this.tokenService.isAuthenticated();\\r\\n  }\\r\\n\\r\\n\\r\\n\\r\\n  hasRole(role: string): boolean {\\r\\n    const user = this.currentUserSubject.value;\\r\\n    return user?.roles.includes(role) || false;\\r\\n  }\\r\\n\\r\\n  hasPermission(permission: string): boolean {\\r\\n    const user = this.currentUserSubject.value;\\r\\n    return user?.permissions.includes(permission) || false;\\r\\n  }\\r\\n\\r\\n  private handleError(error: any): Observable<never> {\\r\\n    let errorMessage = 'An error occurred';\\r\\n\\r\\n    if (error.error?.message) {\\r\\n      errorMessage = error.error.message;\\r\\n    } else if (error.message) {\\r\\n      errorMessage = error.message;\\r\\n    }\\r\\n\\r\\n    return throwError(errorMessage);\\r\\n  }\\r\\n}\\r\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"216ec941d909cfefe95c0cff4a769d277b60d5a4\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_1om0jhlgyl = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_1om0jhlgyl();\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { Router } from '@angular/router';\nimport { TokenService } from './token.service';\ncov_1om0jhlgyl().s[0]++;\nlet AuthService = class AuthService {\n  constructor(http, router, tokenService) {\n    cov_1om0jhlgyl().f[0]++;\n    cov_1om0jhlgyl().s[1]++;\n    this.http = http;\n    cov_1om0jhlgyl().s[2]++;\n    this.router = router;\n    cov_1om0jhlgyl().s[3]++;\n    this.tokenService = tokenService;\n    cov_1om0jhlgyl().s[4]++;\n    this.API_URL = '/api/auth';\n    cov_1om0jhlgyl().s[5]++;\n    this.TOKEN_KEY = 'access_token';\n    cov_1om0jhlgyl().s[6]++;\n    this.REFRESH_TOKEN_KEY = 'refresh_token';\n    cov_1om0jhlgyl().s[7]++;\n    this.USER_KEY = 'user_info';\n    cov_1om0jhlgyl().s[8]++;\n    this.REMEMBER_ME_KEY = 'remember_me';\n    cov_1om0jhlgyl().s[9]++;\n    this.currentUserSubject = new BehaviorSubject(null);\n    cov_1om0jhlgyl().s[10]++;\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    cov_1om0jhlgyl().s[11]++;\n    this.isAuthenticatedSubject = new BehaviorSubject(false);\n    cov_1om0jhlgyl().s[12]++;\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n    cov_1om0jhlgyl().s[13]++;\n    this.initializeAuth();\n  }\n  initializeAuth() {\n    cov_1om0jhlgyl().f[1]++;\n    const token = (cov_1om0jhlgyl().s[14]++, this.tokenService.getToken());\n    const user = (cov_1om0jhlgyl().s[15]++, this.tokenService.getStoredUser());\n    cov_1om0jhlgyl().s[16]++;\n    if ((cov_1om0jhlgyl().b[1][0]++, token) && (cov_1om0jhlgyl().b[1][1]++, user)) {\n      cov_1om0jhlgyl().b[0][0]++;\n      cov_1om0jhlgyl().s[17]++;\n      // Check if token is expired\n      if (this.tokenService.isTokenExpired(token)) {\n        cov_1om0jhlgyl().b[2][0]++;\n        cov_1om0jhlgyl().s[18]++;\n        // Try to refresh the token\n        this.refreshToken().subscribe({\n          next: () => {\n            cov_1om0jhlgyl().f[2]++;\n            cov_1om0jhlgyl().s[19]++;\n            this.currentUserSubject.next(user);\n            cov_1om0jhlgyl().s[20]++;\n            this.isAuthenticatedSubject.next(true);\n            cov_1om0jhlgyl().s[21]++;\n            this.startTokenExpirationCheck();\n          },\n          error: () => {\n            cov_1om0jhlgyl().f[3]++;\n            cov_1om0jhlgyl().s[22]++;\n            // Refresh failed, clear auth data\n            this.clearAuthData();\n          }\n        });\n      } else {\n        cov_1om0jhlgyl().b[2][1]++;\n        cov_1om0jhlgyl().s[23]++;\n        this.currentUserSubject.next(user);\n        cov_1om0jhlgyl().s[24]++;\n        this.isAuthenticatedSubject.next(true);\n        cov_1om0jhlgyl().s[25]++;\n        this.startTokenExpirationCheck();\n      }\n    } else {\n      cov_1om0jhlgyl().b[0][1]++;\n    }\n  }\n  login(credentials) {\n    cov_1om0jhlgyl().f[4]++;\n    cov_1om0jhlgyl().s[26]++;\n    return this.http.post(`${this.API_URL}/login`, credentials).pipe(tap(response => {\n      cov_1om0jhlgyl().f[5]++;\n      cov_1om0jhlgyl().s[27]++;\n      if ((cov_1om0jhlgyl().b[4][0]++, response.success) && (cov_1om0jhlgyl().b[4][1]++, response.accessToken) && (cov_1om0jhlgyl().b[4][2]++, response.user)) {\n        cov_1om0jhlgyl().b[3][0]++;\n        // Store remember me preference\n        const rememberMe = (cov_1om0jhlgyl().s[28]++, (cov_1om0jhlgyl().b[5][0]++, credentials.rememberMe) || (cov_1om0jhlgyl().b[5][1]++, false));\n        cov_1om0jhlgyl().s[29]++;\n        this.tokenService.setRememberMe(rememberMe);\n        cov_1om0jhlgyl().s[30]++;\n        this.tokenService.setToken(response.accessToken, rememberMe);\n        cov_1om0jhlgyl().s[31]++;\n        if (response.refreshToken) {\n          cov_1om0jhlgyl().b[6][0]++;\n          cov_1om0jhlgyl().s[32]++;\n          this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n        } else {\n          cov_1om0jhlgyl().b[6][1]++;\n        }\n        cov_1om0jhlgyl().s[33]++;\n        this.tokenService.setUser(response.user, rememberMe);\n        cov_1om0jhlgyl().s[34]++;\n        this.currentUserSubject.next(response.user);\n        cov_1om0jhlgyl().s[35]++;\n        this.isAuthenticatedSubject.next(true);\n      } else {\n        cov_1om0jhlgyl().b[3][1]++;\n      }\n    }), catchError(this.handleError));\n  }\n  register(userData) {\n    cov_1om0jhlgyl().f[6]++;\n    // Determine endpoint based on data type\n    const endpoint = (cov_1om0jhlgyl().s[36]++, this.isOracleRegistration(userData) ? (cov_1om0jhlgyl().b[7][0]++, 'register-oracle') : (cov_1om0jhlgyl().b[7][1]++, 'register'));\n    cov_1om0jhlgyl().s[37]++;\n    return this.http.post(`${this.API_URL}/${endpoint}`, userData).pipe(tap(response => {\n      cov_1om0jhlgyl().f[7]++;\n      cov_1om0jhlgyl().s[38]++;\n      if ((cov_1om0jhlgyl().b[9][0]++, response.success) && (cov_1om0jhlgyl().b[9][1]++, response.accessToken) && (cov_1om0jhlgyl().b[9][2]++, response.user)) {\n        cov_1om0jhlgyl().b[8][0]++;\n        // For registration, default to remember me = false\n        const rememberMe = (cov_1om0jhlgyl().s[39]++, false);\n        cov_1om0jhlgyl().s[40]++;\n        this.tokenService.setRememberMe(rememberMe);\n        cov_1om0jhlgyl().s[41]++;\n        this.tokenService.setToken(response.accessToken, rememberMe);\n        cov_1om0jhlgyl().s[42]++;\n        if (response.refreshToken) {\n          cov_1om0jhlgyl().b[10][0]++;\n          cov_1om0jhlgyl().s[43]++;\n          this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n        } else {\n          cov_1om0jhlgyl().b[10][1]++;\n        }\n        cov_1om0jhlgyl().s[44]++;\n        this.tokenService.setUser(response.user, rememberMe);\n        cov_1om0jhlgyl().s[45]++;\n        this.currentUserSubject.next(response.user);\n        cov_1om0jhlgyl().s[46]++;\n        this.isAuthenticatedSubject.next(true);\n      } else {\n        cov_1om0jhlgyl().b[8][1]++;\n      }\n    }), catchError(this.handleError));\n  }\n  isOracleRegistration(userData) {\n    cov_1om0jhlgyl().f[8]++;\n    cov_1om0jhlgyl().s[47]++;\n    return 'professionalTitle' in userData;\n  }\n  logout() {\n    cov_1om0jhlgyl().f[9]++;\n    cov_1om0jhlgyl().s[48]++;\n    return this.http.post(`${this.API_URL}/logout`, {}).pipe(tap(() => {\n      cov_1om0jhlgyl().f[10]++;\n      cov_1om0jhlgyl().s[49]++;\n      this.clearAuthData();\n    }), catchError(() => {\n      cov_1om0jhlgyl().f[11]++;\n      cov_1om0jhlgyl().s[50]++;\n      // Even if logout fails on server, clear local data\n      this.clearAuthData();\n      cov_1om0jhlgyl().s[51]++;\n      return throwError('Logout failed');\n    }));\n  }\n  refreshToken() {\n    cov_1om0jhlgyl().f[12]++;\n    const refreshToken = (cov_1om0jhlgyl().s[52]++, this.tokenService.getRefreshToken());\n    cov_1om0jhlgyl().s[53]++;\n    if (!refreshToken) {\n      cov_1om0jhlgyl().b[11][0]++;\n      cov_1om0jhlgyl().s[54]++;\n      return throwError('No refresh token available');\n    } else {\n      cov_1om0jhlgyl().b[11][1]++;\n    }\n    cov_1om0jhlgyl().s[55]++;\n    return this.http.post(`${this.API_URL}/refresh-token`, {\n      refreshToken\n    }).pipe(tap(response => {\n      cov_1om0jhlgyl().f[13]++;\n      cov_1om0jhlgyl().s[56]++;\n      if ((cov_1om0jhlgyl().b[13][0]++, response.success) && (cov_1om0jhlgyl().b[13][1]++, response.accessToken)) {\n        cov_1om0jhlgyl().b[12][0]++;\n        const rememberMe = (cov_1om0jhlgyl().s[57]++, this.tokenService.getRememberMe());\n        cov_1om0jhlgyl().s[58]++;\n        this.tokenService.setToken(response.accessToken, rememberMe);\n        cov_1om0jhlgyl().s[59]++;\n        if (response.refreshToken) {\n          cov_1om0jhlgyl().b[14][0]++;\n          cov_1om0jhlgyl().s[60]++;\n          this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n        } else {\n          cov_1om0jhlgyl().b[14][1]++;\n        }\n        cov_1om0jhlgyl().s[61]++;\n        if (response.user) {\n          cov_1om0jhlgyl().b[15][0]++;\n          cov_1om0jhlgyl().s[62]++;\n          this.tokenService.setUser(response.user, rememberMe);\n          cov_1om0jhlgyl().s[63]++;\n          this.currentUserSubject.next(response.user);\n        } else {\n          cov_1om0jhlgyl().b[15][1]++;\n        }\n      } else {\n        cov_1om0jhlgyl().b[12][1]++;\n      }\n    }), catchError(error => {\n      cov_1om0jhlgyl().f[14]++;\n      cov_1om0jhlgyl().s[64]++;\n      this.clearAuthData();\n      cov_1om0jhlgyl().s[65]++;\n      return throwError(error);\n    }));\n  }\n  getCurrentUser() {\n    cov_1om0jhlgyl().f[15]++;\n    cov_1om0jhlgyl().s[66]++;\n    return this.http.get(`${this.API_URL}/me`).pipe(tap(user => {\n      cov_1om0jhlgyl().f[16]++;\n      const rememberMe = (cov_1om0jhlgyl().s[67]++, this.tokenService.getRememberMe());\n      cov_1om0jhlgyl().s[68]++;\n      this.tokenService.setUser(user, rememberMe);\n      cov_1om0jhlgyl().s[69]++;\n      this.currentUserSubject.next(user);\n    }), catchError(this.handleError));\n  }\n  changePassword(request) {\n    cov_1om0jhlgyl().f[17]++;\n    cov_1om0jhlgyl().s[70]++;\n    return this.http.post(`${this.API_URL}/change-password`, request).pipe(catchError(this.handleError));\n  }\n  forgotPassword(request) {\n    cov_1om0jhlgyl().f[18]++;\n    cov_1om0jhlgyl().s[71]++;\n    return this.http.post(`${this.API_URL}/forgot-password`, request).pipe(catchError(this.handleError));\n  }\n  resetPassword(request) {\n    cov_1om0jhlgyl().f[19]++;\n    cov_1om0jhlgyl().s[72]++;\n    return this.http.post(`${this.API_URL}/reset-password`, request).pipe(catchError(this.handleError));\n  }\n  loginWithOAuth(request) {\n    cov_1om0jhlgyl().f[20]++;\n    cov_1om0jhlgyl().s[73]++;\n    return this.http.post(`${this.API_URL}/oauth-login`, request).pipe(tap(response => {\n      cov_1om0jhlgyl().f[21]++;\n      cov_1om0jhlgyl().s[74]++;\n      if ((cov_1om0jhlgyl().b[17][0]++, response.success) && (cov_1om0jhlgyl().b[17][1]++, response.accessToken) && (cov_1om0jhlgyl().b[17][2]++, response.user)) {\n        cov_1om0jhlgyl().b[16][0]++;\n        // For OAuth, default to remember me = true for better UX\n        const rememberMe = (cov_1om0jhlgyl().s[75]++, true);\n        cov_1om0jhlgyl().s[76]++;\n        this.tokenService.setRememberMe(rememberMe);\n        cov_1om0jhlgyl().s[77]++;\n        this.tokenService.setToken(response.accessToken, rememberMe);\n        cov_1om0jhlgyl().s[78]++;\n        if (response.refreshToken) {\n          cov_1om0jhlgyl().b[18][0]++;\n          cov_1om0jhlgyl().s[79]++;\n          this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n        } else {\n          cov_1om0jhlgyl().b[18][1]++;\n        }\n        cov_1om0jhlgyl().s[80]++;\n        this.tokenService.setUser(response.user, rememberMe);\n        cov_1om0jhlgyl().s[81]++;\n        this.currentUserSubject.next(response.user);\n        cov_1om0jhlgyl().s[82]++;\n        this.isAuthenticatedSubject.next(true);\n      } else {\n        cov_1om0jhlgyl().b[16][1]++;\n      }\n    }), catchError(this.handleError));\n  }\n  // Token management - delegated to TokenService\n  getToken() {\n    cov_1om0jhlgyl().f[22]++;\n    cov_1om0jhlgyl().s[83]++;\n    return this.tokenService.getToken();\n  }\n  checkTokenExpiration() {\n    cov_1om0jhlgyl().f[23]++;\n    const token = (cov_1om0jhlgyl().s[84]++, this.tokenService.getToken());\n    cov_1om0jhlgyl().s[85]++;\n    if ((cov_1om0jhlgyl().b[20][0]++, token) && (cov_1om0jhlgyl().b[20][1]++, this.tokenService.isTokenExpired(token))) {\n      cov_1om0jhlgyl().b[19][0]++;\n      cov_1om0jhlgyl().s[86]++;\n      // Try to refresh the token\n      this.refreshToken().subscribe({\n        next: () => {\n          cov_1om0jhlgyl().f[24]++;\n        } // Token refreshed successfully\n        ,\n\n        error: () => {\n          cov_1om0jhlgyl().f[25]++;\n          cov_1om0jhlgyl().s[87]++;\n          // Refresh failed, logout user\n          this.clearAuthData();\n        }\n      });\n    } else {\n      cov_1om0jhlgyl().b[19][1]++;\n    }\n  }\n  startTokenExpirationCheck() {\n    cov_1om0jhlgyl().f[26]++;\n    cov_1om0jhlgyl().s[88]++;\n    // Check token expiration every 5 minutes\n    setInterval(() => {\n      cov_1om0jhlgyl().f[27]++;\n      cov_1om0jhlgyl().s[89]++;\n      this.checkTokenExpiration();\n    }, 5 * 60 * 1000);\n  }\n  clearAuthData() {\n    cov_1om0jhlgyl().f[28]++;\n    cov_1om0jhlgyl().s[90]++;\n    this.tokenService.clearAllTokens();\n    cov_1om0jhlgyl().s[91]++;\n    this.currentUserSubject.next(null);\n    cov_1om0jhlgyl().s[92]++;\n    this.isAuthenticatedSubject.next(false);\n    cov_1om0jhlgyl().s[93]++;\n    this.router.navigate(['/login']);\n  }\n  isAuthenticated() {\n    cov_1om0jhlgyl().f[29]++;\n    cov_1om0jhlgyl().s[94]++;\n    return this.tokenService.isAuthenticated();\n  }\n  hasRole(role) {\n    cov_1om0jhlgyl().f[30]++;\n    const user = (cov_1om0jhlgyl().s[95]++, this.currentUserSubject.value);\n    cov_1om0jhlgyl().s[96]++;\n    return (cov_1om0jhlgyl().b[21][0]++, user?.roles.includes(role)) || (cov_1om0jhlgyl().b[21][1]++, false);\n  }\n  hasPermission(permission) {\n    cov_1om0jhlgyl().f[31]++;\n    const user = (cov_1om0jhlgyl().s[97]++, this.currentUserSubject.value);\n    cov_1om0jhlgyl().s[98]++;\n    return (cov_1om0jhlgyl().b[22][0]++, user?.permissions.includes(permission)) || (cov_1om0jhlgyl().b[22][1]++, false);\n  }\n  handleError(error) {\n    cov_1om0jhlgyl().f[32]++;\n    let errorMessage = (cov_1om0jhlgyl().s[99]++, 'An error occurred');\n    cov_1om0jhlgyl().s[100]++;\n    if (error.error?.message) {\n      cov_1om0jhlgyl().b[23][0]++;\n      cov_1om0jhlgyl().s[101]++;\n      errorMessage = error.error.message;\n    } else {\n      cov_1om0jhlgyl().b[23][1]++;\n      cov_1om0jhlgyl().s[102]++;\n      if (error.message) {\n        cov_1om0jhlgyl().b[24][0]++;\n        cov_1om0jhlgyl().s[103]++;\n        errorMessage = error.message;\n      } else {\n        cov_1om0jhlgyl().b[24][1]++;\n      }\n    }\n    cov_1om0jhlgyl().s[104]++;\n    return throwError(errorMessage);\n  }\n  static {\n    cov_1om0jhlgyl().s[105]++;\n    this.ctorParameters = () => {\n      cov_1om0jhlgyl().f[33]++;\n      cov_1om0jhlgyl().s[106]++;\n      return [{\n        type: HttpClient\n      }, {\n        type: Router\n      }, {\n        type: TokenService\n      }];\n    };\n  }\n};\ncov_1om0jhlgyl().s[107]++;\nAuthService = __decorate([Injectable({\n  providedIn: 'root'\n})], AuthService);\nexport { AuthService };", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0BmB;IAAA;MAAA;IAAA;EAAA;EAAA;AAAA;AAAA;;AA1BnB,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAqB,sBAAsB;AAC9D,SAASC,eAAe,EAAcC,UAAU,QAAQ,MAAM;AAC9D,SAAcC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AACrD,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAAC;AAiBxC,IAAMC,WAAW,GAAjB,MAAMA,WAAW;EAatBC,YACUC,IAAgB,EAChBC,MAAc,EACdC,YAA0B;IAAA;IAAA;IAF1B,SAAI,GAAJF,IAAI;IAAY;IAChB,WAAM,GAANC,MAAM;IAAQ;IACd,iBAAY,GAAZC,YAAY;IAAc;IAfnB,YAAO,GAAG,WAAW;IAAC;IACtB,cAAS,GAAG,cAAc;IAAC;IAC3B,sBAAiB,GAAG,eAAe;IAAC;IACpC,aAAQ,GAAG,WAAW;IAAC;IACvB,oBAAe,GAAG,aAAa;IAAC;IAEzC,uBAAkB,GAAG,IAAIV,eAAe,CAAkB,IAAI,CAAC;IAAC;IACjE,iBAAY,GAAG,IAAI,CAACW,kBAAkB,CAACC,YAAY,EAAE;IAAC;IAErD,2BAAsB,GAAG,IAAIZ,eAAe,CAAU,KAAK,CAAC;IAAC;IAC9D,qBAAgB,GAAG,IAAI,CAACa,sBAAsB,CAACD,YAAY,EAAE;IAAC;IAOnE,IAAI,CAACE,cAAc,EAAE;EACvB;EAEQA,cAAc;IAAA;IACpB,MAAMC,KAAK,8BAAG,IAAI,CAACL,YAAY,CAACM,QAAQ,EAAE;IAC1C,MAAMC,IAAI,8BAAG,IAAI,CAACP,YAAY,CAACQ,aAAa,EAAE;IAAC;IAE/C,IAAI,kCAAK,kCAAID,IAAI,GAAE;MAAA;MAAA;MACjB;MACA,IAAI,IAAI,CAACP,YAAY,CAACS,cAAc,CAACJ,KAAK,CAAC,EAAE;QAAA;QAAA;QAC3C;QACA,IAAI,CAACK,YAAY,EAAE,CAACC,SAAS,CAAC;UAC5BC,IAAI,EAAE,MAAK;YAAA;YAAA;YACT,IAAI,CAACX,kBAAkB,CAACW,IAAI,CAACL,IAAI,CAAC;YAAC;YACnC,IAAI,CAACJ,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;YAAC;YACvC,IAAI,CAACC,yBAAyB,EAAE;UAClC,CAAC;UACDC,KAAK,EAAE,MAAK;YAAA;YAAA;YACV;YACA,IAAI,CAACC,aAAa,EAAE;UACtB;SACD,CAAC;OACH,MAAM;QAAA;QAAA;QACL,IAAI,CAACd,kBAAkB,CAACW,IAAI,CAACL,IAAI,CAAC;QAAC;QACnC,IAAI,CAACJ,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;QAAC;QACvC,IAAI,CAACC,yBAAyB,EAAE;;KAEnC;MAAA;IAAA;EACH;EAEAG,KAAK,CAACC,WAAyB;IAAA;IAAA;IAC7B,OAAO,IAAI,CAACnB,IAAI,CAACoB,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,QAAQ,EAAEF,WAAW,CAAC,CACtEG,IAAI,CACH3B,GAAG,CAAC4B,QAAQ,IAAG;MAAA;MAAA;MACb,IAAI,qCAAQ,CAACC,OAAO,kCAAID,QAAQ,CAACE,WAAW,kCAAIF,QAAQ,CAACd,IAAI,GAAE;QAAA;QAC7D;QACA,MAAMiB,UAAU,8BAAG,wCAAW,CAACA,UAAU,kCAAI,KAAK;QAAC;QACnD,IAAI,CAACxB,YAAY,CAACyB,aAAa,CAACD,UAAU,CAAC;QAAC;QAE5C,IAAI,CAACxB,YAAY,CAAC0B,QAAQ,CAACL,QAAQ,CAACE,WAAW,EAAEC,UAAU,CAAC;QAAC;QAC7D,IAAIH,QAAQ,CAACX,YAAY,EAAE;UAAA;UAAA;UACzB,IAAI,CAACV,YAAY,CAAC2B,eAAe,CAACN,QAAQ,CAACX,YAAY,EAAEc,UAAU,CAAC;SACrE;UAAA;QAAA;QAAA;QACD,IAAI,CAACxB,YAAY,CAAC4B,OAAO,CAACP,QAAQ,CAACd,IAAI,EAAEiB,UAAU,CAAC;QAAC;QACrD,IAAI,CAACvB,kBAAkB,CAACW,IAAI,CAACS,QAAQ,CAACd,IAAI,CAAC;QAAC;QAC5C,IAAI,CAACJ,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;OACvC;QAAA;MAAA;IACH,CAAC,CAAC,EACFpB,UAAU,CAAC,IAAI,CAACqC,WAAW,CAAC,CAC7B;EACL;EAEAC,QAAQ,CAACC,QAAiD;IAAA;IACxD;IACA,MAAMC,QAAQ,8BAAG,IAAI,CAACC,oBAAoB,CAACF,QAAQ,CAAC,gCAAG,iBAAiB,iCAAG,UAAU;IAAC;IAEtF,OAAO,IAAI,CAACjC,IAAI,CAACoB,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,IAAIa,QAAQ,EAAE,EAAED,QAAQ,CAAC,CACzEX,IAAI,CACH3B,GAAG,CAAC4B,QAAQ,IAAG;MAAA;MAAA;MACb,IAAI,qCAAQ,CAACC,OAAO,kCAAID,QAAQ,CAACE,WAAW,kCAAIF,QAAQ,CAACd,IAAI,GAAE;QAAA;QAC7D;QACA,MAAMiB,UAAU,8BAAG,KAAK;QAAC;QACzB,IAAI,CAACxB,YAAY,CAACyB,aAAa,CAACD,UAAU,CAAC;QAAC;QAE5C,IAAI,CAACxB,YAAY,CAAC0B,QAAQ,CAACL,QAAQ,CAACE,WAAW,EAAEC,UAAU,CAAC;QAAC;QAC7D,IAAIH,QAAQ,CAACX,YAAY,EAAE;UAAA;UAAA;UACzB,IAAI,CAACV,YAAY,CAAC2B,eAAe,CAACN,QAAQ,CAACX,YAAY,EAAEc,UAAU,CAAC;SACrE;UAAA;QAAA;QAAA;QACD,IAAI,CAACxB,YAAY,CAAC4B,OAAO,CAACP,QAAQ,CAACd,IAAI,EAAEiB,UAAU,CAAC;QAAC;QACrD,IAAI,CAACvB,kBAAkB,CAACW,IAAI,CAACS,QAAQ,CAACd,IAAI,CAAC;QAAC;QAC5C,IAAI,CAACJ,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;OACvC;QAAA;MAAA;IACH,CAAC,CAAC,EACFpB,UAAU,CAAC,IAAI,CAACqC,WAAW,CAAC,CAC7B;EACL;EAEQI,oBAAoB,CAACF,QAAiD;IAAA;IAAA;IAC5E,OAAO,mBAAmB,IAAIA,QAAQ;EACxC;EAEAG,MAAM;IAAA;IAAA;IACJ,OAAO,IAAI,CAACpC,IAAI,CAACoB,IAAI,CAAC,GAAG,IAAI,CAACC,OAAO,SAAS,EAAE,EAAE,CAAC,CAChDC,IAAI,CACH3B,GAAG,CAAC,MAAK;MAAA;MAAA;MACP,IAAI,CAACsB,aAAa,EAAE;IACtB,CAAC,CAAC,EACFvB,UAAU,CAAC,MAAK;MAAA;MAAA;MACd;MACA,IAAI,CAACuB,aAAa,EAAE;MAAC;MACrB,OAAOxB,UAAU,CAAC,eAAe,CAAC;IACpC,CAAC,CAAC,CACH;EACL;EAEAmB,YAAY;IAAA;IACV,MAAMA,YAAY,8BAAG,IAAI,CAACV,YAAY,CAACmC,eAAe,EAAE;IAAC;IACzD,IAAI,CAACzB,YAAY,EAAE;MAAA;MAAA;MACjB,OAAOnB,UAAU,CAAC,4BAA4B,CAAC;KAChD;MAAA;IAAA;IAAA;IAED,OAAO,IAAI,CAACO,IAAI,CAACoB,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,gBAAgB,EAAE;MAAET;IAAY,CAAE,CAAC,CACnFU,IAAI,CACH3B,GAAG,CAAC4B,QAAQ,IAAG;MAAA;MAAA;MACb,IAAI,sCAAQ,CAACC,OAAO,mCAAID,QAAQ,CAACE,WAAW,GAAE;QAAA;QAC5C,MAAMC,UAAU,8BAAG,IAAI,CAACxB,YAAY,CAACoC,aAAa,EAAE;QAAC;QACrD,IAAI,CAACpC,YAAY,CAAC0B,QAAQ,CAACL,QAAQ,CAACE,WAAW,EAAEC,UAAU,CAAC;QAAC;QAC7D,IAAIH,QAAQ,CAACX,YAAY,EAAE;UAAA;UAAA;UACzB,IAAI,CAACV,YAAY,CAAC2B,eAAe,CAACN,QAAQ,CAACX,YAAY,EAAEc,UAAU,CAAC;SACrE;UAAA;QAAA;QAAA;QACD,IAAIH,QAAQ,CAACd,IAAI,EAAE;UAAA;UAAA;UACjB,IAAI,CAACP,YAAY,CAAC4B,OAAO,CAACP,QAAQ,CAACd,IAAI,EAAEiB,UAAU,CAAC;UAAC;UACrD,IAAI,CAACvB,kBAAkB,CAACW,IAAI,CAACS,QAAQ,CAACd,IAAI,CAAC;SAC5C;UAAA;QAAA;OACF;QAAA;MAAA;IACH,CAAC,CAAC,EACFf,UAAU,CAACsB,KAAK,IAAG;MAAA;MAAA;MACjB,IAAI,CAACC,aAAa,EAAE;MAAC;MACrB,OAAOxB,UAAU,CAACuB,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACL;EAEAuB,cAAc;IAAA;IAAA;IACZ,OAAO,IAAI,CAACvC,IAAI,CAACwC,GAAG,CAAW,GAAG,IAAI,CAACnB,OAAO,KAAK,CAAC,CACjDC,IAAI,CACH3B,GAAG,CAACc,IAAI,IAAG;MAAA;MACT,MAAMiB,UAAU,8BAAG,IAAI,CAACxB,YAAY,CAACoC,aAAa,EAAE;MAAC;MACrD,IAAI,CAACpC,YAAY,CAAC4B,OAAO,CAACrB,IAAI,EAAEiB,UAAU,CAAC;MAAC;MAC5C,IAAI,CAACvB,kBAAkB,CAACW,IAAI,CAACL,IAAI,CAAC;IACpC,CAAC,CAAC,EACFf,UAAU,CAAC,IAAI,CAACqC,WAAW,CAAC,CAC7B;EACL;EAEAU,cAAc,CAACC,OAA8B;IAAA;IAAA;IAC3C,OAAO,IAAI,CAAC1C,IAAI,CAACoB,IAAI,CAAmB,GAAG,IAAI,CAACC,OAAO,kBAAkB,EAAEqB,OAAO,CAAC,CAChFpB,IAAI,CAAC5B,UAAU,CAAC,IAAI,CAACqC,WAAW,CAAC,CAAC;EACvC;EAEAY,cAAc,CAACD,OAA8B;IAAA;IAAA;IAC3C,OAAO,IAAI,CAAC1C,IAAI,CAACoB,IAAI,CAAmB,GAAG,IAAI,CAACC,OAAO,kBAAkB,EAAEqB,OAAO,CAAC,CAChFpB,IAAI,CAAC5B,UAAU,CAAC,IAAI,CAACqC,WAAW,CAAC,CAAC;EACvC;EAEAa,aAAa,CAACF,OAA6B;IAAA;IAAA;IACzC,OAAO,IAAI,CAAC1C,IAAI,CAACoB,IAAI,CAAmB,GAAG,IAAI,CAACC,OAAO,iBAAiB,EAAEqB,OAAO,CAAC,CAC/EpB,IAAI,CAAC5B,UAAU,CAAC,IAAI,CAACqC,WAAW,CAAC,CAAC;EACvC;EAEAc,cAAc,CAACH,OAA0B;IAAA;IAAA;IACvC,OAAO,IAAI,CAAC1C,IAAI,CAACoB,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,cAAc,EAAEqB,OAAO,CAAC,CACxEpB,IAAI,CACH3B,GAAG,CAAC4B,QAAQ,IAAG;MAAA;MAAA;MACb,IAAI,sCAAQ,CAACC,OAAO,mCAAID,QAAQ,CAACE,WAAW,mCAAIF,QAAQ,CAACd,IAAI,GAAE;QAAA;QAC7D;QACA,MAAMiB,UAAU,8BAAG,IAAI;QAAC;QACxB,IAAI,CAACxB,YAAY,CAACyB,aAAa,CAACD,UAAU,CAAC;QAAC;QAE5C,IAAI,CAACxB,YAAY,CAAC0B,QAAQ,CAACL,QAAQ,CAACE,WAAW,EAAEC,UAAU,CAAC;QAAC;QAC7D,IAAIH,QAAQ,CAACX,YAAY,EAAE;UAAA;UAAA;UACzB,IAAI,CAACV,YAAY,CAAC2B,eAAe,CAACN,QAAQ,CAACX,YAAY,EAAEc,UAAU,CAAC;SACrE;UAAA;QAAA;QAAA;QACD,IAAI,CAACxB,YAAY,CAAC4B,OAAO,CAACP,QAAQ,CAACd,IAAI,EAAEiB,UAAU,CAAC;QAAC;QACrD,IAAI,CAACvB,kBAAkB,CAACW,IAAI,CAACS,QAAQ,CAACd,IAAI,CAAC;QAAC;QAC5C,IAAI,CAACJ,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;OACvC;QAAA;MAAA;IACH,CAAC,CAAC,EACFpB,UAAU,CAAC,IAAI,CAACqC,WAAW,CAAC,CAC7B;EACL;EAEA;EACAvB,QAAQ;IAAA;IAAA;IACN,OAAO,IAAI,CAACN,YAAY,CAACM,QAAQ,EAAE;EACrC;EAEAsC,oBAAoB;IAAA;IAClB,MAAMvC,KAAK,8BAAG,IAAI,CAACL,YAAY,CAACM,QAAQ,EAAE;IAAC;IAC3C,IAAI,mCAAK,mCAAI,IAAI,CAACN,YAAY,CAACS,cAAc,CAACJ,KAAK,CAAC,GAAE;MAAA;MAAA;MACpD;MACA,IAAI,CAACK,YAAY,EAAE,CAACC,SAAS,CAAC;QAC5BC,IAAI,EAAE,MAAK;UAAA;QAEX,CAAC,CADC;QACD;;QACDE,KAAK,EAAE,MAAK;UAAA;UAAA;UACV;UACA,IAAI,CAACC,aAAa,EAAE;QACtB;OACD,CAAC;KACH;MAAA;IAAA;EACH;EAEAF,yBAAyB;IAAA;IAAA;IACvB;IACAgC,WAAW,CAAC,MAAK;MAAA;MAAA;MACf,IAAI,CAACD,oBAAoB,EAAE;IAC7B,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;EACnB;EAEQ7B,aAAa;IAAA;IAAA;IACnB,IAAI,CAACf,YAAY,CAAC8C,cAAc,EAAE;IAAC;IACnC,IAAI,CAAC7C,kBAAkB,CAACW,IAAI,CAAC,IAAI,CAAC;IAAC;IACnC,IAAI,CAACT,sBAAsB,CAACS,IAAI,CAAC,KAAK,CAAC;IAAC;IACxC,IAAI,CAACb,MAAM,CAACgD,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,eAAe;IAAA;IAAA;IACb,OAAO,IAAI,CAAChD,YAAY,CAACgD,eAAe,EAAE;EAC5C;EAIAC,OAAO,CAACC,IAAY;IAAA;IAClB,MAAM3C,IAAI,8BAAG,IAAI,CAACN,kBAAkB,CAACkD,KAAK;IAAC;IAC3C,OAAO,kCAAI,EAAEC,KAAK,CAACC,QAAQ,CAACH,IAAI,CAAC,mCAAI,KAAK;EAC5C;EAEAI,aAAa,CAACC,UAAkB;IAAA;IAC9B,MAAMhD,IAAI,8BAAG,IAAI,CAACN,kBAAkB,CAACkD,KAAK;IAAC;IAC3C,OAAO,kCAAI,EAAEK,WAAW,CAACH,QAAQ,CAACE,UAAU,CAAC,mCAAI,KAAK;EACxD;EAEQ1B,WAAW,CAACf,KAAU;IAAA;IAC5B,IAAI2C,YAAY,8BAAG,mBAAmB;IAAC;IAEvC,IAAI3C,KAAK,CAACA,KAAK,EAAE4C,OAAO,EAAE;MAAA;MAAA;MACxBD,YAAY,GAAG3C,KAAK,CAACA,KAAK,CAAC4C,OAAO;KACnC,MAAM;MAAA;MAAA;MAAA,IAAI5C,KAAK,CAAC4C,OAAO,EAAE;QAAA;QAAA;QACxBD,YAAY,GAAG3C,KAAK,CAAC4C,OAAO;OAC7B;QAAA;MAAA;;IAAA;IAED,OAAOnE,UAAU,CAACkE,YAAY,CAAC;EACjC;;;;;;;;;;;;;;;;;AA3PW7D,WAAW,eAHvBR,UAAU,CAAC;EACVuE,UAAU,EAAE;CACb,CAAC,GACW/D,WAAW,CA4PvB;SA5PYA,WAAW", "names": ["Injectable", "HttpClient", "BehaviorSubject", "throwError", "catchError", "tap", "Router", "TokenService", "AuthService", "constructor", "http", "router", "tokenService", "currentUserSubject", "asObservable", "isAuthenticatedSubject", "initializeAuth", "token", "getToken", "user", "getStoredUser", "isTokenExpired", "refreshToken", "subscribe", "next", "startTokenExpirationCheck", "error", "clearAuthData", "login", "credentials", "post", "API_URL", "pipe", "response", "success", "accessToken", "rememberMe", "setRememberMe", "setToken", "setRefreshToken", "setUser", "handleError", "register", "userData", "endpoint", "isOracleRegistration", "logout", "getRefreshToken", "getRememberMe", "getCurrentUser", "get", "changePassword", "request", "forgotPassword", "resetPassword", "loginWithOAuth", "checkTokenExpiration", "setInterval", "clearAllTokens", "navigate", "isAuthenticated", "hasRole", "role", "value", "roles", "includes", "hasPermission", "permission", "permissions", "errorMessage", "message", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\r\nimport { map, catchError, tap } from 'rxjs/operators';\r\nimport { Router } from '@angular/router';\r\nimport { TokenService } from './token.service';\r\nimport {\r\n  LoginRequest,\r\n  RegisterRequest,\r\n  OracleRegisterRequest,\r\n  AuthResponse,\r\n  UserInfo,\r\n  ChangePasswordRequest,\r\n  ForgotPasswordRequest,\r\n  ResetPasswordRequest,\r\n  ApiResponse,\r\n  OAuthLoginRequest\r\n} from '../models/auth.models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthService {\r\n  private readonly API_URL = '/api/auth';\r\n  private readonly TOKEN_KEY = 'access_token';\r\n  private readonly REFRESH_TOKEN_KEY = 'refresh_token';\r\n  private readonly USER_KEY = 'user_info';\r\n  private readonly REMEMBER_ME_KEY = 'remember_me';\r\n\r\n  private currentUserSubject = new BehaviorSubject<UserInfo | null>(null);\r\n  public currentUser$ = this.currentUserSubject.asObservable();\r\n\r\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);\r\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private router: Router,\r\n    private tokenService: TokenService\r\n  ) {\r\n    this.initializeAuth();\r\n  }\r\n\r\n  private initializeAuth(): void {\r\n    const token = this.tokenService.getToken();\r\n    const user = this.tokenService.getStoredUser();\r\n\r\n    if (token && user) {\r\n      // Check if token is expired\r\n      if (this.tokenService.isTokenExpired(token)) {\r\n        // Try to refresh the token\r\n        this.refreshToken().subscribe({\r\n          next: () => {\r\n            this.currentUserSubject.next(user);\r\n            this.isAuthenticatedSubject.next(true);\r\n            this.startTokenExpirationCheck();\r\n          },\r\n          error: () => {\r\n            // Refresh failed, clear auth data\r\n            this.clearAuthData();\r\n          }\r\n        });\r\n      } else {\r\n        this.currentUserSubject.next(user);\r\n        this.isAuthenticatedSubject.next(true);\r\n        this.startTokenExpirationCheck();\r\n      }\r\n    }\r\n  }\r\n\r\n  login(credentials: LoginRequest): Observable<AuthResponse> {\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/login`, credentials)\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken && response.user) {\r\n            // Store remember me preference\r\n            const rememberMe = credentials.rememberMe || false;\r\n            this.tokenService.setRememberMe(rememberMe);\r\n\r\n            this.tokenService.setToken(response.accessToken, rememberMe);\r\n            if (response.refreshToken) {\r\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\r\n            }\r\n            this.tokenService.setUser(response.user, rememberMe);\r\n            this.currentUserSubject.next(response.user);\r\n            this.isAuthenticatedSubject.next(true);\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  register(userData: RegisterRequest | OracleRegisterRequest): Observable<AuthResponse> {\r\n    // Determine endpoint based on data type\r\n    const endpoint = this.isOracleRegistration(userData) ? 'register-oracle' : 'register';\r\n\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/${endpoint}`, userData)\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken && response.user) {\r\n            // For registration, default to remember me = false\r\n            const rememberMe = false;\r\n            this.tokenService.setRememberMe(rememberMe);\r\n\r\n            this.tokenService.setToken(response.accessToken, rememberMe);\r\n            if (response.refreshToken) {\r\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\r\n            }\r\n            this.tokenService.setUser(response.user, rememberMe);\r\n            this.currentUserSubject.next(response.user);\r\n            this.isAuthenticatedSubject.next(true);\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  private isOracleRegistration(userData: RegisterRequest | OracleRegisterRequest): userData is OracleRegisterRequest {\r\n    return 'professionalTitle' in userData;\r\n  }\r\n\r\n  logout(): Observable<any> {\r\n    return this.http.post(`${this.API_URL}/logout`, {})\r\n      .pipe(\r\n        tap(() => {\r\n          this.clearAuthData();\r\n        }),\r\n        catchError(() => {\r\n          // Even if logout fails on server, clear local data\r\n          this.clearAuthData();\r\n          return throwError('Logout failed');\r\n        })\r\n      );\r\n  }\r\n\r\n  refreshToken(): Observable<AuthResponse> {\r\n    const refreshToken = this.tokenService.getRefreshToken();\r\n    if (!refreshToken) {\r\n      return throwError('No refresh token available');\r\n    }\r\n\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/refresh-token`, { refreshToken })\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken) {\r\n            const rememberMe = this.tokenService.getRememberMe();\r\n            this.tokenService.setToken(response.accessToken, rememberMe);\r\n            if (response.refreshToken) {\r\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\r\n            }\r\n            if (response.user) {\r\n              this.tokenService.setUser(response.user, rememberMe);\r\n              this.currentUserSubject.next(response.user);\r\n            }\r\n          }\r\n        }),\r\n        catchError(error => {\r\n          this.clearAuthData();\r\n          return throwError(error);\r\n        })\r\n      );\r\n  }\r\n\r\n  getCurrentUser(): Observable<UserInfo> {\r\n    return this.http.get<UserInfo>(`${this.API_URL}/me`)\r\n      .pipe(\r\n        tap(user => {\r\n          const rememberMe = this.tokenService.getRememberMe();\r\n          this.tokenService.setUser(user, rememberMe);\r\n          this.currentUserSubject.next(user);\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  changePassword(request: ChangePasswordRequest): Observable<ApiResponse<any>> {\r\n    return this.http.post<ApiResponse<any>>(`${this.API_URL}/change-password`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  forgotPassword(request: ForgotPasswordRequest): Observable<ApiResponse<any>> {\r\n    return this.http.post<ApiResponse<any>>(`${this.API_URL}/forgot-password`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  resetPassword(request: ResetPasswordRequest): Observable<ApiResponse<any>> {\r\n    return this.http.post<ApiResponse<any>>(`${this.API_URL}/reset-password`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  loginWithOAuth(request: OAuthLoginRequest): Observable<AuthResponse> {\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/oauth-login`, request)\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken && response.user) {\r\n            // For OAuth, default to remember me = true for better UX\r\n            const rememberMe = true;\r\n            this.tokenService.setRememberMe(rememberMe);\r\n\r\n            this.tokenService.setToken(response.accessToken, rememberMe);\r\n            if (response.refreshToken) {\r\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\r\n            }\r\n            this.tokenService.setUser(response.user, rememberMe);\r\n            this.currentUserSubject.next(response.user);\r\n            this.isAuthenticatedSubject.next(true);\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Token management - delegated to TokenService\r\n  getToken(): string | null {\r\n    return this.tokenService.getToken();\r\n  }\r\n\r\n  checkTokenExpiration(): void {\r\n    const token = this.tokenService.getToken();\r\n    if (token && this.tokenService.isTokenExpired(token)) {\r\n      // Try to refresh the token\r\n      this.refreshToken().subscribe({\r\n        next: () => {\r\n          // Token refreshed successfully\r\n        },\r\n        error: () => {\r\n          // Refresh failed, logout user\r\n          this.clearAuthData();\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  startTokenExpirationCheck(): void {\r\n    // Check token expiration every 5 minutes\r\n    setInterval(() => {\r\n      this.checkTokenExpiration();\r\n    }, 5 * 60 * 1000);\r\n  }\r\n\r\n  private clearAuthData(): void {\r\n    this.tokenService.clearAllTokens();\r\n    this.currentUserSubject.next(null);\r\n    this.isAuthenticatedSubject.next(false);\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  isAuthenticated(): boolean {\r\n    return this.tokenService.isAuthenticated();\r\n  }\r\n\r\n\r\n\r\n  hasRole(role: string): boolean {\r\n    const user = this.currentUserSubject.value;\r\n    return user?.roles.includes(role) || false;\r\n  }\r\n\r\n  hasPermission(permission: string): boolean {\r\n    const user = this.currentUserSubject.value;\r\n    return user?.permissions.includes(permission) || false;\r\n  }\r\n\r\n  private handleError(error: any): Observable<never> {\r\n    let errorMessage = 'An error occurred';\r\n\r\n    if (error.error?.message) {\r\n      errorMessage = error.error.message;\r\n    } else if (error.message) {\r\n      errorMessage = error.message;\r\n    }\r\n\r\n    return throwError(errorMessage);\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}