.profile-edit-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: var(--theme-background);
}

.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 0;
}

.edit-header h1 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  color: var(--theme-primary);
  font-size: 2rem;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-section {
  margin-bottom: 0;
}

.form-section .mat-card-header {
  padding-bottom: 16px;
}

.form-section .mat-card-title {
  color: var(--theme-primary);
  font-size: 1.3rem;
}

/* Form Layout */
.form-row {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  margin-bottom: 16px;
}

.full-width {
  width: 100%;
}

.half-width {
  flex: 1;
  min-width: 0;
}

.third-width {
  flex: 1;
  min-width: 0;
}

.checkbox-field {
  display: flex;
  align-items: center;
  min-height: 56px;
}

/* Photo Upload Section */
.photo-section .mat-card-content {
  padding: 24px;
}

.photo-uploads {
  display: flex;
  gap: 32px;
  align-items: flex-start;
}

.profile-photo-upload,
.cover-photo-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.photo-preview {
  position: relative;
  cursor: pointer;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.photo-preview:hover {
  transform: scale(1.02);
}

.profile-photo {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--theme-surface);
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.cover-preview {
  position: relative;
  width: 300px;
  height: 120px;
  border-radius: 12px;
  overflow: hidden;
}

.cover-photo {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.photo-overlay.uploading {
  opacity: 1;
  background: rgba(0, 0, 0, 0.7);
}

.photo-preview:hover .photo-overlay {
  opacity: 1;
}

.upload-hint {
  font-size: 12px;
  color: #666;
  margin-top: 8px;
  text-align: center;
}

.photo-overlay mat-icon {
  color: white;
  font-size: 32px;
  width: 32px;
  height: 32px;
}

/* Phone Numbers Section */
.phone-numbers-section {
  margin-top: 24px;
}

.phone-numbers-section h4 {
  margin: 0 0 16px 0;
  color: var(--theme-text-primary);
  font-size: 1.1rem;
}

.phone-number-item {
  border: 1px solid rgba(0,0,0,0.1);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background-color: rgba(0,0,0,0.02);
}

.phone-input {
  flex: 2;
}

.phone-type {
  flex: 1;
  min-width: 120px;
}

.phone-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
  min-width: 120px;
}

/* Business Address Section */
.business-address-section {
  margin-top: 24px;
}

.business-address-section h4 {
  margin: 0 0 16px 0;
  color: var(--theme-text-primary);
  font-size: 1.1rem;
}

/* Social Links Section */
.social-link-item {
  border: 1px solid rgba(0,0,0,0.1);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background-color: rgba(0,0,0,0.02);
}

.platform-select {
  flex: 1;
  min-width: 140px;
}

.url-input {
  flex: 2;
}

.link-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
  min-width: 100px;
}

/* Privacy Settings */
.privacy-controls {
  padding: 16px;
}

.toggle-label {
  font-weight: 500;
  color: var(--theme-text-primary);
}

.toggle-description {
  margin: 8px 0 0 0;
  color: var(--theme-text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  gap: 16px;
}

.loading-container p {
  color: var(--theme-text-secondary);
}

/* Form Validation */
.mat-form-field.ng-invalid.ng-touched .mat-form-field-outline-thick {
  color: var(--theme-error);
}

.mat-error {
  font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-edit-container {
    padding: 16px;
  }
  
  .edit-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .form-row {
    flex-direction: column;
    gap: 0;
  }
  
  .half-width,
  .third-width {
    width: 100%;
  }
  
  .photo-uploads {
    flex-direction: column;
    align-items: center;
    gap: 24px;
  }
  
  .cover-preview {
    width: 100%;
    max-width: 300px;
  }
  
  .phone-controls,
  .link-controls {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .edit-header h1 {
    font-size: 1.5rem;
  }
  
  .profile-photo {
    width: 120px;
    height: 120px;
  }
  
  .cover-preview {
    height: 100px;
  }
  
  .phone-number-item,
  .social-link-item {
    padding: 12px;
  }
}

/* Animation for form sections */
.form-section {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects */
.form-section:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
  transition: box-shadow 0.3s ease;
}

/* Focus styles */
.mat-form-field.mat-focused .mat-form-field-outline-thick {
  color: var(--theme-primary);
}

/* Button styles */
button[mat-stroked-button] {
  border-color: var(--theme-primary);
  color: var(--theme-primary);
}

button[mat-stroked-button]:hover {
  background-color: rgba(103, 58, 183, 0.04);
}

/* Chip styles for future use */
.mat-chip {
  background-color: var(--theme-primary);
  color: white;
}

/* Progress indicator */
.mat-progress-spinner circle {
  stroke: var(--theme-primary);
}

/* Skills Management */
.skill-item {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.skill-fields {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  flex-wrap: wrap;
}

.skill-name {
  flex: 2;
  min-width: 200px;
}

.skill-category,
.skill-proficiency {
  flex: 1;
  min-width: 150px;
}

.remove-skill {
  margin-top: 8px;
}

.add-skill-btn {
  margin-top: 16px;
}

/* Consultation Rates */
.rates-section {
  padding: 16px 0;
}

.rate-fields {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.rate-field {
  flex: 1;
  min-width: 200px;
}

.currency-field {
  flex: 0 0 200px;
}

/* Service Offerings */
.service-item {
  margin-bottom: 24px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  background: #fafafa;
  position: relative;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.service-header h4 {
  margin: 0;
  color: var(--theme-primary);
  font-weight: 500;
}

.service-fields {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.service-name,
.service-description {
  width: 100%;
}

.service-pricing {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.price-field {
  flex: 1;
  min-width: 150px;
}

.duration-field {
  flex: 1;
  min-width: 150px;
}

.remove-service {
  position: absolute;
  top: 16px;
  right: 16px;
}

.add-service-btn {
  margin-top: 16px;
}
