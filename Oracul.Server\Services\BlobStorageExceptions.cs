namespace Oracul.Server.Services
{
    /// <summary>
    /// Base exception for blob storage operations
    /// </summary>
    public class BlobStorageException : Exception
    {
        public BlobStorageException(string message) : base(message) { }
        public BlobStorageException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Exception thrown when a blob is not found
    /// </summary>
    public class BlobNotFoundException : BlobStorageException
    {
        public string BlobPath { get; }

        public BlobNotFoundException(string blobPath) 
            : base($"Blob not found: {blobPath}")
        {
            BlobPath = blobPath;
        }

        public BlobNotFoundException(string blobPath, Exception innerException) 
            : base($"Blob not found: {blobPath}", innerException)
        {
            BlobPath = blobPath;
        }
    }

    /// <summary>
    /// Exception thrown when blob upload fails
    /// </summary>
    public class BlobUploadException : BlobStorageException
    {
        public string? FileName { get; }
        public string? ContainerPath { get; }

        public BlobUploadException(string message) : base(message) { }

        public BlobUploadException(string message, string fileName, string containerPath) 
            : base(message)
        {
            FileName = fileName;
            ContainerPath = containerPath;
        }

        public BlobUploadException(string message, Exception innerException) 
            : base(message, innerException) { }

        public BlobUploadException(string message, string fileName, string containerPath, Exception innerException) 
            : base(message, innerException)
        {
            FileName = fileName;
            ContainerPath = containerPath;
        }
    }

    /// <summary>
    /// Exception thrown when file validation fails
    /// </summary>
    public class FileValidationException : BlobStorageException
    {
        public List<string> ValidationErrors { get; }

        public FileValidationException(List<string> validationErrors) 
            : base($"File validation failed: {string.Join(", ", validationErrors)}")
        {
            ValidationErrors = validationErrors;
        }

        public FileValidationException(string validationError) 
            : base($"File validation failed: {validationError}")
        {
            ValidationErrors = new List<string> { validationError };
        }
    }
}
