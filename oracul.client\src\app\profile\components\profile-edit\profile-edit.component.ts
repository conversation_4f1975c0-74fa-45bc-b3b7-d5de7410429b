import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';

import { ProfileService } from '../../services/profile.service';
import { AuthService } from '../../../auth/services/auth.service';
import { UserProfile, ProfileUpdateRequest, ProfileSkill, ConsultationRates, ServiceOffering } from '../../models/profile.models';

@Component({
  selector: 'app-profile-edit',
  templateUrl: './profile-edit.component.html',
  styleUrls: ['./profile-edit.component.css']
})
export class ProfileEditComponent implements OnInit, OnDestroy {
  profileForm: FormGroup;
  profile: UserProfile | null = null;
  isLoading = true;
  isSaving = false;
  isUploadingProfilePhoto = false;
  isUploadingCoverPhoto = false;
  profilePhotoPreview: string | null = null;
  coverPhotoPreview: string | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private formBuilder: FormBuilder,
    private profileService: ProfileService,
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.profileForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadProfile();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createForm(): FormGroup {
    return this.formBuilder.group({
      // Basic Information
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      professionalTitle: [''],
      headline: ['', Validators.maxLength(220)],
      summary: ['', Validators.maxLength(2000)],
      
      // Location
      location: this.formBuilder.group({
        city: [''],
        state: [''],
        country: [''],
        displayLocation: ['']
      }),
      
      // Contact Information
      contactInfo: this.formBuilder.group({
        email: ['', Validators.email],
        isEmailPublic: [false],
        website: [''],
        portfolioUrl: [''],
        phoneNumbers: this.formBuilder.array([]),
        businessAddress: this.formBuilder.group({
          street: [''],
          city: [''],
          state: [''],
          postalCode: [''],
          country: [''],
          isPublic: [false]
        })
      }),
      
      // Privacy Settings
      isPublic: [true],

      // Social Links
      socialLinks: this.formBuilder.array([]),

      // Skills
      skills: this.formBuilder.array([]),

      // Consultation Rates
      consultationRates: this.formBuilder.group({
        hourlyRate: [null, [Validators.min(0), Validators.max(10000)]],
        sessionRate: [null, [Validators.min(0), Validators.max(10000)]],
        currency: ['BGN', Validators.required]
      }),

      // Service Offerings
      serviceOfferings: this.formBuilder.array([])
    });
  }

  private loadProfile(): void {
    this.profileService.getCurrentUserProfile()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (profile) => {
          this.profile = profile;
          this.populateForm(profile);
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading profile:', error);
          this.snackBar.open('Error loading profile', 'Close', { duration: 5000 });
          this.router.navigate(['/dashboard']);
          this.isLoading = false;
        }
      });
  }

  private populateForm(profile: UserProfile): void {
    this.profileForm.patchValue({
      firstName: profile.firstName,
      lastName: profile.lastName,
      professionalTitle: profile.professionalTitle || '',
      headline: profile.headline || '',
      summary: profile.summary || '',
      location: {
        city: profile.location?.city || '',
        state: profile.location?.state || '',
        country: profile.location?.country || '',
        displayLocation: profile.location?.displayLocation || ''
      },
      contactInfo: {
        email: profile.contactInfo.email || '',
        isEmailPublic: profile.contactInfo.isEmailPublic,
        website: profile.contactInfo.website || '',
        portfolioUrl: profile.contactInfo.portfolioUrl || '',
        businessAddress: {
          street: profile.contactInfo.businessAddress?.street || '',
          city: profile.contactInfo.businessAddress?.city || '',
          state: profile.contactInfo.businessAddress?.state || '',
          postalCode: profile.contactInfo.businessAddress?.postalCode || '',
          country: profile.contactInfo.businessAddress?.country || '',
          isPublic: profile.contactInfo.businessAddress?.isPublic || false
        }
      },
      isPublic: profile.isPublic,
      consultationRates: {
        hourlyRate: profile.consultationRates?.hourlyRate || null,
        sessionRate: profile.consultationRates?.sessionRate || null,
        currency: profile.consultationRates?.currency || 'BGN'
      }
    });

    // Populate phone numbers
    this.setPhoneNumbers(profile.contactInfo.phoneNumbers || []);

    // Populate social links
    this.setSocialLinks(profile.socialLinks || []);

    // Populate skills
    this.setSkills(profile.skills || []);

    // Populate service offerings
    this.setServiceOfferings(profile.serviceOfferings || []);
  }

  // Phone Numbers Management
  get phoneNumbers(): FormArray {
    return this.profileForm.get('contactInfo.phoneNumbers') as FormArray;
  }

  private setPhoneNumbers(phones: any[]): void {
    const phoneArray = this.phoneNumbers;
    phoneArray.clear();
    
    phones.forEach(phone => {
      phoneArray.push(this.formBuilder.group({
        id: [phone.id],
        number: [phone.number, Validators.required],
        type: [phone.type, Validators.required],
        isPublic: [phone.isPublic],
        isPrimary: [phone.isPrimary]
      }));
    });
  }

  addPhoneNumber(): void {
    const phoneGroup = this.formBuilder.group({
      id: [null],
      number: ['', Validators.required],
      type: ['mobile', Validators.required],
      isPublic: [false],
      isPrimary: [false]
    });
    
    this.phoneNumbers.push(phoneGroup);
  }

  removePhoneNumber(index: number): void {
    this.phoneNumbers.removeAt(index);
  }

  // Social Links Management
  get socialLinks(): FormArray {
    return this.profileForm.get('socialLinks') as FormArray;
  }

  private setSocialLinks(links: any[]): void {
    const linksArray = this.socialLinks;
    linksArray.clear();
    
    links.forEach(link => {
      linksArray.push(this.formBuilder.group({
        id: [link.id],
        platform: [link.platform, Validators.required],
        url: [link.url, [Validators.required, Validators.pattern('https?://.+')]],
        displayName: [link.displayName],
        isPublic: [link.isPublic]
      }));
    });
  }

  addSocialLink(): void {
    const linkGroup = this.formBuilder.group({
      id: [null],
      platform: ['linkedin', Validators.required],
      url: ['', [Validators.required, Validators.pattern('https?://.+')]],
      displayName: [''],
      isPublic: [true]
    });
    
    this.socialLinks.push(linkGroup);
  }

  removeSocialLink(index: number): void {
    this.socialLinks.removeAt(index);
  }

  // Skills Management
  get skills(): FormArray {
    return this.profileForm.get('skills') as FormArray;
  }

  private setSkills(skills: ProfileSkill[]): void {
    const skillArray = this.skills;
    skillArray.clear();

    skills.forEach(skill => {
      skillArray.push(this.formBuilder.group({
        id: [skill.id],
        name: [skill.name, Validators.required],
        category: [skill.category || ''],
        proficiencyLevel: [skill.proficiencyLevel || 'intermediate', Validators.required]
      }));
    });
  }

  addSkill(): void {
    const skillGroup = this.formBuilder.group({
      id: [null],
      name: ['', Validators.required],
      category: [''],
      proficiencyLevel: ['intermediate', Validators.required]
    });

    this.skills.push(skillGroup);
  }

  removeSkill(index: number): void {
    this.skills.removeAt(index);
  }

  // Service Offerings Management
  get serviceOfferings(): FormArray {
    return this.profileForm.get('serviceOfferings') as FormArray;
  }

  private setServiceOfferings(services: ServiceOffering[]): void {
    const serviceArray = this.serviceOfferings;
    serviceArray.clear();

    services.forEach(service => {
      serviceArray.push(this.formBuilder.group({
        id: [service.id],
        name: [service.name, Validators.required],
        description: [service.description, Validators.required],
        price: [service.price, [Validators.required, Validators.min(0)]],
        currency: [service.currency || 'BGN', Validators.required],
        duration: [service.duration],
        category: [service.category || ''],
        isActive: [service.isActive !== false] // default to true
      }));
    });
  }

  addServiceOffering(): void {
    const serviceGroup = this.formBuilder.group({
      id: [null],
      name: ['', Validators.required],
      description: ['', Validators.required],
      price: [0, [Validators.required, Validators.min(0)]],
      currency: ['BGN', Validators.required],
      duration: [60], // default 60 minutes
      category: [''],
      isActive: [true]
    });

    this.serviceOfferings.push(serviceGroup);
  }

  removeServiceOffering(index: number): void {
    this.serviceOfferings.removeAt(index);
  }

  // Form Submission
  onSubmit(): void {
    if (this.validateForm()) {
      this.isSaving = true;
      
      const formValue = this.profileForm.value;
      const updateRequest: ProfileUpdateRequest = {
        firstName: formValue.firstName,
        lastName: formValue.lastName,
        professionalTitle: formValue.professionalTitle,
        headline: formValue.headline,
        location: formValue.location,
        contactInfo: formValue.contactInfo,
        summary: formValue.summary,
        isPublic: formValue.isPublic,
        skills: formValue.skills || [],
        consultationRates: formValue.consultationRates,
        serviceOfferings: formValue.serviceOfferings || []
      };

      this.profileService.updateProfile(updateRequest)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (updatedProfile) => {
            this.isSaving = false;
            this.snackBar.open('Profile updated successfully!', 'Close', { duration: 3000 });
            this.router.navigate(['/profile', updatedProfile.slug]);
          },
          error: (error) => {
            this.isSaving = false;
            console.error('Error updating profile:', error);
            this.snackBar.open('Error updating profile. Please try again.', 'Close', { duration: 5000 });
          }
        });
    }
    // Validation is now handled in validateForm() method
  }

  onCancel(): void {
    if (this.profile) {
      this.router.navigate(['/profile', this.profile.slug]);
    } else {
      this.router.navigate(['/dashboard']);
    }
  }

  // File Upload Methods
  onProfilePhotoSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      if (this.validateImageFile(file, 'profile')) {
        this.createImagePreview(file, 'profile');
        this.uploadProfilePhoto(file);
      }
    }
    // Reset the input to allow selecting the same file again
    event.target.value = '';
  }

  onCoverPhotoSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      if (this.validateImageFile(file, 'cover')) {
        this.createImagePreview(file, 'cover');
        this.uploadCoverPhoto(file);
      }
    }
    // Reset the input to allow selecting the same file again
    event.target.value = '';
  }

  private validateImageFile(file: File, type: 'profile' | 'cover'): boolean {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      this.snackBar.open('Please select a valid image file (JPEG, PNG, GIF, or WebP)', 'Close', { duration: 5000 });
      return false;
    }

    // Check file size (5MB for profile, 10MB for cover)
    const maxSize = type === 'profile' ? 5 * 1024 * 1024 : 10 * 1024 * 1024;
    if (file.size > maxSize) {
      const maxSizeMB = maxSize / (1024 * 1024);
      this.snackBar.open(`File size must be less than ${maxSizeMB}MB`, 'Close', { duration: 5000 });
      return false;
    }

    return true;
  }

  private createImagePreview(file: File, type: 'profile' | 'cover'): void {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      if (type === 'profile') {
        this.profilePhotoPreview = e.target.result;
      } else {
        this.coverPhotoPreview = e.target.result;
      }
    };
    reader.readAsDataURL(file);
  }

  private uploadProfilePhoto(file: File): void {
    this.isUploadingProfilePhoto = true;
    this.profileService.uploadProfilePhoto(file)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isUploadingProfilePhoto = false;
          if (this.profile) {
            this.profile.profilePhotoUrl = response.url;
          }
          this.profilePhotoPreview = null; // Clear preview since we have the actual URL
          this.snackBar.open('Profile photo updated successfully!', 'Close', { duration: 3000 });
        },
        error: (error) => {
          this.isUploadingProfilePhoto = false;
          this.profilePhotoPreview = null; // Clear preview on error
          console.error('Error uploading profile photo:', error);
          const errorMessage = error.error?.message || 'Error uploading profile photo. Please try again.';
          this.snackBar.open(errorMessage, 'Close', { duration: 5000 });
        }
      });
  }

  private uploadCoverPhoto(file: File): void {
    this.isUploadingCoverPhoto = true;
    this.profileService.uploadCoverPhoto(file)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isUploadingCoverPhoto = false;
          if (this.profile) {
            this.profile.coverPhotoUrl = response.url;
          }
          this.coverPhotoPreview = null; // Clear preview since we have the actual URL
          this.snackBar.open('Cover photo updated successfully!', 'Close', { duration: 3000 });
        },
        error: (error) => {
          this.isUploadingCoverPhoto = false;
          this.coverPhotoPreview = null; // Clear preview on error
          console.error('Error uploading cover photo:', error);
          const errorMessage = error.error?.message || 'Error uploading cover photo. Please try again.';
          this.snackBar.open(errorMessage, 'Close', { duration: 5000 });
        }
      });
  }

  // Utility Methods
  private markFormGroupTouched(): void {
    Object.keys(this.profileForm.controls).forEach(key => {
      const control = this.profileForm.get(key);
      control?.markAsTouched();
      
      if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            Object.keys(arrayControl.controls).forEach(arrayKey => {
              arrayControl.get(arrayKey)?.markAsTouched();
            });
          }
        });
      }
    });
  }

  getErrorMessage(fieldName: string): string {
    const control = this.profileForm.get(fieldName);
    if (!control || !control.errors) return '';

    const fieldDisplayName = this.getFieldDisplayName(fieldName);

    if (control.hasError('required')) {
      return `${fieldDisplayName} is required`;
    }
    if (control.hasError('email')) {
      return 'Please enter a valid email address';
    }
    if (control.hasError('minlength')) {
      const requiredLength = control.errors['minlength'].requiredLength;
      return `${fieldDisplayName} must be at least ${requiredLength} characters`;
    }
    if (control.hasError('maxlength')) {
      const maxLength = control.errors['maxlength'].requiredLength;
      return `${fieldDisplayName} must be no more than ${maxLength} characters`;
    }
    if (control.hasError('pattern')) {
      return 'Please enter a valid URL';
    }
    if (control.hasError('min')) {
      const minValue = control.errors['min'].min;
      return `${fieldDisplayName} must be at least ${minValue}`;
    }
    if (control.hasError('max')) {
      const maxValue = control.errors['max'].max;
      return `${fieldDisplayName} cannot exceed ${maxValue}`;
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const fieldNames: { [key: string]: string } = {
      'firstName': 'First Name',
      'lastName': 'Last Name',
      'professionalTitle': 'Professional Title',
      'headline': 'Headline',
      'summary': 'Summary',
      'contactInfo.email': 'Email',
      'contactInfo.website': 'Website',
      'contactInfo.portfolioUrl': 'Portfolio URL',
      'location.city': 'City',
      'location.state': 'State',
      'location.country': 'Country',
      'location.displayLocation': 'Display Location',
      'consultationRates.hourlyRate': 'Hourly Rate',
      'consultationRates.sessionRate': 'Session Rate',
      'consultationRates.currency': 'Currency'
    };
    return fieldNames[fieldName] || fieldName;
  }

  // Enhanced form validation
  validateForm(): boolean {
    if (this.profileForm.invalid) {
      this.markFormGroupTouched();

      // Find first invalid field and focus on it
      const firstInvalidField = this.findFirstInvalidField();
      if (firstInvalidField) {
        firstInvalidField.focus();
      }

      // Show specific error message
      const errors = this.getFormErrors();
      if (errors.length > 0) {
        this.snackBar.open(`Please fix the following errors: ${errors.join(', ')}`, 'Close', { duration: 5000 });
      }

      return false;
    }
    return true;
  }

  private findFirstInvalidField(): HTMLElement | null {
    const invalidFields = document.querySelectorAll('.mat-form-field.ng-invalid input, .mat-form-field.ng-invalid textarea, .mat-form-field.ng-invalid mat-select');
    return invalidFields.length > 0 ? invalidFields[0] as HTMLElement : null;
  }

  private getFormErrors(): string[] {
    const errors: string[] = [];

    // Check main form fields
    Object.keys(this.profileForm.controls).forEach(key => {
      const control = this.profileForm.get(key);
      if (control && control.invalid && control.touched) {
        const errorMessage = this.getErrorMessage(key);
        if (errorMessage) {
          errors.push(errorMessage);
        }
      }
    });

    // Check nested form groups
    const contactInfo = this.profileForm.get('contactInfo') as FormGroup;
    if (contactInfo && contactInfo.invalid) {
      Object.keys(contactInfo.controls).forEach(key => {
        const control = contactInfo.get(key);
        if (control && control.invalid && control.touched) {
          const errorMessage = this.getErrorMessage(`contactInfo.${key}`);
          if (errorMessage) {
            errors.push(errorMessage);
          }
        }
      });
    }

    return errors.slice(0, 3); // Limit to first 3 errors to avoid overwhelming the user
  }

  // Platform options for social links
  getPlatformOptions() {
    return [
      { value: 'linkedin', label: 'LinkedIn' },
      { value: 'twitter', label: 'Twitter' },
      { value: 'github', label: 'GitHub' },
      { value: 'behance', label: 'Behance' },
      { value: 'dribbble', label: 'Dribbble' },
      { value: 'instagram', label: 'Instagram' },
      { value: 'facebook', label: 'Facebook' },
      { value: 'youtube', label: 'YouTube' },
      { value: 'other', label: 'Other' }
    ];
  }

  // Phone type options
  getPhoneTypeOptions() {
    return [
      { value: 'mobile', label: 'Mobile' },
      { value: 'business', label: 'Business' },
      { value: 'home', label: 'Home' }
    ];
  }

  // Skill category options
  getSkillCategoryOptions() {
    return [
      { value: 'Astrology', label: 'Astrology' },
      { value: 'Crystal Healing', label: 'Crystal Healing' },
      { value: 'Palmistry', label: 'Palmistry' },
      { value: 'Spiritual Counseling', label: 'Spiritual Counseling' },
      { value: 'Numerology', label: 'Numerology' },
      { value: 'Tarot Reading', label: 'Tarot Reading' },
      { value: 'Energy Healing', label: 'Energy Healing' },
      { value: 'Meditation', label: 'Meditation' },
      { value: 'Other', label: 'Other' }
    ];
  }

  // Proficiency level options
  getProficiencyLevelOptions() {
    return [
      { value: 'beginner', label: 'Beginner' },
      { value: 'intermediate', label: 'Intermediate' },
      { value: 'advanced', label: 'Advanced' },
      { value: 'expert', label: 'Expert' }
    ];
  }

  // Service category options
  getServiceCategoryOptions() {
    return [
      { value: 'Reading', label: 'Reading' },
      { value: 'Consultation', label: 'Consultation' },
      { value: 'Healing', label: 'Healing' },
      { value: 'Workshop', label: 'Workshop' },
      { value: 'Course', label: 'Course' },
      { value: 'Other', label: 'Other' }
    ];
  }

  // Currency options
  getCurrencyOptions() {
    return [
      { value: 'BGN', label: 'BGN (Bulgarian Lev)' },
      { value: 'EUR', label: 'EUR (Euro)' },
      { value: 'USD', label: 'USD (US Dollar)' },
      { value: 'GBP', label: 'GBP (British Pound)' }
    ];
  }
}
