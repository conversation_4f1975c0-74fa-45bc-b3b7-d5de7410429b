<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SpaRoot>..\oracul.client</SpaRoot>
    <SpaProxyLaunchCommand>npm start</SpaProxyLaunchCommand>
    <SpaProxyServerUrl>https://localhost:53792</SpaProxyServerUrl>
    <UserSecretsId>00c9309f-c381-4501-a05f-3e50d0e97bea</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Data.Tables" Version="12.10.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.24.1" />
    <PackageReference Include="Azure.Storage.Files.Shares" Version="12.22.0" />
    <PackageReference Include="Azure.Storage.Queues" Version="12.22.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.SpaProxy">
      <Version>9.*-*</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Azure" Version="1.11.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.11.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\oracul.client\oracul.client.esproj">
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
    <ProjectReference Include="..\Oracul.Data\Oracul.Data.csproj" />
  </ItemGroup>

</Project>
