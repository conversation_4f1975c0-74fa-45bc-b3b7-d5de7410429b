{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./app.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./app.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { ThemeService } from './core/theme/theme.service';\nlet AppComponent = class AppComponent {\n  constructor(themeService) {\n    this.themeService = themeService;\n    this.title = 'oracul.client';\n  }\n  ngOnInit() {\n    // Initialize theme\n    this.themeService.getCurrentTheme();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ThemeService\n    }];\n  }\n};\nAppComponent = __decorate([Component({\n  selector: 'app-root',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AppComponent);\nexport { AppComponent };", "map": {"version": 3, "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,4BAA4B;AAOlD,IAAMC,YAAY,GAAlB,MAAMA,YAAY;EAGvBC,YACUC,YAA0B;IAA1B,iBAAY,GAAZA,YAAY;IAHtB,UAAK,GAAG,eAAe;EAIpB;EAEHC,QAAQ;IACN;IACA,IAAI,CAACD,YAAY,CAACE,eAAe,EAAE;EACrC;;;;;;;AAVWJ,YAAY,eALxBF,SAAS,CAAC;EACTO,QAAQ,EAAE,UAAU;EACpBC,8BAAmC;;CAEpC,CAAC,GACWN,YAAY,CAWxB;SAXYA,YAAY", "names": ["Component", "ThemeService", "AppComponent", "constructor", "themeService", "ngOnInit", "getCurrentTheme", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ThemeService } from './core/theme/theme.service';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.css']\r\n})\r\nexport class AppComponent implements OnInit {\r\n  title = 'oracul.client';\r\n\r\n  constructor(\r\n    private themeService: ThemeService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    // Initialize theme\r\n    this.themeService.getCurrentTheme();\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}