using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Options;
using Oracul.Server.Models;
using System.Text.RegularExpressions;

namespace Oracul.Server.Services
{
    /// <summary>
    /// Azure Blob Storage service implementation
    /// </summary>
    public class BlobStorageService : IBlobStorageService
    {
        private readonly BlobServiceClient _blobServiceClient;
        private readonly BlobStorageSettings _settings;
        private readonly ILogger<BlobStorageService> _logger;
        private readonly BlobContainerClient _containerClient;

        public BlobStorageService(
            IOptions<BlobStorageSettings> settings,
            ILogger<BlobStorageService> logger)
        {
            _settings = settings.Value;
            _logger = logger;

            try
            {
                _blobServiceClient = new BlobServiceClient(_settings.ConnectionString);
                _containerClient = _blobServiceClient.GetBlobContainerClient(_settings.ContainerName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize Azure Blob Storage client");
                throw new InvalidOperationException("Failed to initialize blob storage", ex);
            }
        }

        public async Task<BlobUploadResult> UploadFileAsync(Stream fileStream, string fileName, string contentType, string containerPath)
        {
            try
            {
                // Validate input
                if (fileStream == null || fileStream.Length == 0)
                {
                    return new BlobUploadResult
                    {
                        Success = false,
                        ErrorMessage = "File stream is empty"
                    };
                }

                // Ensure container exists
                await _containerClient.CreateIfNotExistsAsync(PublicAccessType.Blob);

                // Generate unique file name
                var uniqueFileName = GenerateUniqueFileName(fileName);
                var blobPath = $"{containerPath.Trim('/')}/{uniqueFileName}";

                // Get blob client
                var blobClient = _containerClient.GetBlobClient(blobPath);

                // Set blob headers
                var blobHeaders = new BlobHttpHeaders
                {
                    ContentType = contentType,
                    CacheControl = "public, max-age=31536000" // Cache for 1 year
                };

                // Upload the file
                fileStream.Position = 0; // Reset stream position
                var response = await blobClient.UploadAsync(
                    fileStream,
                    new BlobUploadOptions
                    {
                        HttpHeaders = blobHeaders,
                        Conditions = null // Allow overwrite
                    });

                _logger.LogInformation("Successfully uploaded file {FileName} to {BlobPath}", fileName, blobPath);

                return new BlobUploadResult
                {
                    Success = true,
                    BlobUrl = blobClient.Uri.ToString(),
                    BlobPath = blobPath,
                    FileName = uniqueFileName,
                    FileSize = fileStream.Length,
                    ContentType = contentType,
                    UploadedAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload file {FileName} to {ContainerPath}", fileName, containerPath);
                return new BlobUploadResult
                {
                    Success = false,
                    ErrorMessage = $"Upload failed: {ex.Message}"
                };
            }
        }

        public async Task<string> GetFileUrlAsync(string blobPath)
        {
            try
            {
                var blobClient = _containerClient.GetBlobClient(blobPath);
                
                // Check if blob exists
                var exists = await blobClient.ExistsAsync();
                if (!exists.Value)
                {
                    _logger.LogWarning("Blob not found: {BlobPath}", blobPath);
                    return string.Empty;
                }

                return blobClient.Uri.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get URL for blob {BlobPath}", blobPath);
                return string.Empty;
            }
        }

        public async Task<bool> DeleteFileAsync(string blobPath)
        {
            try
            {
                var blobClient = _containerClient.GetBlobClient(blobPath);
                var response = await blobClient.DeleteIfExistsAsync();
                
                if (response.Value)
                {
                    _logger.LogInformation("Successfully deleted blob {BlobPath}", blobPath);
                }
                else
                {
                    _logger.LogWarning("Blob not found for deletion: {BlobPath}", blobPath);
                }

                return response.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete blob {BlobPath}", blobPath);
                return false;
            }
        }

        public async Task<Stream> DownloadFileAsync(string blobPath)
        {
            try
            {
                var blobClient = _containerClient.GetBlobClient(blobPath);
                var response = await blobClient.DownloadStreamingAsync();
                return response.Value.Content;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to download blob {BlobPath}", blobPath);
                throw new FileNotFoundException($"File not found: {blobPath}", ex);
            }
        }

        public async Task<bool> FileExistsAsync(string blobPath)
        {
            try
            {
                var blobClient = _containerClient.GetBlobClient(blobPath);
                var response = await blobClient.ExistsAsync();
                return response.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check if blob exists {BlobPath}", blobPath);
                return false;
            }
        }

        public string GenerateUniqueFileName(string originalFileName, string? prefix = null)
        {
            var extension = Path.GetExtension(originalFileName);
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
            
            // Sanitize the file name
            var sanitizedName = Regex.Replace(nameWithoutExtension, @"[^a-zA-Z0-9\-_]", "-");
            sanitizedName = Regex.Replace(sanitizedName, @"-+", "-").Trim('-');
            
            // Limit length
            if (sanitizedName.Length > 50)
            {
                sanitizedName = sanitizedName.Substring(0, 50);
            }

            var timestamp = DateTime.UtcNow.ToString("yyyyMMdd-HHmmss");
            var guid = Guid.NewGuid().ToString("N")[..8]; // First 8 characters

            var fileName = prefix != null 
                ? $"{prefix}-{sanitizedName}-{timestamp}-{guid}{extension}"
                : $"{sanitizedName}-{timestamp}-{guid}{extension}";

            return fileName.ToLowerInvariant();
        }

        public FileValidationResult ValidateFile(string fileName, string contentType, long fileSize, string[] allowedTypes, long maxSizeBytes)
        {
            var result = new FileValidationResult { IsValid = true };

            // Check file size
            if (fileSize > maxSizeBytes)
            {
                result.IsValid = false;
                result.Errors.Add($"File size ({fileSize / 1024 / 1024:F1} MB) exceeds maximum allowed size ({maxSizeBytes / 1024 / 1024:F1} MB)");
            }

            // Check content type
            if (!allowedTypes.Contains(contentType.ToLower()))
            {
                result.IsValid = false;
                result.Errors.Add($"File type '{contentType}' is not allowed. Allowed types: {string.Join(", ", allowedTypes)}");
            }

            // Check file extension matches content type
            var extension = Path.GetExtension(fileName).ToLower();
            var expectedExtensions = GetExpectedExtensions(contentType);
            if (expectedExtensions.Any() && !expectedExtensions.Contains(extension))
            {
                result.IsValid = false;
                result.Errors.Add($"File extension '{extension}' does not match content type '{contentType}'");
            }

            return result;
        }

        private static string[] GetExpectedExtensions(string contentType)
        {
            return contentType.ToLower() switch
            {
                "image/jpeg" or "image/jpg" => new[] { ".jpg", ".jpeg" },
                "image/png" => new[] { ".png" },
                "image/gif" => new[] { ".gif" },
                "image/webp" => new[] { ".webp" },
                "application/pdf" => new[] { ".pdf" },
                "application/msword" => new[] { ".doc" },
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => new[] { ".docx" },
                _ => Array.Empty<string>()
            };
        }
    }
}
