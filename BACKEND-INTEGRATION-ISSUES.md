# Backend Integration Issues - Profile Edit Functionality

## Summary

The frontend profile edit functionality has been fully implemented and tested, but there are critical backend integration issues that prevent the new features from working properly. The frontend is sending data that the backend cannot receive or process.

## Critical Issues Identified

### 1. Missing Fields in UpdateProfileRequest Model

**Location**: `Oracul.Server/Models/ProfileModels.cs` - Line 217

**Issue**: The backend `UpdateProfileRequest` model is missing the following fields that the frontend is sending:

```csharp
// MISSING FIELDS - Need to be added:
public List<ProfileSkillDto>? Skills { get; set; }
public ConsultationRatesDto? ConsultationRates { get; set; }
public List<ServiceOfferingDto>? ServiceOfferings { get; set; }
```

**Current Backend Model**:
```csharp
public class UpdateProfileRequest
{
    [MaxLength(100)]
    public string? Username { get; set; }
    [MaxLength(100)]
    public string? FirstName { get; set; }
    [MaxLength(100)]
    public string? LastName { get; set; }
    [MaxLength(200)]
    public string? ProfessionalTitle { get; set; }
    [MaxLength(500)]
    public string? Headline { get; set; }
    public string? Summary { get; set; }
    public bool? IsPublic { get; set; }
    public ProfileLocationDto? Location { get; set; }
    public ContactInformationDto? ContactInfo { get; set; }
    // MISSING: Skills, ConsultationRates, ServiceOfferings
}
```

### 2. Missing Database Entities

**Issue**: The database schema is missing entities for:

- **ConsultationRates** - No table or entity exists
- **ServiceOffering** - No table or entity exists

**Current Database Tables** (from `OraculDbContext.cs`):
- ✅ UserProfiles
- ✅ ProfileSkills (exists)
- ❌ ConsultationRates (missing)
- ❌ ServiceOfferings (missing)

### 3. Missing DTO Models

**Issue**: The backend needs DTO models for the new entities:

```csharp
// MISSING - Need to be created:
public class ConsultationRatesDto
{
    [Range(0, 10000)]
    public decimal? HourlyRate { get; set; }
    [Range(0, 10000)]
    public decimal? SessionRate { get; set; }
    [Required]
    [MaxLength(3)]
    public string Currency { get; set; } = "BGN";
}

public class ServiceOfferingDto
{
    public int? Id { get; set; }
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;
    [Required]
    public string Description { get; set; } = string.Empty;
    [Range(0, 100000)]
    public decimal Price { get; set; }
    [Required]
    [MaxLength(3)]
    public string Currency { get; set; } = "BGN";
    public int? Duration { get; set; } // in minutes
    [MaxLength(100)]
    public string? Category { get; set; }
    public bool IsActive { get; set; } = true;
}
```

### 4. ProfileService UpdateProfileAsync Method

**Location**: `Oracul.Server/Services/ProfileService.cs` - Line 228

**Issue**: The `UpdateProfileAsync` method doesn't handle the new fields:
- Skills updates
- ConsultationRates updates  
- ServiceOfferings updates

### 5. UserProfile Entity Missing Navigation Properties

**Location**: `Oracul.Data/Models/UserProfile.cs`

**Issue**: The UserProfile entity needs navigation properties for:

```csharp
// MISSING - Need to be added:
public virtual ConsultationRates? ConsultationRates { get; set; }
public virtual ICollection<ServiceOffering> ServiceOfferings { get; set; } = new List<ServiceOffering>();
```

## Frontend Implementation Status

✅ **COMPLETE**: All frontend functionality is implemented and tested:
- Profile edit form with all fields
- Skills management (add/remove/edit)
- Consultation rates configuration
- Service offerings management
- Form validation and error handling
- Image upload functionality
- Comprehensive test coverage (25 tests passing)

## Required Backend Changes

### Phase 1: Database Schema Updates

1. **Create ConsultationRates Entity**:
```csharp
public class ConsultationRates : BaseEntity
{
    [Required]
    public int UserProfileId { get; set; }
    [Range(0, 10000)]
    public decimal? HourlyRate { get; set; }
    [Range(0, 10000)]
    public decimal? SessionRate { get; set; }
    [Required]
    [MaxLength(3)]
    public string Currency { get; set; } = "BGN";
    
    public virtual UserProfile UserProfile { get; set; } = null!;
}
```

2. **Create ServiceOffering Entity**:
```csharp
public class ServiceOffering : BaseEntity
{
    [Required]
    public int UserProfileId { get; set; }
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;
    [Required]
    public string Description { get; set; } = string.Empty;
    [Range(0, 100000)]
    public decimal Price { get; set; }
    [Required]
    [MaxLength(3)]
    public string Currency { get; set; } = "BGN";
    public int? Duration { get; set; }
    [MaxLength(100)]
    public string? Category { get; set; }
    public bool IsActive { get; set; } = true;
    
    public virtual UserProfile UserProfile { get; set; } = null!;
}
```

3. **Update UserProfile Entity** - Add navigation properties
4. **Create Database Migration**

### Phase 2: Model Updates

1. **Update UpdateProfileRequest** - Add missing fields
2. **Create/Update DTOs** - ConsultationRatesDto, ServiceOfferingDto
3. **Update UserProfileDto** - Include new fields in response

### Phase 3: Service Layer Updates

1. **Update ProfileService.UpdateProfileAsync** - Handle new fields
2. **Update ProfileService.MapToDto** - Map new entities to DTOs
3. **Add validation logic** for new fields

### Phase 4: Testing

1. **Test profile updates** with new fields
2. **Verify data persistence** in database
3. **Test API endpoints** with Postman/Swagger

## Impact Assessment

**High Priority**: This is blocking the profile edit functionality from working properly in production. Users can fill out the form but their skills, consultation rates, and service offerings are not being saved.

**Risk**: Low - Changes are additive and won't break existing functionality.

**Effort**: Medium - Requires database migration, model updates, and service layer changes.

## Next Steps

1. Create database entities and migration
2. Update backend models and DTOs
3. Update ProfileService to handle new fields
4. Test end-to-end functionality
5. Deploy and verify in production

## Test Coverage

The frontend has comprehensive test coverage with 25 passing tests that verify:
- Form initialization and population
- Skills management functionality
- Service offerings management
- Consultation rates handling
- Form validation and submission
- Error handling scenarios
- Image upload functionality
- Backend integration verification (documents the current gaps)
