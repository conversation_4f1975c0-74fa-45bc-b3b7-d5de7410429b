{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/Harmonia/oracul.client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { Location } from '@angular/common';\nimport { provideLocationMocks } from '@angular/common/testing';\nimport * as i0 from '@angular/core';\nimport { inject, Compiler, Injector, NgModule, Injectable, Component, ViewChild } from '@angular/core';\nimport { UrlSerializer, ChildrenOutletContexts, ROUTES, UrlHandlingStrategy, ROUTER_CONFIGURATION, RouteReuseStrategy, TitleStrategy, Router, RouterModule, ɵROUTER_PROVIDERS, ɵwithPreloading, NoPreloading, RouterOutlet, ɵafterNextNavigation } from '@angular/router';\nimport { TestBed } from '@angular/core/testing';\nfunction isUrlHandlingStrategy(opts) {\n  // This property check is needed because UrlHandlingStrategy is an interface and doesn't exist at\n  // runtime.\n  return 'shouldProcessUrl' in opts;\n}\nfunction throwInvalidConfigError(parameter) {\n  throw new Error(`Parameter ${parameter} does not match the one available in the injector. ` + '`setupTestingRouter` is meant to be used as a factory function with dependencies coming from DI.');\n}\n/**\n * Router setup factory function used for testing.\n *\n * @publicApi\n * @deprecated Use `provideRouter` or `RouterTestingModule` instead.\n */\nfunction setupTestingRouter(urlSerializer, contexts, location, compiler, injector, routes, opts, urlHandlingStrategy, routeReuseStrategy, titleStrategy) {\n  // Note: The checks below are to detect misconfigured providers and invalid uses of\n  // `setupTestingRouter`. This function is not used internally (neither in router code or anywhere\n  // in g3). It appears this function was exposed as publicApi by mistake and should not be used\n  // externally either. However, if it is, the documented intent is to be used as a factory function\n  // and parameter values should always match what's available in DI.\n  if (urlSerializer !== inject(UrlSerializer)) {\n    throwInvalidConfigError('urlSerializer');\n  }\n  if (contexts !== inject(ChildrenOutletContexts)) {\n    throwInvalidConfigError('contexts');\n  }\n  if (location !== inject(Location)) {\n    throwInvalidConfigError('location');\n  }\n  if (compiler !== inject(Compiler)) {\n    throwInvalidConfigError('compiler');\n  }\n  if (injector !== inject(Injector)) {\n    throwInvalidConfigError('injector');\n  }\n  if (routes !== inject(ROUTES)) {\n    throwInvalidConfigError('routes');\n  }\n  if (opts) {\n    // Handle deprecated argument ordering.\n    if (isUrlHandlingStrategy(opts)) {\n      if (opts !== inject(UrlHandlingStrategy)) {\n        throwInvalidConfigError('opts (UrlHandlingStrategy)');\n      }\n    } else {\n      if (opts !== inject(ROUTER_CONFIGURATION)) {\n        throwInvalidConfigError('opts (ROUTER_CONFIGURATION)');\n      }\n    }\n  }\n  if (urlHandlingStrategy !== inject(UrlHandlingStrategy)) {\n    throwInvalidConfigError('urlHandlingStrategy');\n  }\n  if (routeReuseStrategy !== inject(RouteReuseStrategy)) {\n    throwInvalidConfigError('routeReuseStrategy');\n  }\n  if (titleStrategy !== inject(TitleStrategy)) {\n    throwInvalidConfigError('titleStrategy');\n  }\n  return new Router();\n}\n/**\n * @description\n *\n * Sets up the router to be used for testing.\n *\n * The modules sets up the router to be used for testing.\n * It provides spy implementations of `Location` and `LocationStrategy`.\n *\n * @usageNotes\n * ### Example\n *\n * ```\n * beforeEach(() => {\n *   TestBed.configureTestingModule({\n *     imports: [\n *       RouterTestingModule.withRoutes(\n *         [{path: '', component: BlankCmp}, {path: 'simple', component: SimpleCmp}]\n *       )\n *     ]\n *   });\n * });\n * ```\n *\n * @publicApi\n */\nclass RouterTestingModule {\n  static withRoutes(routes, config) {\n    return {\n      ngModule: RouterTestingModule,\n      providers: [{\n        provide: ROUTES,\n        multi: true,\n        useValue: routes\n      }, {\n        provide: ROUTER_CONFIGURATION,\n        useValue: config ? config : {}\n      }]\n    };\n  }\n}\nRouterTestingModule.ɵfac = function RouterTestingModule_Factory(t) {\n  return new (t || RouterTestingModule)();\n};\nRouterTestingModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: RouterTestingModule,\n  exports: [RouterModule]\n});\nRouterTestingModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [ɵROUTER_PROVIDERS, provideLocationMocks(), ɵwithPreloading(NoPreloading).ɵproviders, {\n    provide: ROUTES,\n    multi: true,\n    useValue: []\n  }],\n  imports: [RouterModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterTestingModule, [{\n    type: NgModule,\n    args: [{\n      exports: [RouterModule],\n      providers: [ɵROUTER_PROVIDERS, provideLocationMocks(), ɵwithPreloading(NoPreloading).ɵproviders, {\n        provide: ROUTES,\n        multi: true,\n        useValue: []\n      }]\n    }]\n  }], null, null);\n})();\nclass RootFixtureService {\n  createHarness() {\n    if (this.harness) {\n      throw new Error('Only one harness should be created per test.');\n    }\n    this.harness = new RouterTestingHarness(this.getRootFixture());\n    return this.harness;\n  }\n  getRootFixture() {\n    if (this.fixture !== undefined) {\n      return this.fixture;\n    }\n    this.fixture = TestBed.createComponent(RootCmp);\n    this.fixture.detectChanges();\n    return this.fixture;\n  }\n}\nRootFixtureService.ɵfac = function RootFixtureService_Factory(t) {\n  return new (t || RootFixtureService)();\n};\nRootFixtureService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: RootFixtureService,\n  factory: RootFixtureService.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RootFixtureService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass RootCmp {}\nRootCmp.ɵfac = function RootCmp_Factory(t) {\n  return new (t || RootCmp)();\n};\nRootCmp.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: RootCmp,\n  selectors: [[\"ng-component\"]],\n  viewQuery: function RootCmp_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(RouterOutlet, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.outlet = _t.first);\n    }\n  },\n  standalone: true,\n  features: [i0.ɵɵStandaloneFeature],\n  decls: 1,\n  vars: 0,\n  template: function RootCmp_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"router-outlet\");\n    }\n  },\n  dependencies: [RouterOutlet],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RootCmp, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '<router-outlet></router-outlet>',\n      imports: [RouterOutlet]\n    }]\n  }], null, {\n    outlet: [{\n      type: ViewChild,\n      args: [RouterOutlet]\n    }]\n  });\n})();\n/**\n * A testing harness for the `Router` to reduce the boilerplate needed to test routes and routed\n * components.\n *\n * @publicApi\n */\nclass RouterTestingHarness {\n  /**\n   * Creates a `RouterTestingHarness` instance.\n   *\n   * The `RouterTestingHarness` also creates its own root component with a `RouterOutlet` for the\n   * purposes of rendering route components.\n   *\n   * Throws an error if an instance has already been created.\n   * Use of this harness also requires `destroyAfterEach: true` in the `ModuleTeardownOptions`\n   *\n   * @param initialUrl The target of navigation to trigger before returning the harness.\n   */\n  static create(initialUrl) {\n    return _asyncToGenerator(function* () {\n      const harness = TestBed.inject(RootFixtureService).createHarness();\n      if (initialUrl !== undefined) {\n        yield harness.navigateByUrl(initialUrl);\n      }\n      return harness;\n    })();\n  }\n  /** @internal */\n  constructor(fixture) {\n    this.fixture = fixture;\n  }\n  /** Instructs the root fixture to run change detection. */\n  detectChanges() {\n    this.fixture.detectChanges();\n  }\n  /** The `DebugElement` of the `RouterOutlet` component. `null` if the outlet is not activated. */\n  get routeDebugElement() {\n    const outlet = this.fixture.componentInstance.outlet;\n    if (!outlet || !outlet.isActivated) {\n      return null;\n    }\n    return this.fixture.debugElement.query(v => v.componentInstance === outlet.component);\n  }\n  /** The native element of the `RouterOutlet` component. `null` if the outlet is not activated. */\n  get routeNativeElement() {\n    return this.routeDebugElement?.nativeElement ?? null;\n  }\n  navigateByUrl(url, requiredRoutedComponentType) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const router = TestBed.inject(Router);\n      let resolveFn;\n      const redirectTrackingPromise = new Promise(resolve => {\n        resolveFn = resolve;\n      });\n      ɵafterNextNavigation(TestBed.inject(Router), resolveFn);\n      yield router.navigateByUrl(url);\n      yield redirectTrackingPromise;\n      _this.fixture.detectChanges();\n      const outlet = _this.fixture.componentInstance.outlet;\n      // The outlet might not be activated if the user is testing a navigation for a guard that\n      // rejects\n      if (outlet && outlet.isActivated && outlet.activatedRoute.component) {\n        const activatedComponent = outlet.component;\n        if (requiredRoutedComponentType !== undefined && !(activatedComponent instanceof requiredRoutedComponentType)) {\n          throw new Error(`Unexpected routed component type. Expected ${requiredRoutedComponentType.name} but got ${activatedComponent.constructor.name}`);\n        }\n        return activatedComponent;\n      } else {\n        return null;\n      }\n    })();\n  }\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router/testing package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { RouterTestingHarness, RouterTestingModule, setupTestingRouter };", "map": {"version": 3, "names": ["Location", "provideLocationMocks", "i0", "inject", "Compiler", "Injector", "NgModule", "Injectable", "Component", "ViewChild", "UrlSerializer", "ChildrenOutletContexts", "ROUTES", "UrlHandlingStrategy", "ROUTER_CONFIGURATION", "RouteReuseStrategy", "TitleStrategy", "Router", "RouterModule", "ɵROUTER_PROVIDERS", "ɵwithPreloading", "NoPreloading", "RouterOutlet", "ɵafterNextNavigation", "TestBed", "isUrlHandlingStrategy", "opts", "throwInvalidConfigError", "parameter", "Error", "setupTestingRouter", "urlSerializer", "contexts", "location", "compiler", "injector", "routes", "urlHandlingStrategy", "routeReuseStrategy", "titleStrategy", "RouterTestingModule", "with<PERSON>out<PERSON>", "config", "ngModule", "providers", "provide", "multi", "useValue", "ɵfac", "ɵmod", "ɵinj", "ɵproviders", "type", "args", "exports", "RootFixtureService", "createHarness", "harness", "RouterTestingHarness", "getRootFixture", "fixture", "undefined", "createComponent", "RootCmp", "detectChanges", "ɵprov", "providedIn", "ɵcmp", "standalone", "template", "imports", "outlet", "create", "initialUrl", "navigateByUrl", "constructor", "routeDebugElement", "componentInstance", "isActivated", "debugElement", "query", "v", "component", "routeNativeElement", "nativeElement", "url", "requiredRoutedComponentType", "router", "resolveFn", "redirectTrackingPromise", "Promise", "resolve", "activatedRoute", "activatedComponent", "name"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/router/fesm2020/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { Location } from '@angular/common';\nimport { provideLocationMocks } from '@angular/common/testing';\nimport * as i0 from '@angular/core';\nimport { inject, Compiler, Injector, NgModule, Injectable, Component, ViewChild } from '@angular/core';\nimport { UrlSerializer, ChildrenOutletContexts, ROUTES, UrlHandlingStrategy, ROUTER_CONFIGURATION, RouteReuseStrategy, TitleStrategy, Router, RouterModule, ɵROUTER_PROVIDERS, ɵwithPreloading, NoPreloading, RouterOutlet, ɵafterNextNavigation } from '@angular/router';\nimport { TestBed } from '@angular/core/testing';\n\nfunction isUrlHandlingStrategy(opts) {\n    // This property check is needed because UrlHandlingStrategy is an interface and doesn't exist at\n    // runtime.\n    return 'shouldProcessUrl' in opts;\n}\nfunction throwInvalidConfigError(parameter) {\n    throw new Error(`Parameter ${parameter} does not match the one available in the injector. ` +\n        '`setupTestingRouter` is meant to be used as a factory function with dependencies coming from DI.');\n}\n/**\n * Router setup factory function used for testing.\n *\n * @publicApi\n * @deprecated Use `provideRouter` or `RouterTestingModule` instead.\n */\nfunction setupTestingRouter(urlSerializer, contexts, location, compiler, injector, routes, opts, urlHandlingStrategy, routeReuseStrategy, titleStrategy) {\n    // Note: The checks below are to detect misconfigured providers and invalid uses of\n    // `setupTestingRouter`. This function is not used internally (neither in router code or anywhere\n    // in g3). It appears this function was exposed as publicApi by mistake and should not be used\n    // externally either. However, if it is, the documented intent is to be used as a factory function\n    // and parameter values should always match what's available in DI.\n    if (urlSerializer !== inject(UrlSerializer)) {\n        throwInvalidConfigError('urlSerializer');\n    }\n    if (contexts !== inject(ChildrenOutletContexts)) {\n        throwInvalidConfigError('contexts');\n    }\n    if (location !== inject(Location)) {\n        throwInvalidConfigError('location');\n    }\n    if (compiler !== inject(Compiler)) {\n        throwInvalidConfigError('compiler');\n    }\n    if (injector !== inject(Injector)) {\n        throwInvalidConfigError('injector');\n    }\n    if (routes !== inject(ROUTES)) {\n        throwInvalidConfigError('routes');\n    }\n    if (opts) {\n        // Handle deprecated argument ordering.\n        if (isUrlHandlingStrategy(opts)) {\n            if (opts !== inject(UrlHandlingStrategy)) {\n                throwInvalidConfigError('opts (UrlHandlingStrategy)');\n            }\n        }\n        else {\n            if (opts !== inject(ROUTER_CONFIGURATION)) {\n                throwInvalidConfigError('opts (ROUTER_CONFIGURATION)');\n            }\n        }\n    }\n    if (urlHandlingStrategy !== inject(UrlHandlingStrategy)) {\n        throwInvalidConfigError('urlHandlingStrategy');\n    }\n    if (routeReuseStrategy !== inject(RouteReuseStrategy)) {\n        throwInvalidConfigError('routeReuseStrategy');\n    }\n    if (titleStrategy !== inject(TitleStrategy)) {\n        throwInvalidConfigError('titleStrategy');\n    }\n    return new Router();\n}\n/**\n * @description\n *\n * Sets up the router to be used for testing.\n *\n * The modules sets up the router to be used for testing.\n * It provides spy implementations of `Location` and `LocationStrategy`.\n *\n * @usageNotes\n * ### Example\n *\n * ```\n * beforeEach(() => {\n *   TestBed.configureTestingModule({\n *     imports: [\n *       RouterTestingModule.withRoutes(\n *         [{path: '', component: BlankCmp}, {path: 'simple', component: SimpleCmp}]\n *       )\n *     ]\n *   });\n * });\n * ```\n *\n * @publicApi\n */\nclass RouterTestingModule {\n    static withRoutes(routes, config) {\n        return {\n            ngModule: RouterTestingModule,\n            providers: [\n                { provide: ROUTES, multi: true, useValue: routes },\n                { provide: ROUTER_CONFIGURATION, useValue: config ? config : {} },\n            ]\n        };\n    }\n}\nRouterTestingModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: RouterTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nRouterTestingModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: RouterTestingModule, exports: [RouterModule] });\nRouterTestingModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: RouterTestingModule, providers: [\n        ɵROUTER_PROVIDERS,\n        provideLocationMocks(),\n        ɵwithPreloading(NoPreloading).ɵproviders,\n        { provide: ROUTES, multi: true, useValue: [] },\n    ], imports: [RouterModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: RouterTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [RouterModule],\n                    providers: [\n                        ɵROUTER_PROVIDERS,\n                        provideLocationMocks(),\n                        ɵwithPreloading(NoPreloading).ɵproviders,\n                        { provide: ROUTES, multi: true, useValue: [] },\n                    ]\n                }]\n        }] });\n\nclass RootFixtureService {\n    createHarness() {\n        if (this.harness) {\n            throw new Error('Only one harness should be created per test.');\n        }\n        this.harness = new RouterTestingHarness(this.getRootFixture());\n        return this.harness;\n    }\n    getRootFixture() {\n        if (this.fixture !== undefined) {\n            return this.fixture;\n        }\n        this.fixture = TestBed.createComponent(RootCmp);\n        this.fixture.detectChanges();\n        return this.fixture;\n    }\n}\nRootFixtureService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: RootFixtureService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nRootFixtureService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: RootFixtureService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: RootFixtureService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\nclass RootCmp {\n}\nRootCmp.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: RootCmp, deps: [], target: i0.ɵɵFactoryTarget.Component });\nRootCmp.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.10\", type: RootCmp, isStandalone: true, selector: \"ng-component\", viewQueries: [{ propertyName: \"outlet\", first: true, predicate: RouterOutlet, descendants: true }], ngImport: i0, template: '<router-outlet></router-outlet>', isInline: true, dependencies: [{ kind: \"directive\", type: RouterOutlet, selector: \"router-outlet\", inputs: [\"name\"], outputs: [\"activate\", \"deactivate\", \"attach\", \"detach\"], exportAs: [\"outlet\"] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: RootCmp, decorators: [{\n            type: Component,\n            args: [{\n                    standalone: true,\n                    template: '<router-outlet></router-outlet>',\n                    imports: [RouterOutlet],\n                }]\n        }], propDecorators: { outlet: [{\n                type: ViewChild,\n                args: [RouterOutlet]\n            }] } });\n/**\n * A testing harness for the `Router` to reduce the boilerplate needed to test routes and routed\n * components.\n *\n * @publicApi\n */\nclass RouterTestingHarness {\n    /**\n     * Creates a `RouterTestingHarness` instance.\n     *\n     * The `RouterTestingHarness` also creates its own root component with a `RouterOutlet` for the\n     * purposes of rendering route components.\n     *\n     * Throws an error if an instance has already been created.\n     * Use of this harness also requires `destroyAfterEach: true` in the `ModuleTeardownOptions`\n     *\n     * @param initialUrl The target of navigation to trigger before returning the harness.\n     */\n    static async create(initialUrl) {\n        const harness = TestBed.inject(RootFixtureService).createHarness();\n        if (initialUrl !== undefined) {\n            await harness.navigateByUrl(initialUrl);\n        }\n        return harness;\n    }\n    /** @internal */\n    constructor(fixture) {\n        this.fixture = fixture;\n    }\n    /** Instructs the root fixture to run change detection. */\n    detectChanges() {\n        this.fixture.detectChanges();\n    }\n    /** The `DebugElement` of the `RouterOutlet` component. `null` if the outlet is not activated. */\n    get routeDebugElement() {\n        const outlet = this.fixture.componentInstance.outlet;\n        if (!outlet || !outlet.isActivated) {\n            return null;\n        }\n        return this.fixture.debugElement.query(v => v.componentInstance === outlet.component);\n    }\n    /** The native element of the `RouterOutlet` component. `null` if the outlet is not activated. */\n    get routeNativeElement() {\n        return this.routeDebugElement?.nativeElement ?? null;\n    }\n    async navigateByUrl(url, requiredRoutedComponentType) {\n        const router = TestBed.inject(Router);\n        let resolveFn;\n        const redirectTrackingPromise = new Promise(resolve => {\n            resolveFn = resolve;\n        });\n        ɵafterNextNavigation(TestBed.inject(Router), resolveFn);\n        await router.navigateByUrl(url);\n        await redirectTrackingPromise;\n        this.fixture.detectChanges();\n        const outlet = this.fixture.componentInstance.outlet;\n        // The outlet might not be activated if the user is testing a navigation for a guard that\n        // rejects\n        if (outlet && outlet.isActivated && outlet.activatedRoute.component) {\n            const activatedComponent = outlet.component;\n            if (requiredRoutedComponentType !== undefined &&\n                !(activatedComponent instanceof requiredRoutedComponentType)) {\n                throw new Error(`Unexpected routed component type. Expected ${requiredRoutedComponentType.name} but got ${activatedComponent.constructor.name}`);\n            }\n            return activatedComponent;\n        }\n        else {\n            return null;\n        }\n    }\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router/testing package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { RouterTestingHarness, RouterTestingModule, setupTestingRouter };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,SAAS,QAAQ,eAAe;AACtG,SAASC,aAAa,EAAEC,sBAAsB,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,MAAM,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,iBAAiB;AACzQ,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,qBAAqB,CAACC,IAAI,EAAE;EACjC;EACA;EACA,OAAO,kBAAkB,IAAIA,IAAI;AACrC;AACA,SAASC,uBAAuB,CAACC,SAAS,EAAE;EACxC,MAAM,IAAIC,KAAK,CAAE,aAAYD,SAAU,qDAAoD,GACvF,kGAAkG,CAAC;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,kBAAkB,CAACC,aAAa,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEV,IAAI,EAAEW,mBAAmB,EAAEC,kBAAkB,EAAEC,aAAa,EAAE;EACrJ;EACA;EACA;EACA;EACA;EACA,IAAIR,aAAa,KAAK5B,MAAM,CAACO,aAAa,CAAC,EAAE;IACzCiB,uBAAuB,CAAC,eAAe,CAAC;EAC5C;EACA,IAAIK,QAAQ,KAAK7B,MAAM,CAACQ,sBAAsB,CAAC,EAAE;IAC7CgB,uBAAuB,CAAC,UAAU,CAAC;EACvC;EACA,IAAIM,QAAQ,KAAK9B,MAAM,CAACH,QAAQ,CAAC,EAAE;IAC/B2B,uBAAuB,CAAC,UAAU,CAAC;EACvC;EACA,IAAIO,QAAQ,KAAK/B,MAAM,CAACC,QAAQ,CAAC,EAAE;IAC/BuB,uBAAuB,CAAC,UAAU,CAAC;EACvC;EACA,IAAIQ,QAAQ,KAAKhC,MAAM,CAACE,QAAQ,CAAC,EAAE;IAC/BsB,uBAAuB,CAAC,UAAU,CAAC;EACvC;EACA,IAAIS,MAAM,KAAKjC,MAAM,CAACS,MAAM,CAAC,EAAE;IAC3Be,uBAAuB,CAAC,QAAQ,CAAC;EACrC;EACA,IAAID,IAAI,EAAE;IACN;IACA,IAAID,qBAAqB,CAACC,IAAI,CAAC,EAAE;MAC7B,IAAIA,IAAI,KAAKvB,MAAM,CAACU,mBAAmB,CAAC,EAAE;QACtCc,uBAAuB,CAAC,4BAA4B,CAAC;MACzD;IACJ,CAAC,MACI;MACD,IAAID,IAAI,KAAKvB,MAAM,CAACW,oBAAoB,CAAC,EAAE;QACvCa,uBAAuB,CAAC,6BAA6B,CAAC;MAC1D;IACJ;EACJ;EACA,IAAIU,mBAAmB,KAAKlC,MAAM,CAACU,mBAAmB,CAAC,EAAE;IACrDc,uBAAuB,CAAC,qBAAqB,CAAC;EAClD;EACA,IAAIW,kBAAkB,KAAKnC,MAAM,CAACY,kBAAkB,CAAC,EAAE;IACnDY,uBAAuB,CAAC,oBAAoB,CAAC;EACjD;EACA,IAAIY,aAAa,KAAKpC,MAAM,CAACa,aAAa,CAAC,EAAE;IACzCW,uBAAuB,CAAC,eAAe,CAAC;EAC5C;EACA,OAAO,IAAIV,MAAM,EAAE;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuB,mBAAmB,CAAC;EACtB,OAAOC,UAAU,CAACL,MAAM,EAAEM,MAAM,EAAE;IAC9B,OAAO;MACHC,QAAQ,EAAEH,mBAAmB;MAC7BI,SAAS,EAAE,CACP;QAAEC,OAAO,EAAEjC,MAAM;QAAEkC,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAEX;MAAO,CAAC,EAClD;QAAES,OAAO,EAAE/B,oBAAoB;QAAEiC,QAAQ,EAAEL,MAAM,GAAGA,MAAM,GAAG,CAAC;MAAE,CAAC;IAEzE,CAAC;EACL;AACJ;AACAF,mBAAmB,CAACQ,IAAI;EAAA,iBAAyFR,mBAAmB;AAAA,CAAkD;AACtLA,mBAAmB,CAACS,IAAI,kBAD+E/C,EAAE;EAAA,MACSsC,mBAAmB;EAAA,UAAYtB,YAAY;AAAA,EAAI;AACjKsB,mBAAmB,CAACU,IAAI,kBAF+EhD,EAAE;EAAA,WAEyC,CAC1IiB,iBAAiB,EACjBlB,oBAAoB,EAAE,EACtBmB,eAAe,CAACC,YAAY,CAAC,CAAC8B,UAAU,EACxC;IAAEN,OAAO,EAAEjC,MAAM;IAAEkC,KAAK,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAG,CAAC,CACjD;EAAA,UAAY7B,YAAY;AAAA,EAAI;AACjC;EAAA,mDARuGhB,EAAE,mBAQbsC,mBAAmB,EAAc,CAAC;IAClHY,IAAI,EAAE9C,QAAQ;IACd+C,IAAI,EAAE,CAAC;MACCC,OAAO,EAAE,CAACpC,YAAY,CAAC;MACvB0B,SAAS,EAAE,CACPzB,iBAAiB,EACjBlB,oBAAoB,EAAE,EACtBmB,eAAe,CAACC,YAAY,CAAC,CAAC8B,UAAU,EACxC;QAAEN,OAAO,EAAEjC,MAAM;QAAEkC,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAE;MAAG,CAAC;IAEtD,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMQ,kBAAkB,CAAC;EACrBC,aAAa,GAAG;IACZ,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,MAAM,IAAI5B,KAAK,CAAC,8CAA8C,CAAC;IACnE;IACA,IAAI,CAAC4B,OAAO,GAAG,IAAIC,oBAAoB,CAAC,IAAI,CAACC,cAAc,EAAE,CAAC;IAC9D,OAAO,IAAI,CAACF,OAAO;EACvB;EACAE,cAAc,GAAG;IACb,IAAI,IAAI,CAACC,OAAO,KAAKC,SAAS,EAAE;MAC5B,OAAO,IAAI,CAACD,OAAO;IACvB;IACA,IAAI,CAACA,OAAO,GAAGpC,OAAO,CAACsC,eAAe,CAACC,OAAO,CAAC;IAC/C,IAAI,CAACH,OAAO,CAACI,aAAa,EAAE;IAC5B,OAAO,IAAI,CAACJ,OAAO;EACvB;AACJ;AACAL,kBAAkB,CAACP,IAAI;EAAA,iBAAyFO,kBAAkB;AAAA,CAAoD;AACtLA,kBAAkB,CAACU,KAAK,kBAvC+E/D,EAAE;EAAA,OAuCWqD,kBAAkB;EAAA,SAAlBA,kBAAkB;EAAA,YAAc;AAAM,EAAG;AAC7J;EAAA,mDAxCuGrD,EAAE,mBAwCbqD,kBAAkB,EAAc,CAAC;IACjHH,IAAI,EAAE7C,UAAU;IAChB8C,IAAI,EAAE,CAAC;MAAEa,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV,MAAMH,OAAO,CAAC;AAEdA,OAAO,CAACf,IAAI;EAAA,iBAAyFe,OAAO;AAAA,CAAmD;AAC/JA,OAAO,CAACI,IAAI,kBA/C2FjE,EAAE;EAAA,MA+ChB6D,OAAO;EAAA;EAAA;IAAA;MA/CO7D,EAAE,aA+CuGoB,YAAY;IAAA;IAAA;MAAA;MA/CrHpB,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE,8BA+CmM;IAAA;EAAA;EAAA,eAA6DoB,YAAY;EAAA;AAAA,EAAkI;AACvf;EAAA,mDAhDuGpB,EAAE,mBAgDb6D,OAAO,EAAc,CAAC;IACtGX,IAAI,EAAE5C,SAAS;IACf6C,IAAI,EAAE,CAAC;MACCe,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,iCAAiC;MAC3CC,OAAO,EAAE,CAAChD,YAAY;IAC1B,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEiD,MAAM,EAAE,CAAC;MACvBnB,IAAI,EAAE3C,SAAS;MACf4C,IAAI,EAAE,CAAC/B,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoC,oBAAoB,CAAC;EACvB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAac,MAAM,CAACC,UAAU,EAAE;IAAA;MAC5B,MAAMhB,OAAO,GAAGjC,OAAO,CAACrB,MAAM,CAACoD,kBAAkB,CAAC,CAACC,aAAa,EAAE;MAClE,IAAIiB,UAAU,KAAKZ,SAAS,EAAE;QAC1B,MAAMJ,OAAO,CAACiB,aAAa,CAACD,UAAU,CAAC;MAC3C;MACA,OAAOhB,OAAO;IAAC;EACnB;EACA;EACAkB,WAAW,CAACf,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACA;EACAI,aAAa,GAAG;IACZ,IAAI,CAACJ,OAAO,CAACI,aAAa,EAAE;EAChC;EACA;EACA,IAAIY,iBAAiB,GAAG;IACpB,MAAML,MAAM,GAAG,IAAI,CAACX,OAAO,CAACiB,iBAAiB,CAACN,MAAM;IACpD,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACO,WAAW,EAAE;MAChC,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAAClB,OAAO,CAACmB,YAAY,CAACC,KAAK,CAACC,CAAC,IAAIA,CAAC,CAACJ,iBAAiB,KAAKN,MAAM,CAACW,SAAS,CAAC;EACzF;EACA;EACA,IAAIC,kBAAkB,GAAG;IACrB,OAAO,IAAI,CAACP,iBAAiB,EAAEQ,aAAa,IAAI,IAAI;EACxD;EACMV,aAAa,CAACW,GAAG,EAAEC,2BAA2B,EAAE;IAAA;IAAA;MAClD,MAAMC,MAAM,GAAG/D,OAAO,CAACrB,MAAM,CAACc,MAAM,CAAC;MACrC,IAAIuE,SAAS;MACb,MAAMC,uBAAuB,GAAG,IAAIC,OAAO,CAACC,OAAO,IAAI;QACnDH,SAAS,GAAGG,OAAO;MACvB,CAAC,CAAC;MACFpE,oBAAoB,CAACC,OAAO,CAACrB,MAAM,CAACc,MAAM,CAAC,EAAEuE,SAAS,CAAC;MACvD,MAAMD,MAAM,CAACb,aAAa,CAACW,GAAG,CAAC;MAC/B,MAAMI,uBAAuB;MAC7B,KAAI,CAAC7B,OAAO,CAACI,aAAa,EAAE;MAC5B,MAAMO,MAAM,GAAG,KAAI,CAACX,OAAO,CAACiB,iBAAiB,CAACN,MAAM;MACpD;MACA;MACA,IAAIA,MAAM,IAAIA,MAAM,CAACO,WAAW,IAAIP,MAAM,CAACqB,cAAc,CAACV,SAAS,EAAE;QACjE,MAAMW,kBAAkB,GAAGtB,MAAM,CAACW,SAAS;QAC3C,IAAII,2BAA2B,KAAKzB,SAAS,IACzC,EAAEgC,kBAAkB,YAAYP,2BAA2B,CAAC,EAAE;UAC9D,MAAM,IAAIzD,KAAK,CAAE,8CAA6CyD,2BAA2B,CAACQ,IAAK,YAAWD,kBAAkB,CAAClB,WAAW,CAACmB,IAAK,EAAC,CAAC;QACpJ;QACA,OAAOD,kBAAkB;MAC7B,CAAC,MACI;QACD,OAAO,IAAI;MACf;IAAC;EACL;AACJ;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASnC,oBAAoB,EAAElB,mBAAmB,EAAEV,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}