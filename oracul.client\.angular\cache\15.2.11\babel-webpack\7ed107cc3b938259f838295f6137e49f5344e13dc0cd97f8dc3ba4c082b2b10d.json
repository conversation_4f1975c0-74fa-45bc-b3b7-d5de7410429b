{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Version, InjectionToken, inject, NgModule, Optional, Inject, LOCALE_ID, Injectable, Directive, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, EventEmitter, Output, ViewChild } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { VERSION as VERSION$1 } from '@angular/cdk';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { Platform, _isTestEnvironment, normalizePassiveListenerOptions, _getEventTarget } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceNumberProperty, coerceElement } from '@angular/cdk/coercion';\nimport { Observable, Subject } from 'rxjs';\nimport { startWith } from 'rxjs/operators';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { ENTER, SPACE, hasModifierKey } from '@angular/cdk/keycodes';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Current version of Angular Material. */\nconst _c0 = [\"*\", [[\"mat-option\"], [\"ng-container\"]]];\nconst _c1 = [\"*\", \"mat-option, ng-container\"];\nconst _c2 = [\"text\"];\nfunction MatOption_mat_pseudo_checkbox_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"state\", ctx_r0.selected ? \"checked\" : \"unchecked\")(\"disabled\", ctx_r0.disabled);\n  }\n}\nfunction MatOption_mat_pseudo_checkbox_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabled);\n  }\n}\nfunction MatOption_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r3.group.label, \")\");\n  }\n}\nconst _c3 = [[[\"mat-icon\"]], \"*\"];\nconst _c4 = [\"mat-icon\", \"*\"];\nconst VERSION = new Version('15.2.9');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** @docs-private */\nclass AnimationCurves {}\nAnimationCurves.STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)';\nAnimationCurves.DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)';\nAnimationCurves.ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)';\nAnimationCurves.SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)';\n/** @docs-private */\nclass AnimationDurations {}\nAnimationDurations.COMPLEX = '375ms';\nAnimationDurations.ENTERING = '225ms';\nAnimationDurations.EXITING = '195ms';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** @docs-private */\nfunction MATERIAL_SANITY_CHECKS_FACTORY() {\n  return true;\n}\n/** Injection token that configures whether the Material sanity checks are enabled. */\nconst MATERIAL_SANITY_CHECKS = new InjectionToken('mat-sanity-checks', {\n  providedIn: 'root',\n  factory: MATERIAL_SANITY_CHECKS_FACTORY\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n */\nclass MatCommonModule {\n  constructor(highContrastModeDetector, _sanityChecks, _document) {\n    this._sanityChecks = _sanityChecks;\n    this._document = _document;\n    /** Whether we've done the global sanity checks (e.g. a theme is loaded, there is a doctype). */\n    this._hasDoneGlobalChecks = false;\n    // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n    // in MatCommonModule.\n    highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n    if (!this._hasDoneGlobalChecks) {\n      this._hasDoneGlobalChecks = true;\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // Inject in here so the reference to `Platform` can be removed in production mode.\n        const platform = inject(Platform, {\n          optional: true\n        });\n        if (this._checkIsEnabled('doctype')) {\n          _checkDoctypeIsDefined(this._document);\n        }\n        if (this._checkIsEnabled('theme')) {\n          _checkThemeIsPresent(this._document, !!platform?.isBrowser);\n        }\n        if (this._checkIsEnabled('version')) {\n          _checkCdkVersionMatch();\n        }\n      }\n    }\n  }\n  /** Gets whether a specific sanity check is enabled. */\n  _checkIsEnabled(name) {\n    if (_isTestEnvironment()) {\n      return false;\n    }\n    if (typeof this._sanityChecks === 'boolean') {\n      return this._sanityChecks;\n    }\n    return !!this._sanityChecks[name];\n  }\n}\nMatCommonModule.ɵfac = function MatCommonModule_Factory(t) {\n  return new (t || MatCommonModule)(i0.ɵɵinject(i1.HighContrastModeDetector), i0.ɵɵinject(MATERIAL_SANITY_CHECKS, 8), i0.ɵɵinject(DOCUMENT));\n};\nMatCommonModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatCommonModule,\n  imports: [BidiModule],\n  exports: [BidiModule]\n});\nMatCommonModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [BidiModule, BidiModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCommonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule],\n      exports: [BidiModule]\n    }]\n  }], function () {\n    return [{\n      type: i1.HighContrastModeDetector\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MATERIAL_SANITY_CHECKS]\n      }]\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/** Checks that the page has a doctype. */\nfunction _checkDoctypeIsDefined(doc) {\n  if (!doc.doctype) {\n    console.warn('Current document does not have a doctype. This may cause ' + 'some Angular Material components not to behave as expected.');\n  }\n}\n/** Checks that a theme has been included. */\nfunction _checkThemeIsPresent(doc, isBrowser) {\n  // We need to assert that the `body` is defined, because these checks run very early\n  // and the `body` won't be defined if the consumer put their scripts in the `head`.\n  if (!doc.body || !isBrowser) {\n    return;\n  }\n  const testElement = doc.createElement('div');\n  testElement.classList.add('mat-theme-loaded-marker');\n  doc.body.appendChild(testElement);\n  const computedStyle = getComputedStyle(testElement);\n  // In some situations the computed style of the test element can be null. For example in\n  // Firefox, the computed style is null if an application is running inside of a hidden iframe.\n  // See: https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n  if (computedStyle && computedStyle.display !== 'none') {\n    console.warn('Could not find Angular Material core theme. Most Material ' + 'components may not work as expected. For more info refer ' + 'to the theming guide: https://material.angular.io/guide/theming');\n  }\n  testElement.remove();\n}\n/** Checks whether the Material version matches the CDK version. */\nfunction _checkCdkVersionMatch() {\n  if (VERSION.full !== VERSION$1.full) {\n    console.warn('The Angular Material version (' + VERSION.full + ') does not match ' + 'the Angular CDK version (' + VERSION$1.full + ').\\n' + 'Please ensure the versions of these two packages exactly match.');\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction mixinDisabled(base) {\n  return class extends base {\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n    constructor(...args) {\n      super(...args);\n      this._disabled = false;\n    }\n  };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction mixinColor(base, defaultColor) {\n  return class extends base {\n    get color() {\n      return this._color;\n    }\n    set color(value) {\n      const colorPalette = value || this.defaultColor;\n      if (colorPalette !== this._color) {\n        if (this._color) {\n          this._elementRef.nativeElement.classList.remove(`mat-${this._color}`);\n        }\n        if (colorPalette) {\n          this._elementRef.nativeElement.classList.add(`mat-${colorPalette}`);\n        }\n        this._color = colorPalette;\n      }\n    }\n    constructor(...args) {\n      super(...args);\n      this.defaultColor = defaultColor;\n      // Set the default color that can be specified from the mixin.\n      this.color = defaultColor;\n    }\n  };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction mixinDisableRipple(base) {\n  return class extends base {\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple() {\n      return this._disableRipple;\n    }\n    set disableRipple(value) {\n      this._disableRipple = coerceBooleanProperty(value);\n    }\n    constructor(...args) {\n      super(...args);\n      this._disableRipple = false;\n    }\n  };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction mixinTabIndex(base, defaultTabIndex = 0) {\n  return class extends base {\n    get tabIndex() {\n      return this.disabled ? -1 : this._tabIndex;\n    }\n    set tabIndex(value) {\n      // If the specified tabIndex value is null or undefined, fall back to the default value.\n      this._tabIndex = value != null ? coerceNumberProperty(value) : this.defaultTabIndex;\n    }\n    constructor(...args) {\n      super(...args);\n      this._tabIndex = defaultTabIndex;\n      this.defaultTabIndex = defaultTabIndex;\n    }\n  };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction mixinErrorState(base) {\n  return class extends base {\n    /** Updates the error state based on the provided error state matcher. */\n    updateErrorState() {\n      const oldState = this.errorState;\n      const parent = this._parentFormGroup || this._parentForm;\n      const matcher = this.errorStateMatcher || this._defaultErrorStateMatcher;\n      const control = this.ngControl ? this.ngControl.control : null;\n      const newState = matcher.isErrorState(control, parent);\n      if (newState !== oldState) {\n        this.errorState = newState;\n        this.stateChanges.next();\n      }\n    }\n    constructor(...args) {\n      super(...args);\n      /** Whether the component is in an error state. */\n      this.errorState = false;\n    }\n  };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Mixin to augment a directive with an initialized property that will emits when ngOnInit ends. */\nfunction mixinInitialized(base) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      /** Whether this directive has been marked as initialized. */\n      this._isInitialized = false;\n      /**\n       * List of subscribers that subscribed before the directive was initialized. Should be notified\n       * during _markInitialized. Set to null after pending subscribers are notified, and should\n       * not expect to be populated after.\n       */\n      this._pendingSubscribers = [];\n      /**\n       * Observable stream that emits when the directive initializes. If already initialized, the\n       * subscriber is stored to be notified once _markInitialized is called.\n       */\n      this.initialized = new Observable(subscriber => {\n        // If initialized, immediately notify the subscriber. Otherwise store the subscriber to notify\n        // when _markInitialized is called.\n        if (this._isInitialized) {\n          this._notifySubscriber(subscriber);\n        } else {\n          this._pendingSubscribers.push(subscriber);\n        }\n      });\n    }\n    /**\n     * Marks the state as initialized and notifies pending subscribers. Should be called at the end\n     * of ngOnInit.\n     * @docs-private\n     */\n    _markInitialized() {\n      if (this._isInitialized && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('This directive has already been marked as initialized and ' + 'should not be called twice.');\n      }\n      this._isInitialized = true;\n      this._pendingSubscribers.forEach(this._notifySubscriber);\n      this._pendingSubscribers = null;\n    }\n    /** Emits and completes the subscriber stream (should only emit once). */\n    _notifySubscriber(subscriber) {\n      subscriber.next();\n      subscriber.complete();\n    }\n  };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** InjectionToken for datepicker that can be used to override default locale code. */\nconst MAT_DATE_LOCALE = new InjectionToken('MAT_DATE_LOCALE', {\n  providedIn: 'root',\n  factory: MAT_DATE_LOCALE_FACTORY\n});\n/** @docs-private */\nfunction MAT_DATE_LOCALE_FACTORY() {\n  return inject(LOCALE_ID);\n}\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\nclass DateAdapter {\n  constructor() {\n    this._localeChanges = new Subject();\n    /** A stream that emits when the locale changes. */\n    this.localeChanges = this._localeChanges;\n  }\n  /**\n   * Given a potential date object, returns that same date object if it is\n   * a valid date, or `null` if it's not a valid date.\n   * @param obj The object to check.\n   * @returns A date or `null`.\n   */\n  getValidDateOrNull(obj) {\n    return this.isDateInstance(obj) && this.isValid(obj) ? obj : null;\n  }\n  /**\n   * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n   * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n   * string). The default implementation does not allow any deserialization, it simply checks that\n   * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n   * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n   * support passing values from your backend directly to these properties by overriding this method\n   * to also deserialize the format used by your backend.\n   * @param value The value to be deserialized into a date object.\n   * @returns The deserialized date object, either a valid date, null if the value can be\n   *     deserialized into a null date (e.g. the empty string), or an invalid date.\n   */\n  deserialize(value) {\n    if (value == null || this.isDateInstance(value) && this.isValid(value)) {\n      return value;\n    }\n    return this.invalid();\n  }\n  /**\n   * Sets the locale used for all dates.\n   * @param locale The new locale.\n   */\n  setLocale(locale) {\n    this.locale = locale;\n    this._localeChanges.next();\n  }\n  /**\n   * Compares two dates.\n   * @param first The first date to compare.\n   * @param second The second date to compare.\n   * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n   *     a number greater than 0 if the first date is later.\n   */\n  compareDate(first, second) {\n    return this.getYear(first) - this.getYear(second) || this.getMonth(first) - this.getMonth(second) || this.getDate(first) - this.getDate(second);\n  }\n  /**\n   * Checks if two dates are equal.\n   * @param first The first date to check.\n   * @param second The second date to check.\n   * @returns Whether the two dates are equal.\n   *     Null dates are considered equal to other null dates.\n   */\n  sameDate(first, second) {\n    if (first && second) {\n      let firstValid = this.isValid(first);\n      let secondValid = this.isValid(second);\n      if (firstValid && secondValid) {\n        return !this.compareDate(first, second);\n      }\n      return firstValid == secondValid;\n    }\n    return first == second;\n  }\n  /**\n   * Clamp the given date between min and max dates.\n   * @param date The date to clamp.\n   * @param min The minimum value to allow. If null or omitted no min is enforced.\n   * @param max The maximum value to allow. If null or omitted no max is enforced.\n   * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n   *     otherwise `date`.\n   */\n  clampDate(date, min, max) {\n    if (min && this.compareDate(date, min) < 0) {\n      return min;\n    }\n    if (max && this.compareDate(date, max) > 0) {\n      return max;\n    }\n    return date;\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst MAT_DATE_FORMATS = new InjectionToken('mat-date-formats');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Matches strings that have the form of a valid RFC 3339 string\n * (https://tools.ietf.org/html/rfc3339). Note that the string may not actually be a valid date\n * because the regex will match strings an with out of bounds month, date, etc.\n */\nconst ISO_8601_REGEX = /^\\d{4}-\\d{2}-\\d{2}(?:T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|(?:(?:\\+|-)\\d{2}:\\d{2}))?)?$/;\n/** Creates an array and fills it with values. */\nfunction range(length, valueFunction) {\n  const valuesArray = Array(length);\n  for (let i = 0; i < length; i++) {\n    valuesArray[i] = valueFunction(i);\n  }\n  return valuesArray;\n}\n/** Adapts the native JS Date for use with cdk-based components that work with dates. */\nclass NativeDateAdapter extends DateAdapter {\n  constructor(matDateLocale,\n  /**\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 14.0.0\n   */\n  _platform) {\n    super();\n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 14.0.0\n     */\n    this.useUtcForDisplay = false;\n    super.setLocale(matDateLocale);\n  }\n  getYear(date) {\n    return date.getFullYear();\n  }\n  getMonth(date) {\n    return date.getMonth();\n  }\n  getDate(date) {\n    return date.getDate();\n  }\n  getDayOfWeek(date) {\n    return date.getDay();\n  }\n  getMonthNames(style) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      month: style,\n      timeZone: 'utc'\n    });\n    return range(12, i => this._format(dtf, new Date(2017, i, 1)));\n  }\n  getDateNames() {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      day: 'numeric',\n      timeZone: 'utc'\n    });\n    return range(31, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n  getDayOfWeekNames(style) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      weekday: style,\n      timeZone: 'utc'\n    });\n    return range(7, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n  getYearName(date) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      year: 'numeric',\n      timeZone: 'utc'\n    });\n    return this._format(dtf, date);\n  }\n  getFirstDayOfWeek() {\n    // We can't tell using native JS Date what the first day of the week is, we default to Sunday.\n    return 0;\n  }\n  getNumDaysInMonth(date) {\n    return this.getDate(this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + 1, 0));\n  }\n  clone(date) {\n    return new Date(date.getTime());\n  }\n  createDate(year, month, date) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Check for invalid month and date (except upper bound on date which we have to check after\n      // creating the Date).\n      if (month < 0 || month > 11) {\n        throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n      }\n      if (date < 1) {\n        throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n      }\n    }\n    let result = this._createDateWithOverflow(year, month, date);\n    // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n    if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n    }\n    return result;\n  }\n  today() {\n    return new Date();\n  }\n  parse(value, parseFormat) {\n    // We have no way using the native JS Date to set the parse format or locale, so we ignore these\n    // parameters.\n    if (typeof value == 'number') {\n      return new Date(value);\n    }\n    return value ? new Date(Date.parse(value)) : null;\n  }\n  format(date, displayFormat) {\n    if (!this.isValid(date)) {\n      throw Error('NativeDateAdapter: Cannot format invalid date.');\n    }\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      ...displayFormat,\n      timeZone: 'utc'\n    });\n    return this._format(dtf, date);\n  }\n  addCalendarYears(date, years) {\n    return this.addCalendarMonths(date, years * 12);\n  }\n  addCalendarMonths(date, months) {\n    let newDate = this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + months, this.getDate(date));\n    // It's possible to wind up in the wrong month if the original month has more days than the new\n    // month. In this case we want to go to the last day of the desired month.\n    // Note: the additional + 12 % 12 ensures we end up with a positive number, since JS % doesn't\n    // guarantee this.\n    if (this.getMonth(newDate) != ((this.getMonth(date) + months) % 12 + 12) % 12) {\n      newDate = this._createDateWithOverflow(this.getYear(newDate), this.getMonth(newDate), 0);\n    }\n    return newDate;\n  }\n  addCalendarDays(date, days) {\n    return this._createDateWithOverflow(this.getYear(date), this.getMonth(date), this.getDate(date) + days);\n  }\n  toIso8601(date) {\n    return [date.getUTCFullYear(), this._2digit(date.getUTCMonth() + 1), this._2digit(date.getUTCDate())].join('-');\n  }\n  /**\n   * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n   * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n   * invalid date for all other values.\n   */\n  deserialize(value) {\n    if (typeof value === 'string') {\n      if (!value) {\n        return null;\n      }\n      // The `Date` constructor accepts formats other than ISO 8601, so we need to make sure the\n      // string is the right format first.\n      if (ISO_8601_REGEX.test(value)) {\n        let date = new Date(value);\n        if (this.isValid(date)) {\n          return date;\n        }\n      }\n    }\n    return super.deserialize(value);\n  }\n  isDateInstance(obj) {\n    return obj instanceof Date;\n  }\n  isValid(date) {\n    return !isNaN(date.getTime());\n  }\n  invalid() {\n    return new Date(NaN);\n  }\n  /** Creates a date but allows the month and date to overflow. */\n  _createDateWithOverflow(year, month, date) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setFullYear` and `setHours` instead.\n    const d = new Date();\n    d.setFullYear(year, month, date);\n    d.setHours(0, 0, 0, 0);\n    return d;\n  }\n  /**\n   * Pads a number to make it two digits.\n   * @param n The number to pad.\n   * @returns The padded number.\n   */\n  _2digit(n) {\n    return ('00' + n).slice(-2);\n  }\n  /**\n   * When converting Date object to string, javascript built-in functions may return wrong\n   * results because it applies its internal DST rules. The DST rules around the world change\n   * very frequently, and the current valid rule is not always valid in previous years though.\n   * We work around this problem building a new Date object which has its internal UTC\n   * representation with the local date and time.\n   * @param dtf Intl.DateTimeFormat object, containing the desired string format. It must have\n   *    timeZone set to 'utc' to work fine.\n   * @param date Date from which we want to get the string representation according to dtf\n   * @returns A Date object with its UTC representation based on the passed in date info\n   */\n  _format(dtf, date) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setUTCFullYear` and `setUTCHours` instead.\n    const d = new Date();\n    d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n    d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n    return dtf.format(d);\n  }\n}\nNativeDateAdapter.ɵfac = function NativeDateAdapter_Factory(t) {\n  return new (t || NativeDateAdapter)(i0.ɵɵinject(MAT_DATE_LOCALE, 8), i0.ɵɵinject(i1$1.Platform));\n};\nNativeDateAdapter.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NativeDateAdapter,\n  factory: NativeDateAdapter.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NativeDateAdapter, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_DATE_LOCALE]\n      }]\n    }, {\n      type: i1$1.Platform\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst MAT_NATIVE_DATE_FORMATS = {\n  parse: {\n    dateInput: null\n  },\n  display: {\n    dateInput: {\n      year: 'numeric',\n      month: 'numeric',\n      day: 'numeric'\n    },\n    monthYearLabel: {\n      year: 'numeric',\n      month: 'short'\n    },\n    dateA11yLabel: {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    },\n    monthYearA11yLabel: {\n      year: 'numeric',\n      month: 'long'\n    }\n  }\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass NativeDateModule {}\nNativeDateModule.ɵfac = function NativeDateModule_Factory(t) {\n  return new (t || NativeDateModule)();\n};\nNativeDateModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NativeDateModule\n});\nNativeDateModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: DateAdapter,\n    useClass: NativeDateAdapter\n  }]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NativeDateModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: DateAdapter,\n        useClass: NativeDateAdapter\n      }]\n    }]\n  }], null, null);\n})();\nclass MatNativeDateModule {}\nMatNativeDateModule.ɵfac = function MatNativeDateModule_Factory(t) {\n  return new (t || MatNativeDateModule)();\n};\nMatNativeDateModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatNativeDateModule,\n  imports: [NativeDateModule]\n});\nMatNativeDateModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: MAT_DATE_FORMATS,\n    useValue: MAT_NATIVE_DATE_FORMATS\n  }],\n  imports: [NativeDateModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNativeDateModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NativeDateModule],\n      providers: [{\n        provide: MAT_DATE_FORMATS,\n        useValue: MAT_NATIVE_DATE_FORMATS\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Error state matcher that matches when a control is invalid and dirty. */\nclass ShowOnDirtyErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.dirty || form && form.submitted));\n  }\n}\nShowOnDirtyErrorStateMatcher.ɵfac = function ShowOnDirtyErrorStateMatcher_Factory(t) {\n  return new (t || ShowOnDirtyErrorStateMatcher)();\n};\nShowOnDirtyErrorStateMatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ShowOnDirtyErrorStateMatcher,\n  factory: ShowOnDirtyErrorStateMatcher.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShowOnDirtyErrorStateMatcher, [{\n    type: Injectable\n  }], null, null);\n})();\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nclass ErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.touched || form && form.submitted));\n  }\n}\nErrorStateMatcher.ɵfac = function ErrorStateMatcher_Factory(t) {\n  return new (t || ErrorStateMatcher)();\n};\nErrorStateMatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ErrorStateMatcher,\n  factory: ErrorStateMatcher.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ErrorStateMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\nclass MatLine {}\nMatLine.ɵfac = function MatLine_Factory(t) {\n  return new (t || MatLine)();\n};\nMatLine.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatLine,\n  selectors: [[\"\", \"mat-line\", \"\"], [\"\", \"matLine\", \"\"]],\n  hostAttrs: [1, \"mat-line\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatLine, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-line], [matLine]',\n      host: {\n        'class': 'mat-line'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\nfunction setLines(lines, element, prefix = 'mat') {\n  // Note: doesn't need to unsubscribe, because `changes`\n  // gets completed by Angular when the view is destroyed.\n  lines.changes.pipe(startWith(lines)).subscribe(({\n    length\n  }) => {\n    setClass(element, `${prefix}-2-line`, false);\n    setClass(element, `${prefix}-3-line`, false);\n    setClass(element, `${prefix}-multi-line`, false);\n    if (length === 2 || length === 3) {\n      setClass(element, `${prefix}-${length}-line`, true);\n    } else if (length > 3) {\n      setClass(element, `${prefix}-multi-line`, true);\n    }\n  });\n}\n/** Adds or removes a class from an element. */\nfunction setClass(element, className, isAdd) {\n  element.nativeElement.classList.toggle(className, isAdd);\n}\nclass MatLineModule {}\nMatLineModule.ɵfac = function MatLineModule_Factory(t) {\n  return new (t || MatLineModule)();\n};\nMatLineModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatLineModule,\n  declarations: [MatLine],\n  imports: [MatCommonModule],\n  exports: [MatLine, MatCommonModule]\n});\nMatLineModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, MatCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatLineModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: [MatLine, MatCommonModule],\n      declarations: [MatLine]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Reference to a previously launched ripple element.\n */\nclass RippleRef {\n  constructor(_renderer, /** Reference to the ripple HTML element. */\n  element, /** Ripple configuration used for the ripple. */\n  config, /* Whether animations are forcibly disabled for ripples through CSS. */\n  _animationForciblyDisabledThroughCss = false) {\n    this._renderer = _renderer;\n    this.element = element;\n    this.config = config;\n    this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;\n    /** Current state of the ripple. */\n    this.state = 3 /* RippleState.HIDDEN */;\n  }\n  /** Fades out the ripple element. */\n  fadeOut() {\n    this._renderer.fadeOutRipple(this);\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions$1 = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nclass RippleEventManager {\n  constructor() {\n    this._events = new Map();\n    /** Event handler that is bound and which dispatches the events to the different targets. */\n    this._delegateEventHandler = event => {\n      const target = _getEventTarget(event);\n      if (target) {\n        this._events.get(event.type)?.forEach((handlers, element) => {\n          if (element === target || element.contains(target)) {\n            handlers.forEach(handler => handler.handleEvent(event));\n          }\n        });\n      }\n    };\n  }\n  /** Adds an event handler. */\n  addHandler(ngZone, name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (handlersForEvent) {\n      const handlersForElement = handlersForEvent.get(element);\n      if (handlersForElement) {\n        handlersForElement.add(handler);\n      } else {\n        handlersForEvent.set(element, new Set([handler]));\n      }\n    } else {\n      this._events.set(name, new Map([[element, new Set([handler])]]));\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n      });\n    }\n  }\n  /** Removes an event handler. */\n  removeHandler(name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (!handlersForEvent) {\n      return;\n    }\n    const handlersForElement = handlersForEvent.get(element);\n    if (!handlersForElement) {\n      return;\n    }\n    handlersForElement.delete(handler);\n    if (handlersForElement.size === 0) {\n      handlersForEvent.delete(element);\n    }\n    if (handlersForEvent.size === 0) {\n      this._events.delete(name);\n      document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n    }\n  }\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nconst defaultRippleAnimationConfig = {\n  enterDuration: 225,\n  exitDuration: 150\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nclass RippleRenderer {\n  constructor(_target, _ngZone, elementOrElementRef, _platform) {\n    this._target = _target;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    /** Whether the pointer is currently down or not. */\n    this._isPointerDown = false;\n    /**\n     * Map of currently active ripple references.\n     * The ripple reference is mapped to its element event listeners.\n     * The reason why `| null` is used is that event listeners are added only\n     * when the condition is truthy (see the `_startFadeOutTransition` method).\n     */\n    this._activeRipples = new Map();\n    /** Whether pointer-up event listeners have been registered. */\n    this._pointerUpEventsRegistered = false;\n    // Only do anything if we're on the browser.\n    if (_platform.isBrowser) {\n      this._containerElement = coerceElement(elementOrElementRef);\n    }\n  }\n  /**\n   * Fades in a ripple at the given coordinates.\n   * @param x Coordinate within the element, along the X axis at which to start the ripple.\n   * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n   * @param config Extra ripple options.\n   */\n  fadeInRipple(x, y, config = {}) {\n    const containerRect = this._containerRect = this._containerRect || this._containerElement.getBoundingClientRect();\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...config.animation\n    };\n    if (config.centered) {\n      x = containerRect.left + containerRect.width / 2;\n      y = containerRect.top + containerRect.height / 2;\n    }\n    const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n    const offsetX = x - containerRect.left;\n    const offsetY = y - containerRect.top;\n    const enterDuration = animationConfig.enterDuration;\n    const ripple = document.createElement('div');\n    ripple.classList.add('mat-ripple-element');\n    ripple.style.left = `${offsetX - radius}px`;\n    ripple.style.top = `${offsetY - radius}px`;\n    ripple.style.height = `${radius * 2}px`;\n    ripple.style.width = `${radius * 2}px`;\n    // If a custom color has been specified, set it as inline style. If no color is\n    // set, the default color will be applied through the ripple theme styles.\n    if (config.color != null) {\n      ripple.style.backgroundColor = config.color;\n    }\n    ripple.style.transitionDuration = `${enterDuration}ms`;\n    this._containerElement.appendChild(ripple);\n    // By default the browser does not recalculate the styles of dynamically created\n    // ripple elements. This is critical to ensure that the `scale` animates properly.\n    // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n    // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n    const computedStyles = window.getComputedStyle(ripple);\n    const userTransitionProperty = computedStyles.transitionProperty;\n    const userTransitionDuration = computedStyles.transitionDuration;\n    // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n    // `transition: none` or `display: none`). This is technically unexpected since animations are\n    // controlled through the animation config, but this exists for backwards compatibility. This\n    // logic does not need to be super accurate since it covers some edge cases which can be easily\n    // avoided by users.\n    const animationForciblyDisabledThroughCss = userTransitionProperty === 'none' ||\n    // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n    // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n    userTransitionDuration === '0s' || userTransitionDuration === '0s, 0s' ||\n    // If the container is 0x0, it's likely `display: none`.\n    containerRect.width === 0 && containerRect.height === 0;\n    // Exposed reference to the ripple that will be returned.\n    const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n    // Start the enter animation by setting the transform/scale to 100%. The animation will\n    // execute as part of this statement because we forced a style recalculation before.\n    // Note: We use a 3d transform here in order to avoid an issue in Safari where\n    // the ripples aren't clipped when inside the shadow DOM (see #24028).\n    ripple.style.transform = 'scale3d(1, 1, 1)';\n    rippleRef.state = 0 /* RippleState.FADING_IN */;\n    if (!config.persistent) {\n      this._mostRecentTransientRipple = rippleRef;\n    }\n    let eventListeners = null;\n    // Do not register the `transition` event listener if fade-in and fade-out duration\n    // are set to zero. The events won't fire anyway and we can save resources here.\n    if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n      this._ngZone.runOutsideAngular(() => {\n        const onTransitionEnd = () => this._finishRippleTransition(rippleRef);\n        const onTransitionCancel = () => this._destroyRipple(rippleRef);\n        ripple.addEventListener('transitionend', onTransitionEnd);\n        // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n        // directly as otherwise we would keep it part of the ripple container forever.\n        // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n        ripple.addEventListener('transitioncancel', onTransitionCancel);\n        eventListeners = {\n          onTransitionEnd,\n          onTransitionCancel\n        };\n      });\n    }\n    // Add the ripple reference to the list of all active ripples.\n    this._activeRipples.set(rippleRef, eventListeners);\n    // In case there is no fade-in transition duration, we need to manually call the transition\n    // end listener because `transitionend` doesn't fire if there is no transition.\n    if (animationForciblyDisabledThroughCss || !enterDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n    return rippleRef;\n  }\n  /** Fades out a ripple reference. */\n  fadeOutRipple(rippleRef) {\n    // For ripples already fading out or hidden, this should be a noop.\n    if (rippleRef.state === 2 /* RippleState.FADING_OUT */ || rippleRef.state === 3 /* RippleState.HIDDEN */) {\n      return;\n    }\n    const rippleEl = rippleRef.element;\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...rippleRef.config.animation\n    };\n    // This starts the fade-out transition and will fire the transition end listener that\n    // removes the ripple element from the DOM.\n    rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n    rippleEl.style.opacity = '0';\n    rippleRef.state = 2 /* RippleState.FADING_OUT */;\n    // In case there is no fade-out transition duration, we need to manually call the\n    // transition end listener because `transitionend` doesn't fire if there is no transition.\n    if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n  }\n  /** Fades out all currently active ripples. */\n  fadeOutAll() {\n    this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n  }\n  /** Fades out all currently active non-persistent ripples. */\n  fadeOutAllNonPersistent() {\n    this._getActiveRipples().forEach(ripple => {\n      if (!ripple.config.persistent) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  /** Sets up the trigger event listeners */\n  setupTriggerEvents(elementOrElementRef) {\n    const element = coerceElement(elementOrElementRef);\n    if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n      return;\n    }\n    // Remove all previously registered event listeners from the trigger element.\n    this._removeTriggerEvents();\n    this._triggerElement = element;\n    // Use event delegation for the trigger events since they're\n    // set up during creation and are performance-sensitive.\n    pointerDownEvents.forEach(type => {\n      RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n    });\n  }\n  /**\n   * Handles all registered events.\n   * @docs-private\n   */\n  handleEvent(event) {\n    if (event.type === 'mousedown') {\n      this._onMousedown(event);\n    } else if (event.type === 'touchstart') {\n      this._onTouchStart(event);\n    } else {\n      this._onPointerUp();\n    }\n    // If pointer-up events haven't been registered yet, do so now.\n    // We do this on-demand in order to reduce the total number of event listeners\n    // registered by the ripples, which speeds up the rendering time for large UIs.\n    if (!this._pointerUpEventsRegistered) {\n      // The events for hiding the ripple are bound directly on the trigger, because:\n      // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n      // delegation will be diminished by having to look through all the data structures often.\n      // 2. They aren't as performance-sensitive, because they're bound only after the user\n      // has interacted with an element.\n      this._ngZone.runOutsideAngular(() => {\n        pointerUpEvents.forEach(type => {\n          this._triggerElement.addEventListener(type, this, passiveCapturingEventOptions);\n        });\n      });\n      this._pointerUpEventsRegistered = true;\n    }\n  }\n  /** Method that will be called if the fade-in or fade-in transition completed. */\n  _finishRippleTransition(rippleRef) {\n    if (rippleRef.state === 0 /* RippleState.FADING_IN */) {\n      this._startFadeOutTransition(rippleRef);\n    } else if (rippleRef.state === 2 /* RippleState.FADING_OUT */) {\n      this._destroyRipple(rippleRef);\n    }\n  }\n  /**\n   * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n   * is not held down anymore.\n   */\n  _startFadeOutTransition(rippleRef) {\n    const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n    const {\n      persistent\n    } = rippleRef.config;\n    rippleRef.state = 1 /* RippleState.VISIBLE */;\n    // When the timer runs out while the user has kept their pointer down, we want to\n    // keep only the persistent ripples and the latest transient ripple. We do this,\n    // because we don't want stacked transient ripples to appear after their enter\n    // animation has finished.\n    if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n      rippleRef.fadeOut();\n    }\n  }\n  /** Destroys the given ripple by removing it from the DOM and updating its state. */\n  _destroyRipple(rippleRef) {\n    const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n    this._activeRipples.delete(rippleRef);\n    // Clear out the cached bounding rect if we have no more ripples.\n    if (!this._activeRipples.size) {\n      this._containerRect = null;\n    }\n    // If the current ref is the most recent transient ripple, unset it\n    // avoid memory leaks.\n    if (rippleRef === this._mostRecentTransientRipple) {\n      this._mostRecentTransientRipple = null;\n    }\n    rippleRef.state = 3 /* RippleState.HIDDEN */;\n    if (eventListeners !== null) {\n      rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n      rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n    }\n    rippleRef.element.remove();\n  }\n  /** Function being called whenever the trigger is being pressed using mouse. */\n  _onMousedown(event) {\n    // Screen readers will fire fake mouse events for space/enter. Skip launching a\n    // ripple in this case for consistency with the non-screen-reader experience.\n    const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n    const isSyntheticEvent = this._lastTouchStartEvent && Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n    if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n      this._isPointerDown = true;\n      this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n    }\n  }\n  /** Function being called whenever the trigger is being pressed using touch. */\n  _onTouchStart(event) {\n    if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n      // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n      // events will launch a second ripple if we don't ignore mouse events for a specific\n      // time after a touchstart event.\n      this._lastTouchStartEvent = Date.now();\n      this._isPointerDown = true;\n      // Use `changedTouches` so we skip any touches where the user put\n      // their finger down, but used another finger to tap the element again.\n      const touches = event.changedTouches;\n      for (let i = 0; i < touches.length; i++) {\n        this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n      }\n    }\n  }\n  /** Function being called whenever the trigger is being released. */\n  _onPointerUp() {\n    if (!this._isPointerDown) {\n      return;\n    }\n    this._isPointerDown = false;\n    // Fade-out all ripples that are visible and not persistent.\n    this._getActiveRipples().forEach(ripple => {\n      // By default, only ripples that are completely visible will fade out on pointer release.\n      // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n      const isVisible = ripple.state === 1 /* RippleState.VISIBLE */ || ripple.config.terminateOnPointerUp && ripple.state === 0 /* RippleState.FADING_IN */;\n      if (!ripple.config.persistent && isVisible) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  _getActiveRipples() {\n    return Array.from(this._activeRipples.keys());\n  }\n  /** Removes previously registered event listeners from the trigger element. */\n  _removeTriggerEvents() {\n    const trigger = this._triggerElement;\n    if (trigger) {\n      pointerDownEvents.forEach(type => RippleRenderer._eventManager.removeHandler(type, trigger, this));\n      if (this._pointerUpEventsRegistered) {\n        pointerUpEvents.forEach(type => trigger.removeEventListener(type, this, passiveCapturingEventOptions));\n      }\n    }\n  }\n}\nRippleRenderer._eventManager = new RippleEventManager();\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x, y, rect) {\n  const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n  const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n  return Math.sqrt(distX * distX + distY * distY);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that can be used to specify the global ripple options. */\nconst MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken('mat-ripple-global-options');\nclass MatRipple {\n  /**\n   * Whether click events will not trigger the ripple. Ripples can be still launched manually\n   * by using the `launch()` method.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    if (value) {\n      this.fadeOutAllNonPersistent();\n    }\n    this._disabled = value;\n    this._setupTriggerEventsIfEnabled();\n  }\n  /**\n   * The element that triggers the ripple when click events are received.\n   * Defaults to the directive's host element.\n   */\n  get trigger() {\n    return this._trigger || this._elementRef.nativeElement;\n  }\n  set trigger(trigger) {\n    this._trigger = trigger;\n    this._setupTriggerEventsIfEnabled();\n  }\n  constructor(_elementRef, ngZone, platform, globalOptions, _animationMode) {\n    this._elementRef = _elementRef;\n    this._animationMode = _animationMode;\n    /**\n     * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n     * will be the distance from the center of the ripple to the furthest corner of the host element's\n     * bounding rectangle.\n     */\n    this.radius = 0;\n    this._disabled = false;\n    /** Whether ripple directive is initialized and the input bindings are set. */\n    this._isInitialized = false;\n    this._globalOptions = globalOptions || {};\n    this._rippleRenderer = new RippleRenderer(this, ngZone, _elementRef, platform);\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n    this._setupTriggerEventsIfEnabled();\n  }\n  ngOnDestroy() {\n    this._rippleRenderer._removeTriggerEvents();\n  }\n  /** Fades out all currently showing ripple elements. */\n  fadeOutAll() {\n    this._rippleRenderer.fadeOutAll();\n  }\n  /** Fades out all currently showing non-persistent ripple elements. */\n  fadeOutAllNonPersistent() {\n    this._rippleRenderer.fadeOutAllNonPersistent();\n  }\n  /**\n   * Ripple configuration from the directive's input values.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleConfig() {\n    return {\n      centered: this.centered,\n      radius: this.radius,\n      color: this.color,\n      animation: {\n        ...this._globalOptions.animation,\n        ...(this._animationMode === 'NoopAnimations' ? {\n          enterDuration: 0,\n          exitDuration: 0\n        } : {}),\n        ...this.animation\n      },\n      terminateOnPointerUp: this._globalOptions.terminateOnPointerUp\n    };\n  }\n  /**\n   * Whether ripples on pointer-down are disabled or not.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleDisabled() {\n    return this.disabled || !!this._globalOptions.disabled;\n  }\n  /** Sets up the trigger event listeners if ripples are enabled. */\n  _setupTriggerEventsIfEnabled() {\n    if (!this.disabled && this._isInitialized) {\n      this._rippleRenderer.setupTriggerEvents(this.trigger);\n    }\n  }\n  /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n  launch(configOrX, y = 0, config) {\n    if (typeof configOrX === 'number') {\n      return this._rippleRenderer.fadeInRipple(configOrX, y, {\n        ...this.rippleConfig,\n        ...config\n      });\n    } else {\n      return this._rippleRenderer.fadeInRipple(0, 0, {\n        ...this.rippleConfig,\n        ...configOrX\n      });\n    }\n  }\n}\nMatRipple.ɵfac = function MatRipple_Factory(t) {\n  return new (t || MatRipple)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.Platform), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\nMatRipple.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatRipple,\n  selectors: [[\"\", \"mat-ripple\", \"\"], [\"\", \"matRipple\", \"\"]],\n  hostAttrs: [1, \"mat-ripple\"],\n  hostVars: 2,\n  hostBindings: function MatRipple_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-ripple-unbounded\", ctx.unbounded);\n    }\n  },\n  inputs: {\n    color: [\"matRippleColor\", \"color\"],\n    unbounded: [\"matRippleUnbounded\", \"unbounded\"],\n    centered: [\"matRippleCentered\", \"centered\"],\n    radius: [\"matRippleRadius\", \"radius\"],\n    animation: [\"matRippleAnimation\", \"animation\"],\n    disabled: [\"matRippleDisabled\", \"disabled\"],\n    trigger: [\"matRippleTrigger\", \"trigger\"]\n  },\n  exportAs: [\"matRipple\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRipple, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-ripple], [matRipple]',\n      exportAs: 'matRipple',\n      host: {\n        'class': 'mat-ripple',\n        '[class.mat-ripple-unbounded]': 'unbounded'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1$1.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    color: [{\n      type: Input,\n      args: ['matRippleColor']\n    }],\n    unbounded: [{\n      type: Input,\n      args: ['matRippleUnbounded']\n    }],\n    centered: [{\n      type: Input,\n      args: ['matRippleCentered']\n    }],\n    radius: [{\n      type: Input,\n      args: ['matRippleRadius']\n    }],\n    animation: [{\n      type: Input,\n      args: ['matRippleAnimation']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matRippleDisabled']\n    }],\n    trigger: [{\n      type: Input,\n      args: ['matRippleTrigger']\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatRippleModule {}\nMatRippleModule.ɵfac = function MatRippleModule_Factory(t) {\n  return new (t || MatRippleModule)();\n};\nMatRippleModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatRippleModule,\n  declarations: [MatRipple],\n  imports: [MatCommonModule],\n  exports: [MatRipple, MatCommonModule]\n});\nMatRippleModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, MatCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: [MatRipple, MatCommonModule],\n      declarations: [MatRipple]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\nclass MatPseudoCheckbox {\n  constructor(_animationMode) {\n    this._animationMode = _animationMode;\n    /** Display state of the checkbox. */\n    this.state = 'unchecked';\n    /** Whether the checkbox is disabled. */\n    this.disabled = false;\n    /**\n     * Appearance of the pseudo checkbox. Default appearance of 'full' renders a checkmark/mixedmark\n     * indicator inside a square box. 'minimal' appearance only renders the checkmark/mixedmark.\n     */\n    this.appearance = 'full';\n  }\n}\nMatPseudoCheckbox.ɵfac = function MatPseudoCheckbox_Factory(t) {\n  return new (t || MatPseudoCheckbox)(i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\nMatPseudoCheckbox.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatPseudoCheckbox,\n  selectors: [[\"mat-pseudo-checkbox\"]],\n  hostAttrs: [1, \"mat-pseudo-checkbox\"],\n  hostVars: 12,\n  hostBindings: function MatPseudoCheckbox_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-pseudo-checkbox-indeterminate\", ctx.state === \"indeterminate\")(\"mat-pseudo-checkbox-checked\", ctx.state === \"checked\")(\"mat-pseudo-checkbox-disabled\", ctx.disabled)(\"mat-pseudo-checkbox-minimal\", ctx.appearance === \"minimal\")(\"mat-pseudo-checkbox-full\", ctx.appearance === \"full\")(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n    }\n  },\n  inputs: {\n    state: \"state\",\n    disabled: \"disabled\",\n    appearance: \"appearance\"\n  },\n  decls: 0,\n  vars: 0,\n  template: function MatPseudoCheckbox_Template(rf, ctx) {},\n  styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-full{border:2px solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckbox, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'mat-pseudo-checkbox',\n      template: '',\n      host: {\n        'class': 'mat-pseudo-checkbox',\n        '[class.mat-pseudo-checkbox-indeterminate]': 'state === \"indeterminate\"',\n        '[class.mat-pseudo-checkbox-checked]': 'state === \"checked\"',\n        '[class.mat-pseudo-checkbox-disabled]': 'disabled',\n        '[class.mat-pseudo-checkbox-minimal]': 'appearance === \"minimal\"',\n        '[class.mat-pseudo-checkbox-full]': 'appearance === \"full\"',\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"'\n      },\n      styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-full{border:2px solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    state: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    appearance: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatPseudoCheckboxModule {}\nMatPseudoCheckboxModule.ɵfac = function MatPseudoCheckboxModule_Factory(t) {\n  return new (t || MatPseudoCheckboxModule)();\n};\nMatPseudoCheckboxModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatPseudoCheckboxModule,\n  declarations: [MatPseudoCheckbox],\n  imports: [MatCommonModule],\n  exports: [MatPseudoCheckbox]\n});\nMatPseudoCheckboxModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: [MatPseudoCheckbox],\n      declarations: [MatPseudoCheckbox]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token used to provide the parent component to options.\n */\nconst MAT_OPTION_PARENT_COMPONENT = new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n// Boilerplate for applying mixins to MatOptgroup.\n/** @docs-private */\nconst _MatOptgroupMixinBase = mixinDisabled(class {});\n// Counter for unique group ids.\nlet _uniqueOptgroupIdCounter = 0;\nclass _MatOptgroupBase extends _MatOptgroupMixinBase {\n  constructor(parent) {\n    super();\n    /** Unique id for the underlying label. */\n    this._labelId = `mat-optgroup-label-${_uniqueOptgroupIdCounter++}`;\n    this._inert = parent?.inertGroups ?? false;\n  }\n}\n_MatOptgroupBase.ɵfac = function _MatOptgroupBase_Factory(t) {\n  return new (t || _MatOptgroupBase)(i0.ɵɵdirectiveInject(MAT_OPTION_PARENT_COMPONENT, 8));\n};\n_MatOptgroupBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatOptgroupBase,\n  inputs: {\n    label: \"label\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatOptgroupBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_OPTION_PARENT_COMPONENT]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    label: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_OPTGROUP = new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\nclass MatOptgroup extends _MatOptgroupBase {}\nMatOptgroup.ɵfac = /* @__PURE__ */function () {\n  let ɵMatOptgroup_BaseFactory;\n  return function MatOptgroup_Factory(t) {\n    return (ɵMatOptgroup_BaseFactory || (ɵMatOptgroup_BaseFactory = i0.ɵɵgetInheritedFactory(MatOptgroup)))(t || MatOptgroup);\n  };\n}();\nMatOptgroup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatOptgroup,\n  selectors: [[\"mat-optgroup\"]],\n  hostAttrs: [1, \"mat-mdc-optgroup\"],\n  hostVars: 3,\n  hostBindings: function MatOptgroup_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"role\", ctx._inert ? null : \"group\")(\"aria-disabled\", ctx._inert ? null : ctx.disabled.toString())(\"aria-labelledby\", ctx._inert ? null : ctx._labelId);\n    }\n  },\n  inputs: {\n    disabled: \"disabled\"\n  },\n  exportAs: [\"matOptgroup\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_OPTGROUP,\n    useExisting: MatOptgroup\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c1,\n  decls: 5,\n  vars: 4,\n  consts: [[\"aria-hidden\", \"true\", 1, \"mat-mdc-optgroup-label\", 3, \"id\"], [1, \"mdc-list-item__primary-text\"]],\n  template: function MatOptgroup_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c0);\n      i0.ɵɵelementStart(0, \"span\", 0)(1, \"span\", 1);\n      i0.ɵɵtext(2);\n      i0.ɵɵprojection(3);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵprojection(4, 1);\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mdc-list-item--disabled\", ctx.disabled);\n      i0.ɵɵproperty(\"id\", ctx._labelId);\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate1(\"\", ctx.label, \" \");\n    }\n  },\n  styles: [\".mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;min-height:48px}.mat-mdc-optgroup-label:focus{outline:none}[dir=rtl] .mat-mdc-optgroup-label,.mat-mdc-optgroup-label[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{white-space:normal}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptgroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-optgroup',\n      exportAs: 'matOptgroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      inputs: ['disabled'],\n      host: {\n        'class': 'mat-mdc-optgroup',\n        '[attr.role]': '_inert ? null : \"group\"',\n        '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n        '[attr.aria-labelledby]': '_inert ? null : _labelId'\n      },\n      providers: [{\n        provide: MAT_OPTGROUP,\n        useExisting: MatOptgroup\n      }],\n      template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  aria-hidden=\\\"true\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\",\n      styles: [\".mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;min-height:48px}.mat-mdc-optgroup-label:focus{outline:none}[dir=rtl] .mat-mdc-optgroup-label,.mat-mdc-optgroup-label[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{white-space:normal}\"]\n    }]\n  }], null, null);\n})();\n\n/**\n * Option IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\nlet _uniqueIdCounter = 0;\n/** Event object emitted by MatOption when selected or deselected. */\nclass MatOptionSelectionChange {\n  constructor( /** Reference to the option that emitted the event. */\n  source, /** Whether the change in the option's value was a result of a user action. */\n  isUserInput = false) {\n    this.source = source;\n    this.isUserInput = isUserInput;\n  }\n}\nclass _MatOptionBase {\n  /** Whether the wrapping component is in multiple selection mode. */\n  get multiple() {\n    return this._parent && this._parent.multiple;\n  }\n  /** Whether or not the option is currently selected. */\n  get selected() {\n    return this._selected;\n  }\n  /** Whether the option is disabled. */\n  get disabled() {\n    return this.group && this.group.disabled || this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n  /** Whether ripples for the option are disabled. */\n  get disableRipple() {\n    return !!(this._parent && this._parent.disableRipple);\n  }\n  /** Whether to display checkmark for single-selection. */\n  get hideSingleSelectionIndicator() {\n    return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n  }\n  constructor(_element, _changeDetectorRef, _parent, group) {\n    this._element = _element;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._parent = _parent;\n    this.group = group;\n    this._selected = false;\n    this._active = false;\n    this._disabled = false;\n    this._mostRecentViewValue = '';\n    /** The unique ID of the option. */\n    this.id = `mat-option-${_uniqueIdCounter++}`;\n    /** Event emitted when the option is selected or deselected. */\n    // tslint:disable-next-line:no-output-on-prefix\n    this.onSelectionChange = new EventEmitter();\n    /** Emits when the state of the option changes and any parents have to be notified. */\n    this._stateChanges = new Subject();\n  }\n  /**\n   * Whether or not the option is currently active and ready to be selected.\n   * An active option displays styles as if it is focused, but the\n   * focus is actually retained somewhere else. This comes in handy\n   * for components like autocomplete where focus must remain on the input.\n   */\n  get active() {\n    return this._active;\n  }\n  /**\n   * The displayed value of the option. It is necessary to show the selected option in the\n   * select's trigger.\n   */\n  get viewValue() {\n    // TODO(kara): Add input property alternative for node envs.\n    return (this._text?.nativeElement.textContent || '').trim();\n  }\n  /** Selects the option. */\n  select() {\n    if (!this._selected) {\n      this._selected = true;\n      this._changeDetectorRef.markForCheck();\n      this._emitSelectionChangeEvent();\n    }\n  }\n  /** Deselects the option. */\n  deselect() {\n    if (this._selected) {\n      this._selected = false;\n      this._changeDetectorRef.markForCheck();\n      this._emitSelectionChangeEvent();\n    }\n  }\n  /** Sets focus onto this option. */\n  focus(_origin, options) {\n    // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n    // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n    const element = this._getHostElement();\n    if (typeof element.focus === 'function') {\n      element.focus(options);\n    }\n  }\n  /**\n   * This method sets display styles on the option to make it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setActiveStyles() {\n    if (!this._active) {\n      this._active = true;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * This method removes display styles on the option that made it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setInactiveStyles() {\n    if (this._active) {\n      this._active = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel() {\n    return this.viewValue;\n  }\n  /** Ensures the option is selected when activated from the keyboard. */\n  _handleKeydown(event) {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n      this._selectViaInteraction();\n      // Prevent the page from scrolling down and form submits.\n      event.preventDefault();\n    }\n  }\n  /**\n   * `Selects the option while indicating the selection came from the user. Used to\n   * determine if the select's view -> model callback should be invoked.`\n   */\n  _selectViaInteraction() {\n    if (!this.disabled) {\n      this._selected = this.multiple ? !this._selected : true;\n      this._changeDetectorRef.markForCheck();\n      this._emitSelectionChangeEvent(true);\n    }\n  }\n  /** Returns the correct tabindex for the option depending on disabled state. */\n  // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n  // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n  // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Gets the host DOM element. */\n  _getHostElement() {\n    return this._element.nativeElement;\n  }\n  ngAfterViewChecked() {\n    // Since parent components could be using the option's label to display the selected values\n    // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n    // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n    // relatively cheap, however we still limit them only to selected options in order to avoid\n    // hitting the DOM too often.\n    if (this._selected) {\n      const viewValue = this.viewValue;\n      if (viewValue !== this._mostRecentViewValue) {\n        if (this._mostRecentViewValue) {\n          this._stateChanges.next();\n        }\n        this._mostRecentViewValue = viewValue;\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  /** Emits the selection change event. */\n  _emitSelectionChangeEvent(isUserInput = false) {\n    this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n  }\n}\n_MatOptionBase.ɵfac = function _MatOptionBase_Factory(t) {\n  i0.ɵɵinvalidFactory();\n};\n_MatOptionBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatOptionBase,\n  viewQuery: function _MatOptionBase_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c2, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._text = _t.first);\n    }\n  },\n  inputs: {\n    value: \"value\",\n    id: \"id\",\n    disabled: \"disabled\"\n  },\n  outputs: {\n    onSelectionChange: \"onSelectionChange\"\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatOptionBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined\n    }, {\n      type: _MatOptgroupBase\n    }];\n  }, {\n    value: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    onSelectionChange: [{\n      type: Output\n    }],\n    _text: [{\n      type: ViewChild,\n      args: ['text', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * Single option inside of a `<mat-select>` element.\n */\nclass MatOption extends _MatOptionBase {\n  constructor(element, changeDetectorRef, parent, group) {\n    super(element, changeDetectorRef, parent, group);\n  }\n}\nMatOption.ɵfac = function MatOption_Factory(t) {\n  return new (t || MatOption)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_OPTION_PARENT_COMPONENT, 8), i0.ɵɵdirectiveInject(MAT_OPTGROUP, 8));\n};\nMatOption.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatOption,\n  selectors: [[\"mat-option\"]],\n  hostAttrs: [\"role\", \"option\", 1, \"mat-mdc-option\", \"mdc-list-item\"],\n  hostVars: 11,\n  hostBindings: function MatOption_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function MatOption_click_HostBindingHandler() {\n        return ctx._selectViaInteraction();\n      })(\"keydown\", function MatOption_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"id\", ctx.id);\n      i0.ɵɵattribute(\"aria-selected\", ctx.selected)(\"aria-disabled\", ctx.disabled.toString());\n      i0.ɵɵclassProp(\"mdc-list-item--selected\", ctx.selected)(\"mat-mdc-option-multiple\", ctx.multiple)(\"mat-mdc-option-active\", ctx.active)(\"mdc-list-item--disabled\", ctx.disabled);\n    }\n  },\n  exportAs: [\"matOption\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c4,\n  decls: 8,\n  vars: 5,\n  consts: [[\"class\", \"mat-mdc-option-pseudo-checkbox\", 3, \"state\", \"disabled\", 4, \"ngIf\"], [1, \"mdc-list-item__primary-text\"], [\"text\", \"\"], [\"class\", \"mat-mdc-option-pseudo-checkbox\", \"state\", \"checked\", \"appearance\", \"minimal\", 3, \"disabled\", 4, \"ngIf\"], [\"class\", \"cdk-visually-hidden\", 4, \"ngIf\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-option-ripple\", \"mat-mdc-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mat-mdc-option-pseudo-checkbox\", 3, \"state\", \"disabled\"], [\"state\", \"checked\", \"appearance\", \"minimal\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\"], [1, \"cdk-visually-hidden\"]],\n  template: function MatOption_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c3);\n      i0.ɵɵtemplate(0, MatOption_mat_pseudo_checkbox_0_Template, 1, 2, \"mat-pseudo-checkbox\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementStart(2, \"span\", 1, 2);\n      i0.ɵɵprojection(4, 1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(5, MatOption_mat_pseudo_checkbox_5_Template, 1, 1, \"mat-pseudo-checkbox\", 3);\n      i0.ɵɵtemplate(6, MatOption_span_6_Template, 2, 1, \"span\", 4);\n      i0.ɵɵelement(7, \"div\", 5);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.multiple);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", !ctx.multiple && ctx.selected && !ctx.hideSingleSelectionIndicator);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.group && ctx.group._inert);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"matRippleTrigger\", ctx._getHostElement())(\"matRippleDisabled\", ctx.disabled || ctx.disableRipple);\n    }\n  },\n  dependencies: [MatRipple, i3.NgIf, MatPseudoCheckbox],\n  styles: [\".mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option.mdc-list-item{align-items:center}.mat-mdc-option.mdc-list-item--disabled{opacity:.38;cursor:default}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOption, [{\n    type: Component,\n    args: [{\n      selector: 'mat-option',\n      exportAs: 'matOption',\n      host: {\n        'role': 'option',\n        '[class.mdc-list-item--selected]': 'selected',\n        '[class.mat-mdc-option-multiple]': 'multiple',\n        '[class.mat-mdc-option-active]': 'active',\n        '[class.mdc-list-item--disabled]': 'disabled',\n        '[id]': 'id',\n        // Set aria-selected to false for non-selected items and true for selected items. Conform to\n        // [WAI ARIA Listbox authoring practices guide](\n        //  https://www.w3.org/WAI/ARIA/apg/patterns/listbox/), \"If any options are selected, each\n        // selected option has either aria-selected or aria-checked  set to true. All options that are\n        // selectable but not selected have either aria-selected or aria-checked set to false.\" Align\n        // aria-selected implementation of Chips and List components.\n        //\n        // Set `aria-selected=\"false\"` on not-selected listbox options to fix VoiceOver announcing\n        // every option as \"selected\" (#21491).\n        '[attr.aria-selected]': 'selected',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '(click)': '_selectViaInteraction()',\n        '(keydown)': '_handleKeydown($event)',\n        'class': 'mat-mdc-option mdc-list-item'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<mat-pseudo-checkbox *ngIf=\\\"multiple\\\" class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n    [state]=\\\"selected ? 'checked' : 'unchecked'\\\" [disabled]=\\\"disabled\\\"></mat-pseudo-checkbox>\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n<mat-pseudo-checkbox *ngIf=\\\"!multiple && selected && !hideSingleSelectionIndicator\\\"\\n    class=\\\"mat-mdc-option-pseudo-checkbox\\\" state=\\\"checked\\\" [disabled]=\\\"disabled\\\"\\n    appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n<span class=\\\"cdk-visually-hidden\\\" *ngIf=\\\"group && group._inert\\\">({{ group.label }})</span>\\n\\n<div class=\\\"mat-mdc-option-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\"\\n     [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\",\n      styles: [\".mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option.mdc-list-item{align-items:center}.mat-mdc-option.mdc-list-item--disabled{opacity:.38;cursor:default}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_OPTION_PARENT_COMPONENT]\n      }]\n    }, {\n      type: MatOptgroup,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_OPTGROUP]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n  if (optionGroups.length) {\n    let optionsArray = options.toArray();\n    let groups = optionGroups.toArray();\n    let groupCounter = 0;\n    for (let i = 0; i < optionIndex + 1; i++) {\n      if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n        groupCounter++;\n      }\n    }\n    return groupCounter;\n  }\n  return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n  if (optionOffset < currentScrollPosition) {\n    return optionOffset;\n  }\n  if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n    return Math.max(0, optionOffset - panelHeight + optionHeight);\n  }\n  return currentScrollPosition;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatOptionModule {}\nMatOptionModule.ɵfac = function MatOptionModule_Factory(t) {\n  return new (t || MatOptionModule)();\n};\nMatOptionModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatOptionModule,\n  declarations: [MatOption, MatOptgroup],\n  imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule],\n  exports: [MatOption, MatOptgroup]\n});\nMatOptionModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule],\n      exports: [MatOption, MatOptgroup],\n      declarations: [MatOption, MatOptgroup]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AnimationCurves, AnimationDurations, DateAdapter, ErrorStateMatcher, MATERIAL_SANITY_CHECKS, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MAT_DATE_LOCALE_FACTORY, MAT_NATIVE_DATE_FORMATS, MAT_OPTGROUP, MAT_OPTION_PARENT_COMPONENT, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatLine, MatLineModule, MatNativeDateModule, MatOptgroup, MatOption, MatOptionModule, MatOptionSelectionChange, MatPseudoCheckbox, MatPseudoCheckboxModule, MatRipple, MatRippleModule, NativeDateAdapter, NativeDateModule, RippleRef, RippleRenderer, ShowOnDirtyErrorStateMatcher, VERSION, _MatOptgroupBase, _MatOptionBase, _countGroupLabelsBeforeOption, _getOptionScrollPosition, defaultRippleAnimationConfig, mixinColor, mixinDisableRipple, mixinDisabled, mixinErrorState, mixinInitialized, mixinTabIndex, setLines };", "map": {"version": 3, "names": ["i0", "Version", "InjectionToken", "inject", "NgModule", "Optional", "Inject", "LOCALE_ID", "Injectable", "Directive", "Input", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "EventEmitter", "Output", "ViewChild", "i1", "isFakeMousedownFromScreenReader", "isFakeTouchstartFromScreenReader", "BidiModule", "VERSION", "VERSION$1", "i3", "DOCUMENT", "CommonModule", "i1$1", "Platform", "_isTestEnvironment", "normalizePassiveListenerOptions", "_getEventTarget", "coerceBooleanProperty", "coerceNumberProperty", "coerceElement", "Observable", "Subject", "startWith", "ANIMATION_MODULE_TYPE", "ENTER", "SPACE", "hasModifierKey", "AnimationCurves", "STANDARD_CURVE", "DECELERATION_CURVE", "ACCELERATION_CURVE", "SHARP_CURVE", "AnimationDurations", "COMPLEX", "ENTERING", "EXITING", "MATERIAL_SANITY_CHECKS_FACTORY", "MATERIAL_SANITY_CHECKS", "providedIn", "factory", "MatCommonModule", "constructor", "highContrastModeDetector", "_<PERSON><PERSON><PERSON><PERSON>", "_document", "_hasDoneGlobalChecks", "_applyBodyHighContrastModeCssClasses", "ngDevMode", "platform", "optional", "_checkIsEnabled", "_checkDoctypeIsDefined", "_checkThemeIsPresent", "<PERSON><PERSON><PERSON><PERSON>", "_checkCdkVersionMatch", "name", "ɵfac", "HighContrastModeDetector", "ɵmod", "ɵinj", "type", "args", "imports", "exports", "undefined", "decorators", "Document", "doc", "doctype", "console", "warn", "body", "testElement", "createElement", "classList", "add", "append<PERSON><PERSON><PERSON>", "computedStyle", "getComputedStyle", "display", "remove", "full", "mixinDisabled", "base", "disabled", "_disabled", "value", "mixinColor", "defaultColor", "color", "_color", "colorPalette", "_elementRef", "nativeElement", "mixinDisableRipple", "disable<PERSON><PERSON><PERSON>", "_disableRipple", "mixinTabIndex", "defaultTabIndex", "tabIndex", "_tabIndex", "mixinErrorState", "updateErrorState", "oldState", "errorState", "parent", "_parentFormGroup", "_parentForm", "matcher", "errorStateMatcher", "_defaultErrorStateMatcher", "control", "ngControl", "newState", "isErrorState", "stateChanges", "next", "mixinInitialized", "_isInitialized", "_pendingSubscribers", "initialized", "subscriber", "_notifySubscriber", "push", "_markInitialized", "Error", "for<PERSON>ach", "complete", "MAT_DATE_LOCALE", "MAT_DATE_LOCALE_FACTORY", "DateAdapter", "_localeChanges", "localeChanges", "getValidDateOrNull", "obj", "isDateInstance", "<PERSON><PERSON><PERSON><PERSON>", "deserialize", "invalid", "setLocale", "locale", "compareDate", "first", "second", "getYear", "getMonth", "getDate", "sameDate", "firstValid", "second<PERSON><PERSON><PERSON>", "clampDate", "date", "min", "max", "MAT_DATE_FORMATS", "ISO_8601_REGEX", "range", "length", "valueFunction", "valuesArray", "Array", "i", "NativeDateAdapter", "matDateLocale", "_platform", "useUtcForDisplay", "getFullYear", "getDayOfWeek", "getDay", "getMonthNames", "style", "dtf", "Intl", "DateTimeFormat", "month", "timeZone", "_format", "Date", "getDateNames", "day", "getDayOfWeekNames", "weekday", "getYearName", "year", "getFirstDayOfWeek", "getNumDaysInMonth", "_createDateWithOverflow", "clone", "getTime", "createDate", "result", "today", "parse", "parseFormat", "format", "displayFormat", "addCalendarYears", "years", "addCalendarMonths", "months", "newDate", "addCalendarDays", "days", "toIso8601", "getUTCFullYear", "_2digit", "getUTCMonth", "getUTCDate", "join", "test", "isNaN", "NaN", "d", "setFullYear", "setHours", "n", "slice", "setUTCFullYear", "setUTCHours", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "ɵprov", "MAT_NATIVE_DATE_FORMATS", "dateInput", "month<PERSON><PERSON><PERSON><PERSON><PERSON>", "dateA11yLabel", "monthYearA11yLabel", "NativeDateModule", "provide", "useClass", "providers", "MatNativeDateModule", "useValue", "ShowOnDirtyErrorStateMatcher", "form", "dirty", "submitted", "ErrorStateMatcher", "touched", "MatLine", "ɵdir", "selector", "host", "setLines", "lines", "element", "prefix", "changes", "pipe", "subscribe", "setClass", "className", "isAdd", "toggle", "MatLineModule", "declarations", "RippleRef", "_renderer", "config", "_animationForciblyDisabledThroughCss", "state", "fadeOut", "fadeOutRipple", "passiveCapturingEventOptions$1", "passive", "capture", "RippleEventManager", "_events", "Map", "_delegate<PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "target", "get", "handlers", "contains", "handler", "handleEvent", "add<PERSON><PERSON><PERSON>", "ngZone", "handlersForEvent", "handlersForElement", "set", "Set", "runOutsideAngular", "document", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "delete", "size", "removeEventListener", "defaultRippleAnimationConfig", "enterDuration", "exitDuration", "ignoreMouseEventsTimeout", "passiveCapturingEventOptions", "pointerDownEvents", "pointerUpEvents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_target", "_ngZone", "elementOrElementRef", "_isPointerDown", "_activeRipples", "_pointerUpEventsRegistered", "_containerElement", "fadeInRipple", "x", "y", "containerRect", "_containerRect", "getBoundingClientRect", "animationConfig", "animation", "centered", "left", "width", "top", "height", "radius", "distanceToFurthestCorner", "offsetX", "offsetY", "ripple", "backgroundColor", "transitionDuration", "computedStyles", "window", "userTransitionProperty", "transitionProperty", "userTransitionDuration", "animationForciblyDisabledThroughCss", "rippleRef", "transform", "persistent", "_mostRecentTransientRipple", "eventListeners", "onTransitionEnd", "_finishRippleTransition", "onTransitionCancel", "_destroyRipple", "rippleEl", "opacity", "fadeOutAll", "_getActiveRipples", "fadeOutAllNonPersistent", "setupTriggerEvents", "_triggerElement", "_removeTriggerEvents", "_eventManager", "_onMousedown", "_onTouchStart", "_onPointerUp", "_startFadeOutTransition", "isMostRecentTransientRipple", "isFakeMousedown", "isSyntheticEvent", "_lastTouchStartEvent", "now", "rippleDisabled", "clientX", "clientY", "rippleConfig", "touches", "changedTouches", "isVisible", "terminateOnPointerUp", "from", "keys", "trigger", "rect", "distX", "Math", "abs", "right", "distY", "bottom", "sqrt", "MAT_RIPPLE_GLOBAL_OPTIONS", "<PERSON><PERSON><PERSON><PERSON>", "_setupTriggerEventsIfEnabled", "_trigger", "globalOptions", "_animationMode", "_globalOptions", "_ripple<PERSON><PERSON>er", "ngOnInit", "ngOnDestroy", "launch", "configOrX", "ElementRef", "NgZone", "exportAs", "unbounded", "MatRippleModule", "MatPseudoCheckbox", "appearance", "ɵcmp", "encapsulation", "None", "changeDetection", "OnPush", "template", "styles", "MatPseudoCheckboxModule", "MAT_OPTION_PARENT_COMPONENT", "_MatOptgroupMixinBase", "_uniqueOptgroupIdCounter", "_MatOptgroupBase", "_labelId", "_inert", "inertGroups", "label", "MAT_OPTGROUP", "MatOptgroup", "useExisting", "inputs", "_uniqueIdCounter", "MatOptionSelectionChange", "source", "isUserInput", "_MatOptionBase", "multiple", "_parent", "selected", "_selected", "group", "hideSingleSelectionIndicator", "_element", "_changeDetectorRef", "_active", "_mostRecentViewValue", "id", "onSelectionChange", "_stateChanges", "active", "viewValue", "_text", "textContent", "trim", "select", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_emitSelectionChangeEvent", "deselect", "focus", "_origin", "options", "_getHostElement", "setActiveStyles", "setInactiveStyles", "get<PERSON><PERSON><PERSON>", "_handleKeydown", "keyCode", "_selectViaInteraction", "preventDefault", "_getTabIndex", "ngAfterViewChecked", "emit", "ChangeDetectorRef", "static", "MatOption", "changeDetectorRef", "NgIf", "_countGroupLabelsBeforeOption", "optionIndex", "optionGroups", "optionsArray", "toArray", "groups", "groupCounter", "_getOptionScrollPosition", "optionOffset", "optionHeight", "currentScrollPosition", "panelHeight", "MatOptionModule"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/material/fesm2020/core.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Version, InjectionToken, inject, NgModule, Optional, Inject, LOCALE_ID, Injectable, Directive, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, EventEmitter, Output, ViewChild } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { VERSION as VERSION$1 } from '@angular/cdk';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { Platform, _isTestEnvironment, normalizePassiveListenerOptions, _getEventTarget } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceNumberProperty, coerceElement } from '@angular/cdk/coercion';\nimport { Observable, Subject } from 'rxjs';\nimport { startWith } from 'rxjs/operators';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { ENTER, SPACE, hasModifierKey } from '@angular/cdk/keycodes';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Current version of Angular Material. */\nconst VERSION = new Version('15.2.9');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** @docs-private */\nclass AnimationCurves {\n}\nAnimationCurves.STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)';\nAnimationCurves.DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)';\nAnimationCurves.ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)';\nAnimationCurves.SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)';\n/** @docs-private */\nclass AnimationDurations {\n}\nAnimationDurations.COMPLEX = '375ms';\nAnimationDurations.ENTERING = '225ms';\nAnimationDurations.EXITING = '195ms';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** @docs-private */\nfunction MATERIAL_SANITY_CHECKS_FACTORY() {\n    return true;\n}\n/** Injection token that configures whether the Material sanity checks are enabled. */\nconst MATERIAL_SANITY_CHECKS = new InjectionToken('mat-sanity-checks', {\n    providedIn: 'root',\n    factory: MATERIAL_SANITY_CHECKS_FACTORY,\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n */\nclass MatCommonModule {\n    constructor(highContrastModeDetector, _sanityChecks, _document) {\n        this._sanityChecks = _sanityChecks;\n        this._document = _document;\n        /** Whether we've done the global sanity checks (e.g. a theme is loaded, there is a doctype). */\n        this._hasDoneGlobalChecks = false;\n        // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n        // in MatCommonModule.\n        highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n        if (!this._hasDoneGlobalChecks) {\n            this._hasDoneGlobalChecks = true;\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                // Inject in here so the reference to `Platform` can be removed in production mode.\n                const platform = inject(Platform, { optional: true });\n                if (this._checkIsEnabled('doctype')) {\n                    _checkDoctypeIsDefined(this._document);\n                }\n                if (this._checkIsEnabled('theme')) {\n                    _checkThemeIsPresent(this._document, !!platform?.isBrowser);\n                }\n                if (this._checkIsEnabled('version')) {\n                    _checkCdkVersionMatch();\n                }\n            }\n        }\n    }\n    /** Gets whether a specific sanity check is enabled. */\n    _checkIsEnabled(name) {\n        if (_isTestEnvironment()) {\n            return false;\n        }\n        if (typeof this._sanityChecks === 'boolean') {\n            return this._sanityChecks;\n        }\n        return !!this._sanityChecks[name];\n    }\n}\nMatCommonModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCommonModule, deps: [{ token: i1.HighContrastModeDetector }, { token: MATERIAL_SANITY_CHECKS, optional: true }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.NgModule });\nMatCommonModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCommonModule, imports: [BidiModule], exports: [BidiModule] });\nMatCommonModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCommonModule, imports: [BidiModule, BidiModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCommonModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [BidiModule],\n                    exports: [BidiModule],\n                }]\n        }], ctorParameters: function () { return [{ type: i1.HighContrastModeDetector }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MATERIAL_SANITY_CHECKS]\n                }] }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/** Checks that the page has a doctype. */\nfunction _checkDoctypeIsDefined(doc) {\n    if (!doc.doctype) {\n        console.warn('Current document does not have a doctype. This may cause ' +\n            'some Angular Material components not to behave as expected.');\n    }\n}\n/** Checks that a theme has been included. */\nfunction _checkThemeIsPresent(doc, isBrowser) {\n    // We need to assert that the `body` is defined, because these checks run very early\n    // and the `body` won't be defined if the consumer put their scripts in the `head`.\n    if (!doc.body || !isBrowser) {\n        return;\n    }\n    const testElement = doc.createElement('div');\n    testElement.classList.add('mat-theme-loaded-marker');\n    doc.body.appendChild(testElement);\n    const computedStyle = getComputedStyle(testElement);\n    // In some situations the computed style of the test element can be null. For example in\n    // Firefox, the computed style is null if an application is running inside of a hidden iframe.\n    // See: https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n    if (computedStyle && computedStyle.display !== 'none') {\n        console.warn('Could not find Angular Material core theme. Most Material ' +\n            'components may not work as expected. For more info refer ' +\n            'to the theming guide: https://material.angular.io/guide/theming');\n    }\n    testElement.remove();\n}\n/** Checks whether the Material version matches the CDK version. */\nfunction _checkCdkVersionMatch() {\n    if (VERSION.full !== VERSION$1.full) {\n        console.warn('The Angular Material version (' +\n            VERSION.full +\n            ') does not match ' +\n            'the Angular CDK version (' +\n            VERSION$1.full +\n            ').\\n' +\n            'Please ensure the versions of these two packages exactly match.');\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction mixinDisabled(base) {\n    return class extends base {\n        get disabled() {\n            return this._disabled;\n        }\n        set disabled(value) {\n            this._disabled = coerceBooleanProperty(value);\n        }\n        constructor(...args) {\n            super(...args);\n            this._disabled = false;\n        }\n    };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction mixinColor(base, defaultColor) {\n    return class extends base {\n        get color() {\n            return this._color;\n        }\n        set color(value) {\n            const colorPalette = value || this.defaultColor;\n            if (colorPalette !== this._color) {\n                if (this._color) {\n                    this._elementRef.nativeElement.classList.remove(`mat-${this._color}`);\n                }\n                if (colorPalette) {\n                    this._elementRef.nativeElement.classList.add(`mat-${colorPalette}`);\n                }\n                this._color = colorPalette;\n            }\n        }\n        constructor(...args) {\n            super(...args);\n            this.defaultColor = defaultColor;\n            // Set the default color that can be specified from the mixin.\n            this.color = defaultColor;\n        }\n    };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction mixinDisableRipple(base) {\n    return class extends base {\n        /** Whether the ripple effect is disabled or not. */\n        get disableRipple() {\n            return this._disableRipple;\n        }\n        set disableRipple(value) {\n            this._disableRipple = coerceBooleanProperty(value);\n        }\n        constructor(...args) {\n            super(...args);\n            this._disableRipple = false;\n        }\n    };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction mixinTabIndex(base, defaultTabIndex = 0) {\n    return class extends base {\n        get tabIndex() {\n            return this.disabled ? -1 : this._tabIndex;\n        }\n        set tabIndex(value) {\n            // If the specified tabIndex value is null or undefined, fall back to the default value.\n            this._tabIndex = value != null ? coerceNumberProperty(value) : this.defaultTabIndex;\n        }\n        constructor(...args) {\n            super(...args);\n            this._tabIndex = defaultTabIndex;\n            this.defaultTabIndex = defaultTabIndex;\n        }\n    };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction mixinErrorState(base) {\n    return class extends base {\n        /** Updates the error state based on the provided error state matcher. */\n        updateErrorState() {\n            const oldState = this.errorState;\n            const parent = this._parentFormGroup || this._parentForm;\n            const matcher = this.errorStateMatcher || this._defaultErrorStateMatcher;\n            const control = this.ngControl ? this.ngControl.control : null;\n            const newState = matcher.isErrorState(control, parent);\n            if (newState !== oldState) {\n                this.errorState = newState;\n                this.stateChanges.next();\n            }\n        }\n        constructor(...args) {\n            super(...args);\n            /** Whether the component is in an error state. */\n            this.errorState = false;\n        }\n    };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Mixin to augment a directive with an initialized property that will emits when ngOnInit ends. */\nfunction mixinInitialized(base) {\n    return class extends base {\n        constructor(...args) {\n            super(...args);\n            /** Whether this directive has been marked as initialized. */\n            this._isInitialized = false;\n            /**\n             * List of subscribers that subscribed before the directive was initialized. Should be notified\n             * during _markInitialized. Set to null after pending subscribers are notified, and should\n             * not expect to be populated after.\n             */\n            this._pendingSubscribers = [];\n            /**\n             * Observable stream that emits when the directive initializes. If already initialized, the\n             * subscriber is stored to be notified once _markInitialized is called.\n             */\n            this.initialized = new Observable(subscriber => {\n                // If initialized, immediately notify the subscriber. Otherwise store the subscriber to notify\n                // when _markInitialized is called.\n                if (this._isInitialized) {\n                    this._notifySubscriber(subscriber);\n                }\n                else {\n                    this._pendingSubscribers.push(subscriber);\n                }\n            });\n        }\n        /**\n         * Marks the state as initialized and notifies pending subscribers. Should be called at the end\n         * of ngOnInit.\n         * @docs-private\n         */\n        _markInitialized() {\n            if (this._isInitialized && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('This directive has already been marked as initialized and ' +\n                    'should not be called twice.');\n            }\n            this._isInitialized = true;\n            this._pendingSubscribers.forEach(this._notifySubscriber);\n            this._pendingSubscribers = null;\n        }\n        /** Emits and completes the subscriber stream (should only emit once). */\n        _notifySubscriber(subscriber) {\n            subscriber.next();\n            subscriber.complete();\n        }\n    };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** InjectionToken for datepicker that can be used to override default locale code. */\nconst MAT_DATE_LOCALE = new InjectionToken('MAT_DATE_LOCALE', {\n    providedIn: 'root',\n    factory: MAT_DATE_LOCALE_FACTORY,\n});\n/** @docs-private */\nfunction MAT_DATE_LOCALE_FACTORY() {\n    return inject(LOCALE_ID);\n}\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\nclass DateAdapter {\n    constructor() {\n        this._localeChanges = new Subject();\n        /** A stream that emits when the locale changes. */\n        this.localeChanges = this._localeChanges;\n    }\n    /**\n     * Given a potential date object, returns that same date object if it is\n     * a valid date, or `null` if it's not a valid date.\n     * @param obj The object to check.\n     * @returns A date or `null`.\n     */\n    getValidDateOrNull(obj) {\n        return this.isDateInstance(obj) && this.isValid(obj) ? obj : null;\n    }\n    /**\n     * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n     * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n     * string). The default implementation does not allow any deserialization, it simply checks that\n     * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n     * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n     * support passing values from your backend directly to these properties by overriding this method\n     * to also deserialize the format used by your backend.\n     * @param value The value to be deserialized into a date object.\n     * @returns The deserialized date object, either a valid date, null if the value can be\n     *     deserialized into a null date (e.g. the empty string), or an invalid date.\n     */\n    deserialize(value) {\n        if (value == null || (this.isDateInstance(value) && this.isValid(value))) {\n            return value;\n        }\n        return this.invalid();\n    }\n    /**\n     * Sets the locale used for all dates.\n     * @param locale The new locale.\n     */\n    setLocale(locale) {\n        this.locale = locale;\n        this._localeChanges.next();\n    }\n    /**\n     * Compares two dates.\n     * @param first The first date to compare.\n     * @param second The second date to compare.\n     * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n     *     a number greater than 0 if the first date is later.\n     */\n    compareDate(first, second) {\n        return (this.getYear(first) - this.getYear(second) ||\n            this.getMonth(first) - this.getMonth(second) ||\n            this.getDate(first) - this.getDate(second));\n    }\n    /**\n     * Checks if two dates are equal.\n     * @param first The first date to check.\n     * @param second The second date to check.\n     * @returns Whether the two dates are equal.\n     *     Null dates are considered equal to other null dates.\n     */\n    sameDate(first, second) {\n        if (first && second) {\n            let firstValid = this.isValid(first);\n            let secondValid = this.isValid(second);\n            if (firstValid && secondValid) {\n                return !this.compareDate(first, second);\n            }\n            return firstValid == secondValid;\n        }\n        return first == second;\n    }\n    /**\n     * Clamp the given date between min and max dates.\n     * @param date The date to clamp.\n     * @param min The minimum value to allow. If null or omitted no min is enforced.\n     * @param max The maximum value to allow. If null or omitted no max is enforced.\n     * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n     *     otherwise `date`.\n     */\n    clampDate(date, min, max) {\n        if (min && this.compareDate(date, min) < 0) {\n            return min;\n        }\n        if (max && this.compareDate(date, max) > 0) {\n            return max;\n        }\n        return date;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst MAT_DATE_FORMATS = new InjectionToken('mat-date-formats');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Matches strings that have the form of a valid RFC 3339 string\n * (https://tools.ietf.org/html/rfc3339). Note that the string may not actually be a valid date\n * because the regex will match strings an with out of bounds month, date, etc.\n */\nconst ISO_8601_REGEX = /^\\d{4}-\\d{2}-\\d{2}(?:T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|(?:(?:\\+|-)\\d{2}:\\d{2}))?)?$/;\n/** Creates an array and fills it with values. */\nfunction range(length, valueFunction) {\n    const valuesArray = Array(length);\n    for (let i = 0; i < length; i++) {\n        valuesArray[i] = valueFunction(i);\n    }\n    return valuesArray;\n}\n/** Adapts the native JS Date for use with cdk-based components that work with dates. */\nclass NativeDateAdapter extends DateAdapter {\n    constructor(matDateLocale, \n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 14.0.0\n     */\n    _platform) {\n        super();\n        /**\n         * @deprecated No longer being used. To be removed.\n         * @breaking-change 14.0.0\n         */\n        this.useUtcForDisplay = false;\n        super.setLocale(matDateLocale);\n    }\n    getYear(date) {\n        return date.getFullYear();\n    }\n    getMonth(date) {\n        return date.getMonth();\n    }\n    getDate(date) {\n        return date.getDate();\n    }\n    getDayOfWeek(date) {\n        return date.getDay();\n    }\n    getMonthNames(style) {\n        const dtf = new Intl.DateTimeFormat(this.locale, { month: style, timeZone: 'utc' });\n        return range(12, i => this._format(dtf, new Date(2017, i, 1)));\n    }\n    getDateNames() {\n        const dtf = new Intl.DateTimeFormat(this.locale, { day: 'numeric', timeZone: 'utc' });\n        return range(31, i => this._format(dtf, new Date(2017, 0, i + 1)));\n    }\n    getDayOfWeekNames(style) {\n        const dtf = new Intl.DateTimeFormat(this.locale, { weekday: style, timeZone: 'utc' });\n        return range(7, i => this._format(dtf, new Date(2017, 0, i + 1)));\n    }\n    getYearName(date) {\n        const dtf = new Intl.DateTimeFormat(this.locale, { year: 'numeric', timeZone: 'utc' });\n        return this._format(dtf, date);\n    }\n    getFirstDayOfWeek() {\n        // We can't tell using native JS Date what the first day of the week is, we default to Sunday.\n        return 0;\n    }\n    getNumDaysInMonth(date) {\n        return this.getDate(this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + 1, 0));\n    }\n    clone(date) {\n        return new Date(date.getTime());\n    }\n    createDate(year, month, date) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            // Check for invalid month and date (except upper bound on date which we have to check after\n            // creating the Date).\n            if (month < 0 || month > 11) {\n                throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n            }\n            if (date < 1) {\n                throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n            }\n        }\n        let result = this._createDateWithOverflow(year, month, date);\n        // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n        if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n        }\n        return result;\n    }\n    today() {\n        return new Date();\n    }\n    parse(value, parseFormat) {\n        // We have no way using the native JS Date to set the parse format or locale, so we ignore these\n        // parameters.\n        if (typeof value == 'number') {\n            return new Date(value);\n        }\n        return value ? new Date(Date.parse(value)) : null;\n    }\n    format(date, displayFormat) {\n        if (!this.isValid(date)) {\n            throw Error('NativeDateAdapter: Cannot format invalid date.');\n        }\n        const dtf = new Intl.DateTimeFormat(this.locale, { ...displayFormat, timeZone: 'utc' });\n        return this._format(dtf, date);\n    }\n    addCalendarYears(date, years) {\n        return this.addCalendarMonths(date, years * 12);\n    }\n    addCalendarMonths(date, months) {\n        let newDate = this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + months, this.getDate(date));\n        // It's possible to wind up in the wrong month if the original month has more days than the new\n        // month. In this case we want to go to the last day of the desired month.\n        // Note: the additional + 12 % 12 ensures we end up with a positive number, since JS % doesn't\n        // guarantee this.\n        if (this.getMonth(newDate) != (((this.getMonth(date) + months) % 12) + 12) % 12) {\n            newDate = this._createDateWithOverflow(this.getYear(newDate), this.getMonth(newDate), 0);\n        }\n        return newDate;\n    }\n    addCalendarDays(date, days) {\n        return this._createDateWithOverflow(this.getYear(date), this.getMonth(date), this.getDate(date) + days);\n    }\n    toIso8601(date) {\n        return [\n            date.getUTCFullYear(),\n            this._2digit(date.getUTCMonth() + 1),\n            this._2digit(date.getUTCDate()),\n        ].join('-');\n    }\n    /**\n     * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n     * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n     * invalid date for all other values.\n     */\n    deserialize(value) {\n        if (typeof value === 'string') {\n            if (!value) {\n                return null;\n            }\n            // The `Date` constructor accepts formats other than ISO 8601, so we need to make sure the\n            // string is the right format first.\n            if (ISO_8601_REGEX.test(value)) {\n                let date = new Date(value);\n                if (this.isValid(date)) {\n                    return date;\n                }\n            }\n        }\n        return super.deserialize(value);\n    }\n    isDateInstance(obj) {\n        return obj instanceof Date;\n    }\n    isValid(date) {\n        return !isNaN(date.getTime());\n    }\n    invalid() {\n        return new Date(NaN);\n    }\n    /** Creates a date but allows the month and date to overflow. */\n    _createDateWithOverflow(year, month, date) {\n        // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n        // To work around this we use `setFullYear` and `setHours` instead.\n        const d = new Date();\n        d.setFullYear(year, month, date);\n        d.setHours(0, 0, 0, 0);\n        return d;\n    }\n    /**\n     * Pads a number to make it two digits.\n     * @param n The number to pad.\n     * @returns The padded number.\n     */\n    _2digit(n) {\n        return ('00' + n).slice(-2);\n    }\n    /**\n     * When converting Date object to string, javascript built-in functions may return wrong\n     * results because it applies its internal DST rules. The DST rules around the world change\n     * very frequently, and the current valid rule is not always valid in previous years though.\n     * We work around this problem building a new Date object which has its internal UTC\n     * representation with the local date and time.\n     * @param dtf Intl.DateTimeFormat object, containing the desired string format. It must have\n     *    timeZone set to 'utc' to work fine.\n     * @param date Date from which we want to get the string representation according to dtf\n     * @returns A Date object with its UTC representation based on the passed in date info\n     */\n    _format(dtf, date) {\n        // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n        // To work around this we use `setUTCFullYear` and `setUTCHours` instead.\n        const d = new Date();\n        d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n        d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n        return dtf.format(d);\n    }\n}\nNativeDateAdapter.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: NativeDateAdapter, deps: [{ token: MAT_DATE_LOCALE, optional: true }, { token: i1$1.Platform }], target: i0.ɵɵFactoryTarget.Injectable });\nNativeDateAdapter.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: NativeDateAdapter });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: NativeDateAdapter, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_DATE_LOCALE]\n                }] }, { type: i1$1.Platform }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst MAT_NATIVE_DATE_FORMATS = {\n    parse: {\n        dateInput: null,\n    },\n    display: {\n        dateInput: { year: 'numeric', month: 'numeric', day: 'numeric' },\n        monthYearLabel: { year: 'numeric', month: 'short' },\n        dateA11yLabel: { year: 'numeric', month: 'long', day: 'numeric' },\n        monthYearA11yLabel: { year: 'numeric', month: 'long' },\n    },\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass NativeDateModule {\n}\nNativeDateModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: NativeDateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nNativeDateModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: NativeDateModule });\nNativeDateModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: NativeDateModule, providers: [{ provide: DateAdapter, useClass: NativeDateAdapter }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: NativeDateModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [{ provide: DateAdapter, useClass: NativeDateAdapter }],\n                }]\n        }] });\nclass MatNativeDateModule {\n}\nMatNativeDateModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatNativeDateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatNativeDateModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatNativeDateModule, imports: [NativeDateModule] });\nMatNativeDateModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatNativeDateModule, providers: [{ provide: MAT_DATE_FORMATS, useValue: MAT_NATIVE_DATE_FORMATS }], imports: [NativeDateModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatNativeDateModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [NativeDateModule],\n                    providers: [{ provide: MAT_DATE_FORMATS, useValue: MAT_NATIVE_DATE_FORMATS }],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Error state matcher that matches when a control is invalid and dirty. */\nclass ShowOnDirtyErrorStateMatcher {\n    isErrorState(control, form) {\n        return !!(control && control.invalid && (control.dirty || (form && form.submitted)));\n    }\n}\nShowOnDirtyErrorStateMatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nShowOnDirtyErrorStateMatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher, decorators: [{\n            type: Injectable\n        }] });\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nclass ErrorStateMatcher {\n    isErrorState(control, form) {\n        return !!(control && control.invalid && (control.touched || (form && form.submitted)));\n    }\n}\nErrorStateMatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ErrorStateMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nErrorStateMatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ErrorStateMatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ErrorStateMatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\nclass MatLine {\n}\nMatLine.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatLine, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatLine.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatLine, selector: \"[mat-line], [matLine]\", host: { classAttribute: \"mat-line\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatLine, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-line], [matLine]',\n                    host: { 'class': 'mat-line' },\n                }]\n        }] });\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\nfunction setLines(lines, element, prefix = 'mat') {\n    // Note: doesn't need to unsubscribe, because `changes`\n    // gets completed by Angular when the view is destroyed.\n    lines.changes.pipe(startWith(lines)).subscribe(({ length }) => {\n        setClass(element, `${prefix}-2-line`, false);\n        setClass(element, `${prefix}-3-line`, false);\n        setClass(element, `${prefix}-multi-line`, false);\n        if (length === 2 || length === 3) {\n            setClass(element, `${prefix}-${length}-line`, true);\n        }\n        else if (length > 3) {\n            setClass(element, `${prefix}-multi-line`, true);\n        }\n    });\n}\n/** Adds or removes a class from an element. */\nfunction setClass(element, className, isAdd) {\n    element.nativeElement.classList.toggle(className, isAdd);\n}\nclass MatLineModule {\n}\nMatLineModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatLineModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatLineModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatLineModule, declarations: [MatLine], imports: [MatCommonModule], exports: [MatLine, MatCommonModule] });\nMatLineModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatLineModule, imports: [MatCommonModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatLineModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: [MatLine, MatCommonModule],\n                    declarations: [MatLine],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Reference to a previously launched ripple element.\n */\nclass RippleRef {\n    constructor(_renderer, \n    /** Reference to the ripple HTML element. */\n    element, \n    /** Ripple configuration used for the ripple. */\n    config, \n    /* Whether animations are forcibly disabled for ripples through CSS. */\n    _animationForciblyDisabledThroughCss = false) {\n        this._renderer = _renderer;\n        this.element = element;\n        this.config = config;\n        this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;\n        /** Current state of the ripple. */\n        this.state = 3 /* RippleState.HIDDEN */;\n    }\n    /** Fades out the ripple element. */\n    fadeOut() {\n        this._renderer.fadeOutRipple(this);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions$1 = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nclass RippleEventManager {\n    constructor() {\n        this._events = new Map();\n        /** Event handler that is bound and which dispatches the events to the different targets. */\n        this._delegateEventHandler = (event) => {\n            const target = _getEventTarget(event);\n            if (target) {\n                this._events.get(event.type)?.forEach((handlers, element) => {\n                    if (element === target || element.contains(target)) {\n                        handlers.forEach(handler => handler.handleEvent(event));\n                    }\n                });\n            }\n        };\n    }\n    /** Adds an event handler. */\n    addHandler(ngZone, name, element, handler) {\n        const handlersForEvent = this._events.get(name);\n        if (handlersForEvent) {\n            const handlersForElement = handlersForEvent.get(element);\n            if (handlersForElement) {\n                handlersForElement.add(handler);\n            }\n            else {\n                handlersForEvent.set(element, new Set([handler]));\n            }\n        }\n        else {\n            this._events.set(name, new Map([[element, new Set([handler])]]));\n            ngZone.runOutsideAngular(() => {\n                document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n            });\n        }\n    }\n    /** Removes an event handler. */\n    removeHandler(name, element, handler) {\n        const handlersForEvent = this._events.get(name);\n        if (!handlersForEvent) {\n            return;\n        }\n        const handlersForElement = handlersForEvent.get(element);\n        if (!handlersForElement) {\n            return;\n        }\n        handlersForElement.delete(handler);\n        if (handlersForElement.size === 0) {\n            handlersForEvent.delete(element);\n        }\n        if (handlersForEvent.size === 0) {\n            this._events.delete(name);\n            document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n        }\n    }\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nconst defaultRippleAnimationConfig = {\n    enterDuration: 225,\n    exitDuration: 150,\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nclass RippleRenderer {\n    constructor(_target, _ngZone, elementOrElementRef, _platform) {\n        this._target = _target;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        /** Whether the pointer is currently down or not. */\n        this._isPointerDown = false;\n        /**\n         * Map of currently active ripple references.\n         * The ripple reference is mapped to its element event listeners.\n         * The reason why `| null` is used is that event listeners are added only\n         * when the condition is truthy (see the `_startFadeOutTransition` method).\n         */\n        this._activeRipples = new Map();\n        /** Whether pointer-up event listeners have been registered. */\n        this._pointerUpEventsRegistered = false;\n        // Only do anything if we're on the browser.\n        if (_platform.isBrowser) {\n            this._containerElement = coerceElement(elementOrElementRef);\n        }\n    }\n    /**\n     * Fades in a ripple at the given coordinates.\n     * @param x Coordinate within the element, along the X axis at which to start the ripple.\n     * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n     * @param config Extra ripple options.\n     */\n    fadeInRipple(x, y, config = {}) {\n        const containerRect = (this._containerRect =\n            this._containerRect || this._containerElement.getBoundingClientRect());\n        const animationConfig = { ...defaultRippleAnimationConfig, ...config.animation };\n        if (config.centered) {\n            x = containerRect.left + containerRect.width / 2;\n            y = containerRect.top + containerRect.height / 2;\n        }\n        const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n        const offsetX = x - containerRect.left;\n        const offsetY = y - containerRect.top;\n        const enterDuration = animationConfig.enterDuration;\n        const ripple = document.createElement('div');\n        ripple.classList.add('mat-ripple-element');\n        ripple.style.left = `${offsetX - radius}px`;\n        ripple.style.top = `${offsetY - radius}px`;\n        ripple.style.height = `${radius * 2}px`;\n        ripple.style.width = `${radius * 2}px`;\n        // If a custom color has been specified, set it as inline style. If no color is\n        // set, the default color will be applied through the ripple theme styles.\n        if (config.color != null) {\n            ripple.style.backgroundColor = config.color;\n        }\n        ripple.style.transitionDuration = `${enterDuration}ms`;\n        this._containerElement.appendChild(ripple);\n        // By default the browser does not recalculate the styles of dynamically created\n        // ripple elements. This is critical to ensure that the `scale` animates properly.\n        // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n        // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n        const computedStyles = window.getComputedStyle(ripple);\n        const userTransitionProperty = computedStyles.transitionProperty;\n        const userTransitionDuration = computedStyles.transitionDuration;\n        // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n        // `transition: none` or `display: none`). This is technically unexpected since animations are\n        // controlled through the animation config, but this exists for backwards compatibility. This\n        // logic does not need to be super accurate since it covers some edge cases which can be easily\n        // avoided by users.\n        const animationForciblyDisabledThroughCss = userTransitionProperty === 'none' ||\n            // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n            // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n            userTransitionDuration === '0s' ||\n            userTransitionDuration === '0s, 0s' ||\n            // If the container is 0x0, it's likely `display: none`.\n            (containerRect.width === 0 && containerRect.height === 0);\n        // Exposed reference to the ripple that will be returned.\n        const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n        // Start the enter animation by setting the transform/scale to 100%. The animation will\n        // execute as part of this statement because we forced a style recalculation before.\n        // Note: We use a 3d transform here in order to avoid an issue in Safari where\n        // the ripples aren't clipped when inside the shadow DOM (see #24028).\n        ripple.style.transform = 'scale3d(1, 1, 1)';\n        rippleRef.state = 0 /* RippleState.FADING_IN */;\n        if (!config.persistent) {\n            this._mostRecentTransientRipple = rippleRef;\n        }\n        let eventListeners = null;\n        // Do not register the `transition` event listener if fade-in and fade-out duration\n        // are set to zero. The events won't fire anyway and we can save resources here.\n        if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n            this._ngZone.runOutsideAngular(() => {\n                const onTransitionEnd = () => this._finishRippleTransition(rippleRef);\n                const onTransitionCancel = () => this._destroyRipple(rippleRef);\n                ripple.addEventListener('transitionend', onTransitionEnd);\n                // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n                // directly as otherwise we would keep it part of the ripple container forever.\n                // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n                ripple.addEventListener('transitioncancel', onTransitionCancel);\n                eventListeners = { onTransitionEnd, onTransitionCancel };\n            });\n        }\n        // Add the ripple reference to the list of all active ripples.\n        this._activeRipples.set(rippleRef, eventListeners);\n        // In case there is no fade-in transition duration, we need to manually call the transition\n        // end listener because `transitionend` doesn't fire if there is no transition.\n        if (animationForciblyDisabledThroughCss || !enterDuration) {\n            this._finishRippleTransition(rippleRef);\n        }\n        return rippleRef;\n    }\n    /** Fades out a ripple reference. */\n    fadeOutRipple(rippleRef) {\n        // For ripples already fading out or hidden, this should be a noop.\n        if (rippleRef.state === 2 /* RippleState.FADING_OUT */ || rippleRef.state === 3 /* RippleState.HIDDEN */) {\n            return;\n        }\n        const rippleEl = rippleRef.element;\n        const animationConfig = { ...defaultRippleAnimationConfig, ...rippleRef.config.animation };\n        // This starts the fade-out transition and will fire the transition end listener that\n        // removes the ripple element from the DOM.\n        rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n        rippleEl.style.opacity = '0';\n        rippleRef.state = 2 /* RippleState.FADING_OUT */;\n        // In case there is no fade-out transition duration, we need to manually call the\n        // transition end listener because `transitionend` doesn't fire if there is no transition.\n        if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n            this._finishRippleTransition(rippleRef);\n        }\n    }\n    /** Fades out all currently active ripples. */\n    fadeOutAll() {\n        this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n    }\n    /** Fades out all currently active non-persistent ripples. */\n    fadeOutAllNonPersistent() {\n        this._getActiveRipples().forEach(ripple => {\n            if (!ripple.config.persistent) {\n                ripple.fadeOut();\n            }\n        });\n    }\n    /** Sets up the trigger event listeners */\n    setupTriggerEvents(elementOrElementRef) {\n        const element = coerceElement(elementOrElementRef);\n        if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n            return;\n        }\n        // Remove all previously registered event listeners from the trigger element.\n        this._removeTriggerEvents();\n        this._triggerElement = element;\n        // Use event delegation for the trigger events since they're\n        // set up during creation and are performance-sensitive.\n        pointerDownEvents.forEach(type => {\n            RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n        });\n    }\n    /**\n     * Handles all registered events.\n     * @docs-private\n     */\n    handleEvent(event) {\n        if (event.type === 'mousedown') {\n            this._onMousedown(event);\n        }\n        else if (event.type === 'touchstart') {\n            this._onTouchStart(event);\n        }\n        else {\n            this._onPointerUp();\n        }\n        // If pointer-up events haven't been registered yet, do so now.\n        // We do this on-demand in order to reduce the total number of event listeners\n        // registered by the ripples, which speeds up the rendering time for large UIs.\n        if (!this._pointerUpEventsRegistered) {\n            // The events for hiding the ripple are bound directly on the trigger, because:\n            // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n            // delegation will be diminished by having to look through all the data structures often.\n            // 2. They aren't as performance-sensitive, because they're bound only after the user\n            // has interacted with an element.\n            this._ngZone.runOutsideAngular(() => {\n                pointerUpEvents.forEach(type => {\n                    this._triggerElement.addEventListener(type, this, passiveCapturingEventOptions);\n                });\n            });\n            this._pointerUpEventsRegistered = true;\n        }\n    }\n    /** Method that will be called if the fade-in or fade-in transition completed. */\n    _finishRippleTransition(rippleRef) {\n        if (rippleRef.state === 0 /* RippleState.FADING_IN */) {\n            this._startFadeOutTransition(rippleRef);\n        }\n        else if (rippleRef.state === 2 /* RippleState.FADING_OUT */) {\n            this._destroyRipple(rippleRef);\n        }\n    }\n    /**\n     * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n     * is not held down anymore.\n     */\n    _startFadeOutTransition(rippleRef) {\n        const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n        const { persistent } = rippleRef.config;\n        rippleRef.state = 1 /* RippleState.VISIBLE */;\n        // When the timer runs out while the user has kept their pointer down, we want to\n        // keep only the persistent ripples and the latest transient ripple. We do this,\n        // because we don't want stacked transient ripples to appear after their enter\n        // animation has finished.\n        if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n            rippleRef.fadeOut();\n        }\n    }\n    /** Destroys the given ripple by removing it from the DOM and updating its state. */\n    _destroyRipple(rippleRef) {\n        const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n        this._activeRipples.delete(rippleRef);\n        // Clear out the cached bounding rect if we have no more ripples.\n        if (!this._activeRipples.size) {\n            this._containerRect = null;\n        }\n        // If the current ref is the most recent transient ripple, unset it\n        // avoid memory leaks.\n        if (rippleRef === this._mostRecentTransientRipple) {\n            this._mostRecentTransientRipple = null;\n        }\n        rippleRef.state = 3 /* RippleState.HIDDEN */;\n        if (eventListeners !== null) {\n            rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n            rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n        }\n        rippleRef.element.remove();\n    }\n    /** Function being called whenever the trigger is being pressed using mouse. */\n    _onMousedown(event) {\n        // Screen readers will fire fake mouse events for space/enter. Skip launching a\n        // ripple in this case for consistency with the non-screen-reader experience.\n        const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n        const isSyntheticEvent = this._lastTouchStartEvent &&\n            Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n        if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n            this._isPointerDown = true;\n            this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n        }\n    }\n    /** Function being called whenever the trigger is being pressed using touch. */\n    _onTouchStart(event) {\n        if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n            // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n            // events will launch a second ripple if we don't ignore mouse events for a specific\n            // time after a touchstart event.\n            this._lastTouchStartEvent = Date.now();\n            this._isPointerDown = true;\n            // Use `changedTouches` so we skip any touches where the user put\n            // their finger down, but used another finger to tap the element again.\n            const touches = event.changedTouches;\n            for (let i = 0; i < touches.length; i++) {\n                this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n            }\n        }\n    }\n    /** Function being called whenever the trigger is being released. */\n    _onPointerUp() {\n        if (!this._isPointerDown) {\n            return;\n        }\n        this._isPointerDown = false;\n        // Fade-out all ripples that are visible and not persistent.\n        this._getActiveRipples().forEach(ripple => {\n            // By default, only ripples that are completely visible will fade out on pointer release.\n            // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n            const isVisible = ripple.state === 1 /* RippleState.VISIBLE */ ||\n                (ripple.config.terminateOnPointerUp && ripple.state === 0 /* RippleState.FADING_IN */);\n            if (!ripple.config.persistent && isVisible) {\n                ripple.fadeOut();\n            }\n        });\n    }\n    _getActiveRipples() {\n        return Array.from(this._activeRipples.keys());\n    }\n    /** Removes previously registered event listeners from the trigger element. */\n    _removeTriggerEvents() {\n        const trigger = this._triggerElement;\n        if (trigger) {\n            pointerDownEvents.forEach(type => RippleRenderer._eventManager.removeHandler(type, trigger, this));\n            if (this._pointerUpEventsRegistered) {\n                pointerUpEvents.forEach(type => trigger.removeEventListener(type, this, passiveCapturingEventOptions));\n            }\n        }\n    }\n}\nRippleRenderer._eventManager = new RippleEventManager();\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x, y, rect) {\n    const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n    const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n    return Math.sqrt(distX * distX + distY * distY);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that can be used to specify the global ripple options. */\nconst MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken('mat-ripple-global-options');\nclass MatRipple {\n    /**\n     * Whether click events will not trigger the ripple. Ripples can be still launched manually\n     * by using the `launch()` method.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        if (value) {\n            this.fadeOutAllNonPersistent();\n        }\n        this._disabled = value;\n        this._setupTriggerEventsIfEnabled();\n    }\n    /**\n     * The element that triggers the ripple when click events are received.\n     * Defaults to the directive's host element.\n     */\n    get trigger() {\n        return this._trigger || this._elementRef.nativeElement;\n    }\n    set trigger(trigger) {\n        this._trigger = trigger;\n        this._setupTriggerEventsIfEnabled();\n    }\n    constructor(_elementRef, ngZone, platform, globalOptions, _animationMode) {\n        this._elementRef = _elementRef;\n        this._animationMode = _animationMode;\n        /**\n         * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n         * will be the distance from the center of the ripple to the furthest corner of the host element's\n         * bounding rectangle.\n         */\n        this.radius = 0;\n        this._disabled = false;\n        /** Whether ripple directive is initialized and the input bindings are set. */\n        this._isInitialized = false;\n        this._globalOptions = globalOptions || {};\n        this._rippleRenderer = new RippleRenderer(this, ngZone, _elementRef, platform);\n    }\n    ngOnInit() {\n        this._isInitialized = true;\n        this._setupTriggerEventsIfEnabled();\n    }\n    ngOnDestroy() {\n        this._rippleRenderer._removeTriggerEvents();\n    }\n    /** Fades out all currently showing ripple elements. */\n    fadeOutAll() {\n        this._rippleRenderer.fadeOutAll();\n    }\n    /** Fades out all currently showing non-persistent ripple elements. */\n    fadeOutAllNonPersistent() {\n        this._rippleRenderer.fadeOutAllNonPersistent();\n    }\n    /**\n     * Ripple configuration from the directive's input values.\n     * @docs-private Implemented as part of RippleTarget\n     */\n    get rippleConfig() {\n        return {\n            centered: this.centered,\n            radius: this.radius,\n            color: this.color,\n            animation: {\n                ...this._globalOptions.animation,\n                ...(this._animationMode === 'NoopAnimations' ? { enterDuration: 0, exitDuration: 0 } : {}),\n                ...this.animation,\n            },\n            terminateOnPointerUp: this._globalOptions.terminateOnPointerUp,\n        };\n    }\n    /**\n     * Whether ripples on pointer-down are disabled or not.\n     * @docs-private Implemented as part of RippleTarget\n     */\n    get rippleDisabled() {\n        return this.disabled || !!this._globalOptions.disabled;\n    }\n    /** Sets up the trigger event listeners if ripples are enabled. */\n    _setupTriggerEventsIfEnabled() {\n        if (!this.disabled && this._isInitialized) {\n            this._rippleRenderer.setupTriggerEvents(this.trigger);\n        }\n    }\n    /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n    launch(configOrX, y = 0, config) {\n        if (typeof configOrX === 'number') {\n            return this._rippleRenderer.fadeInRipple(configOrX, y, { ...this.rippleConfig, ...config });\n        }\n        else {\n            return this._rippleRenderer.fadeInRipple(0, 0, { ...this.rippleConfig, ...configOrX });\n        }\n    }\n}\nMatRipple.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatRipple, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: i1$1.Platform }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nMatRipple.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: { color: [\"matRippleColor\", \"color\"], unbounded: [\"matRippleUnbounded\", \"unbounded\"], centered: [\"matRippleCentered\", \"centered\"], radius: [\"matRippleRadius\", \"radius\"], animation: [\"matRippleAnimation\", \"animation\"], disabled: [\"matRippleDisabled\", \"disabled\"], trigger: [\"matRippleTrigger\", \"trigger\"] }, host: { properties: { \"class.mat-ripple-unbounded\": \"unbounded\" }, classAttribute: \"mat-ripple\" }, exportAs: [\"matRipple\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatRipple, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-ripple], [matRipple]',\n                    exportAs: 'matRipple',\n                    host: {\n                        'class': 'mat-ripple',\n                        '[class.mat-ripple-unbounded]': 'unbounded',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: i1$1.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { color: [{\n                type: Input,\n                args: ['matRippleColor']\n            }], unbounded: [{\n                type: Input,\n                args: ['matRippleUnbounded']\n            }], centered: [{\n                type: Input,\n                args: ['matRippleCentered']\n            }], radius: [{\n                type: Input,\n                args: ['matRippleRadius']\n            }], animation: [{\n                type: Input,\n                args: ['matRippleAnimation']\n            }], disabled: [{\n                type: Input,\n                args: ['matRippleDisabled']\n            }], trigger: [{\n                type: Input,\n                args: ['matRippleTrigger']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatRippleModule {\n}\nMatRippleModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatRippleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatRippleModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatRippleModule, declarations: [MatRipple], imports: [MatCommonModule], exports: [MatRipple, MatCommonModule] });\nMatRippleModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatRippleModule, imports: [MatCommonModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatRippleModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: [MatRipple, MatCommonModule],\n                    declarations: [MatRipple],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\nclass MatPseudoCheckbox {\n    constructor(_animationMode) {\n        this._animationMode = _animationMode;\n        /** Display state of the checkbox. */\n        this.state = 'unchecked';\n        /** Whether the checkbox is disabled. */\n        this.disabled = false;\n        /**\n         * Appearance of the pseudo checkbox. Default appearance of 'full' renders a checkmark/mixedmark\n         * indicator inside a square box. 'minimal' appearance only renders the checkmark/mixedmark.\n         */\n        this.appearance = 'full';\n    }\n}\nMatPseudoCheckbox.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPseudoCheckbox, deps: [{ token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatPseudoCheckbox.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatPseudoCheckbox, selector: \"mat-pseudo-checkbox\", inputs: { state: \"state\", disabled: \"disabled\", appearance: \"appearance\" }, host: { properties: { \"class.mat-pseudo-checkbox-indeterminate\": \"state === \\\"indeterminate\\\"\", \"class.mat-pseudo-checkbox-checked\": \"state === \\\"checked\\\"\", \"class.mat-pseudo-checkbox-disabled\": \"disabled\", \"class.mat-pseudo-checkbox-minimal\": \"appearance === \\\"minimal\\\"\", \"class.mat-pseudo-checkbox-full\": \"appearance === \\\"full\\\"\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\" }, classAttribute: \"mat-pseudo-checkbox\" }, ngImport: i0, template: '', isInline: true, styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-full{border:2px solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPseudoCheckbox, decorators: [{\n            type: Component,\n            args: [{ encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, selector: 'mat-pseudo-checkbox', template: '', host: {\n                        'class': 'mat-pseudo-checkbox',\n                        '[class.mat-pseudo-checkbox-indeterminate]': 'state === \"indeterminate\"',\n                        '[class.mat-pseudo-checkbox-checked]': 'state === \"checked\"',\n                        '[class.mat-pseudo-checkbox-disabled]': 'disabled',\n                        '[class.mat-pseudo-checkbox-minimal]': 'appearance === \"minimal\"',\n                        '[class.mat-pseudo-checkbox-full]': 'appearance === \"full\"',\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                    }, styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-full{border:2px solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { state: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], appearance: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatPseudoCheckboxModule {\n}\nMatPseudoCheckboxModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPseudoCheckboxModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatPseudoCheckboxModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPseudoCheckboxModule, declarations: [MatPseudoCheckbox], imports: [MatCommonModule], exports: [MatPseudoCheckbox] });\nMatPseudoCheckboxModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPseudoCheckboxModule, imports: [MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPseudoCheckboxModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: [MatPseudoCheckbox],\n                    declarations: [MatPseudoCheckbox],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token used to provide the parent component to options.\n */\nconst MAT_OPTION_PARENT_COMPONENT = new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n// Boilerplate for applying mixins to MatOptgroup.\n/** @docs-private */\nconst _MatOptgroupMixinBase = mixinDisabled(class {\n});\n// Counter for unique group ids.\nlet _uniqueOptgroupIdCounter = 0;\nclass _MatOptgroupBase extends _MatOptgroupMixinBase {\n    constructor(parent) {\n        super();\n        /** Unique id for the underlying label. */\n        this._labelId = `mat-optgroup-label-${_uniqueOptgroupIdCounter++}`;\n        this._inert = parent?.inertGroups ?? false;\n    }\n}\n_MatOptgroupBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatOptgroupBase, deps: [{ token: MAT_OPTION_PARENT_COMPONENT, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_MatOptgroupBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatOptgroupBase, inputs: { label: \"label\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatOptgroupBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_OPTION_PARENT_COMPONENT]\n                }, {\n                    type: Optional\n                }] }]; }, propDecorators: { label: [{\n                type: Input\n            }] } });\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_OPTGROUP = new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\nclass MatOptgroup extends _MatOptgroupBase {\n}\nMatOptgroup.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatOptgroup, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatOptgroup.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatOptgroup, selector: \"mat-optgroup\", inputs: { disabled: \"disabled\" }, host: { properties: { \"attr.role\": \"_inert ? null : \\\"group\\\"\", \"attr.aria-disabled\": \"_inert ? null : disabled.toString()\", \"attr.aria-labelledby\": \"_inert ? null : _labelId\" }, classAttribute: \"mat-mdc-optgroup\" }, providers: [{ provide: MAT_OPTGROUP, useExisting: MatOptgroup }], exportAs: [\"matOptgroup\"], usesInheritance: true, ngImport: i0, template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  aria-hidden=\\\"true\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\", styles: [\".mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;min-height:48px}.mat-mdc-optgroup-label:focus{outline:none}[dir=rtl] .mat-mdc-optgroup-label,.mat-mdc-optgroup-label[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{white-space:normal}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatOptgroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-optgroup', exportAs: 'matOptgroup', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, inputs: ['disabled'], host: {\n                        'class': 'mat-mdc-optgroup',\n                        '[attr.role]': '_inert ? null : \"group\"',\n                        '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n                        '[attr.aria-labelledby]': '_inert ? null : _labelId',\n                    }, providers: [{ provide: MAT_OPTGROUP, useExisting: MatOptgroup }], template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  aria-hidden=\\\"true\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\", styles: [\".mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;min-height:48px}.mat-mdc-optgroup-label:focus{outline:none}[dir=rtl] .mat-mdc-optgroup-label,.mat-mdc-optgroup-label[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{white-space:normal}\"] }]\n        }] });\n\n/**\n * Option IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\nlet _uniqueIdCounter = 0;\n/** Event object emitted by MatOption when selected or deselected. */\nclass MatOptionSelectionChange {\n    constructor(\n    /** Reference to the option that emitted the event. */\n    source, \n    /** Whether the change in the option's value was a result of a user action. */\n    isUserInput = false) {\n        this.source = source;\n        this.isUserInput = isUserInput;\n    }\n}\nclass _MatOptionBase {\n    /** Whether the wrapping component is in multiple selection mode. */\n    get multiple() {\n        return this._parent && this._parent.multiple;\n    }\n    /** Whether or not the option is currently selected. */\n    get selected() {\n        return this._selected;\n    }\n    /** Whether the option is disabled. */\n    get disabled() {\n        return (this.group && this.group.disabled) || this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n    }\n    /** Whether ripples for the option are disabled. */\n    get disableRipple() {\n        return !!(this._parent && this._parent.disableRipple);\n    }\n    /** Whether to display checkmark for single-selection. */\n    get hideSingleSelectionIndicator() {\n        return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n    }\n    constructor(_element, _changeDetectorRef, _parent, group) {\n        this._element = _element;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._parent = _parent;\n        this.group = group;\n        this._selected = false;\n        this._active = false;\n        this._disabled = false;\n        this._mostRecentViewValue = '';\n        /** The unique ID of the option. */\n        this.id = `mat-option-${_uniqueIdCounter++}`;\n        /** Event emitted when the option is selected or deselected. */\n        // tslint:disable-next-line:no-output-on-prefix\n        this.onSelectionChange = new EventEmitter();\n        /** Emits when the state of the option changes and any parents have to be notified. */\n        this._stateChanges = new Subject();\n    }\n    /**\n     * Whether or not the option is currently active and ready to be selected.\n     * An active option displays styles as if it is focused, but the\n     * focus is actually retained somewhere else. This comes in handy\n     * for components like autocomplete where focus must remain on the input.\n     */\n    get active() {\n        return this._active;\n    }\n    /**\n     * The displayed value of the option. It is necessary to show the selected option in the\n     * select's trigger.\n     */\n    get viewValue() {\n        // TODO(kara): Add input property alternative for node envs.\n        return (this._text?.nativeElement.textContent || '').trim();\n    }\n    /** Selects the option. */\n    select() {\n        if (!this._selected) {\n            this._selected = true;\n            this._changeDetectorRef.markForCheck();\n            this._emitSelectionChangeEvent();\n        }\n    }\n    /** Deselects the option. */\n    deselect() {\n        if (this._selected) {\n            this._selected = false;\n            this._changeDetectorRef.markForCheck();\n            this._emitSelectionChangeEvent();\n        }\n    }\n    /** Sets focus onto this option. */\n    focus(_origin, options) {\n        // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n        // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n        const element = this._getHostElement();\n        if (typeof element.focus === 'function') {\n            element.focus(options);\n        }\n    }\n    /**\n     * This method sets display styles on the option to make it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n    setActiveStyles() {\n        if (!this._active) {\n            this._active = true;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * This method removes display styles on the option that made it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n    setInactiveStyles() {\n        if (this._active) {\n            this._active = false;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n        return this.viewValue;\n    }\n    /** Ensures the option is selected when activated from the keyboard. */\n    _handleKeydown(event) {\n        if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n            this._selectViaInteraction();\n            // Prevent the page from scrolling down and form submits.\n            event.preventDefault();\n        }\n    }\n    /**\n     * `Selects the option while indicating the selection came from the user. Used to\n     * determine if the select's view -> model callback should be invoked.`\n     */\n    _selectViaInteraction() {\n        if (!this.disabled) {\n            this._selected = this.multiple ? !this._selected : true;\n            this._changeDetectorRef.markForCheck();\n            this._emitSelectionChangeEvent(true);\n        }\n    }\n    /** Returns the correct tabindex for the option depending on disabled state. */\n    // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n    // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n    // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n    _getTabIndex() {\n        return this.disabled ? '-1' : '0';\n    }\n    /** Gets the host DOM element. */\n    _getHostElement() {\n        return this._element.nativeElement;\n    }\n    ngAfterViewChecked() {\n        // Since parent components could be using the option's label to display the selected values\n        // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n        // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n        // relatively cheap, however we still limit them only to selected options in order to avoid\n        // hitting the DOM too often.\n        if (this._selected) {\n            const viewValue = this.viewValue;\n            if (viewValue !== this._mostRecentViewValue) {\n                if (this._mostRecentViewValue) {\n                    this._stateChanges.next();\n                }\n                this._mostRecentViewValue = viewValue;\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n    /** Emits the selection change event. */\n    _emitSelectionChangeEvent(isUserInput = false) {\n        this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n    }\n}\n_MatOptionBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatOptionBase, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Directive });\n_MatOptionBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatOptionBase, inputs: { value: \"value\", id: \"id\", disabled: \"disabled\" }, outputs: { onSelectionChange: \"onSelectionChange\" }, viewQueries: [{ propertyName: \"_text\", first: true, predicate: [\"text\"], descendants: true, static: true }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatOptionBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined }, { type: _MatOptgroupBase }]; }, propDecorators: { value: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], onSelectionChange: [{\n                type: Output\n            }], _text: [{\n                type: ViewChild,\n                args: ['text', { static: true }]\n            }] } });\n/**\n * Single option inside of a `<mat-select>` element.\n */\nclass MatOption extends _MatOptionBase {\n    constructor(element, changeDetectorRef, parent, group) {\n        super(element, changeDetectorRef, parent, group);\n    }\n}\nMatOption.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatOption, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: MAT_OPTION_PARENT_COMPONENT, optional: true }, { token: MAT_OPTGROUP, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatOption.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatOption, selector: \"mat-option\", host: { attributes: { \"role\": \"option\" }, listeners: { \"click\": \"_selectViaInteraction()\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"class.mdc-list-item--selected\": \"selected\", \"class.mat-mdc-option-multiple\": \"multiple\", \"class.mat-mdc-option-active\": \"active\", \"class.mdc-list-item--disabled\": \"disabled\", \"id\": \"id\", \"attr.aria-selected\": \"selected\", \"attr.aria-disabled\": \"disabled.toString()\" }, classAttribute: \"mat-mdc-option mdc-list-item\" }, exportAs: [\"matOption\"], usesInheritance: true, ngImport: i0, template: \"<mat-pseudo-checkbox *ngIf=\\\"multiple\\\" class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n    [state]=\\\"selected ? 'checked' : 'unchecked'\\\" [disabled]=\\\"disabled\\\"></mat-pseudo-checkbox>\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n<mat-pseudo-checkbox *ngIf=\\\"!multiple && selected && !hideSingleSelectionIndicator\\\"\\n    class=\\\"mat-mdc-option-pseudo-checkbox\\\" state=\\\"checked\\\" [disabled]=\\\"disabled\\\"\\n    appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n<span class=\\\"cdk-visually-hidden\\\" *ngIf=\\\"group && group._inert\\\">({{ group.label }})</span>\\n\\n<div class=\\\"mat-mdc-option-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\"\\n     [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\", styles: [\".mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option.mdc-list-item{align-items:center}.mat-mdc-option.mdc-list-item--disabled{opacity:.38;cursor:default}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: MatPseudoCheckbox, selector: \"mat-pseudo-checkbox\", inputs: [\"state\", \"disabled\", \"appearance\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatOption, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-option', exportAs: 'matOption', host: {\n                        'role': 'option',\n                        '[class.mdc-list-item--selected]': 'selected',\n                        '[class.mat-mdc-option-multiple]': 'multiple',\n                        '[class.mat-mdc-option-active]': 'active',\n                        '[class.mdc-list-item--disabled]': 'disabled',\n                        '[id]': 'id',\n                        // Set aria-selected to false for non-selected items and true for selected items. Conform to\n                        // [WAI ARIA Listbox authoring practices guide](\n                        //  https://www.w3.org/WAI/ARIA/apg/patterns/listbox/), \"If any options are selected, each\n                        // selected option has either aria-selected or aria-checked  set to true. All options that are\n                        // selectable but not selected have either aria-selected or aria-checked set to false.\" Align\n                        // aria-selected implementation of Chips and List components.\n                        //\n                        // Set `aria-selected=\"false\"` on not-selected listbox options to fix VoiceOver announcing\n                        // every option as \"selected\" (#21491).\n                        '[attr.aria-selected]': 'selected',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '(click)': '_selectViaInteraction()',\n                        '(keydown)': '_handleKeydown($event)',\n                        'class': 'mat-mdc-option mdc-list-item',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<mat-pseudo-checkbox *ngIf=\\\"multiple\\\" class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n    [state]=\\\"selected ? 'checked' : 'unchecked'\\\" [disabled]=\\\"disabled\\\"></mat-pseudo-checkbox>\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n<mat-pseudo-checkbox *ngIf=\\\"!multiple && selected && !hideSingleSelectionIndicator\\\"\\n    class=\\\"mat-mdc-option-pseudo-checkbox\\\" state=\\\"checked\\\" [disabled]=\\\"disabled\\\"\\n    appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n<span class=\\\"cdk-visually-hidden\\\" *ngIf=\\\"group && group._inert\\\">({{ group.label }})</span>\\n\\n<div class=\\\"mat-mdc-option-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\"\\n     [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\", styles: [\".mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option.mdc-list-item{align-items:center}.mat-mdc-option.mdc-list-item--disabled{opacity:.38;cursor:default}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_OPTION_PARENT_COMPONENT]\n                }] }, { type: MatOptgroup, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_OPTGROUP]\n                }] }]; } });\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n    if (optionGroups.length) {\n        let optionsArray = options.toArray();\n        let groups = optionGroups.toArray();\n        let groupCounter = 0;\n        for (let i = 0; i < optionIndex + 1; i++) {\n            if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n                groupCounter++;\n            }\n        }\n        return groupCounter;\n    }\n    return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n    if (optionOffset < currentScrollPosition) {\n        return optionOffset;\n    }\n    if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n        return Math.max(0, optionOffset - panelHeight + optionHeight);\n    }\n    return currentScrollPosition;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatOptionModule {\n}\nMatOptionModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatOptionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatOptionModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatOptionModule, declarations: [MatOption, MatOptgroup], imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule], exports: [MatOption, MatOptgroup] });\nMatOptionModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatOptionModule, imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatOptionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule],\n                    exports: [MatOption, MatOptgroup],\n                    declarations: [MatOption, MatOptgroup],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AnimationCurves, AnimationDurations, DateAdapter, ErrorStateMatcher, MATERIAL_SANITY_CHECKS, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MAT_DATE_LOCALE_FACTORY, MAT_NATIVE_DATE_FORMATS, MAT_OPTGROUP, MAT_OPTION_PARENT_COMPONENT, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatLine, MatLineModule, MatNativeDateModule, MatOptgroup, MatOption, MatOptionModule, MatOptionSelectionChange, MatPseudoCheckbox, MatPseudoCheckboxModule, MatRipple, MatRippleModule, NativeDateAdapter, NativeDateModule, RippleRef, RippleRenderer, ShowOnDirtyErrorStateMatcher, VERSION, _MatOptgroupBase, _MatOptionBase, _countGroupLabelsBeforeOption, _getOptionScrollPosition, defaultRippleAnimationConfig, mixinColor, mixinDisableRipple, mixinDisabled, mixinErrorState, mixinInitialized, mixinTabIndex, setLines };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,OAAO,EAAEC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,YAAY,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AAC5N,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,+BAA+B,EAAEC,gCAAgC,QAAQ,mBAAmB;AACrG,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,OAAO,IAAIC,SAAS,QAAQ,cAAc;AACnD,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,IAAI,MAAM,uBAAuB;AAC7C,SAASC,QAAQ,EAAEC,kBAAkB,EAAEC,+BAA+B,EAAEC,eAAe,QAAQ,uBAAuB;AACtH,SAASC,qBAAqB,EAAEC,oBAAoB,EAAEC,aAAa,QAAQ,uBAAuB;AAClG,SAASC,UAAU,EAAEC,OAAO,QAAQ,MAAM;AAC1C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,KAAK,EAAEC,KAAK,EAAEC,cAAc,QAAQ,uBAAuB;;AAEpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;EAAA;IAmFuGxC,EAAE,uCAqqDouB;EAAA;EAAA;IAAA,eArqDtuBA,EAAE;IAAFA,EAAE,+DAqqDqrB;EAAA;AAAA;AAAA;EAAA;IArqDvrBA,EAAE,uCAqqD2oC;EAAA;EAAA;IAAA,eArqD7oCA,EAAE;IAAFA,EAAE,wCAqqDwlC;EAAA;AAAA;AAAA;EAAA;IArqD1lCA,EAAE,6BAqqDgyC;IArqDlyCA,EAAE,UAqqDmzC;IArqDrzCA,EAAE,eAqqD0zC;EAAA;EAAA;IAAA,eArqD5zCA,EAAE;IAAFA,EAAE,aAqqDmzC;IArqDrzCA,EAAE,iDAqqDmzC;EAAA;AAAA;AAAA;AAAA;AAvvD55C,MAAMqB,OAAO,GAAG,IAAIpB,OAAO,CAAC,QAAQ,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwC,eAAe,CAAC;AAEtBA,eAAe,CAACC,cAAc,GAAG,6BAA6B;AAC9DD,eAAe,CAACE,kBAAkB,GAAG,6BAA6B;AAClEF,eAAe,CAACG,kBAAkB,GAAG,2BAA2B;AAChEH,eAAe,CAACI,WAAW,GAAG,6BAA6B;AAC3D;AACA,MAAMC,kBAAkB,CAAC;AAEzBA,kBAAkB,CAACC,OAAO,GAAG,OAAO;AACpCD,kBAAkB,CAACE,QAAQ,GAAG,OAAO;AACrCF,kBAAkB,CAACG,OAAO,GAAG,OAAO;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,8BAA8B,GAAG;EACtC,OAAO,IAAI;AACf;AACA;AACA,MAAMC,sBAAsB,GAAG,IAAIjD,cAAc,CAAC,mBAAmB,EAAE;EACnEkD,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEH;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,eAAe,CAAC;EAClBC,WAAW,CAACC,wBAAwB,EAAEC,aAAa,EAAEC,SAAS,EAAE;IAC5D,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC;IACA;IACAH,wBAAwB,CAACI,oCAAoC,EAAE;IAC/D,IAAI,CAAC,IAAI,CAACD,oBAAoB,EAAE;MAC5B,IAAI,CAACA,oBAAoB,GAAG,IAAI;MAChC,IAAI,OAAOE,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C;QACA,MAAMC,QAAQ,GAAG3D,MAAM,CAACwB,QAAQ,EAAE;UAAEoC,QAAQ,EAAE;QAAK,CAAC,CAAC;QACrD,IAAI,IAAI,CAACC,eAAe,CAAC,SAAS,CAAC,EAAE;UACjCC,sBAAsB,CAAC,IAAI,CAACP,SAAS,CAAC;QAC1C;QACA,IAAI,IAAI,CAACM,eAAe,CAAC,OAAO,CAAC,EAAE;UAC/BE,oBAAoB,CAAC,IAAI,CAACR,SAAS,EAAE,CAAC,CAACI,QAAQ,EAAEK,SAAS,CAAC;QAC/D;QACA,IAAI,IAAI,CAACH,eAAe,CAAC,SAAS,CAAC,EAAE;UACjCI,qBAAqB,EAAE;QAC3B;MACJ;IACJ;EACJ;EACA;EACAJ,eAAe,CAACK,IAAI,EAAE;IAClB,IAAIzC,kBAAkB,EAAE,EAAE;MACtB,OAAO,KAAK;IAChB;IACA,IAAI,OAAO,IAAI,CAAC6B,aAAa,KAAK,SAAS,EAAE;MACzC,OAAO,IAAI,CAACA,aAAa;IAC7B;IACA,OAAO,CAAC,CAAC,IAAI,CAACA,aAAa,CAACY,IAAI,CAAC;EACrC;AACJ;AACAf,eAAe,CAACgB,IAAI;EAAA,iBAA6FhB,eAAe,EAAzBtD,EAAE,UAAyCiB,EAAE,CAACsD,wBAAwB,GAAtEvE,EAAE,UAAiFmD,sBAAsB,MAAzGnD,EAAE,UAAoIwB,QAAQ;AAAA,CAA2C;AAChS8B,eAAe,CAACkB,IAAI,kBADmFxE,EAAE;EAAA,MACSsD,eAAe;EAAA,UAAYlC,UAAU;EAAA,UAAaA,UAAU;AAAA,EAAI;AAClLkC,eAAe,CAACmB,IAAI,kBAFmFzE,EAAE;EAAA,UAEoCoB,UAAU,EAAEA,UAAU;AAAA,EAAI;AACvK;EAAA,mDAHuGpB,EAAE,mBAGTsD,eAAe,EAAc,CAAC;IAClHoB,IAAI,EAAEtE,QAAQ;IACduE,IAAI,EAAE,CAAC;MACCC,OAAO,EAAE,CAACxD,UAAU,CAAC;MACrByD,OAAO,EAAE,CAACzD,UAAU;IACxB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEsD,IAAI,EAAEzD,EAAE,CAACsD;IAAyB,CAAC,EAAE;MAAEG,IAAI,EAAEI,SAAS;MAAEC,UAAU,EAAE,CAAC;QACrGL,IAAI,EAAErE;MACV,CAAC,EAAE;QACCqE,IAAI,EAAEpE,MAAM;QACZqE,IAAI,EAAE,CAACxB,sBAAsB;MACjC,CAAC;IAAE,CAAC,EAAE;MAAEuB,IAAI,EAAEM,QAAQ;MAAED,UAAU,EAAE,CAAC;QACjCL,IAAI,EAAEpE,MAAM;QACZqE,IAAI,EAAE,CAACnD,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA,SAASyC,sBAAsB,CAACgB,GAAG,EAAE;EACjC,IAAI,CAACA,GAAG,CAACC,OAAO,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,2DAA2D,GACpE,6DAA6D,CAAC;EACtE;AACJ;AACA;AACA,SAASlB,oBAAoB,CAACe,GAAG,EAAEd,SAAS,EAAE;EAC1C;EACA;EACA,IAAI,CAACc,GAAG,CAACI,IAAI,IAAI,CAAClB,SAAS,EAAE;IACzB;EACJ;EACA,MAAMmB,WAAW,GAAGL,GAAG,CAACM,aAAa,CAAC,KAAK,CAAC;EAC5CD,WAAW,CAACE,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;EACpDR,GAAG,CAACI,IAAI,CAACK,WAAW,CAACJ,WAAW,CAAC;EACjC,MAAMK,aAAa,GAAGC,gBAAgB,CAACN,WAAW,CAAC;EACnD;EACA;EACA;EACA,IAAIK,aAAa,IAAIA,aAAa,CAACE,OAAO,KAAK,MAAM,EAAE;IACnDV,OAAO,CAACC,IAAI,CAAC,4DAA4D,GACrE,2DAA2D,GAC3D,iEAAiE,CAAC;EAC1E;EACAE,WAAW,CAACQ,MAAM,EAAE;AACxB;AACA;AACA,SAAS1B,qBAAqB,GAAG;EAC7B,IAAI/C,OAAO,CAAC0E,IAAI,KAAKzE,SAAS,CAACyE,IAAI,EAAE;IACjCZ,OAAO,CAACC,IAAI,CAAC,gCAAgC,GACzC/D,OAAO,CAAC0E,IAAI,GACZ,mBAAmB,GACnB,2BAA2B,GAC3BzE,SAAS,CAACyE,IAAI,GACd,MAAM,GACN,iEAAiE,CAAC;EAC1E;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAa,CAACC,IAAI,EAAE;EACzB,OAAO,cAAcA,IAAI,CAAC;IACtB,IAAIC,QAAQ,GAAG;MACX,OAAO,IAAI,CAACC,SAAS;IACzB;IACA,IAAID,QAAQ,CAACE,KAAK,EAAE;MAChB,IAAI,CAACD,SAAS,GAAGpE,qBAAqB,CAACqE,KAAK,CAAC;IACjD;IACA7C,WAAW,CAAC,GAAGoB,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAACwB,SAAS,GAAG,KAAK;IAC1B;EACJ,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,UAAU,CAACJ,IAAI,EAAEK,YAAY,EAAE;EACpC,OAAO,cAAcL,IAAI,CAAC;IACtB,IAAIM,KAAK,GAAG;MACR,OAAO,IAAI,CAACC,MAAM;IACtB;IACA,IAAID,KAAK,CAACH,KAAK,EAAE;MACb,MAAMK,YAAY,GAAGL,KAAK,IAAI,IAAI,CAACE,YAAY;MAC/C,IAAIG,YAAY,KAAK,IAAI,CAACD,MAAM,EAAE;QAC9B,IAAI,IAAI,CAACA,MAAM,EAAE;UACb,IAAI,CAACE,WAAW,CAACC,aAAa,CAACnB,SAAS,CAACM,MAAM,CAAE,OAAM,IAAI,CAACU,MAAO,EAAC,CAAC;QACzE;QACA,IAAIC,YAAY,EAAE;UACd,IAAI,CAACC,WAAW,CAACC,aAAa,CAACnB,SAAS,CAACC,GAAG,CAAE,OAAMgB,YAAa,EAAC,CAAC;QACvE;QACA,IAAI,CAACD,MAAM,GAAGC,YAAY;MAC9B;IACJ;IACAlD,WAAW,CAAC,GAAGoB,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAAC2B,YAAY,GAAGA,YAAY;MAChC;MACA,IAAI,CAACC,KAAK,GAAGD,YAAY;IAC7B;EACJ,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,kBAAkB,CAACX,IAAI,EAAE;EAC9B,OAAO,cAAcA,IAAI,CAAC;IACtB;IACA,IAAIY,aAAa,GAAG;MAChB,OAAO,IAAI,CAACC,cAAc;IAC9B;IACA,IAAID,aAAa,CAACT,KAAK,EAAE;MACrB,IAAI,CAACU,cAAc,GAAG/E,qBAAqB,CAACqE,KAAK,CAAC;IACtD;IACA7C,WAAW,CAAC,GAAGoB,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAACmC,cAAc,GAAG,KAAK;IAC/B;EACJ,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAa,CAACd,IAAI,EAAEe,eAAe,GAAG,CAAC,EAAE;EAC9C,OAAO,cAAcf,IAAI,CAAC;IACtB,IAAIgB,QAAQ,GAAG;MACX,OAAO,IAAI,CAACf,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAACgB,SAAS;IAC9C;IACA,IAAID,QAAQ,CAACb,KAAK,EAAE;MAChB;MACA,IAAI,CAACc,SAAS,GAAGd,KAAK,IAAI,IAAI,GAAGpE,oBAAoB,CAACoE,KAAK,CAAC,GAAG,IAAI,CAACY,eAAe;IACvF;IACAzD,WAAW,CAAC,GAAGoB,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAACuC,SAAS,GAAGF,eAAe;MAChC,IAAI,CAACA,eAAe,GAAGA,eAAe;IAC1C;EACJ,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,eAAe,CAAClB,IAAI,EAAE;EAC3B,OAAO,cAAcA,IAAI,CAAC;IACtB;IACAmB,gBAAgB,GAAG;MACf,MAAMC,QAAQ,GAAG,IAAI,CAACC,UAAU;MAChC,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAACC,WAAW;MACxD,MAAMC,OAAO,GAAG,IAAI,CAACC,iBAAiB,IAAI,IAAI,CAACC,yBAAyB;MACxE,MAAMC,OAAO,GAAG,IAAI,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS,CAACD,OAAO,GAAG,IAAI;MAC9D,MAAME,QAAQ,GAAGL,OAAO,CAACM,YAAY,CAACH,OAAO,EAAEN,MAAM,CAAC;MACtD,IAAIQ,QAAQ,KAAKV,QAAQ,EAAE;QACvB,IAAI,CAACC,UAAU,GAAGS,QAAQ;QAC1B,IAAI,CAACE,YAAY,CAACC,IAAI,EAAE;MAC5B;IACJ;IACA3E,WAAW,CAAC,GAAGoB,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd;MACA,IAAI,CAAC2C,UAAU,GAAG,KAAK;IAC3B;EACJ,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,gBAAgB,CAAClC,IAAI,EAAE;EAC5B,OAAO,cAAcA,IAAI,CAAC;IACtB1C,WAAW,CAAC,GAAGoB,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd;MACA,IAAI,CAACyD,cAAc,GAAG,KAAK;MAC3B;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,mBAAmB,GAAG,EAAE;MAC7B;AACZ;AACA;AACA;MACY,IAAI,CAACC,WAAW,GAAG,IAAIpG,UAAU,CAACqG,UAAU,IAAI;QAC5C;QACA;QACA,IAAI,IAAI,CAACH,cAAc,EAAE;UACrB,IAAI,CAACI,iBAAiB,CAACD,UAAU,CAAC;QACtC,CAAC,MACI;UACD,IAAI,CAACF,mBAAmB,CAACI,IAAI,CAACF,UAAU,CAAC;QAC7C;MACJ,CAAC,CAAC;IACN;IACA;AACR;AACA;AACA;AACA;IACQG,gBAAgB,GAAG;MACf,IAAI,IAAI,CAACN,cAAc,KAAK,OAAOvE,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACxE,MAAM8E,KAAK,CAAC,4DAA4D,GACpE,6BAA6B,CAAC;MACtC;MACA,IAAI,CAACP,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACC,mBAAmB,CAACO,OAAO,CAAC,IAAI,CAACJ,iBAAiB,CAAC;MACxD,IAAI,CAACH,mBAAmB,GAAG,IAAI;IACnC;IACA;IACAG,iBAAiB,CAACD,UAAU,EAAE;MAC1BA,UAAU,CAACL,IAAI,EAAE;MACjBK,UAAU,CAACM,QAAQ,EAAE;IACzB;EACJ,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,IAAI5I,cAAc,CAAC,iBAAiB,EAAE;EAC1DkD,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAE0F;AACb,CAAC,CAAC;AACF;AACA,SAASA,uBAAuB,GAAG;EAC/B,OAAO5I,MAAM,CAACI,SAAS,CAAC;AAC5B;AACA;AACA,MAAMyI,WAAW,CAAC;EACdzF,WAAW,GAAG;IACV,IAAI,CAAC0F,cAAc,GAAG,IAAI9G,OAAO,EAAE;IACnC;IACA,IAAI,CAAC+G,aAAa,GAAG,IAAI,CAACD,cAAc;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,kBAAkB,CAACC,GAAG,EAAE;IACpB,OAAO,IAAI,CAACC,cAAc,CAACD,GAAG,CAAC,IAAI,IAAI,CAACE,OAAO,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAG,IAAI;EACrE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,WAAW,CAACnD,KAAK,EAAE;IACf,IAAIA,KAAK,IAAI,IAAI,IAAK,IAAI,CAACiD,cAAc,CAACjD,KAAK,CAAC,IAAI,IAAI,CAACkD,OAAO,CAAClD,KAAK,CAAE,EAAE;MACtE,OAAOA,KAAK;IAChB;IACA,OAAO,IAAI,CAACoD,OAAO,EAAE;EACzB;EACA;AACJ;AACA;AACA;EACIC,SAAS,CAACC,MAAM,EAAE;IACd,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACT,cAAc,CAACf,IAAI,EAAE;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIyB,WAAW,CAACC,KAAK,EAAEC,MAAM,EAAE;IACvB,OAAQ,IAAI,CAACC,OAAO,CAACF,KAAK,CAAC,GAAG,IAAI,CAACE,OAAO,CAACD,MAAM,CAAC,IAC9C,IAAI,CAACE,QAAQ,CAACH,KAAK,CAAC,GAAG,IAAI,CAACG,QAAQ,CAACF,MAAM,CAAC,IAC5C,IAAI,CAACG,OAAO,CAACJ,KAAK,CAAC,GAAG,IAAI,CAACI,OAAO,CAACH,MAAM,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,QAAQ,CAACL,KAAK,EAAEC,MAAM,EAAE;IACpB,IAAID,KAAK,IAAIC,MAAM,EAAE;MACjB,IAAIK,UAAU,GAAG,IAAI,CAACZ,OAAO,CAACM,KAAK,CAAC;MACpC,IAAIO,WAAW,GAAG,IAAI,CAACb,OAAO,CAACO,MAAM,CAAC;MACtC,IAAIK,UAAU,IAAIC,WAAW,EAAE;QAC3B,OAAO,CAAC,IAAI,CAACR,WAAW,CAACC,KAAK,EAAEC,MAAM,CAAC;MAC3C;MACA,OAAOK,UAAU,IAAIC,WAAW;IACpC;IACA,OAAOP,KAAK,IAAIC,MAAM;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIO,SAAS,CAACC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAE;IACtB,IAAID,GAAG,IAAI,IAAI,CAACX,WAAW,CAACU,IAAI,EAAEC,GAAG,CAAC,GAAG,CAAC,EAAE;MACxC,OAAOA,GAAG;IACd;IACA,IAAIC,GAAG,IAAI,IAAI,CAACZ,WAAW,CAACU,IAAI,EAAEE,GAAG,CAAC,GAAG,CAAC,EAAE;MACxC,OAAOA,GAAG;IACd;IACA,OAAOF,IAAI;EACf;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,GAAG,IAAItK,cAAc,CAAC,kBAAkB,CAAC;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuK,cAAc,GAAG,oFAAoF;AAC3G;AACA,SAASC,KAAK,CAACC,MAAM,EAAEC,aAAa,EAAE;EAClC,MAAMC,WAAW,GAAGC,KAAK,CAACH,MAAM,CAAC;EACjC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,EAAEI,CAAC,EAAE,EAAE;IAC7BF,WAAW,CAACE,CAAC,CAAC,GAAGH,aAAa,CAACG,CAAC,CAAC;EACrC;EACA,OAAOF,WAAW;AACtB;AACA;AACA,MAAMG,iBAAiB,SAAShC,WAAW,CAAC;EACxCzF,WAAW,CAAC0H,aAAa;EACzB;AACJ;AACA;AACA;EACIC,SAAS,EAAE;IACP,KAAK,EAAE;IACP;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,KAAK,CAAC1B,SAAS,CAACwB,aAAa,CAAC;EAClC;EACAnB,OAAO,CAACO,IAAI,EAAE;IACV,OAAOA,IAAI,CAACe,WAAW,EAAE;EAC7B;EACArB,QAAQ,CAACM,IAAI,EAAE;IACX,OAAOA,IAAI,CAACN,QAAQ,EAAE;EAC1B;EACAC,OAAO,CAACK,IAAI,EAAE;IACV,OAAOA,IAAI,CAACL,OAAO,EAAE;EACzB;EACAqB,YAAY,CAAChB,IAAI,EAAE;IACf,OAAOA,IAAI,CAACiB,MAAM,EAAE;EACxB;EACAC,aAAa,CAACC,KAAK,EAAE;IACjB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;MAAEkC,KAAK,EAAEJ,KAAK;MAAEK,QAAQ,EAAE;IAAM,CAAC,CAAC;IACnF,OAAOnB,KAAK,CAAC,EAAE,EAAEK,CAAC,IAAI,IAAI,CAACe,OAAO,CAACL,GAAG,EAAE,IAAIM,IAAI,CAAC,IAAI,EAAEhB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClE;EACAiB,YAAY,GAAG;IACX,MAAMP,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;MAAEuC,GAAG,EAAE,SAAS;MAAEJ,QAAQ,EAAE;IAAM,CAAC,CAAC;IACrF,OAAOnB,KAAK,CAAC,EAAE,EAAEK,CAAC,IAAI,IAAI,CAACe,OAAO,CAACL,GAAG,EAAE,IAAIM,IAAI,CAAC,IAAI,EAAE,CAAC,EAAEhB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACtE;EACAmB,iBAAiB,CAACV,KAAK,EAAE;IACrB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;MAAEyC,OAAO,EAAEX,KAAK;MAAEK,QAAQ,EAAE;IAAM,CAAC,CAAC;IACrF,OAAOnB,KAAK,CAAC,CAAC,EAAEK,CAAC,IAAI,IAAI,CAACe,OAAO,CAACL,GAAG,EAAE,IAAIM,IAAI,CAAC,IAAI,EAAE,CAAC,EAAEhB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACrE;EACAqB,WAAW,CAAC/B,IAAI,EAAE;IACd,MAAMoB,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;MAAE2C,IAAI,EAAE,SAAS;MAAER,QAAQ,EAAE;IAAM,CAAC,CAAC;IACtF,OAAO,IAAI,CAACC,OAAO,CAACL,GAAG,EAAEpB,IAAI,CAAC;EAClC;EACAiC,iBAAiB,GAAG;IAChB;IACA,OAAO,CAAC;EACZ;EACAC,iBAAiB,CAAClC,IAAI,EAAE;IACpB,OAAO,IAAI,CAACL,OAAO,CAAC,IAAI,CAACwC,uBAAuB,CAAC,IAAI,CAAC1C,OAAO,CAACO,IAAI,CAAC,EAAE,IAAI,CAACN,QAAQ,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EACrG;EACAoC,KAAK,CAACpC,IAAI,EAAE;IACR,OAAO,IAAI0B,IAAI,CAAC1B,IAAI,CAACqC,OAAO,EAAE,CAAC;EACnC;EACAC,UAAU,CAACN,IAAI,EAAET,KAAK,EAAEvB,IAAI,EAAE;IAC1B,IAAI,OAAOxG,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C;MACA;MACA,IAAI+H,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,EAAE,EAAE;QACzB,MAAMjD,KAAK,CAAE,wBAAuBiD,KAAM,4CAA2C,CAAC;MAC1F;MACA,IAAIvB,IAAI,GAAG,CAAC,EAAE;QACV,MAAM1B,KAAK,CAAE,iBAAgB0B,IAAK,mCAAkC,CAAC;MACzE;IACJ;IACA,IAAIuC,MAAM,GAAG,IAAI,CAACJ,uBAAuB,CAACH,IAAI,EAAET,KAAK,EAAEvB,IAAI,CAAC;IAC5D;IACA,IAAIuC,MAAM,CAAC7C,QAAQ,EAAE,IAAI6B,KAAK,KAAK,OAAO/H,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC/E,MAAM8E,KAAK,CAAE,iBAAgB0B,IAAK,2BAA0BuB,KAAM,IAAG,CAAC;IAC1E;IACA,OAAOgB,MAAM;EACjB;EACAC,KAAK,GAAG;IACJ,OAAO,IAAId,IAAI,EAAE;EACrB;EACAe,KAAK,CAAC1G,KAAK,EAAE2G,WAAW,EAAE;IACtB;IACA;IACA,IAAI,OAAO3G,KAAK,IAAI,QAAQ,EAAE;MAC1B,OAAO,IAAI2F,IAAI,CAAC3F,KAAK,CAAC;IAC1B;IACA,OAAOA,KAAK,GAAG,IAAI2F,IAAI,CAACA,IAAI,CAACe,KAAK,CAAC1G,KAAK,CAAC,CAAC,GAAG,IAAI;EACrD;EACA4G,MAAM,CAAC3C,IAAI,EAAE4C,aAAa,EAAE;IACxB,IAAI,CAAC,IAAI,CAAC3D,OAAO,CAACe,IAAI,CAAC,EAAE;MACrB,MAAM1B,KAAK,CAAC,gDAAgD,CAAC;IACjE;IACA,MAAM8C,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;MAAE,GAAGuD,aAAa;MAAEpB,QAAQ,EAAE;IAAM,CAAC,CAAC;IACvF,OAAO,IAAI,CAACC,OAAO,CAACL,GAAG,EAAEpB,IAAI,CAAC;EAClC;EACA6C,gBAAgB,CAAC7C,IAAI,EAAE8C,KAAK,EAAE;IAC1B,OAAO,IAAI,CAACC,iBAAiB,CAAC/C,IAAI,EAAE8C,KAAK,GAAG,EAAE,CAAC;EACnD;EACAC,iBAAiB,CAAC/C,IAAI,EAAEgD,MAAM,EAAE;IAC5B,IAAIC,OAAO,GAAG,IAAI,CAACd,uBAAuB,CAAC,IAAI,CAAC1C,OAAO,CAACO,IAAI,CAAC,EAAE,IAAI,CAACN,QAAQ,CAACM,IAAI,CAAC,GAAGgD,MAAM,EAAE,IAAI,CAACrD,OAAO,CAACK,IAAI,CAAC,CAAC;IAChH;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACN,QAAQ,CAACuD,OAAO,CAAC,IAAI,CAAE,CAAC,IAAI,CAACvD,QAAQ,CAACM,IAAI,CAAC,GAAGgD,MAAM,IAAI,EAAE,GAAI,EAAE,IAAI,EAAE,EAAE;MAC7EC,OAAO,GAAG,IAAI,CAACd,uBAAuB,CAAC,IAAI,CAAC1C,OAAO,CAACwD,OAAO,CAAC,EAAE,IAAI,CAACvD,QAAQ,CAACuD,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5F;IACA,OAAOA,OAAO;EAClB;EACAC,eAAe,CAAClD,IAAI,EAAEmD,IAAI,EAAE;IACxB,OAAO,IAAI,CAAChB,uBAAuB,CAAC,IAAI,CAAC1C,OAAO,CAACO,IAAI,CAAC,EAAE,IAAI,CAACN,QAAQ,CAACM,IAAI,CAAC,EAAE,IAAI,CAACL,OAAO,CAACK,IAAI,CAAC,GAAGmD,IAAI,CAAC;EAC3G;EACAC,SAAS,CAACpD,IAAI,EAAE;IACZ,OAAO,CACHA,IAAI,CAACqD,cAAc,EAAE,EACrB,IAAI,CAACC,OAAO,CAACtD,IAAI,CAACuD,WAAW,EAAE,GAAG,CAAC,CAAC,EACpC,IAAI,CAACD,OAAO,CAACtD,IAAI,CAACwD,UAAU,EAAE,CAAC,CAClC,CAACC,IAAI,CAAC,GAAG,CAAC;EACf;EACA;AACJ;AACA;AACA;AACA;EACIvE,WAAW,CAACnD,KAAK,EAAE;IACf,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAI,CAACA,KAAK,EAAE;QACR,OAAO,IAAI;MACf;MACA;MACA;MACA,IAAIqE,cAAc,CAACsD,IAAI,CAAC3H,KAAK,CAAC,EAAE;QAC5B,IAAIiE,IAAI,GAAG,IAAI0B,IAAI,CAAC3F,KAAK,CAAC;QAC1B,IAAI,IAAI,CAACkD,OAAO,CAACe,IAAI,CAAC,EAAE;UACpB,OAAOA,IAAI;QACf;MACJ;IACJ;IACA,OAAO,KAAK,CAACd,WAAW,CAACnD,KAAK,CAAC;EACnC;EACAiD,cAAc,CAACD,GAAG,EAAE;IAChB,OAAOA,GAAG,YAAY2C,IAAI;EAC9B;EACAzC,OAAO,CAACe,IAAI,EAAE;IACV,OAAO,CAAC2D,KAAK,CAAC3D,IAAI,CAACqC,OAAO,EAAE,CAAC;EACjC;EACAlD,OAAO,GAAG;IACN,OAAO,IAAIuC,IAAI,CAACkC,GAAG,CAAC;EACxB;EACA;EACAzB,uBAAuB,CAACH,IAAI,EAAET,KAAK,EAAEvB,IAAI,EAAE;IACvC;IACA;IACA,MAAM6D,CAAC,GAAG,IAAInC,IAAI,EAAE;IACpBmC,CAAC,CAACC,WAAW,CAAC9B,IAAI,EAAET,KAAK,EAAEvB,IAAI,CAAC;IAChC6D,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB,OAAOF,CAAC;EACZ;EACA;AACJ;AACA;AACA;AACA;EACIP,OAAO,CAACU,CAAC,EAAE;IACP,OAAO,CAAC,IAAI,GAAGA,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIxC,OAAO,CAACL,GAAG,EAAEpB,IAAI,EAAE;IACf;IACA;IACA,MAAM6D,CAAC,GAAG,IAAInC,IAAI,EAAE;IACpBmC,CAAC,CAACK,cAAc,CAAClE,IAAI,CAACe,WAAW,EAAE,EAAEf,IAAI,CAACN,QAAQ,EAAE,EAAEM,IAAI,CAACL,OAAO,EAAE,CAAC;IACrEkE,CAAC,CAACM,WAAW,CAACnE,IAAI,CAACoE,QAAQ,EAAE,EAAEpE,IAAI,CAACqE,UAAU,EAAE,EAAErE,IAAI,CAACsE,UAAU,EAAE,EAAEtE,IAAI,CAACuE,eAAe,EAAE,CAAC;IAC5F,OAAOnD,GAAG,CAACuB,MAAM,CAACkB,CAAC,CAAC;EACxB;AACJ;AACAlD,iBAAiB,CAAC1G,IAAI;EAAA,iBAA6F0G,iBAAiB,EA7jB7BhL,EAAE,UA6jB6C8I,eAAe,MA7jB9D9I,EAAE,UA6jByF0B,IAAI,CAACC,QAAQ;AAAA,CAA6C;AAC5PqJ,iBAAiB,CAAC6D,KAAK,kBA9jBgF7O,EAAE;EAAA,OA8jBcgL,iBAAiB;EAAA,SAAjBA,iBAAiB;AAAA,EAAG;AAC3I;EAAA,mDA/jBuGhL,EAAE,mBA+jBTgL,iBAAiB,EAAc,CAAC;IACpHtG,IAAI,EAAElE;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEkE,IAAI,EAAEI,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DL,IAAI,EAAErE;MACV,CAAC,EAAE;QACCqE,IAAI,EAAEpE,MAAM;QACZqE,IAAI,EAAE,CAACmE,eAAe;MAC1B,CAAC;IAAE,CAAC,EAAE;MAAEpE,IAAI,EAAEhD,IAAI,CAACC;IAAS,CAAC,CAAC;EAAE,CAAC;AAAA;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmN,uBAAuB,GAAG;EAC5BhC,KAAK,EAAE;IACHiC,SAAS,EAAE;EACf,CAAC;EACDlJ,OAAO,EAAE;IACLkJ,SAAS,EAAE;MAAE1C,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE,SAAS;MAAEK,GAAG,EAAE;IAAU,CAAC;IAChE+C,cAAc,EAAE;MAAE3C,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE;IAAQ,CAAC;IACnDqD,aAAa,EAAE;MAAE5C,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE,MAAM;MAAEK,GAAG,EAAE;IAAU,CAAC;IACjEiD,kBAAkB,EAAE;MAAE7C,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE;IAAO;EACzD;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuD,gBAAgB,CAAC;AAEvBA,gBAAgB,CAAC7K,IAAI;EAAA,iBAA6F6K,gBAAgB;AAAA,CAAkD;AACpLA,gBAAgB,CAAC3K,IAAI,kBArmBkFxE,EAAE;EAAA,MAqmBUmP;AAAgB,EAAG;AACtIA,gBAAgB,CAAC1K,IAAI,kBAtmBkFzE,EAAE;EAAA,WAsmBuC,CAAC;IAAEoP,OAAO,EAAEpG,WAAW;IAAEqG,QAAQ,EAAErE;EAAkB,CAAC;AAAC,EAAG;AAC1M;EAAA,mDAvmBuGhL,EAAE,mBAumBTmP,gBAAgB,EAAc,CAAC;IACnHzK,IAAI,EAAEtE,QAAQ;IACduE,IAAI,EAAE,CAAC;MACC2K,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEpG,WAAW;QAAEqG,QAAQ,EAAErE;MAAkB,CAAC;IACrE,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMuE,mBAAmB,CAAC;AAE1BA,mBAAmB,CAACjL,IAAI;EAAA,iBAA6FiL,mBAAmB;AAAA,CAAkD;AAC1LA,mBAAmB,CAAC/K,IAAI,kBAhnB+ExE,EAAE;EAAA,MAgnBauP,mBAAmB;EAAA,UAAYJ,gBAAgB;AAAA,EAAI;AACzKI,mBAAmB,CAAC9K,IAAI,kBAjnB+EzE,EAAE;EAAA,WAinB6C,CAAC;IAAEoP,OAAO,EAAE5E,gBAAgB;IAAEgF,QAAQ,EAAEV;EAAwB,CAAC,CAAC;EAAA,UAAYK,gBAAgB;AAAA,EAAI;AACxP;EAAA,mDAlnBuGnP,EAAE,mBAknBTuP,mBAAmB,EAAc,CAAC;IACtH7K,IAAI,EAAEtE,QAAQ;IACduE,IAAI,EAAE,CAAC;MACCC,OAAO,EAAE,CAACuK,gBAAgB,CAAC;MAC3BG,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAE5E,gBAAgB;QAAEgF,QAAQ,EAAEV;MAAwB,CAAC;IAChF,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,4BAA4B,CAAC;EAC/BzH,YAAY,CAACH,OAAO,EAAE6H,IAAI,EAAE;IACxB,OAAO,CAAC,EAAE7H,OAAO,IAAIA,OAAO,CAAC2B,OAAO,KAAK3B,OAAO,CAAC8H,KAAK,IAAKD,IAAI,IAAIA,IAAI,CAACE,SAAU,CAAC,CAAC;EACxF;AACJ;AACAH,4BAA4B,CAACnL,IAAI;EAAA,iBAA6FmL,4BAA4B;AAAA,CAAoD;AAC9MA,4BAA4B,CAACZ,KAAK,kBAxoBqE7O,EAAE;EAAA,OAwoByByP,4BAA4B;EAAA,SAA5BA,4BAA4B;AAAA,EAAG;AACjK;EAAA,mDAzoBuGzP,EAAE,mBAyoBTyP,4BAA4B,EAAc,CAAC;IAC/H/K,IAAI,EAAElE;EACV,CAAC,CAAC;AAAA;AACV;AACA,MAAMqP,iBAAiB,CAAC;EACpB7H,YAAY,CAACH,OAAO,EAAE6H,IAAI,EAAE;IACxB,OAAO,CAAC,EAAE7H,OAAO,IAAIA,OAAO,CAAC2B,OAAO,KAAK3B,OAAO,CAACiI,OAAO,IAAKJ,IAAI,IAAIA,IAAI,CAACE,SAAU,CAAC,CAAC;EAC1F;AACJ;AACAC,iBAAiB,CAACvL,IAAI;EAAA,iBAA6FuL,iBAAiB;AAAA,CAAoD;AACxLA,iBAAiB,CAAChB,KAAK,kBAnpBgF7O,EAAE;EAAA,OAmpBc6P,iBAAiB;EAAA,SAAjBA,iBAAiB;EAAA,YAAc;AAAM,EAAG;AAC/J;EAAA,mDAppBuG7P,EAAE,mBAopBT6P,iBAAiB,EAAc,CAAC;IACpHnL,IAAI,EAAElE,UAAU;IAChBmE,IAAI,EAAE,CAAC;MAAEvB,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2M,OAAO,CAAC;AAEdA,OAAO,CAACzL,IAAI;EAAA,iBAA6FyL,OAAO;AAAA,CAAmD;AACnKA,OAAO,CAACC,IAAI,kBAxqB2FhQ,EAAE;EAAA,MAwqBZ+P,OAAO;EAAA;EAAA;AAAA,EAA0F;AAC9L;EAAA,mDAzqBuG/P,EAAE,mBAyqBT+P,OAAO,EAAc,CAAC;IAC1GrL,IAAI,EAAEjE,SAAS;IACfkE,IAAI,EAAE,CAAC;MACCsL,QAAQ,EAAE,uBAAuB;MACjCC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAW;IAChC,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,SAASC,QAAQ,CAACC,KAAK,EAAEC,OAAO,EAAEC,MAAM,GAAG,KAAK,EAAE;EAC9C;EACA;EACAF,KAAK,CAACG,OAAO,CAACC,IAAI,CAACpO,SAAS,CAACgO,KAAK,CAAC,CAAC,CAACK,SAAS,CAAC,CAAC;IAAE9F;EAAO,CAAC,KAAK;IAC3D+F,QAAQ,CAACL,OAAO,EAAG,GAAEC,MAAO,SAAQ,EAAE,KAAK,CAAC;IAC5CI,QAAQ,CAACL,OAAO,EAAG,GAAEC,MAAO,SAAQ,EAAE,KAAK,CAAC;IAC5CI,QAAQ,CAACL,OAAO,EAAG,GAAEC,MAAO,aAAY,EAAE,KAAK,CAAC;IAChD,IAAI3F,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;MAC9B+F,QAAQ,CAACL,OAAO,EAAG,GAAEC,MAAO,IAAG3F,MAAO,OAAM,EAAE,IAAI,CAAC;IACvD,CAAC,MACI,IAAIA,MAAM,GAAG,CAAC,EAAE;MACjB+F,QAAQ,CAACL,OAAO,EAAG,GAAEC,MAAO,aAAY,EAAE,IAAI,CAAC;IACnD;EACJ,CAAC,CAAC;AACN;AACA;AACA,SAASI,QAAQ,CAACL,OAAO,EAAEM,SAAS,EAAEC,KAAK,EAAE;EACzCP,OAAO,CAAC1J,aAAa,CAACnB,SAAS,CAACqL,MAAM,CAACF,SAAS,EAAEC,KAAK,CAAC;AAC5D;AACA,MAAME,aAAa,CAAC;AAEpBA,aAAa,CAACxM,IAAI;EAAA,iBAA6FwM,aAAa;AAAA,CAAkD;AAC9KA,aAAa,CAACtM,IAAI,kBA1sBqFxE,EAAE;EAAA,MA0sBO8Q,aAAa;EAAA,eAAiBf,OAAO;EAAA,UAAazM,eAAe;EAAA,UAAayM,OAAO,EAAEzM,eAAe;AAAA,EAAI;AAC1NwN,aAAa,CAACrM,IAAI,kBA3sBqFzE,EAAE;EAAA,UA2sBgCsD,eAAe,EAAEA,eAAe;AAAA,EAAI;AAC7K;EAAA,mDA5sBuGtD,EAAE,mBA4sBT8Q,aAAa,EAAc,CAAC;IAChHpM,IAAI,EAAEtE,QAAQ;IACduE,IAAI,EAAE,CAAC;MACCC,OAAO,EAAE,CAACtB,eAAe,CAAC;MAC1BuB,OAAO,EAAE,CAACkL,OAAO,EAAEzM,eAAe,CAAC;MACnCyN,YAAY,EAAE,CAAChB,OAAO;IAC1B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiB,SAAS,CAAC;EACZzN,WAAW,CAAC0N,SAAS,EACrB;EACAZ,OAAO,EACP;EACAa,MAAM,EACN;EACAC,oCAAoC,GAAG,KAAK,EAAE;IAC1C,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACZ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACa,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,oCAAoC,GAAGA,oCAAoC;IAChF;IACA,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACnB;EACA;EACAC,OAAO,GAAG;IACN,IAAI,CAACJ,SAAS,CAACK,aAAa,CAAC,IAAI,CAAC;EACtC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,8BAA8B,GAAG1P,+BAA+B,CAAC;EACnE2P,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAMC,kBAAkB,CAAC;EACrBnO,WAAW,GAAG;IACV,IAAI,CAACoO,OAAO,GAAG,IAAIC,GAAG,EAAE;IACxB;IACA,IAAI,CAACC,qBAAqB,GAAIC,KAAK,IAAK;MACpC,MAAMC,MAAM,GAAGjQ,eAAe,CAACgQ,KAAK,CAAC;MACrC,IAAIC,MAAM,EAAE;QACR,IAAI,CAACJ,OAAO,CAACK,GAAG,CAACF,KAAK,CAACpN,IAAI,CAAC,EAAEkE,OAAO,CAAC,CAACqJ,QAAQ,EAAE5B,OAAO,KAAK;UACzD,IAAIA,OAAO,KAAK0B,MAAM,IAAI1B,OAAO,CAAC6B,QAAQ,CAACH,MAAM,CAAC,EAAE;YAChDE,QAAQ,CAACrJ,OAAO,CAACuJ,OAAO,IAAIA,OAAO,CAACC,WAAW,CAACN,KAAK,CAAC,CAAC;UAC3D;QACJ,CAAC,CAAC;MACN;IACJ,CAAC;EACL;EACA;EACAO,UAAU,CAACC,MAAM,EAAEjO,IAAI,EAAEgM,OAAO,EAAE8B,OAAO,EAAE;IACvC,MAAMI,gBAAgB,GAAG,IAAI,CAACZ,OAAO,CAACK,GAAG,CAAC3N,IAAI,CAAC;IAC/C,IAAIkO,gBAAgB,EAAE;MAClB,MAAMC,kBAAkB,GAAGD,gBAAgB,CAACP,GAAG,CAAC3B,OAAO,CAAC;MACxD,IAAImC,kBAAkB,EAAE;QACpBA,kBAAkB,CAAC/M,GAAG,CAAC0M,OAAO,CAAC;MACnC,CAAC,MACI;QACDI,gBAAgB,CAACE,GAAG,CAACpC,OAAO,EAAE,IAAIqC,GAAG,CAAC,CAACP,OAAO,CAAC,CAAC,CAAC;MACrD;IACJ,CAAC,MACI;MACD,IAAI,CAACR,OAAO,CAACc,GAAG,CAACpO,IAAI,EAAE,IAAIuN,GAAG,CAAC,CAAC,CAACvB,OAAO,EAAE,IAAIqC,GAAG,CAAC,CAACP,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChEG,MAAM,CAACK,iBAAiB,CAAC,MAAM;QAC3BC,QAAQ,CAACC,gBAAgB,CAACxO,IAAI,EAAE,IAAI,CAACwN,qBAAqB,EAAEN,8BAA8B,CAAC;MAC/F,CAAC,CAAC;IACN;EACJ;EACA;EACAuB,aAAa,CAACzO,IAAI,EAAEgM,OAAO,EAAE8B,OAAO,EAAE;IAClC,MAAMI,gBAAgB,GAAG,IAAI,CAACZ,OAAO,CAACK,GAAG,CAAC3N,IAAI,CAAC;IAC/C,IAAI,CAACkO,gBAAgB,EAAE;MACnB;IACJ;IACA,MAAMC,kBAAkB,GAAGD,gBAAgB,CAACP,GAAG,CAAC3B,OAAO,CAAC;IACxD,IAAI,CAACmC,kBAAkB,EAAE;MACrB;IACJ;IACAA,kBAAkB,CAACO,MAAM,CAACZ,OAAO,CAAC;IAClC,IAAIK,kBAAkB,CAACQ,IAAI,KAAK,CAAC,EAAE;MAC/BT,gBAAgB,CAACQ,MAAM,CAAC1C,OAAO,CAAC;IACpC;IACA,IAAIkC,gBAAgB,CAACS,IAAI,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACrB,OAAO,CAACoB,MAAM,CAAC1O,IAAI,CAAC;MACzBuO,QAAQ,CAACK,mBAAmB,CAAC5O,IAAI,EAAE,IAAI,CAACwN,qBAAqB,EAAEN,8BAA8B,CAAC;IAClG;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAM2B,4BAA4B,GAAG;EACjCC,aAAa,EAAE,GAAG;EAClBC,YAAY,EAAE;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAG,GAAG;AACpC;AACA,MAAMC,4BAA4B,GAAGzR,+BAA+B,CAAC;EACjE2P,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAM8B,iBAAiB,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC;AACrD;AACA,MAAMC,eAAe,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa,CAAC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBlQ,WAAW,CAACmQ,OAAO,EAAEC,OAAO,EAAEC,mBAAmB,EAAE1I,SAAS,EAAE;IAC1D,IAAI,CAACwI,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACzI,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAAC2I,cAAc,GAAG,KAAK;IAC3B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAIlC,GAAG,EAAE;IAC/B;IACA,IAAI,CAACmC,0BAA0B,GAAG,KAAK;IACvC;IACA,IAAI7I,SAAS,CAAC/G,SAAS,EAAE;MACrB,IAAI,CAAC6P,iBAAiB,GAAG/R,aAAa,CAAC2R,mBAAmB,CAAC;IAC/D;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,YAAY,CAACC,CAAC,EAAEC,CAAC,EAAEjD,MAAM,GAAG,CAAC,CAAC,EAAE;IAC5B,MAAMkD,aAAa,GAAI,IAAI,CAACC,cAAc,GACtC,IAAI,CAACA,cAAc,IAAI,IAAI,CAACL,iBAAiB,CAACM,qBAAqB,EAAG;IAC1E,MAAMC,eAAe,GAAG;MAAE,GAAGrB,4BAA4B;MAAE,GAAGhC,MAAM,CAACsD;IAAU,CAAC;IAChF,IAAItD,MAAM,CAACuD,QAAQ,EAAE;MACjBP,CAAC,GAAGE,aAAa,CAACM,IAAI,GAAGN,aAAa,CAACO,KAAK,GAAG,CAAC;MAChDR,CAAC,GAAGC,aAAa,CAACQ,GAAG,GAAGR,aAAa,CAACS,MAAM,GAAG,CAAC;IACpD;IACA,MAAMC,MAAM,GAAG5D,MAAM,CAAC4D,MAAM,IAAIC,wBAAwB,CAACb,CAAC,EAAEC,CAAC,EAAEC,aAAa,CAAC;IAC7E,MAAMY,OAAO,GAAGd,CAAC,GAAGE,aAAa,CAACM,IAAI;IACtC,MAAMO,OAAO,GAAGd,CAAC,GAAGC,aAAa,CAACQ,GAAG;IACrC,MAAMzB,aAAa,GAAGoB,eAAe,CAACpB,aAAa;IACnD,MAAM+B,MAAM,GAAGtC,QAAQ,CAACrN,aAAa,CAAC,KAAK,CAAC;IAC5C2P,MAAM,CAAC1P,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;IAC1CyP,MAAM,CAAC1J,KAAK,CAACkJ,IAAI,GAAI,GAAEM,OAAO,GAAGF,MAAO,IAAG;IAC3CI,MAAM,CAAC1J,KAAK,CAACoJ,GAAG,GAAI,GAAEK,OAAO,GAAGH,MAAO,IAAG;IAC1CI,MAAM,CAAC1J,KAAK,CAACqJ,MAAM,GAAI,GAAEC,MAAM,GAAG,CAAE,IAAG;IACvCI,MAAM,CAAC1J,KAAK,CAACmJ,KAAK,GAAI,GAAEG,MAAM,GAAG,CAAE,IAAG;IACtC;IACA;IACA,IAAI5D,MAAM,CAAC3K,KAAK,IAAI,IAAI,EAAE;MACtB2O,MAAM,CAAC1J,KAAK,CAAC2J,eAAe,GAAGjE,MAAM,CAAC3K,KAAK;IAC/C;IACA2O,MAAM,CAAC1J,KAAK,CAAC4J,kBAAkB,GAAI,GAAEjC,aAAc,IAAG;IACtD,IAAI,CAACa,iBAAiB,CAACtO,WAAW,CAACwP,MAAM,CAAC;IAC1C;IACA;IACA;IACA;IACA,MAAMG,cAAc,GAAGC,MAAM,CAAC1P,gBAAgB,CAACsP,MAAM,CAAC;IACtD,MAAMK,sBAAsB,GAAGF,cAAc,CAACG,kBAAkB;IAChE,MAAMC,sBAAsB,GAAGJ,cAAc,CAACD,kBAAkB;IAChE;IACA;IACA;IACA;IACA;IACA,MAAMM,mCAAmC,GAAGH,sBAAsB,KAAK,MAAM;IACzE;IACA;IACAE,sBAAsB,KAAK,IAAI,IAC/BA,sBAAsB,KAAK,QAAQ;IACnC;IACCrB,aAAa,CAACO,KAAK,KAAK,CAAC,IAAIP,aAAa,CAACS,MAAM,KAAK,CAAE;IAC7D;IACA,MAAMc,SAAS,GAAG,IAAI3E,SAAS,CAAC,IAAI,EAAEkE,MAAM,EAAEhE,MAAM,EAAEwE,mCAAmC,CAAC;IAC1F;IACA;IACA;IACA;IACAR,MAAM,CAAC1J,KAAK,CAACoK,SAAS,GAAG,kBAAkB;IAC3CD,SAAS,CAACvE,KAAK,GAAG,CAAC,CAAC;IACpB,IAAI,CAACF,MAAM,CAAC2E,UAAU,EAAE;MACpB,IAAI,CAACC,0BAA0B,GAAGH,SAAS;IAC/C;IACA,IAAII,cAAc,GAAG,IAAI;IACzB;IACA;IACA,IAAI,CAACL,mCAAmC,KAAKvC,aAAa,IAAIoB,eAAe,CAACnB,YAAY,CAAC,EAAE;MACzF,IAAI,CAACO,OAAO,CAAChB,iBAAiB,CAAC,MAAM;QACjC,MAAMqD,eAAe,GAAG,MAAM,IAAI,CAACC,uBAAuB,CAACN,SAAS,CAAC;QACrE,MAAMO,kBAAkB,GAAG,MAAM,IAAI,CAACC,cAAc,CAACR,SAAS,CAAC;QAC/DT,MAAM,CAACrC,gBAAgB,CAAC,eAAe,EAAEmD,eAAe,CAAC;QACzD;QACA;QACA;QACAd,MAAM,CAACrC,gBAAgB,CAAC,kBAAkB,EAAEqD,kBAAkB,CAAC;QAC/DH,cAAc,GAAG;UAAEC,eAAe;UAAEE;QAAmB,CAAC;MAC5D,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAACpC,cAAc,CAACrB,GAAG,CAACkD,SAAS,EAAEI,cAAc,CAAC;IAClD;IACA;IACA,IAAIL,mCAAmC,IAAI,CAACvC,aAAa,EAAE;MACvD,IAAI,CAAC8C,uBAAuB,CAACN,SAAS,CAAC;IAC3C;IACA,OAAOA,SAAS;EACpB;EACA;EACArE,aAAa,CAACqE,SAAS,EAAE;IACrB;IACA,IAAIA,SAAS,CAACvE,KAAK,KAAK,CAAC,CAAC,gCAAgCuE,SAAS,CAACvE,KAAK,KAAK,CAAC,CAAC,0BAA0B;MACtG;IACJ;IACA,MAAMgF,QAAQ,GAAGT,SAAS,CAACtF,OAAO;IAClC,MAAMkE,eAAe,GAAG;MAAE,GAAGrB,4BAA4B;MAAE,GAAGyC,SAAS,CAACzE,MAAM,CAACsD;IAAU,CAAC;IAC1F;IACA;IACA4B,QAAQ,CAAC5K,KAAK,CAAC4J,kBAAkB,GAAI,GAAEb,eAAe,CAACnB,YAAa,IAAG;IACvEgD,QAAQ,CAAC5K,KAAK,CAAC6K,OAAO,GAAG,GAAG;IAC5BV,SAAS,CAACvE,KAAK,GAAG,CAAC,CAAC;IACpB;IACA;IACA,IAAIuE,SAAS,CAACxE,oCAAoC,IAAI,CAACoD,eAAe,CAACnB,YAAY,EAAE;MACjF,IAAI,CAAC6C,uBAAuB,CAACN,SAAS,CAAC;IAC3C;EACJ;EACA;EACAW,UAAU,GAAG;IACT,IAAI,CAACC,iBAAiB,EAAE,CAAC3N,OAAO,CAACsM,MAAM,IAAIA,MAAM,CAAC7D,OAAO,EAAE,CAAC;EAChE;EACA;EACAmF,uBAAuB,GAAG;IACtB,IAAI,CAACD,iBAAiB,EAAE,CAAC3N,OAAO,CAACsM,MAAM,IAAI;MACvC,IAAI,CAACA,MAAM,CAAChE,MAAM,CAAC2E,UAAU,EAAE;QAC3BX,MAAM,CAAC7D,OAAO,EAAE;MACpB;IACJ,CAAC,CAAC;EACN;EACA;EACAoF,kBAAkB,CAAC7C,mBAAmB,EAAE;IACpC,MAAMvD,OAAO,GAAGpO,aAAa,CAAC2R,mBAAmB,CAAC;IAClD,IAAI,CAAC,IAAI,CAAC1I,SAAS,CAAC/G,SAAS,IAAI,CAACkM,OAAO,IAAIA,OAAO,KAAK,IAAI,CAACqG,eAAe,EAAE;MAC3E;IACJ;IACA;IACA,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACD,eAAe,GAAGrG,OAAO;IAC9B;IACA;IACAkD,iBAAiB,CAAC3K,OAAO,CAAClE,IAAI,IAAI;MAC9B+O,cAAc,CAACmD,aAAa,CAACvE,UAAU,CAAC,IAAI,CAACsB,OAAO,EAAEjP,IAAI,EAAE2L,OAAO,EAAE,IAAI,CAAC;IAC9E,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI+B,WAAW,CAACN,KAAK,EAAE;IACf,IAAIA,KAAK,CAACpN,IAAI,KAAK,WAAW,EAAE;MAC5B,IAAI,CAACmS,YAAY,CAAC/E,KAAK,CAAC;IAC5B,CAAC,MACI,IAAIA,KAAK,CAACpN,IAAI,KAAK,YAAY,EAAE;MAClC,IAAI,CAACoS,aAAa,CAAChF,KAAK,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAACiF,YAAY,EAAE;IACvB;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAChD,0BAA0B,EAAE;MAClC;MACA;MACA;MACA;MACA;MACA,IAAI,CAACJ,OAAO,CAAChB,iBAAiB,CAAC,MAAM;QACjCa,eAAe,CAAC5K,OAAO,CAAClE,IAAI,IAAI;UAC5B,IAAI,CAACgS,eAAe,CAAC7D,gBAAgB,CAACnO,IAAI,EAAE,IAAI,EAAE4O,4BAA4B,CAAC;QACnF,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAACS,0BAA0B,GAAG,IAAI;IAC1C;EACJ;EACA;EACAkC,uBAAuB,CAACN,SAAS,EAAE;IAC/B,IAAIA,SAAS,CAACvE,KAAK,KAAK,CAAC,CAAC,6BAA6B;MACnD,IAAI,CAAC4F,uBAAuB,CAACrB,SAAS,CAAC;IAC3C,CAAC,MACI,IAAIA,SAAS,CAACvE,KAAK,KAAK,CAAC,CAAC,8BAA8B;MACzD,IAAI,CAAC+E,cAAc,CAACR,SAAS,CAAC;IAClC;EACJ;EACA;AACJ;AACA;AACA;EACIqB,uBAAuB,CAACrB,SAAS,EAAE;IAC/B,MAAMsB,2BAA2B,GAAGtB,SAAS,KAAK,IAAI,CAACG,0BAA0B;IACjF,MAAM;MAAED;IAAW,CAAC,GAAGF,SAAS,CAACzE,MAAM;IACvCyE,SAAS,CAACvE,KAAK,GAAG,CAAC,CAAC;IACpB;IACA;IACA;IACA;IACA,IAAI,CAACyE,UAAU,KAAK,CAACoB,2BAA2B,IAAI,CAAC,IAAI,CAACpD,cAAc,CAAC,EAAE;MACvE8B,SAAS,CAACtE,OAAO,EAAE;IACvB;EACJ;EACA;EACA8E,cAAc,CAACR,SAAS,EAAE;IACtB,MAAMI,cAAc,GAAG,IAAI,CAACjC,cAAc,CAAC9B,GAAG,CAAC2D,SAAS,CAAC,IAAI,IAAI;IACjE,IAAI,CAAC7B,cAAc,CAACf,MAAM,CAAC4C,SAAS,CAAC;IACrC;IACA,IAAI,CAAC,IAAI,CAAC7B,cAAc,CAACd,IAAI,EAAE;MAC3B,IAAI,CAACqB,cAAc,GAAG,IAAI;IAC9B;IACA;IACA;IACA,IAAIsB,SAAS,KAAK,IAAI,CAACG,0BAA0B,EAAE;MAC/C,IAAI,CAACA,0BAA0B,GAAG,IAAI;IAC1C;IACAH,SAAS,CAACvE,KAAK,GAAG,CAAC,CAAC;IACpB,IAAI2E,cAAc,KAAK,IAAI,EAAE;MACzBJ,SAAS,CAACtF,OAAO,CAAC4C,mBAAmB,CAAC,eAAe,EAAE8C,cAAc,CAACC,eAAe,CAAC;MACtFL,SAAS,CAACtF,OAAO,CAAC4C,mBAAmB,CAAC,kBAAkB,EAAE8C,cAAc,CAACG,kBAAkB,CAAC;IAChG;IACAP,SAAS,CAACtF,OAAO,CAACvK,MAAM,EAAE;EAC9B;EACA;EACA+Q,YAAY,CAAC/E,KAAK,EAAE;IAChB;IACA;IACA,MAAMoF,eAAe,GAAGhW,+BAA+B,CAAC4Q,KAAK,CAAC;IAC9D,MAAMqF,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,IAC9CrL,IAAI,CAACsL,GAAG,EAAE,GAAG,IAAI,CAACD,oBAAoB,GAAG/D,wBAAwB;IACrE,IAAI,CAAC,IAAI,CAACK,OAAO,CAAC4D,cAAc,IAAI,CAACJ,eAAe,IAAI,CAACC,gBAAgB,EAAE;MACvE,IAAI,CAACtD,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACI,YAAY,CAACnC,KAAK,CAACyF,OAAO,EAAEzF,KAAK,CAAC0F,OAAO,EAAE,IAAI,CAAC9D,OAAO,CAAC+D,YAAY,CAAC;IAC9E;EACJ;EACA;EACAX,aAAa,CAAChF,KAAK,EAAE;IACjB,IAAI,CAAC,IAAI,CAAC4B,OAAO,CAAC4D,cAAc,IAAI,CAACnW,gCAAgC,CAAC2Q,KAAK,CAAC,EAAE;MAC1E;MACA;MACA;MACA,IAAI,CAACsF,oBAAoB,GAAGrL,IAAI,CAACsL,GAAG,EAAE;MACtC,IAAI,CAACxD,cAAc,GAAG,IAAI;MAC1B;MACA;MACA,MAAM6D,OAAO,GAAG5F,KAAK,CAAC6F,cAAc;MACpC,KAAK,IAAI5M,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2M,OAAO,CAAC/M,MAAM,EAAEI,CAAC,EAAE,EAAE;QACrC,IAAI,CAACkJ,YAAY,CAACyD,OAAO,CAAC3M,CAAC,CAAC,CAACwM,OAAO,EAAEG,OAAO,CAAC3M,CAAC,CAAC,CAACyM,OAAO,EAAE,IAAI,CAAC9D,OAAO,CAAC+D,YAAY,CAAC;MACxF;IACJ;EACJ;EACA;EACAV,YAAY,GAAG;IACX,IAAI,CAAC,IAAI,CAAClD,cAAc,EAAE;MACtB;IACJ;IACA,IAAI,CAACA,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAAC0C,iBAAiB,EAAE,CAAC3N,OAAO,CAACsM,MAAM,IAAI;MACvC;MACA;MACA,MAAM0C,SAAS,GAAG1C,MAAM,CAAC9D,KAAK,KAAK,CAAC,CAAC,6BAChC8D,MAAM,CAAChE,MAAM,CAAC2G,oBAAoB,IAAI3C,MAAM,CAAC9D,KAAK,KAAK,CAAC,CAAC,2BAA4B;MAC1F,IAAI,CAAC8D,MAAM,CAAChE,MAAM,CAAC2E,UAAU,IAAI+B,SAAS,EAAE;QACxC1C,MAAM,CAAC7D,OAAO,EAAE;MACpB;IACJ,CAAC,CAAC;EACN;EACAkF,iBAAiB,GAAG;IAChB,OAAOzL,KAAK,CAACgN,IAAI,CAAC,IAAI,CAAChE,cAAc,CAACiE,IAAI,EAAE,CAAC;EACjD;EACA;EACApB,oBAAoB,GAAG;IACnB,MAAMqB,OAAO,GAAG,IAAI,CAACtB,eAAe;IACpC,IAAIsB,OAAO,EAAE;MACTzE,iBAAiB,CAAC3K,OAAO,CAAClE,IAAI,IAAI+O,cAAc,CAACmD,aAAa,CAAC9D,aAAa,CAACpO,IAAI,EAAEsT,OAAO,EAAE,IAAI,CAAC,CAAC;MAClG,IAAI,IAAI,CAACjE,0BAA0B,EAAE;QACjCP,eAAe,CAAC5K,OAAO,CAAClE,IAAI,IAAIsT,OAAO,CAAC/E,mBAAmB,CAACvO,IAAI,EAAE,IAAI,EAAE4O,4BAA4B,CAAC,CAAC;MAC1G;IACJ;EACJ;AACJ;AACAG,cAAc,CAACmD,aAAa,GAAG,IAAIlF,kBAAkB,EAAE;AACvD;AACA;AACA;AACA,SAASqD,wBAAwB,CAACb,CAAC,EAAEC,CAAC,EAAE8D,IAAI,EAAE;EAC1C,MAAMC,KAAK,GAAGC,IAAI,CAAC5N,GAAG,CAAC4N,IAAI,CAACC,GAAG,CAAClE,CAAC,GAAG+D,IAAI,CAACvD,IAAI,CAAC,EAAEyD,IAAI,CAACC,GAAG,CAAClE,CAAC,GAAG+D,IAAI,CAACI,KAAK,CAAC,CAAC;EACzE,MAAMC,KAAK,GAAGH,IAAI,CAAC5N,GAAG,CAAC4N,IAAI,CAACC,GAAG,CAACjE,CAAC,GAAG8D,IAAI,CAACrD,GAAG,CAAC,EAAEuD,IAAI,CAACC,GAAG,CAACjE,CAAC,GAAG8D,IAAI,CAACM,MAAM,CAAC,CAAC;EACzE,OAAOJ,IAAI,CAACK,IAAI,CAACN,KAAK,GAAGA,KAAK,GAAGI,KAAK,GAAGA,KAAK,CAAC;AACnD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,yBAAyB,GAAG,IAAIvY,cAAc,CAAC,2BAA2B,CAAC;AACjF,MAAMwY,SAAS,CAAC;EACZ;AACJ;AACA;AACA;EACI,IAAIxS,QAAQ,GAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQ,CAACE,KAAK,EAAE;IAChB,IAAIA,KAAK,EAAE;MACP,IAAI,CAACoQ,uBAAuB,EAAE;IAClC;IACA,IAAI,CAACrQ,SAAS,GAAGC,KAAK;IACtB,IAAI,CAACuS,4BAA4B,EAAE;EACvC;EACA;AACJ;AACA;AACA;EACI,IAAIX,OAAO,GAAG;IACV,OAAO,IAAI,CAACY,QAAQ,IAAI,IAAI,CAAClS,WAAW,CAACC,aAAa;EAC1D;EACA,IAAIqR,OAAO,CAACA,OAAO,EAAE;IACjB,IAAI,CAACY,QAAQ,GAAGZ,OAAO;IACvB,IAAI,CAACW,4BAA4B,EAAE;EACvC;EACApV,WAAW,CAACmD,WAAW,EAAE4L,MAAM,EAAExO,QAAQ,EAAE+U,aAAa,EAAEC,cAAc,EAAE;IACtE,IAAI,CAACpS,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACoS,cAAc,GAAGA,cAAc;IACpC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAChE,MAAM,GAAG,CAAC;IACf,IAAI,CAAC3O,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACiC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAAC2Q,cAAc,GAAGF,aAAa,IAAI,CAAC,CAAC;IACzC,IAAI,CAACG,eAAe,GAAG,IAAIvF,cAAc,CAAC,IAAI,EAAEnB,MAAM,EAAE5L,WAAW,EAAE5C,QAAQ,CAAC;EAClF;EACAmV,QAAQ,GAAG;IACP,IAAI,CAAC7Q,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACuQ,4BAA4B,EAAE;EACvC;EACAO,WAAW,GAAG;IACV,IAAI,CAACF,eAAe,CAACrC,oBAAoB,EAAE;EAC/C;EACA;EACAL,UAAU,GAAG;IACT,IAAI,CAAC0C,eAAe,CAAC1C,UAAU,EAAE;EACrC;EACA;EACAE,uBAAuB,GAAG;IACtB,IAAI,CAACwC,eAAe,CAACxC,uBAAuB,EAAE;EAClD;EACA;AACJ;AACA;AACA;EACI,IAAIiB,YAAY,GAAG;IACf,OAAO;MACHhD,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBK,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBvO,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBiO,SAAS,EAAE;QACP,GAAG,IAAI,CAACuE,cAAc,CAACvE,SAAS;QAChC,IAAI,IAAI,CAACsE,cAAc,KAAK,gBAAgB,GAAG;UAAE3F,aAAa,EAAE,CAAC;UAAEC,YAAY,EAAE;QAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1F,GAAG,IAAI,CAACoB;MACZ,CAAC;MACDqD,oBAAoB,EAAE,IAAI,CAACkB,cAAc,CAAClB;IAC9C,CAAC;EACL;EACA;AACJ;AACA;AACA;EACI,IAAIP,cAAc,GAAG;IACjB,OAAO,IAAI,CAACpR,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC6S,cAAc,CAAC7S,QAAQ;EAC1D;EACA;EACAyS,4BAA4B,GAAG;IAC3B,IAAI,CAAC,IAAI,CAACzS,QAAQ,IAAI,IAAI,CAACkC,cAAc,EAAE;MACvC,IAAI,CAAC4Q,eAAe,CAACvC,kBAAkB,CAAC,IAAI,CAACuB,OAAO,CAAC;IACzD;EACJ;EACA;EACAmB,MAAM,CAACC,SAAS,EAAEjF,CAAC,GAAG,CAAC,EAAEjD,MAAM,EAAE;IAC7B,IAAI,OAAOkI,SAAS,KAAK,QAAQ,EAAE;MAC/B,OAAO,IAAI,CAACJ,eAAe,CAAC/E,YAAY,CAACmF,SAAS,EAAEjF,CAAC,EAAE;QAAE,GAAG,IAAI,CAACsD,YAAY;QAAE,GAAGvG;MAAO,CAAC,CAAC;IAC/F,CAAC,MACI;MACD,OAAO,IAAI,CAAC8H,eAAe,CAAC/E,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;QAAE,GAAG,IAAI,CAACwD,YAAY;QAAE,GAAG2B;MAAU,CAAC,CAAC;IAC1F;EACJ;AACJ;AACAV,SAAS,CAACpU,IAAI;EAAA,iBAA6FoU,SAAS,EAvuCb1Y,EAAE,mBAuuC6BA,EAAE,CAACqZ,UAAU,GAvuC5CrZ,EAAE,mBAuuCuDA,EAAE,CAACsZ,MAAM,GAvuClEtZ,EAAE,mBAuuC6E0B,IAAI,CAACC,QAAQ,GAvuC5F3B,EAAE,mBAuuCuGyY,yBAAyB,MAvuClIzY,EAAE,mBAuuC6JqC,qBAAqB;AAAA,CAA4D;AACvVqW,SAAS,CAAC1I,IAAI,kBAxuCyFhQ,EAAE;EAAA,MAwuCV0Y,SAAS;EAAA;EAAA;EAAA;EAAA;IAAA;MAxuCD1Y,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;AAAA,EAwuC8e;AACvlB;EAAA,mDAzuCuGA,EAAE,mBAyuCT0Y,SAAS,EAAc,CAAC;IAC5GhU,IAAI,EAAEjE,SAAS;IACfkE,IAAI,EAAE,CAAC;MACCsL,QAAQ,EAAE,2BAA2B;MACrCsJ,QAAQ,EAAE,WAAW;MACrBrJ,IAAI,EAAE;QACF,OAAO,EAAE,YAAY;QACrB,8BAA8B,EAAE;MACpC;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExL,IAAI,EAAE1E,EAAE,CAACqZ;IAAW,CAAC,EAAE;MAAE3U,IAAI,EAAE1E,EAAE,CAACsZ;IAAO,CAAC,EAAE;MAAE5U,IAAI,EAAEhD,IAAI,CAACC;IAAS,CAAC,EAAE;MAAE+C,IAAI,EAAEI,SAAS;MAAEC,UAAU,EAAE,CAAC;QACrIL,IAAI,EAAErE;MACV,CAAC,EAAE;QACCqE,IAAI,EAAEpE,MAAM;QACZqE,IAAI,EAAE,CAAC8T,yBAAyB;MACpC,CAAC;IAAE,CAAC,EAAE;MAAE/T,IAAI,EAAEI,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCL,IAAI,EAAErE;MACV,CAAC,EAAE;QACCqE,IAAI,EAAEpE,MAAM;QACZqE,IAAI,EAAE,CAACtC,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEkE,KAAK,EAAE,CAAC;MACpC7B,IAAI,EAAEhE,KAAK;MACXiE,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAE6U,SAAS,EAAE,CAAC;MACZ9U,IAAI,EAAEhE,KAAK;MACXiE,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE8P,QAAQ,EAAE,CAAC;MACX/P,IAAI,EAAEhE,KAAK;MACXiE,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEmQ,MAAM,EAAE,CAAC;MACTpQ,IAAI,EAAEhE,KAAK;MACXiE,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE6P,SAAS,EAAE,CAAC;MACZ9P,IAAI,EAAEhE,KAAK;MACXiE,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEuB,QAAQ,EAAE,CAAC;MACXxB,IAAI,EAAEhE,KAAK;MACXiE,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEqT,OAAO,EAAE,CAAC;MACVtT,IAAI,EAAEhE,KAAK;MACXiE,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8U,eAAe,CAAC;AAEtBA,eAAe,CAACnV,IAAI;EAAA,iBAA6FmV,eAAe;AAAA,CAAkD;AAClLA,eAAe,CAACjV,IAAI,kBA9xCmFxE,EAAE;EAAA,MA8xCSyZ,eAAe;EAAA,eAAiBf,SAAS;EAAA,UAAapV,eAAe;EAAA,UAAaoV,SAAS,EAAEpV,eAAe;AAAA,EAAI;AAClOmW,eAAe,CAAChV,IAAI,kBA/xCmFzE,EAAE;EAAA,UA+xCoCsD,eAAe,EAAEA,eAAe;AAAA,EAAI;AACjL;EAAA,mDAhyCuGtD,EAAE,mBAgyCTyZ,eAAe,EAAc,CAAC;IAClH/U,IAAI,EAAEtE,QAAQ;IACduE,IAAI,EAAE,CAAC;MACCC,OAAO,EAAE,CAACtB,eAAe,CAAC;MAC1BuB,OAAO,EAAE,CAAC6T,SAAS,EAAEpV,eAAe,CAAC;MACrCyN,YAAY,EAAE,CAAC2H,SAAS;IAC5B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgB,iBAAiB,CAAC;EACpBnW,WAAW,CAACuV,cAAc,EAAE;IACxB,IAAI,CAACA,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAAC1H,KAAK,GAAG,WAAW;IACxB;IACA,IAAI,CAAClL,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACyT,UAAU,GAAG,MAAM;EAC5B;AACJ;AACAD,iBAAiB,CAACpV,IAAI;EAAA,iBAA6FoV,iBAAiB,EA30C7B1Z,EAAE,mBA20C6CqC,qBAAqB;AAAA,CAA4D;AACvOqX,iBAAiB,CAACE,IAAI,kBA50CiF5Z,EAAE;EAAA,MA40CF0Z,iBAAiB;EAAA;EAAA;EAAA;EAAA;IAAA;MA50CjB1Z,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA,EA40CuwE;AACh3E;EAAA,mDA70CuGA,EAAE,mBA60CT0Z,iBAAiB,EAAc,CAAC;IACpHhV,IAAI,EAAE/D,SAAS;IACfgE,IAAI,EAAE,CAAC;MAAEkV,aAAa,EAAEjZ,iBAAiB,CAACkZ,IAAI;MAAEC,eAAe,EAAElZ,uBAAuB,CAACmZ,MAAM;MAAE/J,QAAQ,EAAE,qBAAqB;MAAEgK,QAAQ,EAAE,EAAE;MAAE/J,IAAI,EAAE;QAC1I,OAAO,EAAE,qBAAqB;QAC9B,2CAA2C,EAAE,2BAA2B;QACxE,qCAAqC,EAAE,qBAAqB;QAC5D,sCAAsC,EAAE,UAAU;QAClD,qCAAqC,EAAE,0BAA0B;QACjE,kCAAkC,EAAE,uBAAuB;QAC3D,iCAAiC,EAAE;MACvC,CAAC;MAAEgK,MAAM,EAAE,CAAC,6iDAA6iD;IAAE,CAAC;EACxkD,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExV,IAAI,EAAEI,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DL,IAAI,EAAErE;MACV,CAAC,EAAE;QACCqE,IAAI,EAAEpE,MAAM;QACZqE,IAAI,EAAE,CAACtC,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE+O,KAAK,EAAE,CAAC;MACpC1M,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEwF,QAAQ,EAAE,CAAC;MACXxB,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEiZ,UAAU,EAAE,CAAC;MACbjV,IAAI,EAAEhE;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyZ,uBAAuB,CAAC;AAE9BA,uBAAuB,CAAC7V,IAAI;EAAA,iBAA6F6V,uBAAuB;AAAA,CAAkD;AAClMA,uBAAuB,CAAC3V,IAAI,kBA/2C2ExE,EAAE;EAAA,MA+2CiBma,uBAAuB;EAAA,eAAiBT,iBAAiB;EAAA,UAAapW,eAAe;EAAA,UAAaoW,iBAAiB;AAAA,EAAI;AACjPS,uBAAuB,CAAC1V,IAAI,kBAh3C2EzE,EAAE;EAAA,UAg3CoDsD,eAAe;AAAA,EAAI;AAChL;EAAA,mDAj3CuGtD,EAAE,mBAi3CTma,uBAAuB,EAAc,CAAC;IAC1HzV,IAAI,EAAEtE,QAAQ;IACduE,IAAI,EAAE,CAAC;MACCC,OAAO,EAAE,CAACtB,eAAe,CAAC;MAC1BuB,OAAO,EAAE,CAAC6U,iBAAiB,CAAC;MAC5B3I,YAAY,EAAE,CAAC2I,iBAAiB;IACpC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,2BAA2B,GAAG,IAAIla,cAAc,CAAC,6BAA6B,CAAC;;AAErF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMma,qBAAqB,GAAGrU,aAAa,CAAC,MAAM,EACjD,CAAC;AACF;AACA,IAAIsU,wBAAwB,GAAG,CAAC;AAChC,MAAMC,gBAAgB,SAASF,qBAAqB,CAAC;EACjD9W,WAAW,CAACgE,MAAM,EAAE;IAChB,KAAK,EAAE;IACP;IACA,IAAI,CAACiT,QAAQ,GAAI,sBAAqBF,wBAAwB,EAAG,EAAC;IAClE,IAAI,CAACG,MAAM,GAAGlT,MAAM,EAAEmT,WAAW,IAAI,KAAK;EAC9C;AACJ;AACAH,gBAAgB,CAACjW,IAAI;EAAA,iBAA6FiW,gBAAgB,EAt7C3Bva,EAAE,mBAs7C2Coa,2BAA2B;AAAA,CAA4D;AAC3OG,gBAAgB,CAACvK,IAAI,kBAv7CkFhQ,EAAE;EAAA,MAu7CHua,gBAAgB;EAAA;IAAA;EAAA;EAAA,WAv7Cfva,EAAE;AAAA,EAu7CiF;AAC1L;EAAA,mDAx7CuGA,EAAE,mBAw7CTua,gBAAgB,EAAc,CAAC;IACnH7V,IAAI,EAAEjE;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiE,IAAI,EAAEI,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DL,IAAI,EAAEpE,MAAM;QACZqE,IAAI,EAAE,CAACyV,2BAA2B;MACtC,CAAC,EAAE;QACC1V,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEsa,KAAK,EAAE,CAAC;MACpCjW,IAAI,EAAEhE;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA,MAAMka,YAAY,GAAG,IAAI1a,cAAc,CAAC,aAAa,CAAC;AACtD;AACA;AACA;AACA,MAAM2a,WAAW,SAASN,gBAAgB,CAAC;AAE3CM,WAAW,CAACvW,IAAI;EAAA;EAAA;IAAA,gEA78CuFtE,EAAE,uBA68CI6a,WAAW,SAAXA,WAAW;EAAA;AAAA,GAAqD;AAC7KA,WAAW,CAACjB,IAAI,kBA98CuF5Z,EAAE;EAAA,MA88CR6a,WAAW;EAAA;EAAA;EAAA;EAAA;IAAA;MA98CL7a,EAAE;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE,oBA88CqS,CAAC;IAAEoP,OAAO,EAAEwL,YAAY;IAAEE,WAAW,EAAED;EAAY,CAAC,CAAC,GA98C5V7a,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,6BA88C8iB;MA98ChjBA,EAAE,UA88C0mB;MA98C5mBA,EAAE,gBA88CmoB;MA98CroBA,EAAE,eA88C0oB;MA98C5oBA,EAAE,mBA88CotB;IAAA;IAAA;MA98CttBA,EAAE,qDA88CwhB;MA98C1hBA,EAAE,+BA88C6iB;MA98C/iBA,EAAE,aA88C0mB;MA98C5mBA,EAAE,uCA88C0mB;IAAA;EAAA;EAAA;EAAA;EAAA;AAAA,EAAuqB;AAC13C;EAAA,mDA/8CuGA,EAAE,mBA+8CT6a,WAAW,EAAc,CAAC;IAC9GnW,IAAI,EAAE/D,SAAS;IACfgE,IAAI,EAAE,CAAC;MAAEsL,QAAQ,EAAE,cAAc;MAAEsJ,QAAQ,EAAE,aAAa;MAAEM,aAAa,EAAEjZ,iBAAiB,CAACkZ,IAAI;MAAEC,eAAe,EAAElZ,uBAAuB,CAACmZ,MAAM;MAAEe,MAAM,EAAE,CAAC,UAAU,CAAC;MAAE7K,IAAI,EAAE;QACpK,OAAO,EAAE,kBAAkB;QAC3B,aAAa,EAAE,yBAAyB;QACxC,sBAAsB,EAAE,qCAAqC;QAC7D,wBAAwB,EAAE;MAC9B,CAAC;MAAEZ,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEwL,YAAY;QAAEE,WAAW,EAAED;MAAY,CAAC,CAAC;MAAEZ,QAAQ,EAAE,iTAAiT;MAAEC,MAAM,EAAE,CAAC,6cAA6c;IAAE,CAAC;EACv2B,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,IAAIc,gBAAgB,GAAG,CAAC;AACxB;AACA,MAAMC,wBAAwB,CAAC;EAC3B1X,WAAW,EACX;EACA2X,MAAM,EACN;EACAC,WAAW,GAAG,KAAK,EAAE;IACjB,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;AACJ;AACA,MAAMC,cAAc,CAAC;EACjB;EACA,IAAIC,QAAQ,GAAG;IACX,OAAO,IAAI,CAACC,OAAO,IAAI,IAAI,CAACA,OAAO,CAACD,QAAQ;EAChD;EACA;EACA,IAAIE,QAAQ,GAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA;EACA,IAAItV,QAAQ,GAAG;IACX,OAAQ,IAAI,CAACuV,KAAK,IAAI,IAAI,CAACA,KAAK,CAACvV,QAAQ,IAAK,IAAI,CAACC,SAAS;EAChE;EACA,IAAID,QAAQ,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGpE,qBAAqB,CAACqE,KAAK,CAAC;EACjD;EACA;EACA,IAAIS,aAAa,GAAG;IAChB,OAAO,CAAC,EAAE,IAAI,CAACyU,OAAO,IAAI,IAAI,CAACA,OAAO,CAACzU,aAAa,CAAC;EACzD;EACA;EACA,IAAI6U,4BAA4B,GAAG;IAC/B,OAAO,CAAC,EAAE,IAAI,CAACJ,OAAO,IAAI,IAAI,CAACA,OAAO,CAACI,4BAA4B,CAAC;EACxE;EACAnY,WAAW,CAACoY,QAAQ,EAAEC,kBAAkB,EAAEN,OAAO,EAAEG,KAAK,EAAE;IACtD,IAAI,CAACE,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACN,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACK,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC1V,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC2V,oBAAoB,GAAG,EAAE;IAC9B;IACA,IAAI,CAACC,EAAE,GAAI,cAAaf,gBAAgB,EAAG,EAAC;IAC5C;IACA;IACA,IAAI,CAACgB,iBAAiB,GAAG,IAAIlb,YAAY,EAAE;IAC3C;IACA,IAAI,CAACmb,aAAa,GAAG,IAAI9Z,OAAO,EAAE;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI+Z,MAAM,GAAG;IACT,OAAO,IAAI,CAACL,OAAO;EACvB;EACA;AACJ;AACA;AACA;EACI,IAAIM,SAAS,GAAG;IACZ;IACA,OAAO,CAAC,IAAI,CAACC,KAAK,EAAEzV,aAAa,CAAC0V,WAAW,IAAI,EAAE,EAAEC,IAAI,EAAE;EAC/D;EACA;EACAC,MAAM,GAAG;IACL,IAAI,CAAC,IAAI,CAACf,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACI,kBAAkB,CAACY,YAAY,EAAE;MACtC,IAAI,CAACC,yBAAyB,EAAE;IACpC;EACJ;EACA;EACAC,QAAQ,GAAG;IACP,IAAI,IAAI,CAAClB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,GAAG,KAAK;MACtB,IAAI,CAACI,kBAAkB,CAACY,YAAY,EAAE;MACtC,IAAI,CAACC,yBAAyB,EAAE;IACpC;EACJ;EACA;EACAE,KAAK,CAACC,OAAO,EAAEC,OAAO,EAAE;IACpB;IACA;IACA,MAAMxM,OAAO,GAAG,IAAI,CAACyM,eAAe,EAAE;IACtC,IAAI,OAAOzM,OAAO,CAACsM,KAAK,KAAK,UAAU,EAAE;MACrCtM,OAAO,CAACsM,KAAK,CAACE,OAAO,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIE,eAAe,GAAG;IACd,IAAI,CAAC,IAAI,CAAClB,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAI;MACnB,IAAI,CAACD,kBAAkB,CAACY,YAAY,EAAE;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIQ,iBAAiB,GAAG;IAChB,IAAI,IAAI,CAACnB,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,GAAG,KAAK;MACpB,IAAI,CAACD,kBAAkB,CAACY,YAAY,EAAE;IAC1C;EACJ;EACA;EACAS,QAAQ,GAAG;IACP,OAAO,IAAI,CAACd,SAAS;EACzB;EACA;EACAe,cAAc,CAACpL,KAAK,EAAE;IAClB,IAAI,CAACA,KAAK,CAACqL,OAAO,KAAK7a,KAAK,IAAIwP,KAAK,CAACqL,OAAO,KAAK5a,KAAK,KAAK,CAACC,cAAc,CAACsP,KAAK,CAAC,EAAE;MAChF,IAAI,CAACsL,qBAAqB,EAAE;MAC5B;MACAtL,KAAK,CAACuL,cAAc,EAAE;IAC1B;EACJ;EACA;AACJ;AACA;AACA;EACID,qBAAqB,GAAG;IACpB,IAAI,CAAC,IAAI,CAAClX,QAAQ,EAAE;MAChB,IAAI,CAACsV,SAAS,GAAG,IAAI,CAACH,QAAQ,GAAG,CAAC,IAAI,CAACG,SAAS,GAAG,IAAI;MACvD,IAAI,CAACI,kBAAkB,CAACY,YAAY,EAAE;MACtC,IAAI,CAACC,yBAAyB,CAAC,IAAI,CAAC;IACxC;EACJ;EACA;EACA;EACA;EACA;EACAa,YAAY,GAAG;IACX,OAAO,IAAI,CAACpX,QAAQ,GAAG,IAAI,GAAG,GAAG;EACrC;EACA;EACA4W,eAAe,GAAG;IACd,OAAO,IAAI,CAACnB,QAAQ,CAAChV,aAAa;EACtC;EACA4W,kBAAkB,GAAG;IACjB;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC/B,SAAS,EAAE;MAChB,MAAMW,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,IAAIA,SAAS,KAAK,IAAI,CAACL,oBAAoB,EAAE;QACzC,IAAI,IAAI,CAACA,oBAAoB,EAAE;UAC3B,IAAI,CAACG,aAAa,CAAC/T,IAAI,EAAE;QAC7B;QACA,IAAI,CAAC4T,oBAAoB,GAAGK,SAAS;MACzC;IACJ;EACJ;EACAjD,WAAW,GAAG;IACV,IAAI,CAAC+C,aAAa,CAACpT,QAAQ,EAAE;EACjC;EACA;EACA4T,yBAAyB,CAACtB,WAAW,GAAG,KAAK,EAAE;IAC3C,IAAI,CAACa,iBAAiB,CAACwB,IAAI,CAAC,IAAIvC,wBAAwB,CAAC,IAAI,EAAEE,WAAW,CAAC,CAAC;EAChF;AACJ;AACAC,cAAc,CAAC9W,IAAI;EA5oDoFtE,EAAE;AAAA,CA4oD+E;AACxLob,cAAc,CAACpL,IAAI,kBA7oDoFhQ,EAAE;EAAA,MA6oDLob,cAAc;EAAA;IAAA;MA7oDXpb,EAAE;IAAA;IAAA;MAAA;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;AAAA,EA6oDwP;AACjW;EAAA,mDA9oDuGA,EAAE,mBA8oDTob,cAAc,EAAc,CAAC;IACjH1W,IAAI,EAAEjE;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiE,IAAI,EAAE1E,EAAE,CAACqZ;IAAW,CAAC,EAAE;MAAE3U,IAAI,EAAE1E,EAAE,CAACyd;IAAkB,CAAC,EAAE;MAAE/Y,IAAI,EAAEI;IAAU,CAAC,EAAE;MAAEJ,IAAI,EAAE6V;IAAiB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEnU,KAAK,EAAE,CAAC;MAC1K1B,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEqb,EAAE,EAAE,CAAC;MACLrX,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEwF,QAAQ,EAAE,CAAC;MACXxB,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEsb,iBAAiB,EAAE,CAAC;MACpBtX,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAEqb,KAAK,EAAE,CAAC;MACR1X,IAAI,EAAE1D,SAAS;MACf2D,IAAI,EAAE,CAAC,MAAM,EAAE;QAAE+Y,MAAM,EAAE;MAAK,CAAC;IACnC,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMC,SAAS,SAASvC,cAAc,CAAC;EACnC7X,WAAW,CAAC8M,OAAO,EAAEuN,iBAAiB,EAAErW,MAAM,EAAEkU,KAAK,EAAE;IACnD,KAAK,CAACpL,OAAO,EAAEuN,iBAAiB,EAAErW,MAAM,EAAEkU,KAAK,CAAC;EACpD;AACJ;AACAkC,SAAS,CAACrZ,IAAI;EAAA,iBAA6FqZ,SAAS,EApqDb3d,EAAE,mBAoqD6BA,EAAE,CAACqZ,UAAU,GApqD5CrZ,EAAE,mBAoqDuDA,EAAE,CAACyd,iBAAiB,GApqD7Ezd,EAAE,mBAoqDwFoa,2BAA2B,MApqDrHpa,EAAE,mBAoqDgJ4a,YAAY;AAAA,CAA4D;AACjU+C,SAAS,CAAC/D,IAAI,kBArqDyF5Z,EAAE;EAAA,MAqqDV2d,SAAS;EAAA;EAAA,oBAAwD,QAAQ;EAAA;EAAA;IAAA;MArqDjE3d,EAAE;QAAA,OAqqDV,2BAAuB;MAAA;QAAA,OAAvB,0BAAsB;MAAA;IAAA;IAAA;MArqDdA,EAAE;MAAFA,EAAE;MAAFA,EAAE;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,wFAqqDouB;MArqDtuBA,EAAE,gBAqqDqxB;MArqDvxBA,EAAE,gCAqqD20B;MArqD70BA,EAAE,mBAqqDo2B;MArqDt2BA,EAAE,eAqqD22B;MArqD72BA,EAAE,wFAqqD2oC;MArqD7oCA,EAAE,0DAqqD0zC;MArqD5zCA,EAAE,uBAqqDq/C;IAAA;IAAA;MArqDv/CA,EAAE,iCAqqDslB;MArqDxlBA,EAAE,aAqqD8/B;MArqDhgCA,EAAE,uFAqqD8/B;MArqDhgCA,EAAE,aAqqD6xC;MArqD/xCA,EAAE,kDAqqD6xC;MArqD/xCA,EAAE,aAqqDo7C;MArqDt7CA,EAAE,sDAqqDo7C;IAAA;EAAA;EAAA,eAAmiE0Y,SAAS,EAAwPnX,EAAE,CAACsc,IAAI,EAA6FnE,iBAAiB;EAAA;EAAA;EAAA;AAAA,EAAkL;AACxmI;EAAA,mDAtqDuG1Z,EAAE,mBAsqDT2d,SAAS,EAAc,CAAC;IAC5GjZ,IAAI,EAAE/D,SAAS;IACfgE,IAAI,EAAE,CAAC;MAAEsL,QAAQ,EAAE,YAAY;MAAEsJ,QAAQ,EAAE,WAAW;MAAErJ,IAAI,EAAE;QAClD,MAAM,EAAE,QAAQ;QAChB,iCAAiC,EAAE,UAAU;QAC7C,iCAAiC,EAAE,UAAU;QAC7C,+BAA+B,EAAE,QAAQ;QACzC,iCAAiC,EAAE,UAAU;QAC7C,MAAM,EAAE,IAAI;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,qBAAqB;QAC7C,SAAS,EAAE,yBAAyB;QACpC,WAAW,EAAE,wBAAwB;QACrC,OAAO,EAAE;MACb,CAAC;MAAE2J,aAAa,EAAEjZ,iBAAiB,CAACkZ,IAAI;MAAEC,eAAe,EAAElZ,uBAAuB,CAACmZ,MAAM;MAAEC,QAAQ,EAAE,w8BAAw8B;MAAEC,MAAM,EAAE,CAAC,u6DAAu6D;IAAE,CAAC;EAC9+F,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExV,IAAI,EAAE1E,EAAE,CAACqZ;IAAW,CAAC,EAAE;MAAE3U,IAAI,EAAE1E,EAAE,CAACyd;IAAkB,CAAC,EAAE;MAAE/Y,IAAI,EAAEI,SAAS;MAAEC,UAAU,EAAE,CAAC;QACvHL,IAAI,EAAErE;MACV,CAAC,EAAE;QACCqE,IAAI,EAAEpE,MAAM;QACZqE,IAAI,EAAE,CAACyV,2BAA2B;MACtC,CAAC;IAAE,CAAC,EAAE;MAAE1V,IAAI,EAAEmW,WAAW;MAAE9V,UAAU,EAAE,CAAC;QACpCL,IAAI,EAAErE;MACV,CAAC,EAAE;QACCqE,IAAI,EAAEpE,MAAM;QACZqE,IAAI,EAAE,CAACiW,YAAY;MACvB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkD,6BAA6B,CAACC,WAAW,EAAElB,OAAO,EAAEmB,YAAY,EAAE;EACvE,IAAIA,YAAY,CAACrT,MAAM,EAAE;IACrB,IAAIsT,YAAY,GAAGpB,OAAO,CAACqB,OAAO,EAAE;IACpC,IAAIC,MAAM,GAAGH,YAAY,CAACE,OAAO,EAAE;IACnC,IAAIE,YAAY,GAAG,CAAC;IACpB,KAAK,IAAIrT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgT,WAAW,GAAG,CAAC,EAAEhT,CAAC,EAAE,EAAE;MACtC,IAAIkT,YAAY,CAAClT,CAAC,CAAC,CAAC0Q,KAAK,IAAIwC,YAAY,CAAClT,CAAC,CAAC,CAAC0Q,KAAK,KAAK0C,MAAM,CAACC,YAAY,CAAC,EAAE;QACzEA,YAAY,EAAE;MAClB;IACJ;IACA,OAAOA,YAAY;EACvB;EACA,OAAO,CAAC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwB,CAACC,YAAY,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,WAAW,EAAE;EAC9F,IAAIH,YAAY,GAAGE,qBAAqB,EAAE;IACtC,OAAOF,YAAY;EACvB;EACA,IAAIA,YAAY,GAAGC,YAAY,GAAGC,qBAAqB,GAAGC,WAAW,EAAE;IACnE,OAAOtG,IAAI,CAAC5N,GAAG,CAAC,CAAC,EAAE+T,YAAY,GAAGG,WAAW,GAAGF,YAAY,CAAC;EACjE;EACA,OAAOC,qBAAqB;AAChC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,eAAe,CAAC;AAEtBA,eAAe,CAACpa,IAAI;EAAA,iBAA6Foa,eAAe;AAAA,CAAkD;AAClLA,eAAe,CAACla,IAAI,kBA1vDmFxE,EAAE;EAAA,MA0vDS0e,eAAe;EAAA,eAAiBf,SAAS,EAAE9C,WAAW;EAAA,UAAapB,eAAe,EAAEhY,YAAY,EAAE6B,eAAe,EAAE6W,uBAAuB;EAAA,UAAawD,SAAS,EAAE9C,WAAW;AAAA,EAAI;AACnS6D,eAAe,CAACja,IAAI,kBA3vDmFzE,EAAE;EAAA,UA2vDoCyZ,eAAe,EAAEhY,YAAY,EAAE6B,eAAe,EAAE6W,uBAAuB;AAAA,EAAI;AACxN;EAAA,mDA5vDuGna,EAAE,mBA4vDT0e,eAAe,EAAc,CAAC;IAClHha,IAAI,EAAEtE,QAAQ;IACduE,IAAI,EAAE,CAAC;MACCC,OAAO,EAAE,CAAC6U,eAAe,EAAEhY,YAAY,EAAE6B,eAAe,EAAE6W,uBAAuB,CAAC;MAClFtV,OAAO,EAAE,CAAC8Y,SAAS,EAAE9C,WAAW,CAAC;MACjC9J,YAAY,EAAE,CAAC4M,SAAS,EAAE9C,WAAW;IACzC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASpY,eAAe,EAAEK,kBAAkB,EAAEkG,WAAW,EAAE6G,iBAAiB,EAAE1M,sBAAsB,EAAEqH,gBAAgB,EAAE1B,eAAe,EAAEC,uBAAuB,EAAE+F,uBAAuB,EAAE8L,YAAY,EAAER,2BAA2B,EAAE3B,yBAAyB,EAAEnV,eAAe,EAAEyM,OAAO,EAAEe,aAAa,EAAEvB,mBAAmB,EAAEsL,WAAW,EAAE8C,SAAS,EAAEe,eAAe,EAAEzD,wBAAwB,EAAEvB,iBAAiB,EAAES,uBAAuB,EAAEzB,SAAS,EAAEe,eAAe,EAAEzO,iBAAiB,EAAEmE,gBAAgB,EAAE6B,SAAS,EAAEyC,cAAc,EAAEhE,4BAA4B,EAAEpO,OAAO,EAAEkZ,gBAAgB,EAAEa,cAAc,EAAE0C,6BAA6B,EAAEO,wBAAwB,EAAEnL,4BAA4B,EAAE7M,UAAU,EAAEO,kBAAkB,EAAEZ,aAAa,EAAEmB,eAAe,EAAEgB,gBAAgB,EAAEpB,aAAa,EAAEoJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}