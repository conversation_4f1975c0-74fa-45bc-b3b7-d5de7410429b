{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/Harmonia/oracul.client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { AppComponent } from './app.component';\ndescribe('AppComponent', () => {\n  let component;\n  let fixture;\n  let httpMock;\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      declarations: [AppComponent],\n      imports: [HttpClientTestingModule]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(AppComponent);\n    component = fixture.componentInstance;\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should create the app', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should retrieve weather forecasts from the server', () => {\n    const mockForecasts = [{\n      date: '2021-10-01',\n      temperatureC: 20,\n      temperatureF: 68,\n      summary: 'Mild'\n    }, {\n      date: '2021-10-02',\n      temperatureC: 25,\n      temperatureF: 77,\n      summary: 'Warm'\n    }];\n    component.ngOnInit();\n    const req = httpMock.expectOne('/weatherforecast');\n    expect(req.request.method).toEqual('GET');\n    req.flush(mockForecasts);\n    expect(component.forecasts).toEqual(mockForecasts);\n  });\n});", "map": {"version": 3, "mappings": ";AAAA,SAASA,uBAAuB,EAAEC,qBAAqB,QAAQ,8BAA8B;AAC7F,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAE9CC,QAAQ,CAAC,cAAc,EAAE,MAAK;EAC5B,IAAIC,SAAuB;EAC3B,IAAIC,OAAuC;EAC3C,IAAIC,QAA+B;EAEnCC,UAAU,iCAAC,aAAW;IACpB,MAAMN,OAAO,CAACO,sBAAsB,CAAC;MACnCC,YAAY,EAAE,CAACP,YAAY,CAAC;MAC5BQ,OAAO,EAAE,CAACX,uBAAuB;KAClC,CAAC,CAACY,iBAAiB,EAAE;EACxB,CAAC,EAAC;EAEFJ,UAAU,CAAC,MAAK;IACdF,OAAO,GAAGJ,OAAO,CAACW,eAAe,CAACV,YAAY,CAAC;IAC/CE,SAAS,GAAGC,OAAO,CAACQ,iBAAiB;IACrCP,QAAQ,GAAGL,OAAO,CAACa,MAAM,CAACd,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFe,SAAS,CAAC,MAAK;IACbT,QAAQ,CAACU,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,uBAAuB,EAAE,MAAK;IAC/BC,MAAM,CAACd,SAAS,CAAC,CAACe,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,mDAAmD,EAAE,MAAK;IAC3D,MAAMG,aAAa,GAAG,CACpB;MAAEC,IAAI,EAAE,YAAY;MAAEC,YAAY,EAAE,EAAE;MAAEC,YAAY,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAM,CAAE,EAC3E;MAAEH,IAAI,EAAE,YAAY;MAAEC,YAAY,EAAE,EAAE;MAAEC,YAAY,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAM,CAAE,CAC5E;IAEDpB,SAAS,CAACqB,QAAQ,EAAE;IAEpB,MAAMC,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,kBAAkB,CAAC;IAClDT,MAAM,CAACQ,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,OAAO,CAAC,KAAK,CAAC;IACzCJ,GAAG,CAACK,KAAK,CAACX,aAAa,CAAC;IAExBF,MAAM,CAACd,SAAS,CAAC4B,SAAS,CAAC,CAACF,OAAO,CAACV,aAAa,CAAC;EACpD,CAAC,CAAC;AACJ,CAAC,CAAC", "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "AppComponent", "describe", "component", "fixture", "httpMock", "beforeEach", "configureTestingModule", "declarations", "imports", "compileComponents", "createComponent", "componentInstance", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockForecasts", "date", "temperatureC", "temperatureF", "summary", "ngOnInit", "req", "expectOne", "request", "method", "toEqual", "flush", "forecasts"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\app.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\r\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\r\nimport { AppComponent } from './app.component';\r\n\r\ndescribe('AppComponent', () => {\r\n  let component: AppComponent;\r\n  let fixture: ComponentFixture<AppComponent>;\r\n  let httpMock: HttpTestingController;\r\n\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      declarations: [AppComponent],\r\n      imports: [HttpClientTestingModule]\r\n    }).compileComponents();\r\n  });\r\n\r\n  beforeEach(() => {\r\n    fixture = TestBed.createComponent(AppComponent);\r\n    component = fixture.componentInstance;\r\n    httpMock = TestBed.inject(HttpTestingController);\r\n  });\r\n\r\n  afterEach(() => {\r\n    httpMock.verify();\r\n  });\r\n\r\n  it('should create the app', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n\r\n  it('should retrieve weather forecasts from the server', () => {\r\n    const mockForecasts = [\r\n      { date: '2021-10-01', temperatureC: 20, temperatureF: 68, summary: 'Mild' },\r\n      { date: '2021-10-02', temperatureC: 25, temperatureF: 77, summary: 'Warm' }\r\n    ];\r\n\r\n    component.ngOnInit();\r\n\r\n    const req = httpMock.expectOne('/weatherforecast');\r\n    expect(req.request.method).toEqual('GET');\r\n    req.flush(mockForecasts);\r\n\r\n    expect(component.forecasts).toEqual(mockForecasts);\r\n  });\r\n});"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}