{"ast": null, "code": "/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { CompilerConfig, ResourceLoader } from '@angular/compiler';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, PACKAGE_ROOT_URL, Compiler, ViewEncapsulation, MissingTranslationStrategy, Injector, createPlatformFactory, platformCore, COMPILER_OPTIONS, CompilerFactory, Injectable, PLATFORM_ID, ɵglobal, Version } from '@angular/core';\nimport { ɵPLATFORM_BROWSER_ID } from '@angular/common';\nimport { ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS } from '@angular/platform-browser';\nconst ERROR_COLLECTOR_TOKEN = new InjectionToken('ErrorCollector');\n/**\n * A default provider for {@link PACKAGE_ROOT_URL} that maps to '/'.\n */\nconst DEFAULT_PACKAGE_URL_PROVIDER = {\n  provide: PACKAGE_ROOT_URL,\n  useValue: '/'\n};\nconst COMPILER_PROVIDERS = [{\n  provide: Compiler,\n  useFactory: () => new Compiler()\n}];\n/**\n * @publicApi\n *\n * @deprecated\n * Ivy JIT mode doesn't require accessing this symbol.\n * See [JIT API changes due to ViewEngine deprecation](guide/deprecations#jit-api-changes) for\n * additional context.\n */\nclass JitCompilerFactory {\n  /* @internal */\n  constructor(defaultOptions) {\n    const compilerOptions = {\n      useJit: true,\n      defaultEncapsulation: ViewEncapsulation.Emulated,\n      missingTranslation: MissingTranslationStrategy.Warning\n    };\n    this._defaultOptions = [compilerOptions, ...defaultOptions];\n  }\n  createCompiler(options = []) {\n    const opts = _mergeOptions(this._defaultOptions.concat(options));\n    const injector = Injector.create({\n      providers: [COMPILER_PROVIDERS, {\n        provide: CompilerConfig,\n        useFactory: () => {\n          return new CompilerConfig({\n            // let explicit values from the compiler options overwrite options\n            // from the app providers\n            useJit: opts.useJit,\n            // let explicit values from the compiler options overwrite options\n            // from the app providers\n            defaultEncapsulation: opts.defaultEncapsulation,\n            missingTranslation: opts.missingTranslation,\n            preserveWhitespaces: opts.preserveWhitespaces\n          });\n        },\n        deps: []\n      }, opts.providers]\n    });\n    return injector.get(Compiler);\n  }\n}\nfunction _mergeOptions(optionsArr) {\n  return {\n    useJit: _lastDefined(optionsArr.map(options => options.useJit)),\n    defaultEncapsulation: _lastDefined(optionsArr.map(options => options.defaultEncapsulation)),\n    providers: _mergeArrays(optionsArr.map(options => options.providers)),\n    missingTranslation: _lastDefined(optionsArr.map(options => options.missingTranslation)),\n    preserveWhitespaces: _lastDefined(optionsArr.map(options => options.preserveWhitespaces))\n  };\n}\nfunction _lastDefined(args) {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (args[i] !== undefined) {\n      return args[i];\n    }\n  }\n  return undefined;\n}\nfunction _mergeArrays(parts) {\n  const result = [];\n  parts.forEach(part => part && result.push(...part));\n  return result;\n}\n\n/**\n * A platform that included corePlatform and the compiler.\n *\n * @publicApi\n */\nconst platformCoreDynamic = createPlatformFactory(platformCore, 'coreDynamic', [{\n  provide: COMPILER_OPTIONS,\n  useValue: {},\n  multi: true\n}, {\n  provide: CompilerFactory,\n  useClass: JitCompilerFactory,\n  deps: [COMPILER_OPTIONS]\n}]);\nclass ResourceLoaderImpl extends ResourceLoader {\n  get(url) {\n    let resolve;\n    let reject;\n    const promise = new Promise((res, rej) => {\n      resolve = res;\n      reject = rej;\n    });\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', url, true);\n    xhr.responseType = 'text';\n    xhr.onload = function () {\n      // responseText is the old-school way of retrieving response (supported by IE8 & 9)\n      // response/responseType properties were introduced in ResourceLoader Level2 spec (supported\n      // by IE10)\n      const response = xhr.response || xhr.responseText;\n      // normalize IE9 bug (https://bugs.jquery.com/ticket/1450)\n      let status = xhr.status === 1223 ? 204 : xhr.status;\n      // fix status code when it is 0 (0 status is undocumented).\n      // Occurs when accessing file resources or on Android 4.1 stock browser\n      // while retrieving files from application cache.\n      if (status === 0) {\n        status = response ? 200 : 0;\n      }\n      if (200 <= status && status <= 300) {\n        resolve(response);\n      } else {\n        reject(`Failed to load ${url}`);\n      }\n    };\n    xhr.onerror = function () {\n      reject(`Failed to load ${url}`);\n    };\n    xhr.send();\n    return promise;\n  }\n}\nResourceLoaderImpl.ɵfac = /* @__PURE__ */function () {\n  let ɵResourceLoaderImpl_BaseFactory;\n  return function ResourceLoaderImpl_Factory(t) {\n    return (ɵResourceLoaderImpl_BaseFactory || (ɵResourceLoaderImpl_BaseFactory = i0.ɵɵgetInheritedFactory(ResourceLoaderImpl)))(t || ResourceLoaderImpl);\n  };\n}();\nResourceLoaderImpl.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ResourceLoaderImpl,\n  factory: ResourceLoaderImpl.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResourceLoaderImpl, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * @publicApi\n */\nconst INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS = [ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, {\n  provide: COMPILER_OPTIONS,\n  useValue: {\n    providers: [{\n      provide: ResourceLoader,\n      useClass: ResourceLoaderImpl,\n      deps: []\n    }]\n  },\n  multi: true\n}, {\n  provide: PLATFORM_ID,\n  useValue: ɵPLATFORM_BROWSER_ID\n}];\n\n/**\n * An implementation of ResourceLoader that uses a template cache to avoid doing an actual\n * ResourceLoader.\n *\n * The template cache needs to be built and loaded into window.$templateCache\n * via a separate mechanism.\n *\n * @publicApi\n *\n * @deprecated This was previously necessary in some cases to test AOT-compiled components with View\n *     Engine, but is no longer since Ivy.\n */\nclass CachedResourceLoader extends ResourceLoader {\n  constructor() {\n    super();\n    this._cache = ɵglobal.$templateCache;\n    if (this._cache == null) {\n      throw new Error('CachedResourceLoader: Template cache was not found in $templateCache.');\n    }\n  }\n  get(url) {\n    if (this._cache.hasOwnProperty(url)) {\n      return Promise.resolve(this._cache[url]);\n    } else {\n      return Promise.reject('CachedResourceLoader: Did not find cached template for ' + url);\n    }\n  }\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('15.2.10');\n\n/**\n * @publicApi\n *\n * @deprecated This was previously necessary in some cases to test AOT-compiled components with View\n *     Engine, but is no longer since Ivy.\n\n */\nconst RESOURCE_CACHE_PROVIDER = [{\n  provide: ResourceLoader,\n  useClass: CachedResourceLoader,\n  deps: []\n}];\n/**\n * @publicApi\n */\nconst platformBrowserDynamic = createPlatformFactory(platformCoreDynamic, 'browserDynamic', INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { JitCompilerFactory, RESOURCE_CACHE_PROVIDER, VERSION, platformBrowserDynamic, INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS, platformCoreDynamic as ɵplatformCoreDynamic };", "map": {"version": 3, "names": ["CompilerConfig", "Resource<PERSON><PERSON>der", "i0", "InjectionToken", "PACKAGE_ROOT_URL", "Compiler", "ViewEncapsulation", "MissingTranslationStrategy", "Injector", "createPlatformFactory", "platformCore", "COMPILER_OPTIONS", "CompilerFactory", "Injectable", "PLATFORM_ID", "ɵglobal", "Version", "ɵPLATFORM_BROWSER_ID", "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS", "ERROR_COLLECTOR_TOKEN", "DEFAULT_PACKAGE_URL_PROVIDER", "provide", "useValue", "COMPILER_PROVIDERS", "useFactory", "JitCompilerFactory", "constructor", "defaultOptions", "compilerOptions", "useJit", "defaultEncapsulation", "Emulated", "missingTranslation", "Warning", "_defaultOptions", "createCompiler", "options", "opts", "_mergeOptions", "concat", "injector", "create", "providers", "preserveWhitespaces", "deps", "get", "optionsArr", "_lastDefined", "map", "_mergeArrays", "args", "i", "length", "undefined", "parts", "result", "for<PERSON>ach", "part", "push", "platformCoreDynamic", "multi", "useClass", "ResourceLoaderImpl", "url", "resolve", "reject", "promise", "Promise", "res", "rej", "xhr", "XMLHttpRequest", "open", "responseType", "onload", "response", "responseText", "status", "onerror", "send", "ɵfac", "ɵprov", "type", "INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS", "CachedResourceLoader", "_cache", "$templateCache", "Error", "hasOwnProperty", "VERSION", "RESOURCE_CACHE_PROVIDER", "platformBrowserDynamic", "ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS", "ɵplatformCoreDynamic"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/platform-browser-dynamic/fesm2020/platform-browser-dynamic.mjs"], "sourcesContent": ["/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { CompilerConfig, ResourceLoader } from '@angular/compiler';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, PACKAGE_ROOT_URL, Compiler, ViewEncapsulation, MissingTranslationStrategy, Injector, createPlatformFactory, platformCore, COMPILER_OPTIONS, CompilerFactory, Injectable, PLATFORM_ID, ɵglobal, Version } from '@angular/core';\nimport { ɵPLATFORM_BROWSER_ID } from '@angular/common';\nimport { ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS } from '@angular/platform-browser';\n\nconst ERROR_COLLECTOR_TOKEN = new InjectionToken('ErrorCollector');\n/**\n * A default provider for {@link PACKAGE_ROOT_URL} that maps to '/'.\n */\nconst DEFAULT_PACKAGE_URL_PROVIDER = {\n    provide: PACKAGE_ROOT_URL,\n    useValue: '/'\n};\nconst COMPILER_PROVIDERS = [{ provide: Compiler, useFactory: () => new Compiler() }];\n/**\n * @publicApi\n *\n * @deprecated\n * Ivy JIT mode doesn't require accessing this symbol.\n * See [JIT API changes due to ViewEngine deprecation](guide/deprecations#jit-api-changes) for\n * additional context.\n */\nclass JitCompilerFactory {\n    /* @internal */\n    constructor(defaultOptions) {\n        const compilerOptions = {\n            useJit: true,\n            defaultEncapsulation: ViewEncapsulation.Emulated,\n            missingTranslation: MissingTranslationStrategy.Warning,\n        };\n        this._defaultOptions = [compilerOptions, ...defaultOptions];\n    }\n    createCompiler(options = []) {\n        const opts = _mergeOptions(this._defaultOptions.concat(options));\n        const injector = Injector.create({\n            providers: [\n                COMPILER_PROVIDERS, {\n                    provide: CompilerConfig,\n                    useFactory: () => {\n                        return new CompilerConfig({\n                            // let explicit values from the compiler options overwrite options\n                            // from the app providers\n                            useJit: opts.useJit,\n                            // let explicit values from the compiler options overwrite options\n                            // from the app providers\n                            defaultEncapsulation: opts.defaultEncapsulation,\n                            missingTranslation: opts.missingTranslation,\n                            preserveWhitespaces: opts.preserveWhitespaces,\n                        });\n                    },\n                    deps: []\n                },\n                opts.providers\n            ]\n        });\n        return injector.get(Compiler);\n    }\n}\nfunction _mergeOptions(optionsArr) {\n    return {\n        useJit: _lastDefined(optionsArr.map(options => options.useJit)),\n        defaultEncapsulation: _lastDefined(optionsArr.map(options => options.defaultEncapsulation)),\n        providers: _mergeArrays(optionsArr.map(options => options.providers)),\n        missingTranslation: _lastDefined(optionsArr.map(options => options.missingTranslation)),\n        preserveWhitespaces: _lastDefined(optionsArr.map(options => options.preserveWhitespaces)),\n    };\n}\nfunction _lastDefined(args) {\n    for (let i = args.length - 1; i >= 0; i--) {\n        if (args[i] !== undefined) {\n            return args[i];\n        }\n    }\n    return undefined;\n}\nfunction _mergeArrays(parts) {\n    const result = [];\n    parts.forEach((part) => part && result.push(...part));\n    return result;\n}\n\n/**\n * A platform that included corePlatform and the compiler.\n *\n * @publicApi\n */\nconst platformCoreDynamic = createPlatformFactory(platformCore, 'coreDynamic', [\n    { provide: COMPILER_OPTIONS, useValue: {}, multi: true },\n    { provide: CompilerFactory, useClass: JitCompilerFactory, deps: [COMPILER_OPTIONS] },\n]);\n\nclass ResourceLoaderImpl extends ResourceLoader {\n    get(url) {\n        let resolve;\n        let reject;\n        const promise = new Promise((res, rej) => {\n            resolve = res;\n            reject = rej;\n        });\n        const xhr = new XMLHttpRequest();\n        xhr.open('GET', url, true);\n        xhr.responseType = 'text';\n        xhr.onload = function () {\n            // responseText is the old-school way of retrieving response (supported by IE8 & 9)\n            // response/responseType properties were introduced in ResourceLoader Level2 spec (supported\n            // by IE10)\n            const response = xhr.response || xhr.responseText;\n            // normalize IE9 bug (https://bugs.jquery.com/ticket/1450)\n            let status = xhr.status === 1223 ? 204 : xhr.status;\n            // fix status code when it is 0 (0 status is undocumented).\n            // Occurs when accessing file resources or on Android 4.1 stock browser\n            // while retrieving files from application cache.\n            if (status === 0) {\n                status = response ? 200 : 0;\n            }\n            if (200 <= status && status <= 300) {\n                resolve(response);\n            }\n            else {\n                reject(`Failed to load ${url}`);\n            }\n        };\n        xhr.onerror = function () {\n            reject(`Failed to load ${url}`);\n        };\n        xhr.send();\n        return promise;\n    }\n}\nResourceLoaderImpl.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: ResourceLoaderImpl, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\nResourceLoaderImpl.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: ResourceLoaderImpl });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: ResourceLoaderImpl, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * @publicApi\n */\nconst INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS = [\n    ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS,\n    {\n        provide: COMPILER_OPTIONS,\n        useValue: { providers: [{ provide: ResourceLoader, useClass: ResourceLoaderImpl, deps: [] }] },\n        multi: true\n    },\n    { provide: PLATFORM_ID, useValue: ɵPLATFORM_BROWSER_ID },\n];\n\n/**\n * An implementation of ResourceLoader that uses a template cache to avoid doing an actual\n * ResourceLoader.\n *\n * The template cache needs to be built and loaded into window.$templateCache\n * via a separate mechanism.\n *\n * @publicApi\n *\n * @deprecated This was previously necessary in some cases to test AOT-compiled components with View\n *     Engine, but is no longer since Ivy.\n */\nclass CachedResourceLoader extends ResourceLoader {\n    constructor() {\n        super();\n        this._cache = ɵglobal.$templateCache;\n        if (this._cache == null) {\n            throw new Error('CachedResourceLoader: Template cache was not found in $templateCache.');\n        }\n    }\n    get(url) {\n        if (this._cache.hasOwnProperty(url)) {\n            return Promise.resolve(this._cache[url]);\n        }\n        else {\n            return Promise.reject('CachedResourceLoader: Did not find cached template for ' + url);\n        }\n    }\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('15.2.10');\n\n/**\n * @publicApi\n *\n * @deprecated This was previously necessary in some cases to test AOT-compiled components with View\n *     Engine, but is no longer since Ivy.\n\n */\nconst RESOURCE_CACHE_PROVIDER = [{ provide: ResourceLoader, useClass: CachedResourceLoader, deps: [] }];\n/**\n * @publicApi\n */\nconst platformBrowserDynamic = createPlatformFactory(platformCoreDynamic, 'browserDynamic', INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { JitCompilerFactory, RESOURCE_CACHE_PROVIDER, VERSION, platformBrowserDynamic, INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS, platformCoreDynamic as ɵplatformCoreDynamic };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,0BAA0B,EAAEC,QAAQ,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,UAAU,EAAEC,WAAW,EAAEC,OAAO,EAAEC,OAAO,QAAQ,eAAe;AACtP,SAASC,oBAAoB,QAAQ,iBAAiB;AACtD,SAASC,oCAAoC,QAAQ,2BAA2B;AAEhF,MAAMC,qBAAqB,GAAG,IAAIhB,cAAc,CAAC,gBAAgB,CAAC;AAClE;AACA;AACA;AACA,MAAMiB,4BAA4B,GAAG;EACjCC,OAAO,EAAEjB,gBAAgB;EACzBkB,QAAQ,EAAE;AACd,CAAC;AACD,MAAMC,kBAAkB,GAAG,CAAC;EAAEF,OAAO,EAAEhB,QAAQ;EAAEmB,UAAU,EAAE,MAAM,IAAInB,QAAQ;AAAG,CAAC,CAAC;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoB,kBAAkB,CAAC;EACrB;EACAC,WAAW,CAACC,cAAc,EAAE;IACxB,MAAMC,eAAe,GAAG;MACpBC,MAAM,EAAE,IAAI;MACZC,oBAAoB,EAAExB,iBAAiB,CAACyB,QAAQ;MAChDC,kBAAkB,EAAEzB,0BAA0B,CAAC0B;IACnD,CAAC;IACD,IAAI,CAACC,eAAe,GAAG,CAACN,eAAe,EAAE,GAAGD,cAAc,CAAC;EAC/D;EACAQ,cAAc,CAACC,OAAO,GAAG,EAAE,EAAE;IACzB,MAAMC,IAAI,GAAGC,aAAa,CAAC,IAAI,CAACJ,eAAe,CAACK,MAAM,CAACH,OAAO,CAAC,CAAC;IAChE,MAAMI,QAAQ,GAAGhC,QAAQ,CAACiC,MAAM,CAAC;MAC7BC,SAAS,EAAE,CACPnB,kBAAkB,EAAE;QAChBF,OAAO,EAAErB,cAAc;QACvBwB,UAAU,EAAE,MAAM;UACd,OAAO,IAAIxB,cAAc,CAAC;YACtB;YACA;YACA6B,MAAM,EAAEQ,IAAI,CAACR,MAAM;YACnB;YACA;YACAC,oBAAoB,EAAEO,IAAI,CAACP,oBAAoB;YAC/CE,kBAAkB,EAAEK,IAAI,CAACL,kBAAkB;YAC3CW,mBAAmB,EAAEN,IAAI,CAACM;UAC9B,CAAC,CAAC;QACN,CAAC;QACDC,IAAI,EAAE;MACV,CAAC,EACDP,IAAI,CAACK,SAAS;IAEtB,CAAC,CAAC;IACF,OAAOF,QAAQ,CAACK,GAAG,CAACxC,QAAQ,CAAC;EACjC;AACJ;AACA,SAASiC,aAAa,CAACQ,UAAU,EAAE;EAC/B,OAAO;IACHjB,MAAM,EAAEkB,YAAY,CAACD,UAAU,CAACE,GAAG,CAACZ,OAAO,IAAIA,OAAO,CAACP,MAAM,CAAC,CAAC;IAC/DC,oBAAoB,EAAEiB,YAAY,CAACD,UAAU,CAACE,GAAG,CAACZ,OAAO,IAAIA,OAAO,CAACN,oBAAoB,CAAC,CAAC;IAC3FY,SAAS,EAAEO,YAAY,CAACH,UAAU,CAACE,GAAG,CAACZ,OAAO,IAAIA,OAAO,CAACM,SAAS,CAAC,CAAC;IACrEV,kBAAkB,EAAEe,YAAY,CAACD,UAAU,CAACE,GAAG,CAACZ,OAAO,IAAIA,OAAO,CAACJ,kBAAkB,CAAC,CAAC;IACvFW,mBAAmB,EAAEI,YAAY,CAACD,UAAU,CAACE,GAAG,CAACZ,OAAO,IAAIA,OAAO,CAACO,mBAAmB,CAAC;EAC5F,CAAC;AACL;AACA,SAASI,YAAY,CAACG,IAAI,EAAE;EACxB,KAAK,IAAIC,CAAC,GAAGD,IAAI,CAACE,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACvC,IAAID,IAAI,CAACC,CAAC,CAAC,KAAKE,SAAS,EAAE;MACvB,OAAOH,IAAI,CAACC,CAAC,CAAC;IAClB;EACJ;EACA,OAAOE,SAAS;AACpB;AACA,SAASJ,YAAY,CAACK,KAAK,EAAE;EACzB,MAAMC,MAAM,GAAG,EAAE;EACjBD,KAAK,CAACE,OAAO,CAAEC,IAAI,IAAKA,IAAI,IAAIF,MAAM,CAACG,IAAI,CAAC,GAAGD,IAAI,CAAC,CAAC;EACrD,OAAOF,MAAM;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMI,mBAAmB,GAAGlD,qBAAqB,CAACC,YAAY,EAAE,aAAa,EAAE,CAC3E;EAAEW,OAAO,EAAEV,gBAAgB;EAAEW,QAAQ,EAAE,CAAC,CAAC;EAAEsC,KAAK,EAAE;AAAK,CAAC,EACxD;EAAEvC,OAAO,EAAET,eAAe;EAAEiD,QAAQ,EAAEpC,kBAAkB;EAAEmB,IAAI,EAAE,CAACjC,gBAAgB;AAAE,CAAC,CACvF,CAAC;AAEF,MAAMmD,kBAAkB,SAAS7D,cAAc,CAAC;EAC5C4C,GAAG,CAACkB,GAAG,EAAE;IACL,IAAIC,OAAO;IACX,IAAIC,MAAM;IACV,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MACtCL,OAAO,GAAGI,GAAG;MACbH,MAAM,GAAGI,GAAG;IAChB,CAAC,CAAC;IACF,MAAMC,GAAG,GAAG,IAAIC,cAAc,EAAE;IAChCD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAET,GAAG,EAAE,IAAI,CAAC;IAC1BO,GAAG,CAACG,YAAY,GAAG,MAAM;IACzBH,GAAG,CAACI,MAAM,GAAG,YAAY;MACrB;MACA;MACA;MACA,MAAMC,QAAQ,GAAGL,GAAG,CAACK,QAAQ,IAAIL,GAAG,CAACM,YAAY;MACjD;MACA,IAAIC,MAAM,GAAGP,GAAG,CAACO,MAAM,KAAK,IAAI,GAAG,GAAG,GAAGP,GAAG,CAACO,MAAM;MACnD;MACA;MACA;MACA,IAAIA,MAAM,KAAK,CAAC,EAAE;QACdA,MAAM,GAAGF,QAAQ,GAAG,GAAG,GAAG,CAAC;MAC/B;MACA,IAAI,GAAG,IAAIE,MAAM,IAAIA,MAAM,IAAI,GAAG,EAAE;QAChCb,OAAO,CAACW,QAAQ,CAAC;MACrB,CAAC,MACI;QACDV,MAAM,CAAE,kBAAiBF,GAAI,EAAC,CAAC;MACnC;IACJ,CAAC;IACDO,GAAG,CAACQ,OAAO,GAAG,YAAY;MACtBb,MAAM,CAAE,kBAAiBF,GAAI,EAAC,CAAC;IACnC,CAAC;IACDO,GAAG,CAACS,IAAI,EAAE;IACV,OAAOb,OAAO;EAClB;AACJ;AACAJ,kBAAkB,CAACkB,IAAI;EAAA;EAAA;IAAA,8EAA+E9E,EAAE,uBAAQ4D,kBAAkB,SAAlBA,kBAAkB;EAAA;AAAA,GAAsD;AACxLA,kBAAkB,CAACmB,KAAK,kBAD8E/E,EAAE;EAAA,OACY4D,kBAAkB;EAAA,SAAlBA,kBAAkB;AAAA,EAAG;AACzI;EAAA,mDAFsG5D,EAAE,mBAEZ4D,kBAAkB,EAAc,CAAC;IACjHoB,IAAI,EAAErE;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAMsE,2CAA2C,GAAG,CAChDjE,oCAAoC,EACpC;EACIG,OAAO,EAAEV,gBAAgB;EACzBW,QAAQ,EAAE;IAAEoB,SAAS,EAAE,CAAC;MAAErB,OAAO,EAAEpB,cAAc;MAAE4D,QAAQ,EAAEC,kBAAkB;MAAElB,IAAI,EAAE;IAAG,CAAC;EAAE,CAAC;EAC9FgB,KAAK,EAAE;AACX,CAAC,EACD;EAAEvC,OAAO,EAAEP,WAAW;EAAEQ,QAAQ,EAAEL;AAAqB,CAAC,CAC3D;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmE,oBAAoB,SAASnF,cAAc,CAAC;EAC9CyB,WAAW,GAAG;IACV,KAAK,EAAE;IACP,IAAI,CAAC2D,MAAM,GAAGtE,OAAO,CAACuE,cAAc;IACpC,IAAI,IAAI,CAACD,MAAM,IAAI,IAAI,EAAE;MACrB,MAAM,IAAIE,KAAK,CAAC,uEAAuE,CAAC;IAC5F;EACJ;EACA1C,GAAG,CAACkB,GAAG,EAAE;IACL,IAAI,IAAI,CAACsB,MAAM,CAACG,cAAc,CAACzB,GAAG,CAAC,EAAE;MACjC,OAAOI,OAAO,CAACH,OAAO,CAAC,IAAI,CAACqB,MAAM,CAACtB,GAAG,CAAC,CAAC;IAC5C,CAAC,MACI;MACD,OAAOI,OAAO,CAACF,MAAM,CAAC,yDAAyD,GAAGF,GAAG,CAAC;IAC1F;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0B,OAAO,GAAG,IAAIzE,OAAO,CAAC,SAAS,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0E,uBAAuB,GAAG,CAAC;EAAErE,OAAO,EAAEpB,cAAc;EAAE4D,QAAQ,EAAEuB,oBAAoB;EAAExC,IAAI,EAAE;AAAG,CAAC,CAAC;AACvG;AACA;AACA;AACA,MAAM+C,sBAAsB,GAAGlF,qBAAqB,CAACkD,mBAAmB,EAAE,gBAAgB,EAAEwB,2CAA2C,CAAC;;AAExI;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAAS1D,kBAAkB,EAAEiE,uBAAuB,EAAED,OAAO,EAAEE,sBAAsB,EAAER,2CAA2C,IAAIS,4CAA4C,EAAEjC,mBAAmB,IAAIkC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}