{"ast": null, "code": "/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, createPlatformFactory, NgModule } from '@angular/core';\nimport { TestComponentRenderer } from '@angular/core/testing';\nimport { ɵplatformCoreDynamic, ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS } from '@angular/platform-browser-dynamic';\nimport { BrowserTestingModule } from '@angular/platform-browser/testing';\nimport { ɵgetDOM, DOCUMENT } from '@angular/common';\n\n/**\n * A DOM based implementation of the TestComponentRenderer.\n */\nclass DOMTestComponentRenderer extends TestComponentRenderer {\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n  insertRootElement(rootElId) {\n    this.removeAllRootElements();\n    const rootElement = ɵgetDOM().getDefaultDocument().createElement('div');\n    rootElement.setAttribute('id', rootElId);\n    this._doc.body.appendChild(rootElement);\n  }\n  removeAllRootElements() {\n    // TODO(juliemr): can/should this be optional?\n    const oldRoots = this._doc.querySelectorAll('[id^=root]');\n    for (let i = 0; i < oldRoots.length; i++) {\n      ɵgetDOM().remove(oldRoots[i]);\n    }\n  }\n}\nDOMTestComponentRenderer.ɵfac = function DOMTestComponentRenderer_Factory(t) {\n  return new (t || DOMTestComponentRenderer)(i0.ɵɵinject(DOCUMENT));\n};\nDOMTestComponentRenderer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DOMTestComponentRenderer,\n  factory: DOMTestComponentRenderer.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DOMTestComponentRenderer, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Platform for dynamic tests\n *\n * @publicApi\n */\nconst platformCoreDynamicTesting = createPlatformFactory(ɵplatformCoreDynamic, 'coreDynamicTesting', []);\n\n/**\n * @publicApi\n */\nconst platformBrowserDynamicTesting = createPlatformFactory(platformCoreDynamicTesting, 'browserDynamicTesting', ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserDynamicTestingModule {}\nBrowserDynamicTestingModule.ɵfac = function BrowserDynamicTestingModule_Factory(t) {\n  return new (t || BrowserDynamicTestingModule)();\n};\nBrowserDynamicTestingModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BrowserDynamicTestingModule,\n  exports: [BrowserTestingModule]\n});\nBrowserDynamicTestingModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: TestComponentRenderer,\n    useClass: DOMTestComponentRenderer\n  }],\n  imports: [BrowserTestingModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserDynamicTestingModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserTestingModule],\n      providers: [{\n        provide: TestComponentRenderer,\n        useClass: DOMTestComponentRenderer\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserDynamicTestingModule, platformBrowserDynamicTesting, DOMTestComponentRenderer as ɵDOMTestComponentRenderer, platformCoreDynamicTesting as ɵplatformCoreDynamicTesting };", "map": {"version": 3, "names": ["i0", "Injectable", "Inject", "createPlatformFactory", "NgModule", "TestComponent<PERSON><PERSON><PERSON>", "ɵplatformCoreDynamic", "ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS", "BrowserTestingModule", "ɵgetDOM", "DOCUMENT", "DOMTestComponentRenderer", "constructor", "_doc", "insertRootElement", "rootElId", "removeAllRootElements", "rootElement", "getDefaultDocument", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "oldRoots", "querySelectorAll", "i", "length", "remove", "ɵfac", "ɵprov", "type", "undefined", "decorators", "args", "platformCoreDynamicTesting", "platformBrowserDynamicTesting", "BrowserDynamicTestingModule", "ɵmod", "ɵinj", "provide", "useClass", "exports", "providers", "ɵDOMTestComponentRenderer", "ɵplatformCoreDynamicTesting"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/platform-browser-dynamic/fesm2020/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, createPlatformFactory, NgModule } from '@angular/core';\nimport { TestComponentRenderer } from '@angular/core/testing';\nimport { ɵplatformCoreDynamic, ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS } from '@angular/platform-browser-dynamic';\nimport { BrowserTestingModule } from '@angular/platform-browser/testing';\nimport { ɵgetDOM, DOCUMENT } from '@angular/common';\n\n/**\n * A DOM based implementation of the TestComponentRenderer.\n */\nclass DOMTestComponentRenderer extends TestComponentRenderer {\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n    }\n    insertRootElement(rootElId) {\n        this.removeAllRootElements();\n        const rootElement = ɵgetDOM().getDefaultDocument().createElement('div');\n        rootElement.setAttribute('id', rootElId);\n        this._doc.body.appendChild(rootElement);\n    }\n    removeAllRootElements() {\n        // TODO(juliemr): can/should this be optional?\n        const oldRoots = this._doc.querySelectorAll('[id^=root]');\n        for (let i = 0; i < oldRoots.length; i++) {\n            ɵgetDOM().remove(oldRoots[i]);\n        }\n    }\n}\nDOMTestComponentRenderer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DOMTestComponentRenderer, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDOMTestComponentRenderer.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DOMTestComponentRenderer });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DOMTestComponentRenderer, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * Platform for dynamic tests\n *\n * @publicApi\n */\nconst platformCoreDynamicTesting = createPlatformFactory(ɵplatformCoreDynamic, 'coreDynamicTesting', []);\n\n/**\n * @publicApi\n */\nconst platformBrowserDynamicTesting = createPlatformFactory(platformCoreDynamicTesting, 'browserDynamicTesting', ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserDynamicTestingModule {\n}\nBrowserDynamicTestingModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserDynamicTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nBrowserDynamicTestingModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserDynamicTestingModule, exports: [BrowserTestingModule] });\nBrowserDynamicTestingModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserDynamicTestingModule, providers: [\n        { provide: TestComponentRenderer, useClass: DOMTestComponentRenderer },\n    ], imports: [BrowserTestingModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserDynamicTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserTestingModule],\n                    providers: [\n                        { provide: TestComponentRenderer, useClass: DOMTestComponentRenderer },\n                    ]\n                }]\n        }] });\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserDynamicTestingModule, platformBrowserDynamicTesting, DOMTestComponentRenderer as ɵDOMTestComponentRenderer, platformCoreDynamicTesting as ɵplatformCoreDynamicTesting };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,qBAAqB,EAAEC,QAAQ,QAAQ,eAAe;AACnF,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,oBAAoB,EAAEC,4CAA4C,QAAQ,mCAAmC;AACtH,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,OAAO,EAAEC,QAAQ,QAAQ,iBAAiB;;AAEnD;AACA;AACA;AACA,MAAMC,wBAAwB,SAASN,qBAAqB,CAAC;EACzDO,WAAW,CAACC,IAAI,EAAE;IACd,KAAK,EAAE;IACP,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACAC,iBAAiB,CAACC,QAAQ,EAAE;IACxB,IAAI,CAACC,qBAAqB,EAAE;IAC5B,MAAMC,WAAW,GAAGR,OAAO,EAAE,CAACS,kBAAkB,EAAE,CAACC,aAAa,CAAC,KAAK,CAAC;IACvEF,WAAW,CAACG,YAAY,CAAC,IAAI,EAAEL,QAAQ,CAAC;IACxC,IAAI,CAACF,IAAI,CAACQ,IAAI,CAACC,WAAW,CAACL,WAAW,CAAC;EAC3C;EACAD,qBAAqB,GAAG;IACpB;IACA,MAAMO,QAAQ,GAAG,IAAI,CAACV,IAAI,CAACW,gBAAgB,CAAC,YAAY,CAAC;IACzD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACtChB,OAAO,EAAE,CAACkB,MAAM,CAACJ,QAAQ,CAACE,CAAC,CAAC,CAAC;IACjC;EACJ;AACJ;AACAd,wBAAwB,CAACiB,IAAI;EAAA,iBAAyFjB,wBAAwB,EAAlCX,EAAE,UAAkDU,QAAQ;AAAA,CAA6C;AACrNC,wBAAwB,CAACkB,KAAK,kBAD8E7B,EAAE;EAAA,OACYW,wBAAwB;EAAA,SAAxBA,wBAAwB;AAAA,EAAG;AACrJ;EAAA,mDAF4GX,EAAE,mBAElBW,wBAAwB,EAAc,CAAC;IACvHmB,IAAI,EAAE7B;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE6B,IAAI,EAAEC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DF,IAAI,EAAE5B,MAAM;QACZ+B,IAAI,EAAE,CAACvB,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA,MAAMwB,0BAA0B,GAAG/B,qBAAqB,CAACG,oBAAoB,EAAE,oBAAoB,EAAE,EAAE,CAAC;;AAExG;AACA;AACA;AACA,MAAM6B,6BAA6B,GAAGhC,qBAAqB,CAAC+B,0BAA0B,EAAE,uBAAuB,EAAE3B,4CAA4C,CAAC;AAC9J;AACA;AACA;AACA;AACA;AACA,MAAM6B,2BAA2B,CAAC;AAElCA,2BAA2B,CAACR,IAAI;EAAA,iBAAyFQ,2BAA2B;AAAA,CAAkD;AACtMA,2BAA2B,CAACC,IAAI,kBA5B4ErC,EAAE;EAAA,MA4BYoC,2BAA2B;EAAA,UAAY5B,oBAAoB;AAAA,EAAI;AACzL4B,2BAA2B,CAACE,IAAI,kBA7B4EtC,EAAE;EAAA,WA6BoD,CAC1J;IAAEuC,OAAO,EAAElC,qBAAqB;IAAEmC,QAAQ,EAAE7B;EAAyB,CAAC,CACzE;EAAA,UAAYH,oBAAoB;AAAA,EAAI;AACzC;EAAA,mDAhC4GR,EAAE,mBAgClBoC,2BAA2B,EAAc,CAAC;IAC1HN,IAAI,EAAE1B,QAAQ;IACd6B,IAAI,EAAE,CAAC;MACCQ,OAAO,EAAE,CAACjC,oBAAoB,CAAC;MAC/BkC,SAAS,EAAE,CACP;QAAEH,OAAO,EAAElC,qBAAqB;QAAEmC,QAAQ,EAAE7B;MAAyB,CAAC;IAE9E,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASyB,2BAA2B,EAAED,6BAA6B,EAAExB,wBAAwB,IAAIgC,yBAAyB,EAAET,0BAA0B,IAAIU,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}