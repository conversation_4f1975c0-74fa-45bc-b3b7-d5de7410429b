{"ast": null, "code": "function cov_2ojcotxf7q() {\n  var path = \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\environments\\\\environment.ts\";\n  var hash = \"e7af0a3afeb63c63fb354c29e77ec7cd2c21c376\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\environments\\\\environment.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 1,\n          column: 27\n        },\n        end: {\n          line: 12,\n          column: 1\n        }\n      }\n    },\n    fnMap: {},\n    branchMap: {},\n    s: {\n      \"0\": 0\n    },\n    f: {},\n    b: {},\n    inputSourceMap: {\n      version: 3,\n      file: \"environment.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\environments\\\\environment.ts\"],\n      names: [],\n      mappings: \"AAAA,MAAM,CAAC,MAAM,WAAW,GAAG;IACzB,UAAU,EAAE,KAAK;IACjB,MAAM,EAAE,4BAA4B;IACpC,KAAK,EAAE;QACL,MAAM,EAAE;YACN,QAAQ,EAAE,kDAAkD;SAC7D;QACD,QAAQ,EAAE;YACR,KAAK,EAAE,sBAAsB;SAC9B;KACF;CACF,CAAC\",\n      sourcesContent: [\"export const environment = {\\r\\n  production: false,\\r\\n  apiUrl: 'https://localhost:7027/api',\\r\\n  oauth: {\\r\\n    google: {\\r\\n      clientId: 'your-google-client-id.apps.googleusercontent.com'\\r\\n    },\\r\\n    facebook: {\\r\\n      appId: 'your-facebook-app-id'\\r\\n    }\\r\\n  }\\r\\n};\\r\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"e7af0a3afeb63c63fb354c29e77ec7cd2c21c376\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_2ojcotxf7q = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_2ojcotxf7q();\nexport const environment = (cov_2ojcotxf7q().s[0]++, {\n  production: false,\n  apiUrl: 'https://localhost:7027/api',\n  oauth: {\n    google: {\n      clientId: 'your-google-client-id.apps.googleusercontent.com'\n    },\n    facebook: {\n      appId: 'your-facebook-app-id'\n    }\n  }\n});", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAO,MAAMA,WAAW,6BAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,4BAA4B;EACpCC,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,QAAQ,EAAE;KACX;IACDC,QAAQ,EAAE;MACRC,KAAK,EAAE;;;CAGZ", "names": ["environment", "production", "apiUrl", "o<PERSON>h", "google", "clientId", "facebook", "appId"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\environments\\environment.ts"], "sourcesContent": ["export const environment = {\r\n  production: false,\r\n  apiUrl: 'https://localhost:7027/api',\r\n  oauth: {\r\n    google: {\r\n      clientId: 'your-google-client-id.apps.googleusercontent.com'\r\n    },\r\n    facebook: {\r\n      appId: 'your-facebook-app-id'\r\n    }\r\n  }\r\n};\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}