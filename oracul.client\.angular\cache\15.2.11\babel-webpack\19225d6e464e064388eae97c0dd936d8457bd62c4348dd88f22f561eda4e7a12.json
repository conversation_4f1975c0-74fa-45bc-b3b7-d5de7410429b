{"ast": null, "code": "function cov_2e0oh17w9u() {\n  var path = \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\auth\\\\services\\\\token.service.ts\";\n  var hash = \"d00710c491e6980a96841b6cecbce7a58505d270\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\auth\\\\services\\\\token.service.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 3,\n          column: 19\n        },\n        end: {\n          line: 111,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 5,\n          column: 8\n        },\n        end: {\n          line: 5,\n          column: 40\n        }\n      },\n      \"2\": {\n        start: {\n          line: 6,\n          column: 8\n        },\n        end: {\n          line: 6,\n          column: 49\n        }\n      },\n      \"3\": {\n        start: {\n          line: 7,\n          column: 8\n        },\n        end: {\n          line: 7,\n          column: 36\n        }\n      },\n      \"4\": {\n        start: {\n          line: 8,\n          column: 8\n        },\n        end: {\n          line: 8,\n          column: 45\n        }\n      },\n      \"5\": {\n        start: {\n          line: 12,\n          column: 8\n        },\n        end: {\n          line: 12,\n          column: 94\n        }\n      },\n      \"6\": {\n        start: {\n          line: 15,\n          column: 8\n        },\n        end: {\n          line: 22,\n          column: 9\n        }\n      },\n      \"7\": {\n        start: {\n          line: 16,\n          column: 12\n        },\n        end: {\n          line: 16,\n          column: 56\n        }\n      },\n      \"8\": {\n        start: {\n          line: 17,\n          column: 12\n        },\n        end: {\n          line: 17,\n          column: 54\n        }\n      },\n      \"9\": {\n        start: {\n          line: 20,\n          column: 12\n        },\n        end: {\n          line: 20,\n          column: 58\n        }\n      },\n      \"10\": {\n        start: {\n          line: 21,\n          column: 12\n        },\n        end: {\n          line: 21,\n          column: 52\n        }\n      },\n      \"11\": {\n        start: {\n          line: 25,\n          column: 8\n        },\n        end: {\n          line: 25,\n          column: 110\n        }\n      },\n      \"12\": {\n        start: {\n          line: 28,\n          column: 8\n        },\n        end: {\n          line: 35,\n          column: 9\n        }\n      },\n      \"13\": {\n        start: {\n          line: 29,\n          column: 12\n        },\n        end: {\n          line: 29,\n          column: 64\n        }\n      },\n      \"14\": {\n        start: {\n          line: 30,\n          column: 12\n        },\n        end: {\n          line: 30,\n          column: 62\n        }\n      },\n      \"15\": {\n        start: {\n          line: 33,\n          column: 12\n        },\n        end: {\n          line: 33,\n          column: 66\n        }\n      },\n      \"16\": {\n        start: {\n          line: 34,\n          column: 12\n        },\n        end: {\n          line: 34,\n          column: 60\n        }\n      },\n      \"17\": {\n        start: {\n          line: 38,\n          column: 24\n        },\n        end: {\n          line: 38,\n          column: 44\n        }\n      },\n      \"18\": {\n        start: {\n          line: 39,\n          column: 8\n        },\n        end: {\n          line: 46,\n          column: 9\n        }\n      },\n      \"19\": {\n        start: {\n          line: 40,\n          column: 12\n        },\n        end: {\n          line: 40,\n          column: 57\n        }\n      },\n      \"20\": {\n        start: {\n          line: 41,\n          column: 12\n        },\n        end: {\n          line: 41,\n          column: 53\n        }\n      },\n      \"21\": {\n        start: {\n          line: 44,\n          column: 12\n        },\n        end: {\n          line: 44,\n          column: 59\n        }\n      },\n      \"22\": {\n        start: {\n          line: 45,\n          column: 12\n        },\n        end: {\n          line: 45,\n          column: 51\n        }\n      },\n      \"23\": {\n        start: {\n          line: 49,\n          column: 24\n        },\n        end: {\n          line: 49,\n          column: 100\n        }\n      },\n      \"24\": {\n        start: {\n          line: 50,\n          column: 8\n        },\n        end: {\n          line: 50,\n          column: 52\n        }\n      },\n      \"25\": {\n        start: {\n          line: 53,\n          column: 8\n        },\n        end: {\n          line: 58,\n          column: 9\n        }\n      },\n      \"26\": {\n        start: {\n          line: 54,\n          column: 12\n        },\n        end: {\n          line: 54,\n          column: 63\n        }\n      },\n      \"27\": {\n        start: {\n          line: 57,\n          column: 12\n        },\n        end: {\n          line: 57,\n          column: 58\n        }\n      },\n      \"28\": {\n        start: {\n          line: 61,\n          column: 8\n        },\n        end: {\n          line: 61,\n          column: 69\n        }\n      },\n      \"29\": {\n        start: {\n          line: 64,\n          column: 8\n        },\n        end: {\n          line: 75,\n          column: 9\n        }\n      },\n      \"30\": {\n        start: {\n          line: 65,\n          column: 28\n        },\n        end: {\n          line: 65,\n          column: 68\n        }\n      },\n      \"31\": {\n        start: {\n          line: 66,\n          column: 24\n        },\n        end: {\n          line: 66,\n          column: 35\n        }\n      },\n      \"32\": {\n        start: {\n          line: 67,\n          column: 12\n        },\n        end: {\n          line: 68,\n          column: 28\n        }\n      },\n      \"33\": {\n        start: {\n          line: 68,\n          column: 16\n        },\n        end: {\n          line: 68,\n          column: 28\n        }\n      },\n      \"34\": {\n        start: {\n          line: 69,\n          column: 24\n        },\n        end: {\n          line: 69,\n          column: 53\n        }\n      },\n      \"35\": {\n        start: {\n          line: 70,\n          column: 12\n        },\n        end: {\n          line: 70,\n          column: 29\n        }\n      },\n      \"36\": {\n        start: {\n          line: 73,\n          column: 12\n        },\n        end: {\n          line: 73,\n          column: 57\n        }\n      },\n      \"37\": {\n        start: {\n          line: 74,\n          column: 12\n        },\n        end: {\n          line: 74,\n          column: 24\n        }\n      },\n      \"38\": {\n        start: {\n          line: 78,\n          column: 8\n        },\n        end: {\n          line: 95,\n          column: 9\n        }\n      },\n      \"39\": {\n        start: {\n          line: 79,\n          column: 28\n        },\n        end: {\n          line: 79,\n          column: 47\n        }\n      },\n      \"40\": {\n        start: {\n          line: 80,\n          column: 12\n        },\n        end: {\n          line: 81,\n          column: 28\n        }\n      },\n      \"41\": {\n        start: {\n          line: 81,\n          column: 16\n        },\n        end: {\n          line: 81,\n          column: 28\n        }\n      },\n      \"42\": {\n        start: {\n          line: 83,\n          column: 27\n        },\n        end: {\n          line: 83,\n          column: 72\n        }\n      },\n      \"43\": {\n        start: {\n          line: 84,\n          column: 27\n        },\n        end: {\n          line: 84,\n          column: 75\n        }\n      },\n      \"44\": {\n        start: {\n          line: 86,\n          column: 27\n        },\n        end: {\n          line: 86,\n          column: 39\n        }\n      },\n      \"45\": {\n        start: {\n          line: 87,\n          column: 25\n        },\n        end: {\n          line: 89,\n          column: 26\n        }\n      },\n      \"46\": {\n        start: {\n          line: 88,\n          column: 35\n        },\n        end: {\n          line: 88,\n          column: 86\n        }\n      },\n      \"47\": {\n        start: {\n          line: 90,\n          column: 12\n        },\n        end: {\n          line: 90,\n          column: 24\n        }\n      },\n      \"48\": {\n        start: {\n          line: 93,\n          column: 12\n        },\n        end: {\n          line: 93,\n          column: 62\n        }\n      },\n      \"49\": {\n        start: {\n          line: 94,\n          column: 12\n        },\n        end: {\n          line: 94,\n          column: 24\n        }\n      },\n      \"50\": {\n        start: {\n          line: 99,\n          column: 8\n        },\n        end: {\n          line: 99,\n          column: 48\n        }\n      },\n      \"51\": {\n        start: {\n          line: 100,\n          column: 8\n        },\n        end: {\n          line: 100,\n          column: 56\n        }\n      },\n      \"52\": {\n        start: {\n          line: 101,\n          column: 8\n        },\n        end: {\n          line: 101,\n          column: 47\n        }\n      },\n      \"53\": {\n        start: {\n          line: 102,\n          column: 8\n        },\n        end: {\n          line: 102,\n          column: 54\n        }\n      },\n      \"54\": {\n        start: {\n          line: 103,\n          column: 8\n        },\n        end: {\n          line: 103,\n          column: 50\n        }\n      },\n      \"55\": {\n        start: {\n          line: 104,\n          column: 8\n        },\n        end: {\n          line: 104,\n          column: 58\n        }\n      },\n      \"56\": {\n        start: {\n          line: 105,\n          column: 8\n        },\n        end: {\n          line: 105,\n          column: 49\n        }\n      },\n      \"57\": {\n        start: {\n          line: 108,\n          column: 22\n        },\n        end: {\n          line: 108,\n          column: 37\n        }\n      },\n      \"58\": {\n        start: {\n          line: 109,\n          column: 8\n        },\n        end: {\n          line: 109,\n          column: 54\n        }\n      },\n      \"59\": {\n        start: {\n          line: 112,\n          column: 0\n        },\n        end: {\n          line: 116,\n          column: 17\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 4,\n            column: 4\n          },\n          end: {\n            line: 4,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 4,\n            column: 18\n          },\n          end: {\n            line: 9,\n            column: 5\n          }\n        },\n        line: 4\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 11,\n            column: 4\n          },\n          end: {\n            line: 11,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 11,\n            column: 15\n          },\n          end: {\n            line: 13,\n            column: 5\n          }\n        },\n        line: 11\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 14,\n            column: 4\n          },\n          end: {\n            line: 14,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 14,\n            column: 40\n          },\n          end: {\n            line: 23,\n            column: 5\n          }\n        },\n        line: 14\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 24,\n            column: 4\n          },\n          end: {\n            line: 24,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 24,\n            column: 22\n          },\n          end: {\n            line: 26,\n            column: 5\n          }\n        },\n        line: 24\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 27,\n            column: 4\n          },\n          end: {\n            line: 27,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 27,\n            column: 47\n          },\n          end: {\n            line: 36,\n            column: 5\n          }\n        },\n        line: 27\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 37,\n            column: 4\n          },\n          end: {\n            line: 37,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 37,\n            column: 38\n          },\n          end: {\n            line: 47,\n            column: 5\n          }\n        },\n        line: 37\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 48,\n            column: 4\n          },\n          end: {\n            line: 48,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 48,\n            column: 20\n          },\n          end: {\n            line: 51,\n            column: 5\n          }\n        },\n        line: 48\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 52,\n            column: 4\n          },\n          end: {\n            line: 52,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 52,\n            column: 30\n          },\n          end: {\n            line: 59,\n            column: 5\n          }\n        },\n        line: 52\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 60,\n            column: 4\n          },\n          end: {\n            line: 60,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 60,\n            column: 20\n          },\n          end: {\n            line: 62,\n            column: 5\n          }\n        },\n        line: 60\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 63,\n            column: 4\n          },\n          end: {\n            line: 63,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 63,\n            column: 26\n          },\n          end: {\n            line: 76,\n            column: 5\n          }\n        },\n        line: 63\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 77,\n            column: 4\n          },\n          end: {\n            line: 77,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 77,\n            column: 28\n          },\n          end: {\n            line: 96,\n            column: 5\n          }\n        },\n        line: 77\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 88,\n            column: 30\n          },\n          end: {\n            line: 88,\n            column: 31\n          }\n        },\n        loc: {\n          start: {\n            line: 88,\n            column: 35\n          },\n          end: {\n            line: 88,\n            column: 86\n          }\n        },\n        line: 88\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 97,\n            column: 4\n          },\n          end: {\n            line: 97,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 97,\n            column: 21\n          },\n          end: {\n            line: 106,\n            column: 5\n          }\n        },\n        line: 97\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 107,\n            column: 4\n          },\n          end: {\n            line: 107,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 107,\n            column: 22\n          },\n          end: {\n            line: 110,\n            column: 5\n          }\n        },\n        line: 107\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 12,\n            column: 15\n          },\n          end: {\n            line: 12,\n            column: 93\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 12,\n            column: 15\n          },\n          end: {\n            line: 12,\n            column: 51\n          }\n        }, {\n          start: {\n            line: 12,\n            column: 55\n          },\n          end: {\n            line: 12,\n            column: 93\n          }\n        }],\n        line: 12\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 14,\n            column: 20\n          },\n          end: {\n            line: 14,\n            column: 38\n          }\n        },\n        type: \"default-arg\",\n        locations: [{\n          start: {\n            line: 14,\n            column: 33\n          },\n          end: {\n            line: 14,\n            column: 38\n          }\n        }],\n        line: 14\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 15,\n            column: 8\n          },\n          end: {\n            line: 22,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 15,\n            column: 8\n          },\n          end: {\n            line: 22,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 19,\n            column: 13\n          },\n          end: {\n            line: 22,\n            column: 9\n          }\n        }],\n        line: 15\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 25,\n            column: 15\n          },\n          end: {\n            line: 25,\n            column: 109\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 25,\n            column: 15\n          },\n          end: {\n            line: 25,\n            column: 59\n          }\n        }, {\n          start: {\n            line: 25,\n            column: 63\n          },\n          end: {\n            line: 25,\n            column: 109\n          }\n        }],\n        line: 25\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 27,\n            column: 27\n          },\n          end: {\n            line: 27,\n            column: 45\n          }\n        },\n        type: \"default-arg\",\n        locations: [{\n          start: {\n            line: 27,\n            column: 40\n          },\n          end: {\n            line: 27,\n            column: 45\n          }\n        }],\n        line: 27\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 28,\n            column: 8\n          },\n          end: {\n            line: 35,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 28,\n            column: 8\n          },\n          end: {\n            line: 35,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 32,\n            column: 13\n          },\n          end: {\n            line: 35,\n            column: 9\n          }\n        }],\n        line: 28\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 37,\n            column: 18\n          },\n          end: {\n            line: 37,\n            column: 36\n          }\n        },\n        type: \"default-arg\",\n        locations: [{\n          start: {\n            line: 37,\n            column: 31\n          },\n          end: {\n            line: 37,\n            column: 36\n          }\n        }],\n        line: 37\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 39,\n            column: 8\n          },\n          end: {\n            line: 46,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 39,\n            column: 8\n          },\n          end: {\n            line: 46,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 43,\n            column: 13\n          },\n          end: {\n            line: 46,\n            column: 9\n          }\n        }],\n        line: 39\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 49,\n            column: 24\n          },\n          end: {\n            line: 49,\n            column: 100\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 49,\n            column: 24\n          },\n          end: {\n            line: 49,\n            column: 59\n          }\n        }, {\n          start: {\n            line: 49,\n            column: 63\n          },\n          end: {\n            line: 49,\n            column: 100\n          }\n        }],\n        line: 49\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 50,\n            column: 15\n          },\n          end: {\n            line: 50,\n            column: 51\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 50,\n            column: 25\n          },\n          end: {\n            line: 50,\n            column: 44\n          }\n        }, {\n          start: {\n            line: 50,\n            column: 47\n          },\n          end: {\n            line: 50,\n            column: 51\n          }\n        }],\n        line: 50\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 53,\n            column: 8\n          },\n          end: {\n            line: 58,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 53,\n            column: 8\n          },\n          end: {\n            line: 58,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 56,\n            column: 13\n          },\n          end: {\n            line: 58,\n            column: 9\n          }\n        }],\n        line: 53\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 67,\n            column: 12\n          },\n          end: {\n            line: 68,\n            column: 28\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 67,\n            column: 12\n          },\n          end: {\n            line: 68,\n            column: 28\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 67\n      },\n      \"12\": {\n        loc: {\n          start: {\n            line: 80,\n            column: 12\n          },\n          end: {\n            line: 81,\n            column: 28\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 80,\n            column: 12\n          },\n          end: {\n            line: 81,\n            column: 28\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 80\n      },\n      \"13\": {\n        loc: {\n          start: {\n            line: 109,\n            column: 15\n          },\n          end: {\n            line: 109,\n            column: 53\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 109,\n            column: 15\n          },\n          end: {\n            line: 109,\n            column: 22\n          }\n        }, {\n          start: {\n            line: 109,\n            column: 26\n          },\n          end: {\n            line: 109,\n            column: 53\n          }\n        }],\n        line: 109\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0,\n      \"59\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0],\n      \"5\": [0, 0],\n      \"6\": [0],\n      \"7\": [0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0],\n      \"10\": [0, 0],\n      \"11\": [0, 0],\n      \"12\": [0, 0],\n      \"13\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"token.service.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\auth\\\\services\\\\token.service.ts\"],\n      names: [],\n      mappings: \";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAKpC,IAAM,YAAY,GAAlB,MAAM,YAAY;IAAlB;QACY,cAAS,GAAG,cAAc,CAAC;QAC3B,sBAAiB,GAAG,eAAe,CAAC;QACpC,aAAQ,GAAG,WAAW,CAAC;QACvB,oBAAe,GAAG,aAAa,CAAC;IAgHnD,CAAC;IA9GC,mBAAmB;IACnB,QAAQ;QACN,OAAO,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACxF,CAAC;IAED,QAAQ,CAAC,KAAa,EAAE,aAAsB,KAAK;QACjD,IAAI,UAAU,EAAE;YACd,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAC5C,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC3C;aAAM;YACL,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAC9C,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACzC;IACH,CAAC;IAED,eAAe;QACb,OAAO,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACxG,CAAC;IAED,eAAe,CAAC,KAAa,EAAE,aAAsB,KAAK;QACxD,IAAI,UAAU,EAAE;YACd,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;YACpD,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACnD;aAAM;YACL,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;YACtD,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACjD;IACH,CAAC;IAED,OAAO,CAAC,IAAS,EAAE,aAAsB,KAAK;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,UAAU,EAAE;YACd,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC7C,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC1C;aAAM;YACL,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC/C,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACxC;IACH,CAAC;IAED,aAAa;QACX,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7F,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9C,CAAC;IAED,aAAa,CAAC,UAAmB;QAC/B,IAAI,UAAU,EAAE;YACd,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;SACpD;aAAM;YACL,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,aAAa;QACX,OAAO,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,MAAM,CAAC;IAC/D,CAAC;IAED,cAAc,CAAC,KAAa;QAC1B,IAAI;YACF,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAW,CAAC,CAAC;YACnE,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;YACxB,IAAI,CAAC,GAAG;gBAAE,OAAO,IAAI,CAAC;YACtB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAC1C,OAAO,GAAG,GAAG,GAAG,CAAC;SAClB;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,IAAI,CAAC,CAAC,iCAAiC;SAC/C;IACH,CAAC;IAED,gBAAgB,CAAC,KAAa;QAC5B,IAAI;YACF,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,CAAC,OAAO;gBAAE,OAAO,IAAI,CAAC;YAE1B,sCAAsC;YACtC,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAC7D,MAAM,MAAM,GAAG,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAEhE,uBAAuB;YACvB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;YAC5B,MAAM,IAAI,GAAG,kBAAkB,CAC7B,KAAK,CAAC,SAAS,CAAC,GAAG;iBAChB,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;iBACtE,IAAI,CAAC,EAAE,CAAC,CACZ,CAAC;YAEA,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,CAAC,EAAE;YACV,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,CAAC,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAEH,cAAc;QACZ,kDAAkD;QAClD,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAChD,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAE9C,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAClD,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED,eAAe;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;CACF,CAAA;AApHY,YAAY;IAHxB,UAAU,CAAC;QACV,UAAU,EAAE,MAAM;KACnB,CAAC;GACW,YAAY,CAoHxB;SApHY,YAAY\",\n      sourcesContent: [\"import { Injectable } from '@angular/core';\\n\\n@Injectable({\\n  providedIn: 'root'\\n})\\nexport class TokenService {\\n  private readonly TOKEN_KEY = 'access_token';\\n  private readonly REFRESH_TOKEN_KEY = 'refresh_token';\\n  private readonly USER_KEY = 'user_info';\\n  private readonly REMEMBER_ME_KEY = 'remember_me';\\n\\n  // Token management\\n  getToken(): string | null {\\n    return localStorage.getItem(this.TOKEN_KEY) || sessionStorage.getItem(this.TOKEN_KEY);\\n  }\\n\\n  setToken(token: string, rememberMe: boolean = false): void {\\n    if (rememberMe) {\\n      localStorage.setItem(this.TOKEN_KEY, token);\\n      sessionStorage.removeItem(this.TOKEN_KEY);\\n    } else {\\n      sessionStorage.setItem(this.TOKEN_KEY, token);\\n      localStorage.removeItem(this.TOKEN_KEY);\\n    }\\n  }\\n\\n  getRefreshToken(): string | null {\\n    return localStorage.getItem(this.REFRESH_TOKEN_KEY) || sessionStorage.getItem(this.REFRESH_TOKEN_KEY);\\n  }\\n\\n  setRefreshToken(token: string, rememberMe: boolean = false): void {\\n    if (rememberMe) {\\n      localStorage.setItem(this.REFRESH_TOKEN_KEY, token);\\n      sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);\\n    } else {\\n      sessionStorage.setItem(this.REFRESH_TOKEN_KEY, token);\\n      localStorage.removeItem(this.REFRESH_TOKEN_KEY);\\n    }\\n  }\\n\\n  setUser(user: any, rememberMe: boolean = false): void {\\n    const userStr = JSON.stringify(user);\\n    if (rememberMe) {\\n      localStorage.setItem(this.USER_KEY, userStr);\\n      sessionStorage.removeItem(this.USER_KEY);\\n    } else {\\n      sessionStorage.setItem(this.USER_KEY, userStr);\\n      localStorage.removeItem(this.USER_KEY);\\n    }\\n  }\\n\\n  getStoredUser(): any | null {\\n    const userStr = localStorage.getItem(this.USER_KEY) || sessionStorage.getItem(this.USER_KEY);\\n    return userStr ? JSON.parse(userStr) : null;\\n  }\\n\\n  setRememberMe(rememberMe: boolean): void {\\n    if (rememberMe) {\\n      localStorage.setItem(this.REMEMBER_ME_KEY, 'true');\\n    } else {\\n      localStorage.removeItem(this.REMEMBER_ME_KEY);\\n    }\\n  }\\n\\n  getRememberMe(): boolean {\\n    return localStorage.getItem(this.REMEMBER_ME_KEY) === 'true';\\n  }\\n\\n  isTokenExpired(token: string): boolean {\\n    try {\\n      const payload = JSON.parse(this.decodeJwtPayload(token) as string);\\n      const exp = payload.exp;\\n      if (!exp) return true;\\n      const now = Math.floor(Date.now() / 1000);\\n      return now > exp;\\n    } catch (error) {\\n      console.warn(\\\"Invalid token format:\\\", error);\\n      return true; // Treat invalid token as expired\\n    }\\n  }\\n\\n  decodeJwtPayload(token: string): string | null {\\n    try {\\n      const payload = token.split('.')[1];\\n      if (!payload) return null;\\n\\n      // Base64url decode: convert to base64\\n      const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');\\n      const padded = base64 + '='.repeat((4 - base64.length % 4) % 4);\\n\\n      // Decode UTF-8 payload\\n      const binary = atob(padded);\\n      const utf8 = decodeURIComponent(\\n        Array.prototype.map\\n          .call(binary, c => '%' + c.charCodeAt(0).toString(16).padStart(2, '0'))\\n          .join('')\\n      );\\n\\n        return utf8;\\n      } catch (e) {\\n        console.error(\\\"Failed to decode JWT payload:\\\", e);\\n        return null;\\n      }\\n    }\\n\\n  clearAllTokens(): void {\\n    // Clear from both localStorage and sessionStorage\\n    localStorage.removeItem(this.TOKEN_KEY);\\n    localStorage.removeItem(this.REFRESH_TOKEN_KEY);\\n    localStorage.removeItem(this.USER_KEY);\\n    localStorage.removeItem(this.REMEMBER_ME_KEY);\\n    \\n    sessionStorage.removeItem(this.TOKEN_KEY);\\n    sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);\\n    sessionStorage.removeItem(this.USER_KEY);\\n  }\\n\\n  isAuthenticated(): boolean {\\n    const token = this.getToken();\\n    return !!token && !this.isTokenExpired(token);\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"d00710c491e6980a96841b6cecbce7a58505d270\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_2e0oh17w9u = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_2e0oh17w9u();\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\ncov_2e0oh17w9u().s[0]++;\nlet TokenService = class TokenService {\n  constructor() {\n    cov_2e0oh17w9u().f[0]++;\n    cov_2e0oh17w9u().s[1]++;\n    this.TOKEN_KEY = 'access_token';\n    cov_2e0oh17w9u().s[2]++;\n    this.REFRESH_TOKEN_KEY = 'refresh_token';\n    cov_2e0oh17w9u().s[3]++;\n    this.USER_KEY = 'user_info';\n    cov_2e0oh17w9u().s[4]++;\n    this.REMEMBER_ME_KEY = 'remember_me';\n  }\n  // Token management\n  getToken() {\n    cov_2e0oh17w9u().f[1]++;\n    cov_2e0oh17w9u().s[5]++;\n    return (cov_2e0oh17w9u().b[0][0]++, localStorage.getItem(this.TOKEN_KEY)) || (cov_2e0oh17w9u().b[0][1]++, sessionStorage.getItem(this.TOKEN_KEY));\n  }\n  setToken(token, rememberMe = (cov_2e0oh17w9u().b[1][0]++, false)) {\n    cov_2e0oh17w9u().f[2]++;\n    cov_2e0oh17w9u().s[6]++;\n    if (rememberMe) {\n      cov_2e0oh17w9u().b[2][0]++;\n      cov_2e0oh17w9u().s[7]++;\n      localStorage.setItem(this.TOKEN_KEY, token);\n      cov_2e0oh17w9u().s[8]++;\n      sessionStorage.removeItem(this.TOKEN_KEY);\n    } else {\n      cov_2e0oh17w9u().b[2][1]++;\n      cov_2e0oh17w9u().s[9]++;\n      sessionStorage.setItem(this.TOKEN_KEY, token);\n      cov_2e0oh17w9u().s[10]++;\n      localStorage.removeItem(this.TOKEN_KEY);\n    }\n  }\n  getRefreshToken() {\n    cov_2e0oh17w9u().f[3]++;\n    cov_2e0oh17w9u().s[11]++;\n    return (cov_2e0oh17w9u().b[3][0]++, localStorage.getItem(this.REFRESH_TOKEN_KEY)) || (cov_2e0oh17w9u().b[3][1]++, sessionStorage.getItem(this.REFRESH_TOKEN_KEY));\n  }\n  setRefreshToken(token, rememberMe = (cov_2e0oh17w9u().b[4][0]++, false)) {\n    cov_2e0oh17w9u().f[4]++;\n    cov_2e0oh17w9u().s[12]++;\n    if (rememberMe) {\n      cov_2e0oh17w9u().b[5][0]++;\n      cov_2e0oh17w9u().s[13]++;\n      localStorage.setItem(this.REFRESH_TOKEN_KEY, token);\n      cov_2e0oh17w9u().s[14]++;\n      sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    } else {\n      cov_2e0oh17w9u().b[5][1]++;\n      cov_2e0oh17w9u().s[15]++;\n      sessionStorage.setItem(this.REFRESH_TOKEN_KEY, token);\n      cov_2e0oh17w9u().s[16]++;\n      localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    }\n  }\n  setUser(user, rememberMe = (cov_2e0oh17w9u().b[6][0]++, false)) {\n    cov_2e0oh17w9u().f[5]++;\n    const userStr = (cov_2e0oh17w9u().s[17]++, JSON.stringify(user));\n    cov_2e0oh17w9u().s[18]++;\n    if (rememberMe) {\n      cov_2e0oh17w9u().b[7][0]++;\n      cov_2e0oh17w9u().s[19]++;\n      localStorage.setItem(this.USER_KEY, userStr);\n      cov_2e0oh17w9u().s[20]++;\n      sessionStorage.removeItem(this.USER_KEY);\n    } else {\n      cov_2e0oh17w9u().b[7][1]++;\n      cov_2e0oh17w9u().s[21]++;\n      sessionStorage.setItem(this.USER_KEY, userStr);\n      cov_2e0oh17w9u().s[22]++;\n      localStorage.removeItem(this.USER_KEY);\n    }\n  }\n  getStoredUser() {\n    cov_2e0oh17w9u().f[6]++;\n    const userStr = (cov_2e0oh17w9u().s[23]++, (cov_2e0oh17w9u().b[8][0]++, localStorage.getItem(this.USER_KEY)) || (cov_2e0oh17w9u().b[8][1]++, sessionStorage.getItem(this.USER_KEY)));\n    cov_2e0oh17w9u().s[24]++;\n    return userStr ? (cov_2e0oh17w9u().b[9][0]++, JSON.parse(userStr)) : (cov_2e0oh17w9u().b[9][1]++, null);\n  }\n  setRememberMe(rememberMe) {\n    cov_2e0oh17w9u().f[7]++;\n    cov_2e0oh17w9u().s[25]++;\n    if (rememberMe) {\n      cov_2e0oh17w9u().b[10][0]++;\n      cov_2e0oh17w9u().s[26]++;\n      localStorage.setItem(this.REMEMBER_ME_KEY, 'true');\n    } else {\n      cov_2e0oh17w9u().b[10][1]++;\n      cov_2e0oh17w9u().s[27]++;\n      localStorage.removeItem(this.REMEMBER_ME_KEY);\n    }\n  }\n  getRememberMe() {\n    cov_2e0oh17w9u().f[8]++;\n    cov_2e0oh17w9u().s[28]++;\n    return localStorage.getItem(this.REMEMBER_ME_KEY) === 'true';\n  }\n  isTokenExpired(token) {\n    cov_2e0oh17w9u().f[9]++;\n    cov_2e0oh17w9u().s[29]++;\n    try {\n      const payload = (cov_2e0oh17w9u().s[30]++, JSON.parse(this.decodeJwtPayload(token)));\n      const exp = (cov_2e0oh17w9u().s[31]++, payload.exp);\n      cov_2e0oh17w9u().s[32]++;\n      if (!exp) {\n        cov_2e0oh17w9u().b[11][0]++;\n        cov_2e0oh17w9u().s[33]++;\n        return true;\n      } else {\n        cov_2e0oh17w9u().b[11][1]++;\n      }\n      const now = (cov_2e0oh17w9u().s[34]++, Math.floor(Date.now() / 1000));\n      cov_2e0oh17w9u().s[35]++;\n      return now > exp;\n    } catch (error) {\n      cov_2e0oh17w9u().s[36]++;\n      console.warn(\"Invalid token format:\", error);\n      cov_2e0oh17w9u().s[37]++;\n      return true; // Treat invalid token as expired\n    }\n  }\n\n  decodeJwtPayload(token) {\n    cov_2e0oh17w9u().f[10]++;\n    cov_2e0oh17w9u().s[38]++;\n    try {\n      const payload = (cov_2e0oh17w9u().s[39]++, token.split('.')[1]);\n      cov_2e0oh17w9u().s[40]++;\n      if (!payload) {\n        cov_2e0oh17w9u().b[12][0]++;\n        cov_2e0oh17w9u().s[41]++;\n        return null;\n      } else {\n        cov_2e0oh17w9u().b[12][1]++;\n      }\n      // Base64url decode: convert to base64\n      const base64 = (cov_2e0oh17w9u().s[42]++, payload.replace(/-/g, '+').replace(/_/g, '/'));\n      const padded = (cov_2e0oh17w9u().s[43]++, base64 + '='.repeat((4 - base64.length % 4) % 4));\n      // Decode UTF-8 payload\n      const binary = (cov_2e0oh17w9u().s[44]++, atob(padded));\n      const utf8 = (cov_2e0oh17w9u().s[45]++, decodeURIComponent(Array.prototype.map.call(binary, c => {\n        cov_2e0oh17w9u().f[11]++;\n        cov_2e0oh17w9u().s[46]++;\n        return '%' + c.charCodeAt(0).toString(16).padStart(2, '0');\n      }).join('')));\n      cov_2e0oh17w9u().s[47]++;\n      return utf8;\n    } catch (e) {\n      cov_2e0oh17w9u().s[48]++;\n      console.error(\"Failed to decode JWT payload:\", e);\n      cov_2e0oh17w9u().s[49]++;\n      return null;\n    }\n  }\n  clearAllTokens() {\n    cov_2e0oh17w9u().f[12]++;\n    cov_2e0oh17w9u().s[50]++;\n    // Clear from both localStorage and sessionStorage\n    localStorage.removeItem(this.TOKEN_KEY);\n    cov_2e0oh17w9u().s[51]++;\n    localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    cov_2e0oh17w9u().s[52]++;\n    localStorage.removeItem(this.USER_KEY);\n    cov_2e0oh17w9u().s[53]++;\n    localStorage.removeItem(this.REMEMBER_ME_KEY);\n    cov_2e0oh17w9u().s[54]++;\n    sessionStorage.removeItem(this.TOKEN_KEY);\n    cov_2e0oh17w9u().s[55]++;\n    sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    cov_2e0oh17w9u().s[56]++;\n    sessionStorage.removeItem(this.USER_KEY);\n  }\n  isAuthenticated() {\n    cov_2e0oh17w9u().f[13]++;\n    const token = (cov_2e0oh17w9u().s[57]++, this.getToken());\n    cov_2e0oh17w9u().s[58]++;\n    return (cov_2e0oh17w9u().b[13][0]++, !!token) && (cov_2e0oh17w9u().b[13][1]++, !this.isTokenExpired(token));\n  }\n};\ncov_2e0oh17w9u().s[59]++;\nTokenService = __decorate([Injectable({\n  providedIn: 'root'\n})], TokenService);\nexport { TokenService };", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkBM;IAAA;MAAA;IAAA;EAAA;EAAA;AAAA;AAAA;;AAlBN,SAASA,UAAU,QAAQ,eAAe;AAAC;AAKpC,IAAMC,YAAY,GAAlB,MAAMA,YAAY;EAAlBC;IAAA;IAAA;IACY,cAAS,GAAG,cAAc;IAAC;IAC3B,sBAAiB,GAAG,eAAe;IAAC;IACpC,aAAQ,GAAG,WAAW;IAAC;IACvB,oBAAe,GAAG,aAAa;EAgHlD;EA9GE;EACAC,QAAQ;IAAA;IAAA;IACN,OAAO,yCAAY,CAACC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,kCAAIC,cAAc,CAACF,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC;EACvF;EAEAE,QAAQ,CAACC,KAAa,EAAEC,0CAAsB,KAAK;IAAA;IAAA;IACjD,IAAIA,UAAU,EAAE;MAAA;MAAA;MACdC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACN,SAAS,EAAEG,KAAK,CAAC;MAAC;MAC5CF,cAAc,CAACM,UAAU,CAAC,IAAI,CAACP,SAAS,CAAC;KAC1C,MAAM;MAAA;MAAA;MACLC,cAAc,CAACK,OAAO,CAAC,IAAI,CAACN,SAAS,EAAEG,KAAK,CAAC;MAAC;MAC9CE,YAAY,CAACE,UAAU,CAAC,IAAI,CAACP,SAAS,CAAC;;EAE3C;EAEAQ,eAAe;IAAA;IAAA;IACb,OAAO,yCAAY,CAACT,OAAO,CAAC,IAAI,CAACU,iBAAiB,CAAC,kCAAIR,cAAc,CAACF,OAAO,CAAC,IAAI,CAACU,iBAAiB,CAAC;EACvG;EAEAC,eAAe,CAACP,KAAa,EAAEC,0CAAsB,KAAK;IAAA;IAAA;IACxD,IAAIA,UAAU,EAAE;MAAA;MAAA;MACdC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACG,iBAAiB,EAAEN,KAAK,CAAC;MAAC;MACpDF,cAAc,CAACM,UAAU,CAAC,IAAI,CAACE,iBAAiB,CAAC;KAClD,MAAM;MAAA;MAAA;MACLR,cAAc,CAACK,OAAO,CAAC,IAAI,CAACG,iBAAiB,EAAEN,KAAK,CAAC;MAAC;MACtDE,YAAY,CAACE,UAAU,CAAC,IAAI,CAACE,iBAAiB,CAAC;;EAEnD;EAEAE,OAAO,CAACC,IAAS,EAAER,0CAAsB,KAAK;IAAA;IAC5C,MAAMS,OAAO,8BAAGC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC;IAAC;IACrC,IAAIR,UAAU,EAAE;MAAA;MAAA;MACdC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACU,QAAQ,EAAEH,OAAO,CAAC;MAAC;MAC7CZ,cAAc,CAACM,UAAU,CAAC,IAAI,CAACS,QAAQ,CAAC;KACzC,MAAM;MAAA;MAAA;MACLf,cAAc,CAACK,OAAO,CAAC,IAAI,CAACU,QAAQ,EAAEH,OAAO,CAAC;MAAC;MAC/CR,YAAY,CAACE,UAAU,CAAC,IAAI,CAACS,QAAQ,CAAC;;EAE1C;EAEAC,aAAa;IAAA;IACX,MAAMJ,OAAO,8BAAG,yCAAY,CAACd,OAAO,CAAC,IAAI,CAACiB,QAAQ,CAAC,kCAAIf,cAAc,CAACF,OAAO,CAAC,IAAI,CAACiB,QAAQ,CAAC;IAAC;IAC7F,OAAOH,OAAO,gCAAGC,IAAI,CAACI,KAAK,CAACL,OAAO,CAAC,iCAAG,IAAI;EAC7C;EAEAM,aAAa,CAACf,UAAmB;IAAA;IAAA;IAC/B,IAAIA,UAAU,EAAE;MAAA;MAAA;MACdC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACc,eAAe,EAAE,MAAM,CAAC;KACnD,MAAM;MAAA;MAAA;MACLf,YAAY,CAACE,UAAU,CAAC,IAAI,CAACa,eAAe,CAAC;;EAEjD;EAEAC,aAAa;IAAA;IAAA;IACX,OAAOhB,YAAY,CAACN,OAAO,CAAC,IAAI,CAACqB,eAAe,CAAC,KAAK,MAAM;EAC9D;EAEAE,cAAc,CAACnB,KAAa;IAAA;IAAA;IAC1B,IAAI;MACF,MAAMoB,OAAO,8BAAGT,IAAI,CAACI,KAAK,CAAC,IAAI,CAACM,gBAAgB,CAACrB,KAAK,CAAW,CAAC;MAClE,MAAMsB,GAAG,8BAAGF,OAAO,CAACE,GAAG;MAAC;MACxB,IAAI,CAACA,GAAG,EAAE;QAAA;QAAA;QAAA,OAAO,IAAI;MAAA,CAAC;QAAA;MAAA;MACtB,MAAMC,GAAG,8BAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACH,GAAG,EAAE,GAAG,IAAI,CAAC;MAAC;MAC1C,OAAOA,GAAG,GAAGD,GAAG;KACjB,CAAC,OAAOK,KAAK,EAAE;MAAA;MACdC,OAAO,CAACC,IAAI,CAAC,uBAAuB,EAAEF,KAAK,CAAC;MAAC;MAC7C,OAAO,IAAI,CAAC,CAAC;;EAEjB;;EAEAN,gBAAgB,CAACrB,KAAa;IAAA;IAAA;IAC5B,IAAI;MACF,MAAMoB,OAAO,8BAAGpB,KAAK,CAAC8B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAAC;MACpC,IAAI,CAACV,OAAO,EAAE;QAAA;QAAA;QAAA,OAAO,IAAI;MAAA,CAAC;QAAA;MAAA;MAE1B;MACA,MAAMW,MAAM,8BAAGX,OAAO,CAACY,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MAC5D,MAAMC,MAAM,8BAAGF,MAAM,GAAG,GAAG,CAACG,MAAM,CAAC,CAAC,CAAC,GAAGH,MAAM,CAACI,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC;MAE/D;MACA,MAAMC,MAAM,8BAAGC,IAAI,CAACJ,MAAM,CAAC;MAC3B,MAAMK,IAAI,8BAAGC,kBAAkB,CAC7BC,KAAK,CAACC,SAAS,CAACC,GAAG,CAChBC,IAAI,CAACP,MAAM,EAAEQ,CAAC,IAAI;QAAA;QAAA;QAAA,UAAG,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAAD,CAAC,CAAC,CACtEC,IAAI,CAAC,EAAE,CAAC,CACZ;MAAC;MAEA,OAAOV,IAAI;KACZ,CAAC,OAAOW,CAAC,EAAE;MAAA;MACVrB,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEsB,CAAC,CAAC;MAAC;MAClD,OAAO,IAAI;;EAEf;EAEFC,cAAc;IAAA;IAAA;IACZ;IACAhD,YAAY,CAACE,UAAU,CAAC,IAAI,CAACP,SAAS,CAAC;IAAC;IACxCK,YAAY,CAACE,UAAU,CAAC,IAAI,CAACE,iBAAiB,CAAC;IAAC;IAChDJ,YAAY,CAACE,UAAU,CAAC,IAAI,CAACS,QAAQ,CAAC;IAAC;IACvCX,YAAY,CAACE,UAAU,CAAC,IAAI,CAACa,eAAe,CAAC;IAAC;IAE9CnB,cAAc,CAACM,UAAU,CAAC,IAAI,CAACP,SAAS,CAAC;IAAC;IAC1CC,cAAc,CAACM,UAAU,CAAC,IAAI,CAACE,iBAAiB,CAAC;IAAC;IAClDR,cAAc,CAACM,UAAU,CAAC,IAAI,CAACS,QAAQ,CAAC;EAC1C;EAEAsC,eAAe;IAAA;IACb,MAAMnD,KAAK,8BAAG,IAAI,CAACL,QAAQ,EAAE;IAAC;IAC9B,OAAO,+BAAC,CAACK,KAAK,mCAAI,CAAC,IAAI,CAACmB,cAAc,CAACnB,KAAK,CAAC;EAC/C;CACD;AAAA;AApHYP,YAAY,eAHxBD,UAAU,CAAC;EACV4D,UAAU,EAAE;CACb,CAAC,GACW3D,YAAY,CAoHxB;SApHYA,YAAY", "names": ["Injectable", "TokenService", "constructor", "getToken", "getItem", "TOKEN_KEY", "sessionStorage", "setToken", "token", "rememberMe", "localStorage", "setItem", "removeItem", "getRefreshToken", "REFRESH_TOKEN_KEY", "setRefreshToken", "setUser", "user", "userStr", "JSON", "stringify", "USER_KEY", "getStoredUser", "parse", "setRememberMe", "REMEMBER_ME_KEY", "getRememberMe", "isTokenExpired", "payload", "decodeJwtPayload", "exp", "now", "Math", "floor", "Date", "error", "console", "warn", "split", "base64", "replace", "padded", "repeat", "length", "binary", "atob", "utf8", "decodeURIComponent", "Array", "prototype", "map", "call", "c", "charCodeAt", "toString", "padStart", "join", "e", "clearAllTokens", "isAuthenticated", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\services\\token.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class TokenService {\n  private readonly TOKEN_KEY = 'access_token';\n  private readonly REFRESH_TOKEN_KEY = 'refresh_token';\n  private readonly USER_KEY = 'user_info';\n  private readonly REMEMBER_ME_KEY = 'remember_me';\n\n  // Token management\n  getToken(): string | null {\n    return localStorage.getItem(this.TOKEN_KEY) || sessionStorage.getItem(this.TOKEN_KEY);\n  }\n\n  setToken(token: string, rememberMe: boolean = false): void {\n    if (rememberMe) {\n      localStorage.setItem(this.TOKEN_KEY, token);\n      sessionStorage.removeItem(this.TOKEN_KEY);\n    } else {\n      sessionStorage.setItem(this.TOKEN_KEY, token);\n      localStorage.removeItem(this.TOKEN_KEY);\n    }\n  }\n\n  getRefreshToken(): string | null {\n    return localStorage.getItem(this.REFRESH_TOKEN_KEY) || sessionStorage.getItem(this.REFRESH_TOKEN_KEY);\n  }\n\n  setRefreshToken(token: string, rememberMe: boolean = false): void {\n    if (rememberMe) {\n      localStorage.setItem(this.REFRESH_TOKEN_KEY, token);\n      sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    } else {\n      sessionStorage.setItem(this.REFRESH_TOKEN_KEY, token);\n      localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    }\n  }\n\n  setUser(user: any, rememberMe: boolean = false): void {\n    const userStr = JSON.stringify(user);\n    if (rememberMe) {\n      localStorage.setItem(this.USER_KEY, userStr);\n      sessionStorage.removeItem(this.USER_KEY);\n    } else {\n      sessionStorage.setItem(this.USER_KEY, userStr);\n      localStorage.removeItem(this.USER_KEY);\n    }\n  }\n\n  getStoredUser(): any | null {\n    const userStr = localStorage.getItem(this.USER_KEY) || sessionStorage.getItem(this.USER_KEY);\n    return userStr ? JSON.parse(userStr) : null;\n  }\n\n  setRememberMe(rememberMe: boolean): void {\n    if (rememberMe) {\n      localStorage.setItem(this.REMEMBER_ME_KEY, 'true');\n    } else {\n      localStorage.removeItem(this.REMEMBER_ME_KEY);\n    }\n  }\n\n  getRememberMe(): boolean {\n    return localStorage.getItem(this.REMEMBER_ME_KEY) === 'true';\n  }\n\n  isTokenExpired(token: string): boolean {\n    try {\n      const payload = JSON.parse(this.decodeJwtPayload(token) as string);\n      const exp = payload.exp;\n      if (!exp) return true;\n      const now = Math.floor(Date.now() / 1000);\n      return now > exp;\n    } catch (error) {\n      console.warn(\"Invalid token format:\", error);\n      return true; // Treat invalid token as expired\n    }\n  }\n\n  decodeJwtPayload(token: string): string | null {\n    try {\n      const payload = token.split('.')[1];\n      if (!payload) return null;\n\n      // Base64url decode: convert to base64\n      const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');\n      const padded = base64 + '='.repeat((4 - base64.length % 4) % 4);\n\n      // Decode UTF-8 payload\n      const binary = atob(padded);\n      const utf8 = decodeURIComponent(\n        Array.prototype.map\n          .call(binary, c => '%' + c.charCodeAt(0).toString(16).padStart(2, '0'))\n          .join('')\n      );\n\n        return utf8;\n      } catch (e) {\n        console.error(\"Failed to decode JWT payload:\", e);\n        return null;\n      }\n    }\n\n  clearAllTokens(): void {\n    // Clear from both localStorage and sessionStorage\n    localStorage.removeItem(this.TOKEN_KEY);\n    localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    localStorage.removeItem(this.USER_KEY);\n    localStorage.removeItem(this.REMEMBER_ME_KEY);\n    \n    sessionStorage.removeItem(this.TOKEN_KEY);\n    sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    sessionStorage.removeItem(this.USER_KEY);\n  }\n\n  isAuthenticated(): boolean {\n    const token = this.getToken();\n    return !!token && !this.isTokenExpired(token);\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}