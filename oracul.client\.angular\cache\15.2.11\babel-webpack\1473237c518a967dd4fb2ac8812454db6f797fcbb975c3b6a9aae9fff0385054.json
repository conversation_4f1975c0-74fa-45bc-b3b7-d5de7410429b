{"ast": null, "code": "function cov_vl6nt70nd() {\n  var path = \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\core\\\\theme\\\\theme.service.ts\";\n  var hash = \"d6430791d007be17bf415a1b69a2f1b80d8f8b1d\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\core\\\\theme\\\\theme.service.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 5,\n          column: 19\n        },\n        end: {\n          line: 158,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 7,\n          column: 8\n        },\n        end: {\n          line: 7,\n          column: 48\n        }\n      },\n      \"2\": {\n        start: {\n          line: 8,\n          column: 8\n        },\n        end: {\n          line: 8,\n          column: 78\n        }\n      },\n      \"3\": {\n        start: {\n          line: 9,\n          column: 8\n        },\n        end: {\n          line: 9,\n          column: 69\n        }\n      },\n      \"4\": {\n        start: {\n          line: 10,\n          column: 8\n        },\n        end: {\n          line: 10,\n          column: 30\n        }\n      },\n      \"5\": {\n        start: {\n          line: 13,\n          column: 31\n        },\n        end: {\n          line: 13,\n          column: 75\n        }\n      },\n      \"6\": {\n        start: {\n          line: 14,\n          column: 8\n        },\n        end: {\n          line: 20,\n          column: 9\n        }\n      },\n      \"7\": {\n        start: {\n          line: 15,\n          column: 31\n        },\n        end: {\n          line: 15,\n          column: 92\n        }\n      },\n      \"8\": {\n        start: {\n          line: 15,\n          column: 62\n        },\n        end: {\n          line: 15,\n          column: 91\n        }\n      },\n      \"9\": {\n        start: {\n          line: 16,\n          column: 12\n        },\n        end: {\n          line: 19,\n          column: 13\n        }\n      },\n      \"10\": {\n        start: {\n          line: 17,\n          column: 16\n        },\n        end: {\n          line: 17,\n          column: 42\n        }\n      },\n      \"11\": {\n        start: {\n          line: 18,\n          column: 16\n        },\n        end: {\n          line: 18,\n          column: 23\n        }\n      },\n      \"12\": {\n        start: {\n          line: 22,\n          column: 8\n        },\n        end: {\n          line: 22,\n          column: 47\n        }\n      },\n      \"13\": {\n        start: {\n          line: 25,\n          column: 8\n        },\n        end: {\n          line: 25,\n          column: 45\n        }\n      },\n      \"14\": {\n        start: {\n          line: 26,\n          column: 8\n        },\n        end: {\n          line: 26,\n          column: 31\n        }\n      },\n      \"15\": {\n        start: {\n          line: 27,\n          column: 8\n        },\n        end: {\n          line: 27,\n          column: 65\n        }\n      },\n      \"16\": {\n        start: {\n          line: 30,\n          column: 22\n        },\n        end: {\n          line: 30,\n          column: 70\n        }\n      },\n      \"17\": {\n        start: {\n          line: 30,\n          column: 49\n        },\n        end: {\n          line: 30,\n          column: 69\n        }\n      },\n      \"18\": {\n        start: {\n          line: 31,\n          column: 8\n        },\n        end: {\n          line: 33,\n          column: 9\n        }\n      },\n      \"19\": {\n        start: {\n          line: 32,\n          column: 12\n        },\n        end: {\n          line: 32,\n          column: 33\n        }\n      },\n      \"20\": {\n        start: {\n          line: 36,\n          column: 8\n        },\n        end: {\n          line: 36,\n          column: 46\n        }\n      },\n      \"21\": {\n        start: {\n          line: 39,\n          column: 8\n        },\n        end: {\n          line: 39,\n          column: 32\n        }\n      },\n      \"22\": {\n        start: {\n          line: 42,\n          column: 21\n        },\n        end: {\n          line: 42,\n          column: 45\n        }\n      },\n      \"23\": {\n        start: {\n          line: 43,\n          column: 21\n        },\n        end: {\n          line: 43,\n          column: 34\n        }\n      },\n      \"24\": {\n        start: {\n          line: 44,\n          column: 23\n        },\n        end: {\n          line: 44,\n          column: 35\n        }\n      },\n      \"25\": {\n        start: {\n          line: 46,\n          column: 8\n        },\n        end: {\n          line: 46,\n          column: 45\n        }\n      },\n      \"26\": {\n        start: {\n          line: 48,\n          column: 8\n        },\n        end: {\n          line: 48,\n          column: 66\n        }\n      },\n      \"27\": {\n        start: {\n          line: 49,\n          column: 8\n        },\n        end: {\n          line: 49,\n          column: 77\n        }\n      },\n      \"28\": {\n        start: {\n          line: 50,\n          column: 8\n        },\n        end: {\n          line: 50,\n          column: 75\n        }\n      },\n      \"29\": {\n        start: {\n          line: 51,\n          column: 8\n        },\n        end: {\n          line: 51,\n          column: 64\n        }\n      },\n      \"30\": {\n        start: {\n          line: 52,\n          column: 8\n        },\n        end: {\n          line: 52,\n          column: 75\n        }\n      },\n      \"31\": {\n        start: {\n          line: 53,\n          column: 8\n        },\n        end: {\n          line: 53,\n          column: 73\n        }\n      },\n      \"32\": {\n        start: {\n          line: 54,\n          column: 8\n        },\n        end: {\n          line: 54,\n          column: 60\n        }\n      },\n      \"33\": {\n        start: {\n          line: 55,\n          column: 8\n        },\n        end: {\n          line: 55,\n          column: 66\n        }\n      },\n      \"34\": {\n        start: {\n          line: 56,\n          column: 8\n        },\n        end: {\n          line: 56,\n          column: 62\n        }\n      },\n      \"35\": {\n        start: {\n          line: 57,\n          column: 8\n        },\n        end: {\n          line: 57,\n          column: 72\n        }\n      },\n      \"36\": {\n        start: {\n          line: 58,\n          column: 8\n        },\n        end: {\n          line: 58,\n          column: 66\n        }\n      },\n      \"37\": {\n        start: {\n          line: 60,\n          column: 8\n        },\n        end: {\n          line: 60,\n          column: 76\n        }\n      },\n      \"38\": {\n        start: {\n          line: 61,\n          column: 8\n        },\n        end: {\n          line: 61,\n          column: 80\n        }\n      },\n      \"39\": {\n        start: {\n          line: 62,\n          column: 8\n        },\n        end: {\n          line: 62,\n          column: 78\n        }\n      },\n      \"40\": {\n        start: {\n          line: 63,\n          column: 8\n        },\n        end: {\n          line: 63,\n          column: 70\n        }\n      },\n      \"41\": {\n        start: {\n          line: 65,\n          column: 8\n        },\n        end: {\n          line: 65,\n          column: 84\n        }\n      },\n      \"42\": {\n        start: {\n          line: 66,\n          column: 8\n        },\n        end: {\n          line: 66,\n          column: 88\n        }\n      },\n      \"43\": {\n        start: {\n          line: 67,\n          column: 8\n        },\n        end: {\n          line: 67,\n          column: 78\n        }\n      },\n      \"44\": {\n        start: {\n          line: 69,\n          column: 8\n        },\n        end: {\n          line: 69,\n          column: 84\n        }\n      },\n      \"45\": {\n        start: {\n          line: 70,\n          column: 8\n        },\n        end: {\n          line: 70,\n          column: 84\n        }\n      },\n      \"46\": {\n        start: {\n          line: 71,\n          column: 8\n        },\n        end: {\n          line: 71,\n          column: 80\n        }\n      },\n      \"47\": {\n        start: {\n          line: 72,\n          column: 8\n        },\n        end: {\n          line: 72,\n          column: 82\n        }\n      },\n      \"48\": {\n        start: {\n          line: 73,\n          column: 8\n        },\n        end: {\n          line: 73,\n          column: 88\n        }\n      },\n      \"49\": {\n        start: {\n          line: 74,\n          column: 8\n        },\n        end: {\n          line: 74,\n          column: 88\n        }\n      },\n      \"50\": {\n        start: {\n          line: 75,\n          column: 8\n        },\n        end: {\n          line: 75,\n          column: 84\n        }\n      },\n      \"51\": {\n        start: {\n          line: 76,\n          column: 8\n        },\n        end: {\n          line: 76,\n          column: 86\n        }\n      },\n      \"52\": {\n        start: {\n          line: 78,\n          column: 8\n        },\n        end: {\n          line: 78,\n          column: 66\n        }\n      },\n      \"53\": {\n        start: {\n          line: 79,\n          column: 8\n        },\n        end: {\n          line: 79,\n          column: 50\n        }\n      },\n      \"54\": {\n        start: {\n          line: 81,\n          column: 8\n        },\n        end: {\n          line: 101,\n          column: 16\n        }\n      },\n      \"55\": {\n        start: {\n          line: 82,\n          column: 12\n        },\n        end: {\n          line: 82,\n          column: 52\n        }\n      },\n      \"56\": {\n        start: {\n          line: 84,\n          column: 38\n        },\n        end: {\n          line: 89,\n          column: 13\n        }\n      },\n      \"57\": {\n        start: {\n          line: 90,\n          column: 12\n        },\n        end: {\n          line: 98,\n          column: 15\n        }\n      },\n      \"58\": {\n        start: {\n          line: 91,\n          column: 33\n        },\n        end: {\n          line: 91,\n          column: 68\n        }\n      },\n      \"59\": {\n        start: {\n          line: 92,\n          column: 16\n        },\n        end: {\n          line: 97,\n          column: 19\n        }\n      },\n      \"60\": {\n        start: {\n          line: 93,\n          column: 36\n        },\n        end: {\n          line: 93,\n          column: 38\n        }\n      },\n      \"61\": {\n        start: {\n          line: 95,\n          column: 42\n        },\n        end: {\n          line: 95,\n          column: 74\n        }\n      },\n      \"62\": {\n        start: {\n          line: 96,\n          column: 20\n        },\n        end: {\n          line: 96,\n          column: 66\n        }\n      },\n      \"63\": {\n        start: {\n          line: 100,\n          column: 12\n        },\n        end: {\n          line: 100,\n          column: 85\n        }\n      },\n      \"64\": {\n        start: {\n          line: 105,\n          column: 26\n        },\n        end: {\n          line: 105,\n          column: 47\n        }\n      },\n      \"65\": {\n        start: {\n          line: 106,\n          column: 8\n        },\n        end: {\n          line: 130,\n          column: 10\n        }\n      },\n      \"66\": {\n        start: {\n          line: 134,\n          column: 8\n        },\n        end: {\n          line: 134,\n          column: 63\n        }\n      },\n      \"67\": {\n        start: {\n          line: 138,\n          column: 8\n        },\n        end: {\n          line: 148,\n          column: 9\n        }\n      },\n      \"68\": {\n        start: {\n          line: 139,\n          column: 26\n        },\n        end: {\n          line: 139,\n          column: 47\n        }\n      },\n      \"69\": {\n        start: {\n          line: 140,\n          column: 12\n        },\n        end: {\n          line: 143,\n          column: 13\n        }\n      },\n      \"70\": {\n        start: {\n          line: 141,\n          column: 16\n        },\n        end: {\n          line: 141,\n          column: 37\n        }\n      },\n      \"71\": {\n        start: {\n          line: 142,\n          column: 16\n        },\n        end: {\n          line: 142,\n          column: 28\n        }\n      },\n      \"72\": {\n        start: {\n          line: 144,\n          column: 12\n        },\n        end: {\n          line: 144,\n          column: 25\n        }\n      },\n      \"73\": {\n        start: {\n          line: 147,\n          column: 12\n        },\n        end: {\n          line: 147,\n          column: 25\n        }\n      },\n      \"74\": {\n        start: {\n          line: 151,\n          column: 8\n        },\n        end: {\n          line: 155,\n          column: 52\n        }\n      },\n      \"75\": {\n        start: {\n          line: 157,\n          column: 13\n        },\n        end: {\n          line: 157,\n          column: 44\n        }\n      },\n      \"76\": {\n        start: {\n          line: 157,\n          column: 41\n        },\n        end: {\n          line: 157,\n          column: 43\n        }\n      },\n      \"77\": {\n        start: {\n          line: 159,\n          column: 0\n        },\n        end: {\n          line: 163,\n          column: 17\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 6,\n            column: 4\n          },\n          end: {\n            line: 6,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 6,\n            column: 18\n          },\n          end: {\n            line: 11,\n            column: 5\n          }\n        },\n        line: 6\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 12,\n            column: 4\n          },\n          end: {\n            line: 12,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 12,\n            column: 21\n          },\n          end: {\n            line: 23,\n            column: 5\n          }\n        },\n        line: 12\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 15,\n            column: 53\n          },\n          end: {\n            line: 15,\n            column: 54\n          }\n        },\n        loc: {\n          start: {\n            line: 15,\n            column: 62\n          },\n          end: {\n            line: 15,\n            column: 91\n          }\n        },\n        line: 15\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 24,\n            column: 4\n          },\n          end: {\n            line: 24,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 24,\n            column: 20\n          },\n          end: {\n            line: 28,\n            column: 5\n          }\n        },\n        line: 24\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 29,\n            column: 4\n          },\n          end: {\n            line: 29,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 29,\n            column: 30\n          },\n          end: {\n            line: 34,\n            column: 5\n          }\n        },\n        line: 29\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 30,\n            column: 44\n          },\n          end: {\n            line: 30,\n            column: 45\n          }\n        },\n        loc: {\n          start: {\n            line: 30,\n            column: 49\n          },\n          end: {\n            line: 30,\n            column: 69\n          }\n        },\n        line: 30\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 35,\n            column: 4\n          },\n          end: {\n            line: 35,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 35,\n            column: 22\n          },\n          end: {\n            line: 37,\n            column: 5\n          }\n        },\n        line: 35\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 38,\n            column: 4\n          },\n          end: {\n            line: 38,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 38,\n            column: 25\n          },\n          end: {\n            line: 40,\n            column: 5\n          }\n        },\n        line: 38\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 41,\n            column: 4\n          },\n          end: {\n            line: 41,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 41,\n            column: 22\n          },\n          end: {\n            line: 102,\n            column: 5\n          }\n        },\n        line: 41\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 81,\n            column: 19\n          },\n          end: {\n            line: 81,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 81,\n            column: 25\n          },\n          end: {\n            line: 101,\n            column: 9\n          }\n        },\n        line: 81\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 90,\n            column: 38\n          },\n          end: {\n            line: 90,\n            column: 39\n          }\n        },\n        loc: {\n          start: {\n            line: 90,\n            column: 50\n          },\n          end: {\n            line: 98,\n            column: 13\n          }\n        },\n        line: 90\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 92,\n            column: 33\n          },\n          end: {\n            line: 92,\n            column: 34\n          }\n        },\n        loc: {\n          start: {\n            line: 92,\n            column: 39\n          },\n          end: {\n            line: 97,\n            column: 17\n          }\n        },\n        line: 92\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 104,\n            column: 4\n          },\n          end: {\n            line: 104,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 104,\n            column: 36\n          },\n          end: {\n            line: 131,\n            column: 5\n          }\n        },\n        line: 104\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 133,\n            column: 4\n          },\n          end: {\n            line: 133,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 133,\n            column: 18\n          },\n          end: {\n            line: 135,\n            column: 5\n          }\n        },\n        line: 133\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 137,\n            column: 4\n          },\n          end: {\n            line: 137,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 137,\n            column: 27\n          },\n          end: {\n            line: 149,\n            column: 5\n          }\n        },\n        line: 137\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 150,\n            column: 4\n          },\n          end: {\n            line: 150,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 150,\n            column: 25\n          },\n          end: {\n            line: 156,\n            column: 5\n          }\n        },\n        line: 150\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 157,\n            column: 35\n          },\n          end: {\n            line: 157,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 157,\n            column: 41\n          },\n          end: {\n            line: 157,\n            column: 43\n          }\n        },\n        line: 157\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 14,\n            column: 8\n          },\n          end: {\n            line: 20,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 14,\n            column: 8\n          },\n          end: {\n            line: 20,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 14\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 16,\n            column: 12\n          },\n          end: {\n            line: 19,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 16,\n            column: 12\n          },\n          end: {\n            line: 19,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 16\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 31,\n            column: 8\n          },\n          end: {\n            line: 33,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 31,\n            column: 8\n          },\n          end: {\n            line: 33,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 31\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 113,\n            column: 24\n          },\n          end: {\n            line: 113,\n            column: 41\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 113,\n            column: 24\n          },\n          end: {\n            line: 113,\n            column: 35\n          }\n        }, {\n          start: {\n            line: 113,\n            column: 39\n          },\n          end: {\n            line: 113,\n            column: 41\n          }\n        }],\n        line: 113\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 117,\n            column: 24\n          },\n          end: {\n            line: 117,\n            column: 45\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 117,\n            column: 24\n          },\n          end: {\n            line: 117,\n            column: 39\n          }\n        }, {\n          start: {\n            line: 117,\n            column: 43\n          },\n          end: {\n            line: 117,\n            column: 45\n          }\n        }],\n        line: 117\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 122,\n            column: 28\n          },\n          end: {\n            line: 122,\n            column: 54\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 122,\n            column: 28\n          },\n          end: {\n            line: 122,\n            column: 48\n          }\n        }, {\n          start: {\n            line: 122,\n            column: 52\n          },\n          end: {\n            line: 122,\n            column: 54\n          }\n        }],\n        line: 122\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 126,\n            column: 28\n          },\n          end: {\n            line: 126,\n            column: 56\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 126,\n            column: 28\n          },\n          end: {\n            line: 126,\n            column: 50\n          }\n        }, {\n          start: {\n            line: 126,\n            column: 54\n          },\n          end: {\n            line: 126,\n            column: 56\n          }\n        }],\n        line: 126\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 140,\n            column: 12\n          },\n          end: {\n            line: 143,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 140,\n            column: 12\n          },\n          end: {\n            line: 143,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 140\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 151,\n            column: 15\n          },\n          end: {\n            line: 155,\n            column: 51\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 151,\n            column: 15\n          },\n          end: {\n            line: 151,\n            column: 20\n          }\n        }, {\n          start: {\n            line: 152,\n            column: 12\n          },\n          end: {\n            line: 152,\n            column: 42\n          }\n        }, {\n          start: {\n            line: 153,\n            column: 12\n          },\n          end: {\n            line: 153,\n            column: 24\n          }\n        }, {\n          start: {\n            line: 154,\n            column: 12\n          },\n          end: {\n            line: 154,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 155,\n            column: 12\n          },\n          end: {\n            line: 155,\n            column: 51\n          }\n        }],\n        line: 151\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0,\n      \"59\": 0,\n      \"60\": 0,\n      \"61\": 0,\n      \"62\": 0,\n      \"63\": 0,\n      \"64\": 0,\n      \"65\": 0,\n      \"66\": 0,\n      \"67\": 0,\n      \"68\": 0,\n      \"69\": 0,\n      \"70\": 0,\n      \"71\": 0,\n      \"72\": 0,\n      \"73\": 0,\n      \"74\": 0,\n      \"75\": 0,\n      \"76\": 0,\n      \"77\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0, 0, 0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"theme.service.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Projects\\\\Harmonia\\\\oracul.client\\\\src\\\\app\\\\core\\\\theme\\\\theme.service.ts\"],\n      names: [],\n      mappings: \";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,eAAe,EAAc,MAAM,MAAM,CAAC;AACnD,OAAO,EAA2C,qBAAqB,EAAE,gBAAgB,EAAE,MAAM,gBAAgB,CAAC;AAK3G,IAAM,YAAY,GAAlB,MAAM,YAAY;IAKvB;QAJiB,sBAAiB,GAAG,cAAc,CAAC;QAC5C,wBAAmB,GAAG,IAAI,eAAe,CAAc,qBAAqB,CAAC,CAAC;QAC/E,kBAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,CAAC;QAG7D,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAEO,cAAc;QACpB,MAAM,cAAc,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACpE,IAAI,cAAc,EAAE;YAClB,MAAM,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,cAAc,CAAC,CAAC;YACjF,IAAI,UAAU,EAAE;gBACd,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAC1B,OAAO;aACR;SACF;QACD,sBAAsB;QACtB,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;IACzC,CAAC;IAED,QAAQ,CAAC,KAAkB;QACzB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACvB,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED,cAAc,CAAC,SAAiB;QAC9B,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;QAC/D,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACtB;IACH,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;IACxC,CAAC;IAED,kBAAkB;QAChB,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAEO,UAAU,CAAC,KAAkB;QACnC,MAAM,IAAI,GAAG,QAAQ,CAAC,eAAe,CAAC;QACtC,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;QAC3B,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAE5B,0CAA0C;QAC1C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAErC,8BAA8B;QAC9B,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,iBAAiB,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QAC1D,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,uBAAuB,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;QACrE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,sBAAsB,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;QACnE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,sBAAsB,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;QACnE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,qBAAqB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QACjE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,iBAAiB,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QAC1D,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,eAAe,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QACtD,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QAChE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,iBAAiB,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QAE1D,cAAc;QACd,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,sBAAsB,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,wBAAwB,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,uBAAuB,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,mBAAmB,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE9D,YAAY;QACZ,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,0BAA0B,EAAE,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC5E,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,4BAA4B,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAChF,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,uBAAuB,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEtE,eAAe;QACf,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,mBAAmB,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC5E,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,uBAAuB,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC5E,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,qBAAqB,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACxE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,sBAAsB,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAE1E,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,qBAAqB,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,yBAAyB,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAChF,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,uBAAuB,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC5E,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,wBAAwB,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAE9E,+CAA+C;QAC/C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QAC1D,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QAE1C,uEAAuE;QACvE,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YAExC,wDAAwD;YACxD,MAAM,iBAAiB,GAAG;gBACxB,wBAAwB,EAAE,0BAA0B,EAAE,4BAA4B,EAAE,iBAAiB;gBACrG,qBAAqB,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,uBAAuB;gBAC5F,uBAAuB,EAAE,2BAA2B,EAAE,cAAc,EAAE,eAAe;gBACrF,cAAc,EAAE,qBAAqB,EAAE,8BAA8B,EAAE,WAAW;aACnF,CAAC;YAEF,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACnC,MAAM,QAAQ,GAAG,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBACrD,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;oBACpB,MAAM,OAAO,GAAG,EAAiB,CAAC;oBAClC,gCAAgC;oBAChC,MAAM,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;oBACvD,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;gBAChD,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,+DAA+D;YAC/D,MAAM,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QAC3E,CAAC,EAAE,GAAG,CAAC,CAAC;IACV,CAAC;IAED,uCAAuC;IACvC,iBAAiB,CAAC,IAAY,EAAE,MAA4B;QAC1D,MAAM,SAAS,GAAG,qBAAqB,CAAC;QACxC,OAAO;YACL,IAAI;YACJ,MAAM,EAAE;gBACN,GAAG,SAAS,CAAC,MAAM;gBACnB,GAAG,MAAM;gBACT,IAAI,EAAE;oBACJ,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI;oBACxB,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;iBACvB;gBACD,QAAQ,EAAE;oBACR,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ;oBAC5B,GAAG,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;iBAC3B;gBACD,KAAK,EAAE;oBACL,MAAM,EAAE;wBACN,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM;wBAChC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,IAAI,EAAE,CAAC;qBAChC;oBACD,QAAQ,EAAE;wBACR,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;wBAClC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,IAAI,EAAE,CAAC;qBAClC;iBACF;aACF;SACF,CAAC;IACJ,CAAC;IAED,qCAAqC;IACrC,WAAW;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,yBAAyB;IACzB,WAAW,CAAC,SAAiB;QAC3B,IAAI;YACF,MAAM,KAAK,GAAgB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACjD,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;gBAC7B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,OAAO,IAAI,CAAC;aACb;YACD,OAAO,KAAK,CAAC;SACd;QAAC,MAAM;YACN,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAEO,aAAa,CAAC,KAAU;QAC9B,OAAO,KAAK;YACL,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ;YAC9B,KAAK,CAAC,MAAM;YACZ,OAAO,KAAK,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ;YACxC,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,QAAQ,CAAC;IACjD,CAAC;;;AA5KU,YAAY;IAHxB,UAAU,CAAC;QACV,UAAU,EAAE,MAAM;KACnB,CAAC;GACW,YAAY,CA6KxB;SA7KY,YAAY\",\n      sourcesContent: [\"import { Injectable } from '@angular/core';\\r\\nimport { BehaviorSubject, Observable } from 'rxjs';\\r\\nimport { ThemeConfig, ThemeColors, DEFAULT_THEME, MYSTICAL_PURPLE_THEME, AVAILABLE_THEMES } from './theme.config';\\r\\n\\r\\n@Injectable({\\r\\n  providedIn: 'root'\\r\\n})\\r\\nexport class ThemeService {\\r\\n  private readonly THEME_STORAGE_KEY = 'oracul-theme';\\r\\n  private currentThemeSubject = new BehaviorSubject<ThemeConfig>(MYSTICAL_PURPLE_THEME);\\r\\n  public currentTheme$ = this.currentThemeSubject.asObservable();\\r\\n\\r\\n  constructor() {\\r\\n    this.loadSavedTheme();\\r\\n  }\\r\\n\\r\\n  private loadSavedTheme(): void {\\r\\n    const savedThemeName = localStorage.getItem(this.THEME_STORAGE_KEY);\\r\\n    if (savedThemeName) {\\r\\n      const savedTheme = AVAILABLE_THEMES.find(theme => theme.name === savedThemeName);\\r\\n      if (savedTheme) {\\r\\n        this.setTheme(savedTheme);\\r\\n        return;\\r\\n      }\\r\\n    }\\r\\n    // Apply default theme\\r\\n    this.applyTheme(MYSTICAL_PURPLE_THEME);\\r\\n  }\\r\\n\\r\\n  setTheme(theme: ThemeConfig): void {\\r\\n    this.currentThemeSubject.next(theme);\\r\\n    this.applyTheme(theme);\\r\\n    localStorage.setItem(this.THEME_STORAGE_KEY, theme.name);\\r\\n  }\\r\\n\\r\\n  setThemeByName(themeName: string): void {\\r\\n    const theme = AVAILABLE_THEMES.find(t => t.name === themeName);\\r\\n    if (theme) {\\r\\n      this.setTheme(theme);\\r\\n    }\\r\\n  }\\r\\n\\r\\n  getCurrentTheme(): ThemeConfig {\\r\\n    return this.currentThemeSubject.value;\\r\\n  }\\r\\n\\r\\n  getAvailableThemes(): ThemeConfig[] {\\r\\n    return AVAILABLE_THEMES;\\r\\n  }\\r\\n\\r\\n  private applyTheme(theme: ThemeConfig): void {\\r\\n    const root = document.documentElement;\\r\\n    const body = document.body;\\r\\n    const colors = theme.colors;\\r\\n\\r\\n    // Add transition class to prevent flicker\\r\\n    body.classList.add('theme-changing');\\r\\n\\r\\n    // Apply CSS custom properties\\r\\n    root.style.setProperty('--theme-primary', colors.primary);\\r\\n    root.style.setProperty('--theme-primary-light', colors.primaryLight);\\r\\n    root.style.setProperty('--theme-primary-dark', colors.primaryDark);\\r\\n    root.style.setProperty('--theme-accent', colors.accent);\\r\\n    root.style.setProperty('--theme-accent-light', colors.accentLight);\\r\\n    root.style.setProperty('--theme-accent-dark', colors.accentDark);\\r\\n    root.style.setProperty('--theme-warn', colors.warn);\\r\\n    root.style.setProperty('--theme-success', colors.success);\\r\\n    root.style.setProperty('--theme-error', colors.error);\\r\\n    root.style.setProperty('--theme-background', colors.background);\\r\\n    root.style.setProperty('--theme-surface', colors.surface);\\r\\n\\r\\n    // Text colors\\r\\n    root.style.setProperty('--theme-text-primary', colors.text.primary);\\r\\n    root.style.setProperty('--theme-text-secondary', colors.text.secondary);\\r\\n    root.style.setProperty('--theme-text-disabled', colors.text.disabled);\\r\\n    root.style.setProperty('--theme-text-hint', colors.text.hint);\\r\\n\\r\\n    // Gradients\\r\\n    root.style.setProperty('--theme-gradient-primary', colors.gradient.primary);\\r\\n    root.style.setProperty('--theme-gradient-secondary', colors.gradient.secondary);\\r\\n    root.style.setProperty('--theme-gradient-auth', colors.gradient.auth);\\r\\n\\r\\n    // OAuth colors\\r\\n    root.style.setProperty('--theme-google-bg', colors.oauth.google.background);\\r\\n    root.style.setProperty('--theme-google-border', colors.oauth.google.border);\\r\\n    root.style.setProperty('--theme-google-text', colors.oauth.google.text);\\r\\n    root.style.setProperty('--theme-google-hover', colors.oauth.google.hover);\\r\\n\\r\\n    root.style.setProperty('--theme-facebook-bg', colors.oauth.facebook.background);\\r\\n    root.style.setProperty('--theme-facebook-border', colors.oauth.facebook.border);\\r\\n    root.style.setProperty('--theme-facebook-text', colors.oauth.facebook.text);\\r\\n    root.style.setProperty('--theme-facebook-hover', colors.oauth.facebook.hover);\\r\\n\\r\\n    // Update body class for theme-specific styling\\r\\n    body.className = body.className.replace(/theme-\\\\w+/g, '');\\r\\n    body.classList.add(`theme-${theme.name}`);\\r\\n\\r\\n    // Force a repaint to ensure Material components pick up the new colors\\r\\n    setTimeout(() => {\\r\\n      body.classList.remove('theme-changing');\\r\\n\\r\\n      // Force style recalculation for all Material components\\r\\n      const materialSelectors = [\\r\\n        '.mat-mdc-raised-button', '.mat-mdc-outlined-button', '.mat-mdc-unelevated-button', '.mat-mdc-button',\\r\\n        '.mat-mdc-form-field', '.mat-mdc-checkbox', '.mat-mdc-radio-button', '.mat-mdc-slide-toggle',\\r\\n        '.mat-mdc-progress-bar', '.mat-mdc-progress-spinner', '.mat-mdc-tab', '.mat-mdc-chip',\\r\\n        '.mat-toolbar', '.mat-mdc-menu-panel', '.mat-mdc-snack-bar-container', '.mat-card'\\r\\n      ];\\r\\n\\r\\n      materialSelectors.forEach(selector => {\\r\\n        const elements = document.querySelectorAll(selector);\\r\\n        elements.forEach(el => {\\r\\n          const element = el as HTMLElement;\\r\\n          // Force recomputation of styles\\r\\n          const computedStyle = window.getComputedStyle(element);\\r\\n          element.style.cssText = element.style.cssText;\\r\\n        });\\r\\n      });\\r\\n\\r\\n      // Dispatch a custom event to notify components of theme change\\r\\n      window.dispatchEvent(new CustomEvent('themeChanged', { detail: theme }));\\r\\n    }, 100);\\r\\n  }\\r\\n\\r\\n  // Helper method to create custom theme\\r\\n  createCustomTheme(name: string, colors: Partial<ThemeColors>): ThemeConfig {\\r\\n    const baseTheme = MYSTICAL_PURPLE_THEME;\\r\\n    return {\\r\\n      name,\\r\\n      colors: {\\r\\n        ...baseTheme.colors,\\r\\n        ...colors,\\r\\n        text: {\\r\\n          ...baseTheme.colors.text,\\r\\n          ...(colors.text || {})\\r\\n        },\\r\\n        gradient: {\\r\\n          ...baseTheme.colors.gradient,\\r\\n          ...(colors.gradient || {})\\r\\n        },\\r\\n        oauth: {\\r\\n          google: {\\r\\n            ...baseTheme.colors.oauth.google,\\r\\n            ...(colors.oauth?.google || {})\\r\\n          },\\r\\n          facebook: {\\r\\n            ...baseTheme.colors.oauth.facebook,\\r\\n            ...(colors.oauth?.facebook || {})\\r\\n          }\\r\\n        }\\r\\n      }\\r\\n    };\\r\\n  }\\r\\n\\r\\n  // Export current theme configuration\\r\\n  exportTheme(): string {\\r\\n    return JSON.stringify(this.getCurrentTheme(), null, 2);\\r\\n  }\\r\\n\\r\\n  // Import theme from JSON\\r\\n  importTheme(themeJson: string): boolean {\\r\\n    try {\\r\\n      const theme: ThemeConfig = JSON.parse(themeJson);\\r\\n      if (this.validateTheme(theme)) {\\r\\n        this.setTheme(theme);\\r\\n        return true;\\r\\n      }\\r\\n      return false;\\r\\n    } catch {\\r\\n      return false;\\r\\n    }\\r\\n  }\\r\\n\\r\\n  private validateTheme(theme: any): theme is ThemeConfig {\\r\\n    return theme &&\\r\\n           typeof theme.name === 'string' &&\\r\\n           theme.colors &&\\r\\n           typeof theme.colors.primary === 'string' &&\\r\\n           typeof theme.colors.accent === 'string';\\r\\n  }\\r\\n}\\r\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"d6430791d007be17bf415a1b69a2f1b80d8f8b1d\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_vl6nt70nd = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_vl6nt70nd();\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { MYSTICAL_PURPLE_THEME, AVAILABLE_THEMES } from './theme.config';\ncov_vl6nt70nd().s[0]++;\nlet ThemeService = class ThemeService {\n  constructor() {\n    cov_vl6nt70nd().f[0]++;\n    cov_vl6nt70nd().s[1]++;\n    this.THEME_STORAGE_KEY = 'oracul-theme';\n    cov_vl6nt70nd().s[2]++;\n    this.currentThemeSubject = new BehaviorSubject(MYSTICAL_PURPLE_THEME);\n    cov_vl6nt70nd().s[3]++;\n    this.currentTheme$ = this.currentThemeSubject.asObservable();\n    cov_vl6nt70nd().s[4]++;\n    this.loadSavedTheme();\n  }\n  loadSavedTheme() {\n    cov_vl6nt70nd().f[1]++;\n    const savedThemeName = (cov_vl6nt70nd().s[5]++, localStorage.getItem(this.THEME_STORAGE_KEY));\n    cov_vl6nt70nd().s[6]++;\n    if (savedThemeName) {\n      cov_vl6nt70nd().b[0][0]++;\n      const savedTheme = (cov_vl6nt70nd().s[7]++, AVAILABLE_THEMES.find(theme => {\n        cov_vl6nt70nd().f[2]++;\n        cov_vl6nt70nd().s[8]++;\n        return theme.name === savedThemeName;\n      }));\n      cov_vl6nt70nd().s[9]++;\n      if (savedTheme) {\n        cov_vl6nt70nd().b[1][0]++;\n        cov_vl6nt70nd().s[10]++;\n        this.setTheme(savedTheme);\n        cov_vl6nt70nd().s[11]++;\n        return;\n      } else {\n        cov_vl6nt70nd().b[1][1]++;\n      }\n    } else {\n      cov_vl6nt70nd().b[0][1]++;\n    }\n    // Apply default theme\n    cov_vl6nt70nd().s[12]++;\n    this.applyTheme(MYSTICAL_PURPLE_THEME);\n  }\n  setTheme(theme) {\n    cov_vl6nt70nd().f[3]++;\n    cov_vl6nt70nd().s[13]++;\n    this.currentThemeSubject.next(theme);\n    cov_vl6nt70nd().s[14]++;\n    this.applyTheme(theme);\n    cov_vl6nt70nd().s[15]++;\n    localStorage.setItem(this.THEME_STORAGE_KEY, theme.name);\n  }\n  setThemeByName(themeName) {\n    cov_vl6nt70nd().f[4]++;\n    const theme = (cov_vl6nt70nd().s[16]++, AVAILABLE_THEMES.find(t => {\n      cov_vl6nt70nd().f[5]++;\n      cov_vl6nt70nd().s[17]++;\n      return t.name === themeName;\n    }));\n    cov_vl6nt70nd().s[18]++;\n    if (theme) {\n      cov_vl6nt70nd().b[2][0]++;\n      cov_vl6nt70nd().s[19]++;\n      this.setTheme(theme);\n    } else {\n      cov_vl6nt70nd().b[2][1]++;\n    }\n  }\n  getCurrentTheme() {\n    cov_vl6nt70nd().f[6]++;\n    cov_vl6nt70nd().s[20]++;\n    return this.currentThemeSubject.value;\n  }\n  getAvailableThemes() {\n    cov_vl6nt70nd().f[7]++;\n    cov_vl6nt70nd().s[21]++;\n    return AVAILABLE_THEMES;\n  }\n  applyTheme(theme) {\n    cov_vl6nt70nd().f[8]++;\n    const root = (cov_vl6nt70nd().s[22]++, document.documentElement);\n    const body = (cov_vl6nt70nd().s[23]++, document.body);\n    const colors = (cov_vl6nt70nd().s[24]++, theme.colors);\n    // Add transition class to prevent flicker\n    cov_vl6nt70nd().s[25]++;\n    body.classList.add('theme-changing');\n    // Apply CSS custom properties\n    cov_vl6nt70nd().s[26]++;\n    root.style.setProperty('--theme-primary', colors.primary);\n    cov_vl6nt70nd().s[27]++;\n    root.style.setProperty('--theme-primary-light', colors.primaryLight);\n    cov_vl6nt70nd().s[28]++;\n    root.style.setProperty('--theme-primary-dark', colors.primaryDark);\n    cov_vl6nt70nd().s[29]++;\n    root.style.setProperty('--theme-accent', colors.accent);\n    cov_vl6nt70nd().s[30]++;\n    root.style.setProperty('--theme-accent-light', colors.accentLight);\n    cov_vl6nt70nd().s[31]++;\n    root.style.setProperty('--theme-accent-dark', colors.accentDark);\n    cov_vl6nt70nd().s[32]++;\n    root.style.setProperty('--theme-warn', colors.warn);\n    cov_vl6nt70nd().s[33]++;\n    root.style.setProperty('--theme-success', colors.success);\n    cov_vl6nt70nd().s[34]++;\n    root.style.setProperty('--theme-error', colors.error);\n    cov_vl6nt70nd().s[35]++;\n    root.style.setProperty('--theme-background', colors.background);\n    cov_vl6nt70nd().s[36]++;\n    root.style.setProperty('--theme-surface', colors.surface);\n    // Text colors\n    cov_vl6nt70nd().s[37]++;\n    root.style.setProperty('--theme-text-primary', colors.text.primary);\n    cov_vl6nt70nd().s[38]++;\n    root.style.setProperty('--theme-text-secondary', colors.text.secondary);\n    cov_vl6nt70nd().s[39]++;\n    root.style.setProperty('--theme-text-disabled', colors.text.disabled);\n    cov_vl6nt70nd().s[40]++;\n    root.style.setProperty('--theme-text-hint', colors.text.hint);\n    // Gradients\n    cov_vl6nt70nd().s[41]++;\n    root.style.setProperty('--theme-gradient-primary', colors.gradient.primary);\n    cov_vl6nt70nd().s[42]++;\n    root.style.setProperty('--theme-gradient-secondary', colors.gradient.secondary);\n    cov_vl6nt70nd().s[43]++;\n    root.style.setProperty('--theme-gradient-auth', colors.gradient.auth);\n    // OAuth colors\n    cov_vl6nt70nd().s[44]++;\n    root.style.setProperty('--theme-google-bg', colors.oauth.google.background);\n    cov_vl6nt70nd().s[45]++;\n    root.style.setProperty('--theme-google-border', colors.oauth.google.border);\n    cov_vl6nt70nd().s[46]++;\n    root.style.setProperty('--theme-google-text', colors.oauth.google.text);\n    cov_vl6nt70nd().s[47]++;\n    root.style.setProperty('--theme-google-hover', colors.oauth.google.hover);\n    cov_vl6nt70nd().s[48]++;\n    root.style.setProperty('--theme-facebook-bg', colors.oauth.facebook.background);\n    cov_vl6nt70nd().s[49]++;\n    root.style.setProperty('--theme-facebook-border', colors.oauth.facebook.border);\n    cov_vl6nt70nd().s[50]++;\n    root.style.setProperty('--theme-facebook-text', colors.oauth.facebook.text);\n    cov_vl6nt70nd().s[51]++;\n    root.style.setProperty('--theme-facebook-hover', colors.oauth.facebook.hover);\n    // Update body class for theme-specific styling\n    cov_vl6nt70nd().s[52]++;\n    body.className = body.className.replace(/theme-\\w+/g, '');\n    cov_vl6nt70nd().s[53]++;\n    body.classList.add(`theme-${theme.name}`);\n    // Force a repaint to ensure Material components pick up the new colors\n    cov_vl6nt70nd().s[54]++;\n    setTimeout(() => {\n      cov_vl6nt70nd().f[9]++;\n      cov_vl6nt70nd().s[55]++;\n      body.classList.remove('theme-changing');\n      // Force style recalculation for all Material components\n      const materialSelectors = (cov_vl6nt70nd().s[56]++, ['.mat-mdc-raised-button', '.mat-mdc-outlined-button', '.mat-mdc-unelevated-button', '.mat-mdc-button', '.mat-mdc-form-field', '.mat-mdc-checkbox', '.mat-mdc-radio-button', '.mat-mdc-slide-toggle', '.mat-mdc-progress-bar', '.mat-mdc-progress-spinner', '.mat-mdc-tab', '.mat-mdc-chip', '.mat-toolbar', '.mat-mdc-menu-panel', '.mat-mdc-snack-bar-container', '.mat-card']);\n      cov_vl6nt70nd().s[57]++;\n      materialSelectors.forEach(selector => {\n        cov_vl6nt70nd().f[10]++;\n        const elements = (cov_vl6nt70nd().s[58]++, document.querySelectorAll(selector));\n        cov_vl6nt70nd().s[59]++;\n        elements.forEach(el => {\n          cov_vl6nt70nd().f[11]++;\n          const element = (cov_vl6nt70nd().s[60]++, el);\n          // Force recomputation of styles\n          const computedStyle = (cov_vl6nt70nd().s[61]++, window.getComputedStyle(element));\n          cov_vl6nt70nd().s[62]++;\n          element.style.cssText = element.style.cssText;\n        });\n      });\n      // Dispatch a custom event to notify components of theme change\n      cov_vl6nt70nd().s[63]++;\n      window.dispatchEvent(new CustomEvent('themeChanged', {\n        detail: theme\n      }));\n    }, 100);\n  }\n  // Helper method to create custom theme\n  createCustomTheme(name, colors) {\n    cov_vl6nt70nd().f[12]++;\n    const baseTheme = (cov_vl6nt70nd().s[64]++, MYSTICAL_PURPLE_THEME);\n    cov_vl6nt70nd().s[65]++;\n    return {\n      name,\n      colors: {\n        ...baseTheme.colors,\n        ...colors,\n        text: {\n          ...baseTheme.colors.text,\n          ...((cov_vl6nt70nd().b[3][0]++, colors.text) || (cov_vl6nt70nd().b[3][1]++, {}))\n        },\n        gradient: {\n          ...baseTheme.colors.gradient,\n          ...((cov_vl6nt70nd().b[4][0]++, colors.gradient) || (cov_vl6nt70nd().b[4][1]++, {}))\n        },\n        oauth: {\n          google: {\n            ...baseTheme.colors.oauth.google,\n            ...((cov_vl6nt70nd().b[5][0]++, colors.oauth?.google) || (cov_vl6nt70nd().b[5][1]++, {}))\n          },\n          facebook: {\n            ...baseTheme.colors.oauth.facebook,\n            ...((cov_vl6nt70nd().b[6][0]++, colors.oauth?.facebook) || (cov_vl6nt70nd().b[6][1]++, {}))\n          }\n        }\n      }\n    };\n  }\n  // Export current theme configuration\n  exportTheme() {\n    cov_vl6nt70nd().f[13]++;\n    cov_vl6nt70nd().s[66]++;\n    return JSON.stringify(this.getCurrentTheme(), null, 2);\n  }\n  // Import theme from JSON\n  importTheme(themeJson) {\n    cov_vl6nt70nd().f[14]++;\n    cov_vl6nt70nd().s[67]++;\n    try {\n      const theme = (cov_vl6nt70nd().s[68]++, JSON.parse(themeJson));\n      cov_vl6nt70nd().s[69]++;\n      if (this.validateTheme(theme)) {\n        cov_vl6nt70nd().b[7][0]++;\n        cov_vl6nt70nd().s[70]++;\n        this.setTheme(theme);\n        cov_vl6nt70nd().s[71]++;\n        return true;\n      } else {\n        cov_vl6nt70nd().b[7][1]++;\n      }\n      cov_vl6nt70nd().s[72]++;\n      return false;\n    } catch {\n      cov_vl6nt70nd().s[73]++;\n      return false;\n    }\n  }\n  validateTheme(theme) {\n    cov_vl6nt70nd().f[15]++;\n    cov_vl6nt70nd().s[74]++;\n    return (cov_vl6nt70nd().b[8][0]++, theme) && (cov_vl6nt70nd().b[8][1]++, typeof theme.name === 'string') && (cov_vl6nt70nd().b[8][2]++, theme.colors) && (cov_vl6nt70nd().b[8][3]++, typeof theme.colors.primary === 'string') && (cov_vl6nt70nd().b[8][4]++, typeof theme.colors.accent === 'string');\n  }\n  static {\n    cov_vl6nt70nd().s[75]++;\n    this.ctorParameters = () => {\n      cov_vl6nt70nd().f[16]++;\n      cov_vl6nt70nd().s[76]++;\n      return [];\n    };\n  }\n};\ncov_vl6nt70nd().s[77]++;\nThemeService = __decorate([Injectable({\n  providedIn: 'root'\n})], ThemeService);\nexport { ThemeService };", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoBM;IAAA;MAAA;IAAA;EAAA;EAAA;AAAA;AAAA;;AApBN,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,eAAe,QAAoB,MAAM;AAClD,SAAkDC,qBAAqB,EAAEC,gBAAgB,QAAQ,gBAAgB;AAAC;AAK3G,IAAMC,YAAY,GAAlB,MAAMA,YAAY;EAKvBC;IAAA;IAAA;IAJiB,sBAAiB,GAAG,cAAc;IAAC;IAC5C,wBAAmB,GAAG,IAAIJ,eAAe,CAAcC,qBAAqB,CAAC;IAAC;IAC/E,kBAAa,GAAG,IAAI,CAACI,mBAAmB,CAACC,YAAY,EAAE;IAAC;IAG7D,IAAI,CAACC,cAAc,EAAE;EACvB;EAEQA,cAAc;IAAA;IACpB,MAAMC,cAAc,4BAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACC,iBAAiB,CAAC;IAAC;IACpE,IAAIH,cAAc,EAAE;MAAA;MAClB,MAAMI,UAAU,4BAAGV,gBAAgB,CAACW,IAAI,CAACC,KAAK,IAAI;QAAA;QAAA;QAAA,YAAK,CAACC,IAAI,KAAKP,cAAc;MAAd,CAAc,CAAC;MAAC;MACjF,IAAII,UAAU,EAAE;QAAA;QAAA;QACd,IAAI,CAACI,QAAQ,CAACJ,UAAU,CAAC;QAAC;QAC1B;OACD;QAAA;MAAA;KACF;MAAA;IAAA;IACD;IAAA;IACA,IAAI,CAACK,UAAU,CAAChB,qBAAqB,CAAC;EACxC;EAEAe,QAAQ,CAACF,KAAkB;IAAA;IAAA;IACzB,IAAI,CAACT,mBAAmB,CAACa,IAAI,CAACJ,KAAK,CAAC;IAAC;IACrC,IAAI,CAACG,UAAU,CAACH,KAAK,CAAC;IAAC;IACvBL,YAAY,CAACU,OAAO,CAAC,IAAI,CAACR,iBAAiB,EAAEG,KAAK,CAACC,IAAI,CAAC;EAC1D;EAEAK,cAAc,CAACC,SAAiB;IAAA;IAC9B,MAAMP,KAAK,6BAAGZ,gBAAgB,CAACW,IAAI,CAACS,CAAC,IAAI;MAAA;MAAA;MAAA,QAAC,CAACP,IAAI,KAAKM,SAAS;IAAT,CAAS,CAAC;IAAC;IAC/D,IAAIP,KAAK,EAAE;MAAA;MAAA;MACT,IAAI,CAACE,QAAQ,CAACF,KAAK,CAAC;KACrB;MAAA;IAAA;EACH;EAEAS,eAAe;IAAA;IAAA;IACb,OAAO,IAAI,CAAClB,mBAAmB,CAACmB,KAAK;EACvC;EAEAC,kBAAkB;IAAA;IAAA;IAChB,OAAOvB,gBAAgB;EACzB;EAEQe,UAAU,CAACH,KAAkB;IAAA;IACnC,MAAMY,IAAI,6BAAGC,QAAQ,CAACC,eAAe;IACrC,MAAMC,IAAI,6BAAGF,QAAQ,CAACE,IAAI;IAC1B,MAAMC,MAAM,6BAAGhB,KAAK,CAACgB,MAAM;IAE3B;IAAA;IACAD,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAEpC;IAAA;IACAN,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAEJ,MAAM,CAACK,OAAO,CAAC;IAAC;IAC1DT,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,uBAAuB,EAAEJ,MAAM,CAACM,YAAY,CAAC;IAAC;IACrEV,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAEJ,MAAM,CAACO,WAAW,CAAC;IAAC;IACnEX,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAEJ,MAAM,CAACQ,MAAM,CAAC;IAAC;IACxDZ,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAEJ,MAAM,CAACS,WAAW,CAAC;IAAC;IACnEb,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,qBAAqB,EAAEJ,MAAM,CAACU,UAAU,CAAC;IAAC;IACjEd,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,cAAc,EAAEJ,MAAM,CAACW,IAAI,CAAC;IAAC;IACpDf,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAEJ,MAAM,CAACY,OAAO,CAAC;IAAC;IAC1DhB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,eAAe,EAAEJ,MAAM,CAACa,KAAK,CAAC;IAAC;IACtDjB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,oBAAoB,EAAEJ,MAAM,CAACc,UAAU,CAAC;IAAC;IAChElB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAEJ,MAAM,CAACe,OAAO,CAAC;IAEzD;IAAA;IACAnB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAEJ,MAAM,CAACgB,IAAI,CAACX,OAAO,CAAC;IAAC;IACpET,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,wBAAwB,EAAEJ,MAAM,CAACgB,IAAI,CAACC,SAAS,CAAC;IAAC;IACxErB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,uBAAuB,EAAEJ,MAAM,CAACgB,IAAI,CAACE,QAAQ,CAAC;IAAC;IACtEtB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAEJ,MAAM,CAACgB,IAAI,CAACG,IAAI,CAAC;IAE7D;IAAA;IACAvB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,0BAA0B,EAAEJ,MAAM,CAACoB,QAAQ,CAACf,OAAO,CAAC;IAAC;IAC5ET,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,4BAA4B,EAAEJ,MAAM,CAACoB,QAAQ,CAACH,SAAS,CAAC;IAAC;IAChFrB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,uBAAuB,EAAEJ,MAAM,CAACoB,QAAQ,CAACC,IAAI,CAAC;IAErE;IAAA;IACAzB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAEJ,MAAM,CAACsB,KAAK,CAACC,MAAM,CAACT,UAAU,CAAC;IAAC;IAC5ElB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,uBAAuB,EAAEJ,MAAM,CAACsB,KAAK,CAACC,MAAM,CAACC,MAAM,CAAC;IAAC;IAC5E5B,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,qBAAqB,EAAEJ,MAAM,CAACsB,KAAK,CAACC,MAAM,CAACP,IAAI,CAAC;IAAC;IACxEpB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAEJ,MAAM,CAACsB,KAAK,CAACC,MAAM,CAACE,KAAK,CAAC;IAAC;IAE1E7B,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,qBAAqB,EAAEJ,MAAM,CAACsB,KAAK,CAACI,QAAQ,CAACZ,UAAU,CAAC;IAAC;IAChFlB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,yBAAyB,EAAEJ,MAAM,CAACsB,KAAK,CAACI,QAAQ,CAACF,MAAM,CAAC;IAAC;IAChF5B,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,uBAAuB,EAAEJ,MAAM,CAACsB,KAAK,CAACI,QAAQ,CAACV,IAAI,CAAC;IAAC;IAC5EpB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,wBAAwB,EAAEJ,MAAM,CAACsB,KAAK,CAACI,QAAQ,CAACD,KAAK,CAAC;IAE7E;IAAA;IACA1B,IAAI,CAAC4B,SAAS,GAAG5B,IAAI,CAAC4B,SAAS,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;IAAC;IAC1D7B,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC,SAASlB,KAAK,CAACC,IAAI,EAAE,CAAC;IAEzC;IAAA;IACA4C,UAAU,CAAC,MAAK;MAAA;MAAA;MACd9B,IAAI,CAACE,SAAS,CAAC6B,MAAM,CAAC,gBAAgB,CAAC;MAEvC;MACA,MAAMC,iBAAiB,6BAAG,CACxB,wBAAwB,EAAE,0BAA0B,EAAE,4BAA4B,EAAE,iBAAiB,EACrG,qBAAqB,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,uBAAuB,EAC5F,uBAAuB,EAAE,2BAA2B,EAAE,cAAc,EAAE,eAAe,EACrF,cAAc,EAAE,qBAAqB,EAAE,8BAA8B,EAAE,WAAW,CACnF;MAAC;MAEFA,iBAAiB,CAACC,OAAO,CAACC,QAAQ,IAAG;QAAA;QACnC,MAAMC,QAAQ,6BAAGrC,QAAQ,CAACsC,gBAAgB,CAACF,QAAQ,CAAC;QAAC;QACrDC,QAAQ,CAACF,OAAO,CAACI,EAAE,IAAG;UAAA;UACpB,MAAMC,OAAO,6BAAGD,EAAiB;UACjC;UACA,MAAME,aAAa,6BAAGC,MAAM,CAACC,gBAAgB,CAACH,OAAO,CAAC;UAAC;UACvDA,OAAO,CAAClC,KAAK,CAACsC,OAAO,GAAGJ,OAAO,CAAClC,KAAK,CAACsC,OAAO;QAC/C,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF;MAAA;MACAF,MAAM,CAACG,aAAa,CAAC,IAAIC,WAAW,CAAC,cAAc,EAAE;QAAEC,MAAM,EAAE5D;MAAK,CAAE,CAAC,CAAC;IAC1E,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACA6D,iBAAiB,CAAC5D,IAAY,EAAEe,MAA4B;IAAA;IAC1D,MAAM8C,SAAS,6BAAG3E,qBAAqB;IAAC;IACxC,OAAO;MACLc,IAAI;MACJe,MAAM,EAAE;QACN,GAAG8C,SAAS,CAAC9C,MAAM;QACnB,GAAGA,MAAM;QACTgB,IAAI,EAAE;UACJ,GAAG8B,SAAS,CAAC9C,MAAM,CAACgB,IAAI;UACxB,IAAI,kCAAM,CAACA,IAAI,iCAAI,EAAE;SACtB;QACDI,QAAQ,EAAE;UACR,GAAG0B,SAAS,CAAC9C,MAAM,CAACoB,QAAQ;UAC5B,IAAI,kCAAM,CAACA,QAAQ,iCAAI,EAAE;SAC1B;QACDE,KAAK,EAAE;UACLC,MAAM,EAAE;YACN,GAAGuB,SAAS,CAAC9C,MAAM,CAACsB,KAAK,CAACC,MAAM;YAChC,IAAI,kCAAM,CAACD,KAAK,EAAEC,MAAM,iCAAI,EAAE;WAC/B;UACDG,QAAQ,EAAE;YACR,GAAGoB,SAAS,CAAC9C,MAAM,CAACsB,KAAK,CAACI,QAAQ;YAClC,IAAI,kCAAM,CAACJ,KAAK,EAAEI,QAAQ,iCAAI,EAAE;;;;KAIvC;EACH;EAEA;EACAqB,WAAW;IAAA;IAAA;IACT,OAAOC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACxD,eAAe,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;EACxD;EAEA;EACAyD,WAAW,CAACC,SAAiB;IAAA;IAAA;IAC3B,IAAI;MACF,MAAMnE,KAAK,6BAAgBgE,IAAI,CAACI,KAAK,CAACD,SAAS,CAAC;MAAC;MACjD,IAAI,IAAI,CAACE,aAAa,CAACrE,KAAK,CAAC,EAAE;QAAA;QAAA;QAC7B,IAAI,CAACE,QAAQ,CAACF,KAAK,CAAC;QAAC;QACrB,OAAO,IAAI;OACZ;QAAA;MAAA;MAAA;MACD,OAAO,KAAK;KACb,CAAC,MAAM;MAAA;MACN,OAAO,KAAK;;EAEhB;EAEQqE,aAAa,CAACrE,KAAU;IAAA;IAAA;IAC9B,OAAO,iCAAK,iCACL,OAAOA,KAAK,CAACC,IAAI,KAAK,QAAQ,iCAC9BD,KAAK,CAACgB,MAAM,iCACZ,OAAOhB,KAAK,CAACgB,MAAM,CAACK,OAAO,KAAK,QAAQ,iCACxC,OAAOrB,KAAK,CAACgB,MAAM,CAACQ,MAAM,KAAK,QAAQ;EAChD;;;;;;;;;;;AA5KWnC,YAAY,eAHxBJ,UAAU,CAAC;EACVqF,UAAU,EAAE;CACb,CAAC,GACWjF,YAAY,CA6KxB;SA7KYA,YAAY", "names": ["Injectable", "BehaviorSubject", "MYSTICAL_PURPLE_THEME", "AVAILABLE_THEMES", "ThemeService", "constructor", "currentThemeSubject", "asObservable", "loadSavedTheme", "savedThemeName", "localStorage", "getItem", "THEME_STORAGE_KEY", "savedTheme", "find", "theme", "name", "setTheme", "applyTheme", "next", "setItem", "setThemeByName", "themeName", "t", "getCurrentTheme", "value", "getAvailableThemes", "root", "document", "documentElement", "body", "colors", "classList", "add", "style", "setProperty", "primary", "primaryLight", "primaryDark", "accent", "accentLight", "accentDark", "warn", "success", "error", "background", "surface", "text", "secondary", "disabled", "hint", "gradient", "auth", "o<PERSON>h", "google", "border", "hover", "facebook", "className", "replace", "setTimeout", "remove", "materialSelectors", "for<PERSON>ach", "selector", "elements", "querySelectorAll", "el", "element", "computedStyle", "window", "getComputedStyle", "cssText", "dispatchEvent", "CustomEvent", "detail", "createCustomTheme", "baseTheme", "exportTheme", "JSON", "stringify", "importTheme", "themeJson", "parse", "validateTheme", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\core\\theme\\theme.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { ThemeConfig, ThemeColors, DEFAULT_THEME, MYSTICAL_PURPLE_THEME, AVAILABLE_THEMES } from './theme.config';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ThemeService {\r\n  private readonly THEME_STORAGE_KEY = 'oracul-theme';\r\n  private currentThemeSubject = new BehaviorSubject<ThemeConfig>(MYSTICAL_PURPLE_THEME);\r\n  public currentTheme$ = this.currentThemeSubject.asObservable();\r\n\r\n  constructor() {\r\n    this.loadSavedTheme();\r\n  }\r\n\r\n  private loadSavedTheme(): void {\r\n    const savedThemeName = localStorage.getItem(this.THEME_STORAGE_KEY);\r\n    if (savedThemeName) {\r\n      const savedTheme = AVAILABLE_THEMES.find(theme => theme.name === savedThemeName);\r\n      if (savedTheme) {\r\n        this.setTheme(savedTheme);\r\n        return;\r\n      }\r\n    }\r\n    // Apply default theme\r\n    this.applyTheme(MYSTICAL_PURPLE_THEME);\r\n  }\r\n\r\n  setTheme(theme: ThemeConfig): void {\r\n    this.currentThemeSubject.next(theme);\r\n    this.applyTheme(theme);\r\n    localStorage.setItem(this.THEME_STORAGE_KEY, theme.name);\r\n  }\r\n\r\n  setThemeByName(themeName: string): void {\r\n    const theme = AVAILABLE_THEMES.find(t => t.name === themeName);\r\n    if (theme) {\r\n      this.setTheme(theme);\r\n    }\r\n  }\r\n\r\n  getCurrentTheme(): ThemeConfig {\r\n    return this.currentThemeSubject.value;\r\n  }\r\n\r\n  getAvailableThemes(): ThemeConfig[] {\r\n    return AVAILABLE_THEMES;\r\n  }\r\n\r\n  private applyTheme(theme: ThemeConfig): void {\r\n    const root = document.documentElement;\r\n    const body = document.body;\r\n    const colors = theme.colors;\r\n\r\n    // Add transition class to prevent flicker\r\n    body.classList.add('theme-changing');\r\n\r\n    // Apply CSS custom properties\r\n    root.style.setProperty('--theme-primary', colors.primary);\r\n    root.style.setProperty('--theme-primary-light', colors.primaryLight);\r\n    root.style.setProperty('--theme-primary-dark', colors.primaryDark);\r\n    root.style.setProperty('--theme-accent', colors.accent);\r\n    root.style.setProperty('--theme-accent-light', colors.accentLight);\r\n    root.style.setProperty('--theme-accent-dark', colors.accentDark);\r\n    root.style.setProperty('--theme-warn', colors.warn);\r\n    root.style.setProperty('--theme-success', colors.success);\r\n    root.style.setProperty('--theme-error', colors.error);\r\n    root.style.setProperty('--theme-background', colors.background);\r\n    root.style.setProperty('--theme-surface', colors.surface);\r\n\r\n    // Text colors\r\n    root.style.setProperty('--theme-text-primary', colors.text.primary);\r\n    root.style.setProperty('--theme-text-secondary', colors.text.secondary);\r\n    root.style.setProperty('--theme-text-disabled', colors.text.disabled);\r\n    root.style.setProperty('--theme-text-hint', colors.text.hint);\r\n\r\n    // Gradients\r\n    root.style.setProperty('--theme-gradient-primary', colors.gradient.primary);\r\n    root.style.setProperty('--theme-gradient-secondary', colors.gradient.secondary);\r\n    root.style.setProperty('--theme-gradient-auth', colors.gradient.auth);\r\n\r\n    // OAuth colors\r\n    root.style.setProperty('--theme-google-bg', colors.oauth.google.background);\r\n    root.style.setProperty('--theme-google-border', colors.oauth.google.border);\r\n    root.style.setProperty('--theme-google-text', colors.oauth.google.text);\r\n    root.style.setProperty('--theme-google-hover', colors.oauth.google.hover);\r\n\r\n    root.style.setProperty('--theme-facebook-bg', colors.oauth.facebook.background);\r\n    root.style.setProperty('--theme-facebook-border', colors.oauth.facebook.border);\r\n    root.style.setProperty('--theme-facebook-text', colors.oauth.facebook.text);\r\n    root.style.setProperty('--theme-facebook-hover', colors.oauth.facebook.hover);\r\n\r\n    // Update body class for theme-specific styling\r\n    body.className = body.className.replace(/theme-\\w+/g, '');\r\n    body.classList.add(`theme-${theme.name}`);\r\n\r\n    // Force a repaint to ensure Material components pick up the new colors\r\n    setTimeout(() => {\r\n      body.classList.remove('theme-changing');\r\n\r\n      // Force style recalculation for all Material components\r\n      const materialSelectors = [\r\n        '.mat-mdc-raised-button', '.mat-mdc-outlined-button', '.mat-mdc-unelevated-button', '.mat-mdc-button',\r\n        '.mat-mdc-form-field', '.mat-mdc-checkbox', '.mat-mdc-radio-button', '.mat-mdc-slide-toggle',\r\n        '.mat-mdc-progress-bar', '.mat-mdc-progress-spinner', '.mat-mdc-tab', '.mat-mdc-chip',\r\n        '.mat-toolbar', '.mat-mdc-menu-panel', '.mat-mdc-snack-bar-container', '.mat-card'\r\n      ];\r\n\r\n      materialSelectors.forEach(selector => {\r\n        const elements = document.querySelectorAll(selector);\r\n        elements.forEach(el => {\r\n          const element = el as HTMLElement;\r\n          // Force recomputation of styles\r\n          const computedStyle = window.getComputedStyle(element);\r\n          element.style.cssText = element.style.cssText;\r\n        });\r\n      });\r\n\r\n      // Dispatch a custom event to notify components of theme change\r\n      window.dispatchEvent(new CustomEvent('themeChanged', { detail: theme }));\r\n    }, 100);\r\n  }\r\n\r\n  // Helper method to create custom theme\r\n  createCustomTheme(name: string, colors: Partial<ThemeColors>): ThemeConfig {\r\n    const baseTheme = MYSTICAL_PURPLE_THEME;\r\n    return {\r\n      name,\r\n      colors: {\r\n        ...baseTheme.colors,\r\n        ...colors,\r\n        text: {\r\n          ...baseTheme.colors.text,\r\n          ...(colors.text || {})\r\n        },\r\n        gradient: {\r\n          ...baseTheme.colors.gradient,\r\n          ...(colors.gradient || {})\r\n        },\r\n        oauth: {\r\n          google: {\r\n            ...baseTheme.colors.oauth.google,\r\n            ...(colors.oauth?.google || {})\r\n          },\r\n          facebook: {\r\n            ...baseTheme.colors.oauth.facebook,\r\n            ...(colors.oauth?.facebook || {})\r\n          }\r\n        }\r\n      }\r\n    };\r\n  }\r\n\r\n  // Export current theme configuration\r\n  exportTheme(): string {\r\n    return JSON.stringify(this.getCurrentTheme(), null, 2);\r\n  }\r\n\r\n  // Import theme from JSON\r\n  importTheme(themeJson: string): boolean {\r\n    try {\r\n      const theme: ThemeConfig = JSON.parse(themeJson);\r\n      if (this.validateTheme(theme)) {\r\n        this.setTheme(theme);\r\n        return true;\r\n      }\r\n      return false;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private validateTheme(theme: any): theme is ThemeConfig {\r\n    return theme &&\r\n           typeof theme.name === 'string' &&\r\n           theme.colors &&\r\n           typeof theme.colors.primary === 'string' &&\r\n           typeof theme.colors.accent === 'string';\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}