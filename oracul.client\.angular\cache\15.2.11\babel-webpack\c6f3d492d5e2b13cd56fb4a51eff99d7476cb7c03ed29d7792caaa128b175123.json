{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/Harmonia/oracul.client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { AppComponent } from './app.component';\nimport { ThemeService } from './core/theme/theme.service';\ndescribe('AppComponent', () => {\n  let component;\n  let fixture;\n  let httpMock;\n  let mockThemeService;\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    const themeServiceSpy = jasmine.createSpyObj('ThemeService', ['getCurrentTheme']);\n    yield TestBed.configureTestingModule({\n      declarations: [AppComponent],\n      imports: [HttpClientTestingModule],\n      providers: [{\n        provide: ThemeService,\n        useValue: themeServiceSpy\n      }]\n    }).compileComponents();\n    mockThemeService = TestBed.inject(ThemeService);\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(AppComponent);\n    component = fixture.componentInstance;\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should create the app', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should initialize theme on init', () => {\n    spyOn(component, 'ngOnInit').and.callThrough();\n    component.ngOnInit();\n    expect(component.ngOnInit).toHaveBeenCalled();\n  });\n});", "map": {"version": 3, "mappings": ";AAAA,SAASA,uBAAuB,EAAEC,qBAAqB,QAAQ,8BAA8B;AAC7F,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,4BAA4B;AAEzDC,QAAQ,CAAC,cAAc,EAAE,MAAK;EAC5B,IAAIC,SAAuB;EAC3B,IAAIC,OAAuC;EAC3C,IAAIC,QAA+B;EACnC,IAAIC,gBAA8C;EAElDC,UAAU,iCAAC,aAAW;IACpB,MAAMC,eAAe,GAAGC,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,iBAAiB,CAAC,CAAC;IAEjF,MAAMX,OAAO,CAACY,sBAAsB,CAAC;MACnCC,YAAY,EAAE,CAACZ,YAAY,CAAC;MAC5Ba,OAAO,EAAE,CAAChB,uBAAuB,CAAC;MAClCiB,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEd,YAAY;QAAEe,QAAQ,EAAER;MAAe,CAAE;KAEvD,CAAC,CAACS,iBAAiB,EAAE;IAEtBX,gBAAgB,GAAGP,OAAO,CAACmB,MAAM,CAACjB,YAAY,CAAiC;EACjF,CAAC,EAAC;EAEFM,UAAU,CAAC,MAAK;IACdH,OAAO,GAAGL,OAAO,CAACoB,eAAe,CAACnB,YAAY,CAAC;IAC/CG,SAAS,GAAGC,OAAO,CAACgB,iBAAiB;IACrCf,QAAQ,GAAGN,OAAO,CAACmB,MAAM,CAACpB,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFuB,SAAS,CAAC,MAAK;IACbhB,QAAQ,CAACiB,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,uBAAuB,EAAE,MAAK;IAC/BC,MAAM,CAACrB,SAAS,CAAC,CAACsB,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,iCAAiC,EAAE,MAAK;IACzCG,KAAK,CAACvB,SAAS,EAAE,UAAU,CAAC,CAACwB,GAAG,CAACC,WAAW,EAAE;IAE9CzB,SAAS,CAAC0B,QAAQ,EAAE;IAEpBL,MAAM,CAACrB,SAAS,CAAC0B,QAAQ,CAAC,CAACC,gBAAgB,EAAE;EAC/C,CAAC,CAAC;AACJ,CAAC,CAAC", "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "AppComponent", "ThemeService", "describe", "component", "fixture", "httpMock", "mockThemeService", "beforeEach", "themeServiceSpy", "jasmine", "createSpyObj", "configureTestingModule", "declarations", "imports", "providers", "provide", "useValue", "compileComponents", "inject", "createComponent", "componentInstance", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "spyOn", "and", "callThrough", "ngOnInit", "toHaveBeenCalled"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\app.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\r\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\r\nimport { AppComponent } from './app.component';\r\nimport { ThemeService } from './core/theme/theme.service';\r\n\r\ndescribe('AppComponent', () => {\r\n  let component: AppComponent;\r\n  let fixture: ComponentFixture<AppComponent>;\r\n  let httpMock: HttpTestingController;\r\n  let mockThemeService: jasmine.SpyObj<ThemeService>;\r\n\r\n  beforeEach(async () => {\r\n    const themeServiceSpy = jasmine.createSpyObj('ThemeService', ['getCurrentTheme']);\r\n\r\n    await TestBed.configureTestingModule({\r\n      declarations: [AppComponent],\r\n      imports: [HttpClientTestingModule],\r\n      providers: [\r\n        { provide: ThemeService, useValue: themeServiceSpy }\r\n      ]\r\n    }).compileComponents();\r\n\r\n    mockThemeService = TestBed.inject(ThemeService) as jasmine.SpyObj<ThemeService>;\r\n  });\r\n\r\n  beforeEach(() => {\r\n    fixture = TestBed.createComponent(AppComponent);\r\n    component = fixture.componentInstance;\r\n    httpMock = TestBed.inject(HttpTestingController);\r\n  });\r\n\r\n  afterEach(() => {\r\n    httpMock.verify();\r\n  });\r\n\r\n  it('should create the app', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n\r\n  it('should initialize theme on init', () => {\r\n    spyOn(component, 'ngOnInit').and.callThrough();\r\n\r\n    component.ngOnInit();\r\n\r\n    expect(component.ngOnInit).toHaveBeenCalled();\r\n  });\r\n});"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}