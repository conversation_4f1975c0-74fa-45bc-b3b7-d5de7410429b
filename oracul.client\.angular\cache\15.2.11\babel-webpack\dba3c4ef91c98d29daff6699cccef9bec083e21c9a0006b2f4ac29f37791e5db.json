{"ast": null, "code": "import { FormGroup, Validators, FormArray } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/profile.service\";\nimport * as i3 from \"../../../auth/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/select\";\nimport * as i13 from \"@angular/material/core\";\nimport * as i14 from \"@angular/material/checkbox\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nimport * as i16 from \"@angular/material/slide-toggle\";\nfunction ProfileEditComponent_div_0_mat_icon_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 77);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"save\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_spinner_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 78);\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"camera_alt\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 79);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"photo_camera\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_spinner_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 78);\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"camera_alt\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 79);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"photo_camera\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.getErrorMessage(\"firstName\"), \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.getErrorMessage(\"lastName\"), \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.getErrorMessage(\"contactInfo.email\"), \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_130_mat_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r32 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r32.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r32.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_130_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 24)(2, \"mat-form-field\", 81)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-form-field\", 83)(7, \"mat-label\");\n    i0.ɵɵtext(8, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-select\", 84);\n    i0.ɵɵtemplate(10, ProfileEditComponent_div_0_div_130_mat_option_10_Template, 2, 2, \"mat-option\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 85)(12, \"mat-checkbox\", 52);\n    i0.ɵɵtext(13, \"Public\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-checkbox\", 86);\n    i0.ɵɵtext(15, \"Primary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_div_130_Template_button_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r34);\n      const i_r30 = restoredCtx.index;\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.removePhoneNumber(i_r30));\n    });\n    i0.ɵɵelementStart(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"delete\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const i_r30 = ctx.index;\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroupName\", i_r30);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r17.getPhoneTypeOptions());\n  }\n}\nfunction ProfileEditComponent_div_0_div_169_mat_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const platform_r38 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", platform_r38.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", platform_r38.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_169_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 24)(2, \"mat-form-field\", 90)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Platform\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-select\", 91);\n    i0.ɵɵtemplate(6, ProfileEditComponent_div_0_div_169_mat_option_6_Template, 2, 2, \"mat-option\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-form-field\", 92)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"URL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 94)(12, \"mat-checkbox\", 52);\n    i0.ɵɵtext(13, \"Public\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_div_169_Template_button_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r40);\n      const i_r36 = restoredCtx.index;\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r39.removeSocialLink(i_r36));\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"delete\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const i_r36 = ctx.index;\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroupName\", i_r36);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r18.getPlatformOptions());\n  }\n}\nfunction ProfileEditComponent_div_0_div_182_mat_error_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Skill name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_div_182_mat_option_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r47 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r47.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r47.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_182_mat_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const level_r48 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", level_r48.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", level_r48.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_182_mat_error_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Proficiency level is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_div_182_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"div\", 96)(2, \"mat-form-field\", 97)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Skill Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 98);\n    i0.ɵɵtemplate(6, ProfileEditComponent_div_0_div_182_mat_error_6_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-form-field\", 99)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"mat-select\", 100)(11, \"mat-option\", 101);\n    i0.ɵɵtext(12, \"Select Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, ProfileEditComponent_div_0_div_182_mat_option_13_Template, 2, 2, \"mat-option\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"mat-form-field\", 102)(15, \"mat-label\");\n    i0.ɵɵtext(16, \"Proficiency Level\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-select\", 103);\n    i0.ɵɵtemplate(18, ProfileEditComponent_div_0_div_182_mat_option_18_Template, 2, 2, \"mat-option\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ProfileEditComponent_div_0_div_182_mat_error_19_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_div_182_Template_button_click_20_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r50);\n      const i_r42 = restoredCtx.index;\n      const ctx_r49 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r49.removeSkill(i_r42));\n    });\n    i0.ɵɵelementStart(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const skill_r41 = ctx.$implicit;\n    const i_r42 = ctx.index;\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    let tmp_1_0;\n    let tmp_4_0;\n    i0.ɵɵproperty(\"formGroupName\", i_r42);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = skill_r41.get(\"name\")) == null ? null : tmp_1_0.hasError(\"required\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r19.getSkillCategoryOptions());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r19.getProficiencyLevelOptions());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = skill_r41.get(\"proficiencyLevel\")) == null ? null : tmp_4_0.hasError(\"required\"));\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_202_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Rate must be positive \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_203_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Rate cannot exceed 10,000 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_210_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Rate must be positive \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_211_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Rate cannot exceed 10,000 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_option_216_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const currency_r51 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", currency_r51.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", currency_r51.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_217_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Currency is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_div_226_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Service name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_div_226_mat_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r60 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r60.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r60.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_226_mat_error_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Description is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_div_226_mat_error_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Price is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_div_226_mat_error_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Price must be positive \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_div_226_mat_option_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const currency_r61 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", currency_r61.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", currency_r61.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_226_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r63 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"div\", 106)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-slide-toggle\", 107);\n    i0.ɵɵtext(5, \" Active \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 108)(7, \"mat-form-field\", 109)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Service Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 110);\n    i0.ɵɵtemplate(11, ProfileEditComponent_div_0_div_226_mat_error_11_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"mat-form-field\", 111)(13, \"mat-label\");\n    i0.ɵɵtext(14, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"mat-select\", 100)(16, \"mat-option\", 101);\n    i0.ɵɵtext(17, \"Select Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, ProfileEditComponent_div_0_div_226_mat_option_18_Template, 2, 2, \"mat-option\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-form-field\", 112)(20, \"mat-label\");\n    i0.ɵɵtext(21, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"textarea\", 113);\n    i0.ɵɵtemplate(23, ProfileEditComponent_div_0_div_226_mat_error_23_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 114)(25, \"mat-form-field\", 115)(26, \"mat-label\");\n    i0.ɵɵtext(27, \"Price\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(28, \"input\", 116);\n    i0.ɵɵelementStart(29, \"span\", 62);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, ProfileEditComponent_div_0_div_226_mat_error_31_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵtemplate(32, ProfileEditComponent_div_0_div_226_mat_error_32_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"mat-form-field\", 64)(34, \"mat-label\");\n    i0.ɵɵtext(35, \"Currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"mat-select\", 65);\n    i0.ɵɵtemplate(37, ProfileEditComponent_div_0_div_226_mat_option_37_Template, 2, 2, \"mat-option\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"mat-form-field\", 117)(39, \"mat-label\");\n    i0.ɵɵtext(40, \"Duration (minutes)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(41, \"input\", 118);\n    i0.ɵɵelementStart(42, \"mat-hint\");\n    i0.ɵɵtext(43, \"Optional - leave blank if not applicable\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"button\", 119);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_div_226_Template_button_click_44_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r63);\n      const i_r53 = restoredCtx.index;\n      const ctx_r62 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r62.removeServiceOffering(i_r53));\n    });\n    i0.ɵɵelementStart(45, \"mat-icon\");\n    i0.ɵɵtext(46, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const service_r52 = ctx.$implicit;\n    const i_r53 = ctx.index;\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    let tmp_2_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    i0.ɵɵproperty(\"formGroupName\", i_r53);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Service \", i_r53 + 1, \"\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = service_r52.get(\"name\")) == null ? null : tmp_2_0.hasError(\"required\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r26.getServiceCategoryOptions());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = service_r52.get(\"description\")) == null ? null : tmp_4_0.hasError(\"required\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(((tmp_5_0 = service_r52.get(\"currency\")) == null ? null : tmp_5_0.value) || \"BGN\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = service_r52.get(\"price\")) == null ? null : tmp_6_0.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = service_r52.get(\"price\")) == null ? null : tmp_7_0.hasError(\"min\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r26.getCurrencyOptions());\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_244_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 79);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_245_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"save\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"h1\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Edit Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 4)(7, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r64.onCancel());\n    });\n    i0.ɵɵtext(8, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r66.onSubmit());\n    });\n    i0.ɵɵtemplate(10, ProfileEditComponent_div_0_mat_icon_10_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtemplate(11, ProfileEditComponent_div_0_mat_icon_11_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"form\", 8)(14, \"mat-card\", 9)(15, \"mat-card-header\")(16, \"mat-card-title\");\n    i0.ɵɵtext(17, \"Profile Photos\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"mat-card-content\")(19, \"div\", 10)(20, \"div\", 11)(21, \"div\", 12);\n    i0.ɵɵelement(22, \"img\", 13);\n    i0.ɵɵelementStart(23, \"div\", 14);\n    i0.ɵɵtemplate(24, ProfileEditComponent_div_0_mat_spinner_24_Template, 1, 0, \"mat-spinner\", 15);\n    i0.ɵɵtemplate(25, ProfileEditComponent_div_0_mat_icon_25_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"input\", 16, 17);\n    i0.ɵɵlistener(\"change\", function ProfileEditComponent_div_0_Template_input_change_26_listener($event) {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r67 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r67.onProfilePhotoSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const _r6 = i0.ɵɵreference(27);\n      return i0.ɵɵresetView(_r6.click());\n    });\n    i0.ɵɵtemplate(29, ProfileEditComponent_div_0_mat_icon_29_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtemplate(30, ProfileEditComponent_div_0_mat_icon_30_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\", 18);\n    i0.ɵɵtext(33, \"Max 5MB \\u2022 JPEG, PNG, GIF, WebP\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 19)(35, \"div\", 20)(36, \"div\", 21)(37, \"div\", 14);\n    i0.ɵɵtemplate(38, ProfileEditComponent_div_0_mat_spinner_38_Template, 1, 0, \"mat-spinner\", 15);\n    i0.ɵɵtemplate(39, ProfileEditComponent_div_0_mat_icon_39_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"input\", 16, 22);\n    i0.ɵɵlistener(\"change\", function ProfileEditComponent_div_0_Template_input_change_40_listener($event) {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r69 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r69.onCoverPhotoSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_42_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const _r11 = i0.ɵɵreference(41);\n      return i0.ɵɵresetView(_r11.click());\n    });\n    i0.ɵɵtemplate(43, ProfileEditComponent_div_0_mat_icon_43_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtemplate(44, ProfileEditComponent_div_0_mat_icon_44_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"p\", 18);\n    i0.ɵɵtext(47, \"Max 10MB \\u2022 JPEG, PNG, GIF, WebP\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(48, \"mat-card\", 23)(49, \"mat-card-header\")(50, \"mat-card-title\");\n    i0.ɵɵtext(51, \"Basic Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"mat-card-content\")(53, \"div\", 24)(54, \"mat-form-field\", 25)(55, \"mat-label\");\n    i0.ɵɵtext(56, \"First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(57, \"input\", 26);\n    i0.ɵɵtemplate(58, ProfileEditComponent_div_0_mat_error_58_Template, 2, 1, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"mat-form-field\", 25)(60, \"mat-label\");\n    i0.ɵɵtext(61, \"Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(62, \"input\", 27);\n    i0.ɵɵtemplate(63, ProfileEditComponent_div_0_mat_error_63_Template, 2, 1, \"mat-error\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"mat-form-field\", 28)(65, \"mat-label\");\n    i0.ɵɵtext(66, \"Professional Title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(67, \"input\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"mat-form-field\", 28)(69, \"mat-label\");\n    i0.ɵɵtext(70, \"Professional Headline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(71, \"textarea\", 30);\n    i0.ɵɵelementStart(72, \"mat-hint\", 31);\n    i0.ɵɵtext(73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(74, \"mat-form-field\", 28)(75, \"mat-label\");\n    i0.ɵɵtext(76, \"Professional Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(77, \"textarea\", 32);\n    i0.ɵɵelementStart(78, \"mat-hint\", 31);\n    i0.ɵɵtext(79);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(80, \"mat-card\", 33)(81, \"mat-card-header\")(82, \"mat-card-title\");\n    i0.ɵɵtext(83, \"Location\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(84, \"mat-card-content\")(85, \"div\", 24)(86, \"mat-form-field\", 25)(87, \"mat-label\");\n    i0.ɵɵtext(88, \"City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(89, \"input\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"mat-form-field\", 25)(91, \"mat-label\");\n    i0.ɵɵtext(92, \"State/Province\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(93, \"input\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(94, \"div\", 24)(95, \"mat-form-field\", 25)(96, \"mat-label\");\n    i0.ɵɵtext(97, \"Country\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(98, \"input\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(99, \"mat-form-field\", 25)(100, \"mat-label\");\n    i0.ɵɵtext(101, \"Display Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(102, \"input\", 37);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(103, \"mat-card\", 38)(104, \"mat-card-header\")(105, \"mat-card-title\");\n    i0.ɵɵtext(106, \"Contact Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(107, \"mat-card-content\")(108, \"div\", 24)(109, \"mat-form-field\", 25)(110, \"mat-label\");\n    i0.ɵɵtext(111, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(112, \"input\", 39);\n    i0.ɵɵtemplate(113, ProfileEditComponent_div_0_mat_error_113_Template, 2, 1, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(114, \"div\", 40)(115, \"mat-checkbox\", 41);\n    i0.ɵɵtext(116, \" Make email public \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(117, \"div\", 24)(118, \"mat-form-field\", 25)(119, \"mat-label\");\n    i0.ɵɵtext(120, \"Website\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(121, \"input\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(122, \"mat-form-field\", 25)(123, \"mat-label\");\n    i0.ɵɵtext(124, \"Portfolio URL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(125, \"input\", 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(126, \"div\", 44)(127, \"h4\");\n    i0.ɵɵtext(128, \"Phone Numbers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(129, \"div\", 45);\n    i0.ɵɵtemplate(130, ProfileEditComponent_div_0_div_130_Template, 19, 2, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(131, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_131_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r71 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r71.addPhoneNumber());\n    });\n    i0.ɵɵelementStart(132, \"mat-icon\");\n    i0.ɵɵtext(133, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(134, \" Add Phone Number \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(135, \"div\", 48)(136, \"h4\");\n    i0.ɵɵtext(137, \"Business Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(138, \"mat-form-field\", 28)(139, \"mat-label\");\n    i0.ɵɵtext(140, \"Street Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(141, \"input\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(142, \"div\", 24)(143, \"mat-form-field\", 50)(144, \"mat-label\");\n    i0.ɵɵtext(145, \"City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(146, \"input\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(147, \"mat-form-field\", 50)(148, \"mat-label\");\n    i0.ɵɵtext(149, \"State\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(150, \"input\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(151, \"mat-form-field\", 50)(152, \"mat-label\");\n    i0.ɵɵtext(153, \"Postal Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(154, \"input\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(155, \"div\", 24)(156, \"mat-form-field\", 25)(157, \"mat-label\");\n    i0.ɵɵtext(158, \"Country\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(159, \"input\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(160, \"div\", 40)(161, \"mat-checkbox\", 52);\n    i0.ɵɵtext(162, \" Make business address public \");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(163, \"mat-card\", 23)(164, \"mat-card-header\")(165, \"mat-card-title\");\n    i0.ɵɵtext(166, \"Social Links\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(167, \"mat-card-content\")(168, \"div\", 53);\n    i0.ɵɵtemplate(169, ProfileEditComponent_div_0_div_169_Template, 17, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(170, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_170_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.addSocialLink());\n    });\n    i0.ɵɵelementStart(171, \"mat-icon\");\n    i0.ɵɵtext(172, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(173, \" Add Social Link \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(174, \"mat-card\", 23)(175, \"mat-card-header\")(176, \"mat-card-title\");\n    i0.ɵɵtext(177, \"Skills & Expertise\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(178, \"mat-card-subtitle\");\n    i0.ɵɵtext(179, \"Add your skills and areas of expertise\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(180, \"mat-card-content\")(181, \"div\", 55);\n    i0.ɵɵtemplate(182, ProfileEditComponent_div_0_div_182_Template, 23, 5, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(183, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_183_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.addSkill());\n    });\n    i0.ɵɵelementStart(184, \"mat-icon\");\n    i0.ɵɵtext(185, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(186, \" Add Skill \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(187, \"mat-card\", 23)(188, \"mat-card-header\")(189, \"mat-card-title\");\n    i0.ɵɵtext(190, \"Consultation Rates\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(191, \"mat-card-subtitle\");\n    i0.ɵɵtext(192, \"Set your pricing for consultations\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(193, \"mat-card-content\")(194, \"div\", 58)(195, \"div\", 59)(196, \"mat-form-field\", 60)(197, \"mat-label\");\n    i0.ɵɵtext(198, \"Hourly Rate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(199, \"input\", 61);\n    i0.ɵɵelementStart(200, \"span\", 62);\n    i0.ɵɵtext(201);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(202, ProfileEditComponent_div_0_mat_error_202_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵtemplate(203, ProfileEditComponent_div_0_mat_error_203_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(204, \"mat-form-field\", 60)(205, \"mat-label\");\n    i0.ɵɵtext(206, \"Session Rate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(207, \"input\", 63);\n    i0.ɵɵelementStart(208, \"span\", 62);\n    i0.ɵɵtext(209);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(210, ProfileEditComponent_div_0_mat_error_210_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵtemplate(211, ProfileEditComponent_div_0_mat_error_211_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(212, \"mat-form-field\", 64)(213, \"mat-label\");\n    i0.ɵɵtext(214, \"Currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(215, \"mat-select\", 65);\n    i0.ɵɵtemplate(216, ProfileEditComponent_div_0_mat_option_216_Template, 2, 2, \"mat-option\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(217, ProfileEditComponent_div_0_mat_error_217_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(218, \"mat-card\", 23)(219, \"mat-card-header\")(220, \"mat-card-title\");\n    i0.ɵɵtext(221, \"Service Offerings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(222, \"mat-card-subtitle\");\n    i0.ɵɵtext(223, \"Define your specific services and packages\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(224, \"mat-card-content\")(225, \"div\", 67);\n    i0.ɵɵtemplate(226, ProfileEditComponent_div_0_div_226_Template, 47, 9, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(227, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_227_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.addServiceOffering());\n    });\n    i0.ɵɵelementStart(228, \"mat-icon\");\n    i0.ɵɵtext(229, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(230, \" Add Service \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(231, \"mat-card\", 23)(232, \"mat-card-header\")(233, \"mat-card-title\");\n    i0.ɵɵtext(234, \"Privacy Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(235, \"mat-card-content\")(236, \"div\", 70)(237, \"mat-slide-toggle\", 71)(238, \"span\", 72);\n    i0.ɵɵtext(239, \"Make profile public\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(240, \"p\", 73);\n    i0.ɵɵtext(241, \" When enabled, your profile will be visible to everyone and searchable. When disabled, only you can see your profile. \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(242, \"div\", 74)(243, \"button\", 75);\n    i0.ɵɵtemplate(244, ProfileEditComponent_div_0_mat_icon_244_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtemplate(245, ProfileEditComponent_div_0_mat_icon_245_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtext(246);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(247, \"button\", 76);\n    i0.ɵɵtext(248, \" Cancel \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_22_0;\n    let tmp_23_0;\n    let tmp_24_0;\n    let tmp_25_0;\n    let tmp_26_0;\n    let tmp_30_0;\n    let tmp_31_0;\n    let tmp_32_0;\n    let tmp_33_0;\n    let tmp_34_0;\n    let tmp_35_0;\n    let tmp_37_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isSaving);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isSaving || ctx_r0.profileForm.invalid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isSaving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isSaving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isSaving ? \"Saving...\" : \"Save Changes\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.profileForm);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"src\", ctx_r0.profilePhotoPreview || (ctx_r0.profile == null ? null : ctx_r0.profile.profilePhotoUrl) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"uploading\", ctx_r0.isUploadingProfilePhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isUploadingProfilePhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isUploadingProfilePhoto);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isUploadingProfilePhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isUploadingProfilePhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isUploadingProfilePhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isUploadingProfilePhoto ? \"Uploading...\" : \"Change Profile Photo\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleProp(\"background-image\", ctx_r0.coverPhotoPreview ? \"url(\" + ctx_r0.coverPhotoPreview + \")\" : ctx_r0.profile && ctx_r0.profile.coverPhotoUrl ? \"url(\" + ctx_r0.profile.coverPhotoUrl + \")\" : \"var(--theme-gradient-primary)\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"uploading\", ctx_r0.isUploadingCoverPhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isUploadingCoverPhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isUploadingCoverPhoto);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isUploadingCoverPhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isUploadingCoverPhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isUploadingCoverPhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isUploadingCoverPhoto ? \"Uploading...\" : \"Change Cover Photo\", \" \");\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_22_0 = ctx_r0.profileForm.get(\"firstName\")) == null ? null : tmp_22_0.invalid) && ((tmp_22_0 = ctx_r0.profileForm.get(\"firstName\")) == null ? null : tmp_22_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_23_0 = ctx_r0.profileForm.get(\"lastName\")) == null ? null : tmp_23_0.invalid) && ((tmp_23_0 = ctx_r0.profileForm.get(\"lastName\")) == null ? null : tmp_23_0.touched));\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\", ((tmp_24_0 = ctx_r0.profileForm.get(\"headline\")) == null ? null : tmp_24_0.value == null ? null : tmp_24_0.value.length) || 0, \"/220\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ((tmp_25_0 = ctx_r0.profileForm.get(\"summary\")) == null ? null : tmp_25_0.value == null ? null : tmp_25_0.value.length) || 0, \"/2000\");\n    i0.ɵɵadvance(34);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_26_0 = ctx_r0.profileForm.get(\"contactInfo.email\")) == null ? null : tmp_26_0.invalid) && ((tmp_26_0 = ctx_r0.profileForm.get(\"contactInfo.email\")) == null ? null : tmp_26_0.touched));\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.phoneNumbers.controls);\n    i0.ɵɵadvance(39);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.socialLinks.controls);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.skills.controls);\n    i0.ɵɵadvance(19);\n    i0.ɵɵtextInterpolate(((tmp_30_0 = ctx_r0.profileForm.get(\"consultationRates.currency\")) == null ? null : tmp_30_0.value) || \"BGN\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_31_0 = ctx_r0.profileForm.get(\"consultationRates.hourlyRate\")) == null ? null : tmp_31_0.hasError(\"min\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_32_0 = ctx_r0.profileForm.get(\"consultationRates.hourlyRate\")) == null ? null : tmp_32_0.hasError(\"max\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(((tmp_33_0 = ctx_r0.profileForm.get(\"consultationRates.currency\")) == null ? null : tmp_33_0.value) || \"BGN\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_34_0 = ctx_r0.profileForm.get(\"consultationRates.sessionRate\")) == null ? null : tmp_34_0.hasError(\"min\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_35_0 = ctx_r0.profileForm.get(\"consultationRates.sessionRate\")) == null ? null : tmp_35_0.hasError(\"max\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.getCurrencyOptions());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_37_0 = ctx_r0.profileForm.get(\"consultationRates.currency\")) == null ? null : tmp_37_0.hasError(\"required\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.serviceOfferings.controls);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isSaving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isSaving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isSaving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isSaving ? \"Saving...\" : \"Save Changes\", \" \");\n  }\n}\nfunction ProfileEditComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 120);\n    i0.ɵɵelement(1, \"mat-spinner\", 121);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading profile...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ProfileEditComponent {\n  constructor(formBuilder, profileService, authService, router, snackBar) {\n    this.formBuilder = formBuilder;\n    this.profileService = profileService;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.profile = null;\n    this.isLoading = true;\n    this.isSaving = false;\n    this.isUploadingProfilePhoto = false;\n    this.isUploadingCoverPhoto = false;\n    this.profilePhotoPreview = null;\n    this.coverPhotoPreview = null;\n    this.destroy$ = new Subject();\n    this.profileForm = this.createForm();\n  }\n  ngOnInit() {\n    this.loadProfile();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  createForm() {\n    return this.formBuilder.group({\n      // Basic Information\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      professionalTitle: [''],\n      headline: ['', Validators.maxLength(220)],\n      summary: ['', Validators.maxLength(2000)],\n      // Location\n      location: this.formBuilder.group({\n        city: [''],\n        state: [''],\n        country: [''],\n        displayLocation: ['']\n      }),\n      // Contact Information\n      contactInfo: this.formBuilder.group({\n        email: ['', Validators.email],\n        isEmailPublic: [false],\n        website: [''],\n        portfolioUrl: [''],\n        phoneNumbers: this.formBuilder.array([]),\n        businessAddress: this.formBuilder.group({\n          street: [''],\n          city: [''],\n          state: [''],\n          postalCode: [''],\n          country: [''],\n          isPublic: [false]\n        })\n      }),\n      // Privacy Settings\n      isPublic: [true],\n      // Social Links\n      socialLinks: this.formBuilder.array([]),\n      // Skills\n      skills: this.formBuilder.array([]),\n      // Consultation Rates\n      consultationRates: this.formBuilder.group({\n        hourlyRate: [null, [Validators.min(0), Validators.max(10000)]],\n        sessionRate: [null, [Validators.min(0), Validators.max(10000)]],\n        currency: ['BGN', Validators.required]\n      }),\n      // Service Offerings\n      serviceOfferings: this.formBuilder.array([])\n    });\n  }\n  loadProfile() {\n    this.profileService.getCurrentUserProfile().pipe(takeUntil(this.destroy$)).subscribe({\n      next: profile => {\n        this.profile = profile;\n        this.populateForm(profile);\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading profile:', error);\n        this.snackBar.open('Error loading profile', 'Close', {\n          duration: 5000\n        });\n        this.router.navigate(['/dashboard']);\n        this.isLoading = false;\n      }\n    });\n  }\n  populateForm(profile) {\n    this.profileForm.patchValue({\n      firstName: profile.firstName,\n      lastName: profile.lastName,\n      professionalTitle: profile.professionalTitle || '',\n      headline: profile.headline || '',\n      summary: profile.summary || '',\n      location: {\n        city: profile.location?.city || '',\n        state: profile.location?.state || '',\n        country: profile.location?.country || '',\n        displayLocation: profile.location?.displayLocation || ''\n      },\n      contactInfo: {\n        email: profile.contactInfo.email || '',\n        isEmailPublic: profile.contactInfo.isEmailPublic,\n        website: profile.contactInfo.website || '',\n        portfolioUrl: profile.contactInfo.portfolioUrl || '',\n        businessAddress: {\n          street: profile.contactInfo.businessAddress?.street || '',\n          city: profile.contactInfo.businessAddress?.city || '',\n          state: profile.contactInfo.businessAddress?.state || '',\n          postalCode: profile.contactInfo.businessAddress?.postalCode || '',\n          country: profile.contactInfo.businessAddress?.country || '',\n          isPublic: profile.contactInfo.businessAddress?.isPublic || false\n        }\n      },\n      isPublic: profile.isPublic,\n      consultationRates: {\n        hourlyRate: profile.consultationRates?.hourlyRate || null,\n        sessionRate: profile.consultationRates?.sessionRate || null,\n        currency: profile.consultationRates?.currency || 'BGN'\n      }\n    });\n    // Populate phone numbers\n    this.setPhoneNumbers(profile.contactInfo.phoneNumbers || []);\n    // Populate social links\n    this.setSocialLinks(profile.socialLinks || []);\n    // Populate skills\n    this.setSkills(profile.skills || []);\n    // Populate service offerings\n    this.setServiceOfferings(profile.serviceOfferings || []);\n  }\n  // Phone Numbers Management\n  get phoneNumbers() {\n    return this.profileForm.get('contactInfo.phoneNumbers');\n  }\n  setPhoneNumbers(phones) {\n    const phoneArray = this.phoneNumbers;\n    phoneArray.clear();\n    phones.forEach(phone => {\n      phoneArray.push(this.formBuilder.group({\n        id: [phone.id],\n        number: [phone.number, Validators.required],\n        type: [phone.type, Validators.required],\n        isPublic: [phone.isPublic],\n        isPrimary: [phone.isPrimary]\n      }));\n    });\n  }\n  addPhoneNumber() {\n    const phoneGroup = this.formBuilder.group({\n      id: [null],\n      number: ['', Validators.required],\n      type: ['mobile', Validators.required],\n      isPublic: [false],\n      isPrimary: [false]\n    });\n    this.phoneNumbers.push(phoneGroup);\n  }\n  removePhoneNumber(index) {\n    this.phoneNumbers.removeAt(index);\n  }\n  // Social Links Management\n  get socialLinks() {\n    return this.profileForm.get('socialLinks');\n  }\n  setSocialLinks(links) {\n    const linksArray = this.socialLinks;\n    linksArray.clear();\n    links.forEach(link => {\n      linksArray.push(this.formBuilder.group({\n        id: [link.id],\n        platform: [link.platform, Validators.required],\n        url: [link.url, [Validators.required, Validators.pattern('https?://.+')]],\n        displayName: [link.displayName],\n        isPublic: [link.isPublic]\n      }));\n    });\n  }\n  addSocialLink() {\n    const linkGroup = this.formBuilder.group({\n      id: [null],\n      platform: ['linkedin', Validators.required],\n      url: ['', [Validators.required, Validators.pattern('https?://.+')]],\n      displayName: [''],\n      isPublic: [true]\n    });\n    this.socialLinks.push(linkGroup);\n  }\n  removeSocialLink(index) {\n    this.socialLinks.removeAt(index);\n  }\n  // Skills Management\n  get skills() {\n    return this.profileForm.get('skills');\n  }\n  setSkills(skills) {\n    const skillArray = this.skills;\n    skillArray.clear();\n    skills.forEach(skill => {\n      skillArray.push(this.formBuilder.group({\n        id: [skill.id],\n        name: [skill.name, Validators.required],\n        category: [skill.category || ''],\n        proficiencyLevel: [skill.proficiencyLevel || 'intermediate', Validators.required]\n      }));\n    });\n  }\n  addSkill() {\n    const skillGroup = this.formBuilder.group({\n      id: [null],\n      name: ['', Validators.required],\n      category: [''],\n      proficiencyLevel: ['intermediate', Validators.required]\n    });\n    this.skills.push(skillGroup);\n  }\n  removeSkill(index) {\n    this.skills.removeAt(index);\n  }\n  // Service Offerings Management\n  get serviceOfferings() {\n    return this.profileForm.get('serviceOfferings');\n  }\n  setServiceOfferings(services) {\n    const serviceArray = this.serviceOfferings;\n    serviceArray.clear();\n    services.forEach(service => {\n      serviceArray.push(this.formBuilder.group({\n        id: [service.id],\n        name: [service.name, Validators.required],\n        description: [service.description, Validators.required],\n        price: [service.price, [Validators.required, Validators.min(0)]],\n        currency: [service.currency || 'BGN', Validators.required],\n        duration: [service.duration],\n        category: [service.category || ''],\n        isActive: [service.isActive !== false] // default to true\n      }));\n    });\n  }\n\n  addServiceOffering() {\n    const serviceGroup = this.formBuilder.group({\n      id: [null],\n      name: ['', Validators.required],\n      description: ['', Validators.required],\n      price: [0, [Validators.required, Validators.min(0)]],\n      currency: ['BGN', Validators.required],\n      duration: [60],\n      category: [''],\n      isActive: [true]\n    });\n    this.serviceOfferings.push(serviceGroup);\n  }\n  removeServiceOffering(index) {\n    this.serviceOfferings.removeAt(index);\n  }\n  // Form Submission\n  onSubmit() {\n    if (this.validateForm()) {\n      this.isSaving = true;\n      const formValue = this.profileForm.value;\n      const updateRequest = {\n        firstName: formValue.firstName,\n        lastName: formValue.lastName,\n        professionalTitle: formValue.professionalTitle,\n        headline: formValue.headline,\n        location: formValue.location,\n        contactInfo: formValue.contactInfo,\n        summary: formValue.summary,\n        isPublic: formValue.isPublic,\n        skills: formValue.skills || [],\n        consultationRates: formValue.consultationRates,\n        serviceOfferings: formValue.serviceOfferings || []\n      };\n      this.profileService.updateProfile(updateRequest).pipe(takeUntil(this.destroy$)).subscribe({\n        next: updatedProfile => {\n          this.isSaving = false;\n          this.snackBar.open('Profile updated successfully!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate(['/profile', updatedProfile.slug]);\n        },\n        error: error => {\n          this.isSaving = false;\n          console.error('Error updating profile:', error);\n          this.snackBar.open('Error updating profile. Please try again.', 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    }\n    // Validation is now handled in validateForm() method\n  }\n\n  onCancel() {\n    if (this.profile) {\n      this.router.navigate(['/profile', this.profile.slug]);\n    } else {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  // File Upload Methods\n  onProfilePhotoSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      if (this.validateImageFile(file, 'profile')) {\n        this.createImagePreview(file, 'profile');\n        this.uploadProfilePhoto(file);\n      }\n    }\n    // Reset the input to allow selecting the same file again\n    event.target.value = '';\n  }\n  onCoverPhotoSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      if (this.validateImageFile(file, 'cover')) {\n        this.createImagePreview(file, 'cover');\n        this.uploadCoverPhoto(file);\n      }\n    }\n    // Reset the input to allow selecting the same file again\n    event.target.value = '';\n  }\n  validateImageFile(file, type) {\n    // Check file type\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      this.snackBar.open('Please select a valid image file (JPEG, PNG, GIF, or WebP)', 'Close', {\n        duration: 5000\n      });\n      return false;\n    }\n    // Check file size (5MB for profile, 10MB for cover)\n    const maxSize = type === 'profile' ? 5 * 1024 * 1024 : 10 * 1024 * 1024;\n    if (file.size > maxSize) {\n      const maxSizeMB = maxSize / (1024 * 1024);\n      this.snackBar.open(`File size must be less than ${maxSizeMB}MB`, 'Close', {\n        duration: 5000\n      });\n      return false;\n    }\n    return true;\n  }\n  createImagePreview(file, type) {\n    const reader = new FileReader();\n    reader.onload = e => {\n      if (type === 'profile') {\n        this.profilePhotoPreview = e.target.result;\n      } else {\n        this.coverPhotoPreview = e.target.result;\n      }\n    };\n    reader.readAsDataURL(file);\n  }\n  uploadProfilePhoto(file) {\n    this.isUploadingProfilePhoto = true;\n    this.profileService.uploadProfilePhoto(file).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        this.isUploadingProfilePhoto = false;\n        if (this.profile) {\n          this.profile.profilePhotoUrl = response.url;\n        }\n        this.profilePhotoPreview = null; // Clear preview since we have the actual URL\n        this.snackBar.open('Profile photo updated successfully!', 'Close', {\n          duration: 3000\n        });\n      },\n      error: error => {\n        this.isUploadingProfilePhoto = false;\n        this.profilePhotoPreview = null; // Clear preview on error\n        console.error('Error uploading profile photo:', error);\n        const errorMessage = error.error?.message || 'Error uploading profile photo. Please try again.';\n        this.snackBar.open(errorMessage, 'Close', {\n          duration: 5000\n        });\n      }\n    });\n  }\n  uploadCoverPhoto(file) {\n    this.isUploadingCoverPhoto = true;\n    this.profileService.uploadCoverPhoto(file).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        this.isUploadingCoverPhoto = false;\n        if (this.profile) {\n          this.profile.coverPhotoUrl = response.url;\n        }\n        this.coverPhotoPreview = null; // Clear preview since we have the actual URL\n        this.snackBar.open('Cover photo updated successfully!', 'Close', {\n          duration: 3000\n        });\n      },\n      error: error => {\n        this.isUploadingCoverPhoto = false;\n        this.coverPhotoPreview = null; // Clear preview on error\n        console.error('Error uploading cover photo:', error);\n        const errorMessage = error.error?.message || 'Error uploading cover photo. Please try again.';\n        this.snackBar.open(errorMessage, 'Close', {\n          duration: 5000\n        });\n      }\n    });\n  }\n  // Utility Methods\n  markFormGroupTouched() {\n    Object.keys(this.profileForm.controls).forEach(key => {\n      const control = this.profileForm.get(key);\n      control?.markAsTouched();\n      if (control instanceof FormArray) {\n        control.controls.forEach(arrayControl => {\n          if (arrayControl instanceof FormGroup) {\n            Object.keys(arrayControl.controls).forEach(arrayKey => {\n              arrayControl.get(arrayKey)?.markAsTouched();\n            });\n          }\n        });\n      }\n    });\n  }\n  getErrorMessage(fieldName) {\n    const control = this.profileForm.get(fieldName);\n    if (!control || !control.errors) return '';\n    const fieldDisplayName = this.getFieldDisplayName(fieldName);\n    if (control.hasError('required')) {\n      return `${fieldDisplayName} is required`;\n    }\n    if (control.hasError('email')) {\n      return 'Please enter a valid email address';\n    }\n    if (control.hasError('minlength')) {\n      const requiredLength = control.errors['minlength'].requiredLength;\n      return `${fieldDisplayName} must be at least ${requiredLength} characters`;\n    }\n    if (control.hasError('maxlength')) {\n      const maxLength = control.errors['maxlength'].requiredLength;\n      return `${fieldDisplayName} must be no more than ${maxLength} characters`;\n    }\n    if (control.hasError('pattern')) {\n      return 'Please enter a valid URL';\n    }\n    if (control.hasError('min')) {\n      const minValue = control.errors['min'].min;\n      return `${fieldDisplayName} must be at least ${minValue}`;\n    }\n    if (control.hasError('max')) {\n      const maxValue = control.errors['max'].max;\n      return `${fieldDisplayName} cannot exceed ${maxValue}`;\n    }\n    return '';\n  }\n  getFieldDisplayName(fieldName) {\n    const fieldNames = {\n      'firstName': 'First Name',\n      'lastName': 'Last Name',\n      'professionalTitle': 'Professional Title',\n      'headline': 'Headline',\n      'summary': 'Summary',\n      'contactInfo.email': 'Email',\n      'contactInfo.website': 'Website',\n      'contactInfo.portfolioUrl': 'Portfolio URL',\n      'location.city': 'City',\n      'location.state': 'State',\n      'location.country': 'Country',\n      'location.displayLocation': 'Display Location',\n      'consultationRates.hourlyRate': 'Hourly Rate',\n      'consultationRates.sessionRate': 'Session Rate',\n      'consultationRates.currency': 'Currency'\n    };\n    return fieldNames[fieldName] || fieldName;\n  }\n  // Enhanced form validation\n  validateForm() {\n    if (this.profileForm.invalid) {\n      this.markFormGroupTouched();\n      // Find first invalid field and focus on it\n      const firstInvalidField = this.findFirstInvalidField();\n      if (firstInvalidField) {\n        firstInvalidField.focus();\n      }\n      // Show specific error message\n      const errors = this.getFormErrors();\n      if (errors.length > 0) {\n        this.snackBar.open(`Please fix the following errors: ${errors.join(', ')}`, 'Close', {\n          duration: 5000\n        });\n      }\n      return false;\n    }\n    return true;\n  }\n  findFirstInvalidField() {\n    const invalidFields = document.querySelectorAll('.mat-form-field.ng-invalid input, .mat-form-field.ng-invalid textarea, .mat-form-field.ng-invalid mat-select');\n    return invalidFields.length > 0 ? invalidFields[0] : null;\n  }\n  getFormErrors() {\n    const errors = [];\n    // Check main form fields\n    Object.keys(this.profileForm.controls).forEach(key => {\n      const control = this.profileForm.get(key);\n      if (control && control.invalid && control.touched) {\n        const errorMessage = this.getErrorMessage(key);\n        if (errorMessage) {\n          errors.push(errorMessage);\n        }\n      }\n    });\n    // Check nested form groups\n    const contactInfo = this.profileForm.get('contactInfo');\n    if (contactInfo && contactInfo.invalid) {\n      Object.keys(contactInfo.controls).forEach(key => {\n        const control = contactInfo.get(key);\n        if (control && control.invalid && control.touched) {\n          const errorMessage = this.getErrorMessage(`contactInfo.${key}`);\n          if (errorMessage) {\n            errors.push(errorMessage);\n          }\n        }\n      });\n    }\n    return errors.slice(0, 3); // Limit to first 3 errors to avoid overwhelming the user\n  }\n  // Platform options for social links\n  getPlatformOptions() {\n    return [{\n      value: 'linkedin',\n      label: 'LinkedIn'\n    }, {\n      value: 'twitter',\n      label: 'Twitter'\n    }, {\n      value: 'github',\n      label: 'GitHub'\n    }, {\n      value: 'behance',\n      label: 'Behance'\n    }, {\n      value: 'dribbble',\n      label: 'Dribbble'\n    }, {\n      value: 'instagram',\n      label: 'Instagram'\n    }, {\n      value: 'facebook',\n      label: 'Facebook'\n    }, {\n      value: 'youtube',\n      label: 'YouTube'\n    }, {\n      value: 'other',\n      label: 'Other'\n    }];\n  }\n  // Phone type options\n  getPhoneTypeOptions() {\n    return [{\n      value: 'mobile',\n      label: 'Mobile'\n    }, {\n      value: 'business',\n      label: 'Business'\n    }, {\n      value: 'home',\n      label: 'Home'\n    }];\n  }\n  // Skill category options\n  getSkillCategoryOptions() {\n    return [{\n      value: 'Astrology',\n      label: 'Astrology'\n    }, {\n      value: 'Crystal Healing',\n      label: 'Crystal Healing'\n    }, {\n      value: 'Palmistry',\n      label: 'Palmistry'\n    }, {\n      value: 'Spiritual Counseling',\n      label: 'Spiritual Counseling'\n    }, {\n      value: 'Numerology',\n      label: 'Numerology'\n    }, {\n      value: 'Tarot Reading',\n      label: 'Tarot Reading'\n    }, {\n      value: 'Energy Healing',\n      label: 'Energy Healing'\n    }, {\n      value: 'Meditation',\n      label: 'Meditation'\n    }, {\n      value: 'Other',\n      label: 'Other'\n    }];\n  }\n  // Proficiency level options\n  getProficiencyLevelOptions() {\n    return [{\n      value: 'beginner',\n      label: 'Beginner'\n    }, {\n      value: 'intermediate',\n      label: 'Intermediate'\n    }, {\n      value: 'advanced',\n      label: 'Advanced'\n    }, {\n      value: 'expert',\n      label: 'Expert'\n    }];\n  }\n  // Service category options\n  getServiceCategoryOptions() {\n    return [{\n      value: 'Reading',\n      label: 'Reading'\n    }, {\n      value: 'Consultation',\n      label: 'Consultation'\n    }, {\n      value: 'Healing',\n      label: 'Healing'\n    }, {\n      value: 'Workshop',\n      label: 'Workshop'\n    }, {\n      value: 'Course',\n      label: 'Course'\n    }, {\n      value: 'Other',\n      label: 'Other'\n    }];\n  }\n  // Currency options\n  getCurrencyOptions() {\n    return [{\n      value: 'BGN',\n      label: 'BGN (Bulgarian Lev)'\n    }, {\n      value: 'EUR',\n      label: 'EUR (Euro)'\n    }, {\n      value: 'USD',\n      label: 'USD (US Dollar)'\n    }, {\n      value: 'GBP',\n      label: 'GBP (British Pound)'\n    }];\n  }\n  static {\n    this.ɵfac = function ProfileEditComponent_Factory(t) {\n      return new (t || ProfileEditComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProfileService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileEditComponent,\n      selectors: [[\"app-profile-edit\"]],\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"profile-edit-container\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"profile-edit-container\"], [1, \"edit-header\"], [1, \"header-actions\"], [\"mat-stroked-button\", \"\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [4, \"ngIf\"], [1, \"profile-form\", 3, \"formGroup\"], [1, \"photo-section\"], [1, \"photo-uploads\"], [1, \"profile-photo-upload\"], [1, \"photo-preview\"], [\"alt\", \"Profile Photo\", 1, \"profile-photo\", 3, \"src\"], [1, \"photo-overlay\"], [\"diameter\", \"24\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \"image/jpeg,image/jpg,image/png,image/gif,image/webp\", 2, \"display\", \"none\", 3, \"change\"], [\"profilePhotoInput\", \"\"], [1, \"upload-hint\"], [1, \"cover-photo-upload\"], [1, \"cover-preview\"], [1, \"cover-photo\"], [\"coverPhotoInput\", \"\"], [1, \"form-section\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"required\", \"\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"professionalTitle\", \"placeholder\", \"e.g., Senior Software Engineer, UX Designer\"], [\"matInput\", \"\", \"formControlName\", \"headline\", \"rows\", \"2\", \"placeholder\", \"A brief, compelling description of what you do\", \"maxlength\", \"220\"], [\"align\", \"end\"], [\"matInput\", \"\", \"formControlName\", \"summary\", \"rows\", \"6\", \"placeholder\", \"Describe your expertise, experience, and what makes you unique\", \"maxlength\", \"2000\"], [\"formGroupName\", \"location\", 1, \"form-section\"], [\"matInput\", \"\", \"formControlName\", \"city\"], [\"matInput\", \"\", \"formControlName\", \"state\"], [\"matInput\", \"\", \"formControlName\", \"country\"], [\"matInput\", \"\", \"formControlName\", \"displayLocation\", \"placeholder\", \"e.g., San Francisco, CA\"], [\"formGroupName\", \"contactInfo\", 1, \"form-section\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"type\", \"email\"], [1, \"checkbox-field\"], [\"formControlName\", \"isEmailPublic\"], [\"matInput\", \"\", \"formControlName\", \"website\", \"placeholder\", \"https://yourwebsite.com\"], [\"matInput\", \"\", \"formControlName\", \"portfolioUrl\", \"placeholder\", \"https://portfolio.com\"], [1, \"phone-numbers-section\"], [\"formArrayName\", \"phoneNumbers\"], [\"class\", \"phone-number-item\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"formGroupName\", \"businessAddress\", 1, \"business-address-section\"], [\"matInput\", \"\", \"formControlName\", \"street\"], [\"appearance\", \"outline\", 1, \"third-width\"], [\"matInput\", \"\", \"formControlName\", \"postalCode\"], [\"formControlName\", \"isPublic\"], [\"formArrayName\", \"socialLinks\"], [\"class\", \"social-link-item\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"formArrayName\", \"skills\"], [\"class\", \"skill-item\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"add-skill-btn\", 3, \"click\"], [\"formGroupName\", \"consultationRates\", 1, \"rates-section\"], [1, \"rate-fields\"], [\"appearance\", \"outline\", 1, \"rate-field\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"hourlyRate\", \"placeholder\", \"0.00\", \"min\", \"0\", \"max\", \"10000\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"sessionRate\", \"placeholder\", \"0.00\", \"min\", \"0\", \"max\", \"10000\"], [\"appearance\", \"outline\", 1, \"currency-field\"], [\"formControlName\", \"currency\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formArrayName\", \"serviceOfferings\"], [\"class\", \"service-item\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"add-service-btn\", 3, \"click\"], [1, \"privacy-controls\"], [\"formControlName\", \"isPublic\", \"color\", \"primary\"], [1, \"toggle-label\"], [1, \"toggle-description\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", \"routerLink\", \"/profile\"], [\"diameter\", \"20\"], [\"diameter\", \"24\"], [\"diameter\", \"16\"], [1, \"phone-number-item\", 3, \"formGroupName\"], [\"appearance\", \"outline\", 1, \"phone-input\"], [\"matInput\", \"\", \"formControlName\", \"number\", \"placeholder\", \"+****************\"], [\"appearance\", \"outline\", 1, \"phone-type\"], [\"formControlName\", \"type\"], [1, \"phone-controls\"], [\"formControlName\", \"isPrimary\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\"], [3, \"value\"], [1, \"social-link-item\", 3, \"formGroupName\"], [\"appearance\", \"outline\", 1, \"platform-select\"], [\"formControlName\", \"platform\"], [\"appearance\", \"outline\", 1, \"url-input\"], [\"matInput\", \"\", \"formControlName\", \"url\", \"placeholder\", \"https://linkedin.com/in/yourname\"], [1, \"link-controls\"], [1, \"skill-item\", 3, \"formGroupName\"], [1, \"skill-fields\"], [\"appearance\", \"outline\", 1, \"skill-name\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"placeholder\", \"e.g., Tarot Reading\"], [\"appearance\", \"outline\", 1, \"skill-category\"], [\"formControlName\", \"category\"], [\"value\", \"\"], [\"appearance\", \"outline\", 1, \"skill-proficiency\"], [\"formControlName\", \"proficiencyLevel\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 1, \"remove-skill\", 3, \"click\"], [1, \"service-item\", 3, \"formGroupName\"], [1, \"service-header\"], [\"formControlName\", \"isActive\", \"color\", \"primary\"], [1, \"service-fields\"], [\"appearance\", \"outline\", 1, \"service-name\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"placeholder\", \"e.g., Personal Tarot Reading\"], [\"appearance\", \"outline\", 1, \"service-category\"], [\"appearance\", \"outline\", 1, \"service-description\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"rows\", \"3\", \"placeholder\", \"Describe what this service includes...\"], [1, \"service-pricing\"], [\"appearance\", \"outline\", 1, \"price-field\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"price\", \"placeholder\", \"0.00\", \"min\", \"0\"], [\"appearance\", \"outline\", 1, \"duration-field\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"duration\", \"placeholder\", \"60\", \"min\", \"1\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 1, \"remove-service\", 3, \"click\"], [1, \"loading-container\"], [\"diameter\", \"50\"]],\n      template: function ProfileEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ProfileEditComponent_div_0_Template, 249, 46, \"div\", 0);\n          i0.ɵɵtemplate(1, ProfileEditComponent_div_1_Template, 4, 0, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MaxLengthValidator, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i1.FormArrayName, i4.RouterLink, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardSubtitle, i7.MatCardTitle, i8.MatButton, i8.MatIconButton, i9.MatIcon, i10.MatFormField, i10.MatLabel, i10.MatHint, i10.MatError, i10.MatSuffix, i11.MatInput, i12.MatSelect, i13.MatOption, i14.MatCheckbox, i15.MatProgressSpinner, i16.MatSlideToggle],\n      styles: [\".profile-edit-container[_ngcontent-%COMP%] {\\r\\n  max-width: 800px;\\r\\n  margin: 0 auto;\\r\\n  padding: 20px;\\r\\n  background-color: var(--theme-background);\\r\\n}\\r\\n\\r\\n.edit-header[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  margin-bottom: 24px;\\r\\n  padding: 20px 0;\\r\\n}\\r\\n\\r\\n.edit-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n  margin: 0;\\r\\n  color: var(--theme-primary);\\r\\n  font-size: 2rem;\\r\\n}\\r\\n\\r\\n.header-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.profile-form[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 24px;\\r\\n}\\r\\n\\r\\n.form-section[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 0;\\r\\n}\\r\\n\\r\\n.form-section[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%] {\\r\\n  padding-bottom: 16px;\\r\\n}\\r\\n\\r\\n.form-section[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-primary);\\r\\n  font-size: 1.3rem;\\r\\n}\\r\\n\\r\\n\\r\\n.form-row[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 16px;\\r\\n  align-items: flex-start;\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\n.full-width[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n.half-width[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  min-width: 0;\\r\\n}\\r\\n\\r\\n.third-width[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  min-width: 0;\\r\\n}\\r\\n\\r\\n.checkbox-field[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  min-height: 56px;\\r\\n}\\r\\n\\r\\n\\r\\n.photo-section[_ngcontent-%COMP%]   .mat-card-content[_ngcontent-%COMP%] {\\r\\n  padding: 24px;\\r\\n}\\r\\n\\r\\n.photo-uploads[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 32px;\\r\\n  align-items: flex-start;\\r\\n}\\r\\n\\r\\n.profile-photo-upload[_ngcontent-%COMP%], .cover-photo-upload[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.photo-preview[_ngcontent-%COMP%] {\\r\\n  position: relative;\\r\\n  cursor: pointer;\\r\\n  border-radius: 12px;\\r\\n  overflow: hidden;\\r\\n  transition: transform 0.3s ease;\\r\\n}\\r\\n\\r\\n.photo-preview[_ngcontent-%COMP%]:hover {\\r\\n  transform: scale(1.02);\\r\\n}\\r\\n\\r\\n.profile-photo[_ngcontent-%COMP%] {\\r\\n  width: 150px;\\r\\n  height: 150px;\\r\\n  border-radius: 50%;\\r\\n  object-fit: cover;\\r\\n  border: 4px solid var(--theme-surface);\\r\\n  box-shadow: 0 4px 16px rgba(0,0,0,0.1);\\r\\n}\\r\\n\\r\\n.cover-preview[_ngcontent-%COMP%] {\\r\\n  position: relative;\\r\\n  width: 300px;\\r\\n  height: 120px;\\r\\n  border-radius: 12px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.cover-photo[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-size: cover;\\r\\n  background-position: center;\\r\\n  background-repeat: no-repeat;\\r\\n}\\r\\n\\r\\n.photo-overlay[_ngcontent-%COMP%] {\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  background: rgba(0,0,0,0.5);\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  opacity: 0;\\r\\n  transition: opacity 0.3s ease;\\r\\n}\\r\\n\\r\\n.photo-overlay.uploading[_ngcontent-%COMP%] {\\r\\n  opacity: 1;\\r\\n  background: rgba(0, 0, 0, 0.7);\\r\\n}\\r\\n\\r\\n.photo-preview[_ngcontent-%COMP%]:hover   .photo-overlay[_ngcontent-%COMP%] {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n.upload-hint[_ngcontent-%COMP%] {\\r\\n  font-size: 12px;\\r\\n  color: #666;\\r\\n  margin-top: 8px;\\r\\n  text-align: center;\\r\\n}\\r\\n\\r\\n.photo-overlay[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  color: white;\\r\\n  font-size: 32px;\\r\\n  width: 32px;\\r\\n  height: 32px;\\r\\n}\\r\\n\\r\\n\\r\\n.phone-numbers-section[_ngcontent-%COMP%] {\\r\\n  margin-top: 24px;\\r\\n}\\r\\n\\r\\n.phone-numbers-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 16px 0;\\r\\n  color: var(--theme-text-primary);\\r\\n  font-size: 1.1rem;\\r\\n}\\r\\n\\r\\n.phone-number-item[_ngcontent-%COMP%] {\\r\\n  border: 1px solid rgba(0,0,0,0.1);\\r\\n  border-radius: 8px;\\r\\n  padding: 16px;\\r\\n  margin-bottom: 12px;\\r\\n  background-color: rgba(0,0,0,0.02);\\r\\n}\\r\\n\\r\\n.phone-input[_ngcontent-%COMP%] {\\r\\n  flex: 2;\\r\\n}\\r\\n\\r\\n.phone-type[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  min-width: 120px;\\r\\n}\\r\\n\\r\\n.phone-controls[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 8px;\\r\\n  align-items: flex-start;\\r\\n  min-width: 120px;\\r\\n}\\r\\n\\r\\n\\r\\n.business-address-section[_ngcontent-%COMP%] {\\r\\n  margin-top: 24px;\\r\\n}\\r\\n\\r\\n.business-address-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 16px 0;\\r\\n  color: var(--theme-text-primary);\\r\\n  font-size: 1.1rem;\\r\\n}\\r\\n\\r\\n\\r\\n.social-link-item[_ngcontent-%COMP%] {\\r\\n  border: 1px solid rgba(0,0,0,0.1);\\r\\n  border-radius: 8px;\\r\\n  padding: 16px;\\r\\n  margin-bottom: 12px;\\r\\n  background-color: rgba(0,0,0,0.02);\\r\\n}\\r\\n\\r\\n.platform-select[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  min-width: 140px;\\r\\n}\\r\\n\\r\\n.url-input[_ngcontent-%COMP%] {\\r\\n  flex: 2;\\r\\n}\\r\\n\\r\\n.link-controls[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 8px;\\r\\n  align-items: flex-start;\\r\\n  min-width: 100px;\\r\\n}\\r\\n\\r\\n\\r\\n.privacy-controls[_ngcontent-%COMP%] {\\r\\n  padding: 16px;\\r\\n}\\r\\n\\r\\n.toggle-label[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  color: var(--theme-text-primary);\\r\\n}\\r\\n\\r\\n.toggle-description[_ngcontent-%COMP%] {\\r\\n  margin: 8px 0 0 0;\\r\\n  color: var(--theme-text-secondary);\\r\\n  font-size: 0.9rem;\\r\\n  line-height: 1.4;\\r\\n}\\r\\n\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  min-height: 50vh;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n\\r\\n.mat-form-field.ng-invalid.ng-touched[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-error);\\r\\n}\\r\\n\\r\\n.mat-error[_ngcontent-%COMP%] {\\r\\n  font-size: 0.8rem;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .profile-edit-container[_ngcontent-%COMP%] {\\r\\n    padding: 16px;\\r\\n  }\\r\\n  \\r\\n  .edit-header[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    gap: 16px;\\r\\n    text-align: center;\\r\\n  }\\r\\n  \\r\\n  .header-actions[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n    justify-content: center;\\r\\n  }\\r\\n  \\r\\n  .form-row[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    gap: 0;\\r\\n  }\\r\\n  \\r\\n  .half-width[_ngcontent-%COMP%], .third-width[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n  }\\r\\n  \\r\\n  .photo-uploads[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: center;\\r\\n    gap: 24px;\\r\\n  }\\r\\n  \\r\\n  .cover-preview[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n    max-width: 300px;\\r\\n  }\\r\\n  \\r\\n  .phone-controls[_ngcontent-%COMP%], .link-controls[_ngcontent-%COMP%] {\\r\\n    flex-direction: row;\\r\\n    align-items: center;\\r\\n    justify-content: space-between;\\r\\n    width: 100%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (max-width: 480px) {\\r\\n  .edit-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\r\\n    font-size: 1.5rem;\\r\\n  }\\r\\n  \\r\\n  .profile-photo[_ngcontent-%COMP%] {\\r\\n    width: 120px;\\r\\n    height: 120px;\\r\\n  }\\r\\n  \\r\\n  .cover-preview[_ngcontent-%COMP%] {\\r\\n    height: 100px;\\r\\n  }\\r\\n  \\r\\n  .phone-number-item[_ngcontent-%COMP%], .social-link-item[_ngcontent-%COMP%] {\\r\\n    padding: 12px;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n.form-section[_ngcontent-%COMP%] {\\r\\n  animation: _ngcontent-%COMP%_slideInUp 0.3s ease-out;\\r\\n}\\r\\n\\r\\n@keyframes _ngcontent-%COMP%_slideInUp {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(20px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n.form-section[_ngcontent-%COMP%]:hover {\\r\\n  box-shadow: 0 4px 16px rgba(0,0,0,0.1);\\r\\n  transition: box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\n\\r\\n.mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n\\r\\nbutton[mat-stroked-button][_ngcontent-%COMP%] {\\r\\n  border-color: var(--theme-primary);\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\nbutton[mat-stroked-button][_ngcontent-%COMP%]:hover {\\r\\n  background-color: rgba(103, 58, 183, 0.04);\\r\\n}\\r\\n\\r\\n\\r\\n.mat-chip[_ngcontent-%COMP%] {\\r\\n  background-color: var(--theme-primary);\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n\\r\\n.mat-progress-spinner[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%] {\\r\\n  stroke: var(--theme-primary);\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAAsBA,SAAS,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAE9E,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;ICYjCC,gCAA2B;IACzBA,kCAAyC;IAC3CA,iBAAW;;;;;IACXA,gCAA4B;IAAAA,oBAAI;IAAAA,iBAAW;;;;;IAmBnCA,kCAAyE;;;;;IACzEA,gCAA2C;IAAAA,0BAAU;IAAAA,iBAAW;;;;;IAQlEA,gCAA0C;IACxCA,kCAAyC;IAC3CA,iBAAW;;;;;IACXA,gCAA2C;IAAAA,4BAAY;IAAAA,iBAAW;;;;;IAY9DA,kCAAuE;;;;;IACvEA,gCAAyC;IAAAA,0BAAU;IAAAA,iBAAW;;;;;IASlEA,gCAAwC;IACtCA,kCAAyC;IAC3CA,iBAAW;;;;;IACXA,gCAAyC;IAAAA,4BAAY;IAAAA,iBAAW;;;;;IAmBlEA,iCAAkG;IAChGA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,qEACF;;;;;IAMAA,iCAAgG;IAC9FA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,oEACF;;;;;IAuEAA,iCAAkH;IAChHA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,6EACF;;;;;IAqCQA,sCAA4E;IAC1EA,YACF;IAAAA,iBAAa;;;;IAF0CA,sCAAoB;IACzEA,eACF;IADEA,+CACF;;;;;;IAbRA,+BACmD;IAGlCA,4BAAY;IAAAA,iBAAY;IACnCA,4BAAyE;IAC3EA,iBAAiB;IAEjBA,0CAAwD;IAC3CA,oBAAI;IAAAA,iBAAY;IAC3BA,sCAAmC;IACjCA,oGAEa;IACfA,iBAAa;IAGfA,gCAA4B;IACeA,uBAAM;IAAAA,iBAAe;IAC9DA,yCAA0C;IAAAA,wBAAO;IAAAA,iBAAe;IAChEA,mCAAkF;IAA7CA;MAAA;MAAA;MAAA;MAAA,OAASA,+CAAoB;IAAA,EAAC;IACjEA,iCAAU;IAAAA,uBAAM;IAAAA,iBAAW;;;;;IApB9BA,qCAAmB;IAUaA,gBAAwB;IAAxBA,uDAAwB;;;;;IA4EvDA,sCAAmF;IACjFA,YACF;IAAAA,iBAAa;;;;IAF6CA,0CAAwB;IAChFA,eACF;IADEA,mDACF;;;;;;IARRA,+BACkD;IAGjCA,wBAAQ;IAAAA,iBAAY;IAC/BA,sCAAuC;IACrCA,kGAEa;IACfA,iBAAa;IAGfA,0CAAuD;IAC1CA,mBAAG;IAAAA,iBAAY;IAC1BA,6BAAqF;IACvFA,iBAAiB;IAEjBA,gCAA2B;IACgBA,uBAAM;IAAAA,iBAAe;IAC9DA,mCAAiF;IAA5CA;MAAA;MAAA;MAAA;MAAA,OAASA,8CAAmB;IAAA,EAAC;IAChEA,iCAAU;IAAAA,uBAAM;IAAAA,iBAAW;;;;;IAnB9BA,qCAAmB;IAKiBA,eAAuB;IAAvBA,sDAAuB;;;;;IAwC1DA,iCAA2D;IACzDA,wCACF;IAAAA,iBAAY;;;;;IAOVA,sCAAwF;IACtFA,YACF;IAAAA,iBAAa;;;;IAFkDA,0CAAwB;IACrFA,eACF;IADEA,mDACF;;;;;IAOAA,sCAAqF;IACnFA,YACF;IAAAA,iBAAa;;;;IAFkDA,uCAAqB;IAClFA,eACF;IADEA,gDACF;;;;;IAEFA,iCAAuE;IACrEA,+CACF;IAAAA,iBAAY;;;;;;IA7BlBA,+BAAiG;IAGhFA,0BAAU;IAAAA,iBAAY;IACjCA,4BAAyE;IACzEA,+FAEY;IACdA,iBAAiB;IAEjBA,0CAA4D;IAC/CA,wBAAQ;IAAAA,iBAAY;IAC/BA,wCAAuC;IAChBA,gCAAe;IAAAA,iBAAa;IACjDA,oGAEa;IACfA,iBAAa;IAGfA,4CAA+D;IAClDA,kCAAiB;IAAAA,iBAAY;IACxCA,wCAA+C;IAC7CA,oGAEa;IACfA,iBAAa;IACbA,iGAEY;IACdA,iBAAiB;IAEjBA,oCAAiG;IAA5DA;MAAA;MAAA;MAAA;MAAA,OAASA,yCAAc;IAAA,EAAC;IAC3DA,iCAAU;IAAAA,uBAAM;IAAAA,iBAAW;;;;;;;;IAjCyBA,qCAAmB;IAK3DA,eAA6C;IAA7CA,sGAA6C;IAStBA,eAA4B;IAA5BA,2DAA4B;IAS/BA,eAA+B;IAA/BA,8DAA+B;IAInDA,eAAyD;IAAzDA,kHAAyD;;;;;IA+BvEA,iCAAoF;IAClFA,uCACF;IAAAA,iBAAY;;;;;IACZA,iCAAoF;IAClFA,2CACF;IAAAA,iBAAY;;;;;IAOZA,iCAAqF;IACnFA,uCACF;IAAAA,iBAAY;;;;;IACZA,iCAAqF;IACnFA,2CACF;IAAAA,iBAAY;;;;;IAMVA,sCAAmF;IACjFA,YACF;IAAAA,iBAAa;;;;IAF6CA,0CAAwB;IAChFA,eACF;IADEA,mDACF;;;;;IAEFA,iCAAuF;IACrFA,sCACF;IAAAA,iBAAY;;;;;IA2BVA,iCAA6D;IAC3DA,0CACF;IAAAA,iBAAY;;;;;IAOVA,sCAA0F;IACxFA,YACF;IAAAA,iBAAa;;;;IAFoDA,0CAAwB;IACvFA,eACF;IADEA,mDACF;;;;;IAQFA,iCAAoE;IAClEA,yCACF;IAAAA,iBAAY;;;;;IAQVA,iCAA8D;IAC5DA,mCACF;IAAAA,iBAAY;;;;;IACZA,iCAAyD;IACvDA,wCACF;IAAAA,iBAAY;;;;;IAMVA,sCAAmF;IACjFA,YACF;IAAAA,iBAAa;;;;IAF6CA,0CAAwB;IAChFA,eACF;IADEA,mDACF;;;;;;IAtDVA,gCAA+G;IAEvGA,YAAmB;IAAAA,iBAAK;IAC5BA,6CAA6D;IAC3DA,wBACF;IAAAA,iBAAmB;IAGrBA,gCAA4B;IAEbA,4BAAY;IAAAA,iBAAY;IACnCA,8BAAkF;IAClFA,iGAEY;IACdA,iBAAiB;IAEjBA,4CAA8D;IACjDA,yBAAQ;IAAAA,iBAAY;IAC/BA,wCAAuC;IAChBA,gCAAe;IAAAA,iBAAa;IACjDA,oGAEa;IACfA,iBAAa;IAGfA,4CAAiE;IACpDA,4BAAW;IAAAA,iBAAY;IAClCA,iCAC0E;IAC1EA,iGAEY;IACdA,iBAAiB;IAEjBA,iCAA6B;IAEdA,sBAAK;IAAAA,iBAAY;IAC5BA,8BAAiF;IACjFA,iCAAgB;IAAAA,aAA6C;IAAAA,iBAAO;IACpEA,iGAEY;IACZA,iGAEY;IACdA,iBAAiB;IAEjBA,2CAA4D;IAC/CA,yBAAQ;IAAAA,iBAAY;IAC/BA,uCAAuC;IACrCA,oGAEa;IACfA,iBAAa;IAGfA,4CAA4D;IAC/CA,mCAAkB;IAAAA,iBAAY;IACzCA,8BAAkF;IAClFA,iCAAU;IAAAA,yDAAwC;IAAAA,iBAAW;IAIjEA,oCAA6G;IAAxEA;MAAA;MAAA;MAAA;MAAA,OAASA,mDAAwB;IAAA,EAAC;IACrEA,iCAAU;IAAAA,uBAAM;IAAAA,iBAAW;;;;;;;;;;;IAlEqCA,qCAAmB;IAEjFA,eAAmB;IAAnBA,gDAAmB;IAUTA,eAA+C;IAA/CA,wGAA+C;IASxBA,eAA8B;IAA9BA,6DAA8B;IAUrDA,eAAsD;IAAtDA,+GAAsD;IAShDA,eAA6C;IAA7CA,uGAA6C;IACjDA,eAAgD;IAAhDA,yGAAgD;IAGhDA,eAA2C;IAA3CA,oGAA2C;IAQpBA,eAAuB;IAAvBA,sDAAuB;;;;;IA+CpEA,gCAA2B;IACzBA,kCAAyC;IAC3CA,iBAAW;;;;;IACXA,gCAA4B;IAAAA,oBAAI;IAAAA,iBAAW;;;;;;IAhhBnDA,8BAAuD;IAGvCA,oBAAI;IAAAA,iBAAW;IACzBA,8BACF;IAAAA,iBAAK;IACLA,8BAA4B;IACCA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAC7CA,wBACF;IAAAA,iBAAS;IACTA,iCAI+C;IAD7CA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAEpBA,uFAEW;IACXA,uFAA2C;IAC3CA,aACF;IAAAA,iBAAS;IAIbA,gCAAqD;IAI/BA,+BAAc;IAAAA,iBAAiB;IAEjDA,yCAAkB;IAIVA,2BAC+C;IAC/CA,gCAAuE;IACrEA,8FAAyE;IACzEA,uFAAgE;IAClEA,iBAAM;IAERA,sCAC2F;IADrDA;MAAAA;MAAA;MAAA,OAAUA,qDAA8B;IAAA,EAAC;IAA/EA,iBAC2F;IAC3FA,kCAE6C;IADrCA;MAAAA;MAAA;MAAA,OAASA,0BAAyB;IAAA,EAAC;IAEzCA,uFAEW;IACXA,uFAAkE;IAClEA,aACF;IAAAA,iBAAS;IACTA,8BAAuB;IAAAA,oDAA8B;IAAAA,iBAAI;IAG3DA,gCAAgC;IAMxBA,8FAAuE;IACvEA,uFAA8D;IAChEA,iBAAM;IAGVA,sCAC2F;IADvDA;MAAAA;MAAA;MAAA,OAAUA,mDAA4B;IAAA,EAAC;IAA3EA,iBAC2F;IAC3FA,kCAE2C;IADnCA;MAAAA;MAAA;MAAA,OAASA,2BAAuB;IAAA,EAAC;IAEvCA,uFAEW;IACXA,uFAAgE;IAChEA,aACF;IAAAA,iBAAS;IACTA,8BAAuB;IAAAA,qDAA+B;IAAAA,iBAAI;IAOlEA,qCAA+B;IAEXA,kCAAiB;IAAAA,iBAAiB;IAEpDA,yCAAkB;IAGDA,2BAAU;IAAAA,iBAAY;IACjCA,6BAAqD;IACrDA,yFAEY;IACdA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,0BAAS;IAAAA,iBAAY;IAChCA,6BAAoD;IACpDA,yFAEY;IACdA,iBAAiB;IAGnBA,2CAAwD;IAC3CA,mCAAkB;IAAAA,iBAAY;IACzCA,6BACiE;IACnEA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,sCAAqB;IAAAA,iBAAY;IAC5CA,gCAEqC;IACrCA,qCAAsB;IAAAA,aAAyD;IAAAA,iBAAW;IAG5FA,2CAAwD;IAC3CA,qCAAoB;IAAAA,iBAAY;IAC3CA,gCAEsC;IACtCA,qCAAsB;IAAAA,aAAyD;IAAAA,iBAAW;IAMhGA,qCAAwD;IAEpCA,yBAAQ;IAAAA,iBAAiB;IAE3CA,yCAAkB;IAGDA,qBAAI;IAAAA,iBAAY;IAC3BA,6BAAuC;IACzCA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,+BAAc;IAAAA,iBAAY;IACrCA,6BAAwC;IAC1CA,iBAAiB;IAGnBA,gCAAsB;IAEPA,wBAAO;IAAAA,iBAAY;IAC9BA,6BAA0C;IAC5CA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,kCAAgB;IAAAA,iBAAY;IACvCA,8BAC6C;IAC/CA,iBAAiB;IAMvBA,sCAA2D;IAEvCA,qCAAmB;IAAAA,iBAAiB;IAEtDA,0CAAkB;IAGDA,uBAAK;IAAAA,iBAAY;IAC5BA,8BAAqD;IACrDA,2FAEY;IACdA,iBAAiB;IAEjBA,iCAA4B;IAExBA,qCACF;IAAAA,iBAAe;IAInBA,iCAAsB;IAEPA,yBAAO;IAAAA,iBAAY;IAC9BA,8BAAgF;IAClFA,iBAAiB;IAEjBA,4CAAwD;IAC3CA,+BAAa;IAAAA,iBAAY;IACpCA,8BAAmF;IACrFA,iBAAiB;IAInBA,iCAAmC;IAC7BA,+BAAa;IAAAA,iBAAK;IACtBA,iCAAkC;IAChCA,iFAyBM;IACRA,iBAAM;IACNA,oCAAoE;IAAzCA;MAAAA;MAAA;MAAA,OAASA,uCAAgB;IAAA,EAAC;IACnDA,kCAAU;IAAAA,qBAAG;IAAAA,iBAAW;IACxBA,oCACF;IAAAA,iBAAS;IAIXA,iCAAsE;IAChEA,kCAAgB;IAAAA,iBAAK;IACzBA,4CAAwD;IAC3CA,gCAAc;IAAAA,iBAAY;IACrCA,8BAAyC;IAC3CA,iBAAiB;IAEjBA,iCAAsB;IAEPA,sBAAI;IAAAA,iBAAY;IAC3BA,8BAAuC;IACzCA,iBAAiB;IAEjBA,4CAAyD;IAC5CA,uBAAK;IAAAA,iBAAY;IAC5BA,8BAAwC;IAC1CA,iBAAiB;IAEjBA,4CAAyD;IAC5CA,6BAAW;IAAAA,iBAAY;IAClCA,8BAA6C;IAC/CA,iBAAiB;IAGnBA,iCAAsB;IAEPA,yBAAO;IAAAA,iBAAY;IAC9BA,8BAA0C;IAC5CA,iBAAiB;IAEjBA,iCAA4B;IAExBA,gDACF;IAAAA,iBAAe;IAQzBA,sCAA+B;IAEXA,8BAAY;IAAAA,iBAAiB;IAE/CA,0CAAkB;IAEdA,iFAwBM;IACRA,iBAAM;IACNA,oCAAmE;IAAxCA;MAAAA;MAAA;MAAA,OAASA,sCAAe;IAAA,EAAC;IAClDA,kCAAU;IAAAA,qBAAG;IAAAA,iBAAW;IACxBA,mCACF;IAAAA,iBAAS;IAKbA,sCAA+B;IAEXA,oCAAkB;IAAAA,iBAAiB;IACnDA,2CAAmB;IAAAA,wDAAsC;IAAAA,iBAAoB;IAE/EA,0CAAkB;IAEdA,iFAoCM;IACRA,iBAAM;IACNA,oCAAoF;IAAzDA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAC7CA,kCAAU;IAAAA,qBAAG;IAAAA,iBAAW;IACxBA,6BACF;IAAAA,iBAAS;IAKbA,sCAA+B;IAEXA,oCAAkB;IAAAA,iBAAiB;IACnDA,2CAAmB;IAAAA,oDAAkC;IAAAA,iBAAoB;IAE3EA,0CAAkB;IAICA,6BAAW;IAAAA,iBAAY;IAClCA,8BAAkG;IAClGA,kCAAgB;IAAAA,cAAmE;IAAAA,iBAAO;IAC1FA,2FAEY;IACZA,2FAEY;IACdA,iBAAiB;IAEjBA,4CAAwD;IAC3CA,8BAAY;IAAAA,iBAAY;IACnCA,8BAAmG;IACnGA,kCAAgB;IAAAA,cAAmE;IAAAA,iBAAO;IAC1FA,2FAEY;IACZA,2FAEY;IACdA,iBAAiB;IAEjBA,4CAA4D;IAC/CA,0BAAQ;IAAAA,iBAAY;IAC/BA,wCAAuC;IACrCA,8FAEa;IACfA,iBAAa;IACbA,2FAEY;IACdA,iBAAiB;IAOzBA,sCAA+B;IAEXA,mCAAiB;IAAAA,iBAAiB;IAClDA,2CAAmB;IAAAA,4DAA0C;IAAAA,iBAAoB;IAEnFA,0CAAkB;IAEdA,iFAqEM;IACRA,iBAAM;IACNA,oCAAgG;IAArEA;MAAAA;MAAA;MAAA,OAASA,2CAAoB;IAAA,EAAC;IACvDA,kCAAU;IAAAA,qBAAG;IAAAA,iBAAW;IACxBA,+BACF;IAAAA,iBAAS;IAKbA,sCAA+B;IAEXA,kCAAgB;IAAAA,iBAAiB;IAEnDA,0CAAkB;IAGeA,qCAAmB;IAAAA,iBAAO;IACrDA,+BAA8B;IAC5BA,wIAEF;IAAAA,iBAAI;IAOZA,iCAA0B;IAEtBA,yFAEW;IACXA,yFAA2C;IAC3CA,cACF;IAAAA,iBAAS;IACTA,oCAA+D;IAC7DA,0BACF;IAAAA,iBAAS;;;;;;;;;;;;;;;;IA9gBuCA,eAAqB;IAArBA,0CAAqB;IAOnEA,eAA4C;IAA5CA,wEAA4C;IACjCA,eAAc;IAAdA,sCAAc;IAGdA,eAAe;IAAfA,uCAAe;IAC1BA,eACF;IADEA,+EACF;IAIEA,eAAyB;IAAzBA,8CAAyB;IAUdA,eAA8F;IAA9FA,6KAA8F;IAExEA,eAA2C;IAA3CA,2DAA2C;IACtDA,eAA6B;IAA7BA,qDAA6B;IAChCA,eAA8B;IAA9BA,sDAA8B;IAOrCA,eAAoC;IAApCA,yDAAoC;IAC/BA,eAA6B;IAA7BA,qDAA6B;IAG7BA,eAA8B;IAA9BA,sDAA8B;IACzCA,eACF;IADEA,yGACF;IAOOA,eAC8G;IAD9GA,uOAC8G;IACtFA,eAAyC;IAAzCA,yDAAyC;IACpDA,eAA2B;IAA3BA,mDAA2B;IAC9BA,eAA4B;IAA5BA,oDAA4B;IAQrCA,eAAkC;IAAlCA,uDAAkC;IAC7BA,eAA2B;IAA3BA,mDAA2B;IAG3BA,eAA4B;IAA5BA,oDAA4B;IACvCA,eACF;IADEA,qGACF;IAiBYA,gBAAoF;IAApFA,mMAAoF;IAQpFA,eAAkF;IAAlFA,iMAAkF;IAiB1EA,gBAAyD;IAAzDA,gKAAyD;IAQzDA,eAAyD;IAAzDA,gKAAyD;IAgDjEA,gBAAoG;IAApGA,mNAAoG;IA4BzFA,gBAA0B;IAA1BA,sDAA0B;IAiF7BA,gBAAyB;IAAzBA,qDAAyB;IAyCxBA,gBAAoB;IAApBA,gDAAoB;IAyDvBA,gBAAmE;IAAnEA,kIAAmE;IACvEA,eAAsE;IAAtEA,oIAAsE;IAGtEA,eAAsE;IAAtEA,oIAAsE;IAQlEA,eAAmE;IAAnEA,kIAAmE;IACvEA,eAAuE;IAAvEA,qIAAuE;IAGvEA,eAAuE;IAAvEA,qIAAuE;IAQhDA,eAAuB;IAAvBA,qDAAuB;IAI9CA,eAAyE;IAAzEA,uIAAyE;IAiBhEA,eAA8B;IAA9BA,0DAA8B;IAkGHA,gBAAqB;IAArBA,0CAAqB;IAChEA,eAAc;IAAdA,sCAAc;IAGdA,eAAe;IAAfA,uCAAe;IAC1BA,eACF;IADEA,+EACF;;;;;IASNA,gCAAiD;IAC/CA,mCAAyC;IACzCA,yBAAG;IAAAA,kCAAkB;IAAAA,iBAAI;;;AD9gB3B,OAAM,MAAOC,oBAAoB;EAY/BC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAJrB,gBAAW,GAAXJ,WAAW;IACX,mBAAc,GAAdC,cAAc;IACd,gBAAW,GAAXC,WAAW;IACX,WAAM,GAANC,MAAM;IACN,aAAQ,GAARC,QAAQ;IAflB,YAAO,GAAuB,IAAI;IAClC,cAAS,GAAG,IAAI;IAChB,aAAQ,GAAG,KAAK;IAChB,4BAAuB,GAAG,KAAK;IAC/B,0BAAqB,GAAG,KAAK;IAC7B,wBAAmB,GAAkB,IAAI;IACzC,sBAAiB,GAAkB,IAAI;IAE/B,aAAQ,GAAG,IAAIT,OAAO,EAAQ;IASpC,IAAI,CAACU,WAAW,GAAG,IAAI,CAACC,UAAU,EAAE;EACtC;EAEAC,QAAQ;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,WAAW;IACT,IAAI,CAACC,QAAQ,CAACC,IAAI,EAAE;IACpB,IAAI,CAACD,QAAQ,CAACE,QAAQ,EAAE;EAC1B;EAEQN,UAAU;IAChB,OAAO,IAAI,CAACN,WAAW,CAACa,KAAK,CAAC;MAC5B;MACAC,SAAS,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACuB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACuB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,iBAAiB,EAAE,CAAC,EAAE,CAAC;MACvBC,QAAQ,EAAE,CAAC,EAAE,EAAE1B,UAAU,CAAC2B,SAAS,CAAC,GAAG,CAAC,CAAC;MACzCC,OAAO,EAAE,CAAC,EAAE,EAAE5B,UAAU,CAAC2B,SAAS,CAAC,IAAI,CAAC,CAAC;MAEzC;MACAE,QAAQ,EAAE,IAAI,CAACtB,WAAW,CAACa,KAAK,CAAC;QAC/BU,IAAI,EAAE,CAAC,EAAE,CAAC;QACVC,KAAK,EAAE,CAAC,EAAE,CAAC;QACXC,OAAO,EAAE,CAAC,EAAE,CAAC;QACbC,eAAe,EAAE,CAAC,EAAE;OACrB,CAAC;MAEF;MACAC,WAAW,EAAE,IAAI,CAAC3B,WAAW,CAACa,KAAK,CAAC;QAClCe,KAAK,EAAE,CAAC,EAAE,EAAEnC,UAAU,CAACmC,KAAK,CAAC;QAC7BC,aAAa,EAAE,CAAC,KAAK,CAAC;QACtBC,OAAO,EAAE,CAAC,EAAE,CAAC;QACbC,YAAY,EAAE,CAAC,EAAE,CAAC;QAClBC,YAAY,EAAE,IAAI,CAAChC,WAAW,CAACiC,KAAK,CAAC,EAAE,CAAC;QACxCC,eAAe,EAAE,IAAI,CAAClC,WAAW,CAACa,KAAK,CAAC;UACtCsB,MAAM,EAAE,CAAC,EAAE,CAAC;UACZZ,IAAI,EAAE,CAAC,EAAE,CAAC;UACVC,KAAK,EAAE,CAAC,EAAE,CAAC;UACXY,UAAU,EAAE,CAAC,EAAE,CAAC;UAChBX,OAAO,EAAE,CAAC,EAAE,CAAC;UACbY,QAAQ,EAAE,CAAC,KAAK;SACjB;OACF,CAAC;MAEF;MACAA,QAAQ,EAAE,CAAC,IAAI,CAAC;MAEhB;MACAC,WAAW,EAAE,IAAI,CAACtC,WAAW,CAACiC,KAAK,CAAC,EAAE,CAAC;MAEvC;MACAM,MAAM,EAAE,IAAI,CAACvC,WAAW,CAACiC,KAAK,CAAC,EAAE,CAAC;MAElC;MACAO,iBAAiB,EAAE,IAAI,CAACxC,WAAW,CAACa,KAAK,CAAC;QACxC4B,UAAU,EAAE,CAAC,IAAI,EAAE,CAAChD,UAAU,CAACiD,GAAG,CAAC,CAAC,CAAC,EAAEjD,UAAU,CAACkD,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9DC,WAAW,EAAE,CAAC,IAAI,EAAE,CAACnD,UAAU,CAACiD,GAAG,CAAC,CAAC,CAAC,EAAEjD,UAAU,CAACkD,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAC/DE,QAAQ,EAAE,CAAC,KAAK,EAAEpD,UAAU,CAACsB,QAAQ;OACtC,CAAC;MAEF;MACA+B,gBAAgB,EAAE,IAAI,CAAC9C,WAAW,CAACiC,KAAK,CAAC,EAAE;KAC5C,CAAC;EACJ;EAEQzB,WAAW;IACjB,IAAI,CAACP,cAAc,CAAC8C,qBAAqB,EAAE,CACxCC,IAAI,CAACpD,SAAS,CAAC,IAAI,CAACc,QAAQ,CAAC,CAAC,CAC9BuC,SAAS,CAAC;MACTtC,IAAI,EAAGuC,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACC,YAAY,CAACD,OAAO,CAAC;QAC1B,IAAI,CAACE,SAAS,GAAG,KAAK;MACxB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACjD,QAAQ,CAACmD,IAAI,CAAC,uBAAuB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACxE,IAAI,CAACrD,MAAM,CAACsD,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACpC,IAAI,CAACL,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEQD,YAAY,CAACD,OAAoB;IACvC,IAAI,CAAC7C,WAAW,CAACqD,UAAU,CAAC;MAC1B5C,SAAS,EAAEoC,OAAO,CAACpC,SAAS;MAC5BG,QAAQ,EAAEiC,OAAO,CAACjC,QAAQ;MAC1BC,iBAAiB,EAAEgC,OAAO,CAAChC,iBAAiB,IAAI,EAAE;MAClDC,QAAQ,EAAE+B,OAAO,CAAC/B,QAAQ,IAAI,EAAE;MAChCE,OAAO,EAAE6B,OAAO,CAAC7B,OAAO,IAAI,EAAE;MAC9BC,QAAQ,EAAE;QACRC,IAAI,EAAE2B,OAAO,CAAC5B,QAAQ,EAAEC,IAAI,IAAI,EAAE;QAClCC,KAAK,EAAE0B,OAAO,CAAC5B,QAAQ,EAAEE,KAAK,IAAI,EAAE;QACpCC,OAAO,EAAEyB,OAAO,CAAC5B,QAAQ,EAAEG,OAAO,IAAI,EAAE;QACxCC,eAAe,EAAEwB,OAAO,CAAC5B,QAAQ,EAAEI,eAAe,IAAI;OACvD;MACDC,WAAW,EAAE;QACXC,KAAK,EAAEsB,OAAO,CAACvB,WAAW,CAACC,KAAK,IAAI,EAAE;QACtCC,aAAa,EAAEqB,OAAO,CAACvB,WAAW,CAACE,aAAa;QAChDC,OAAO,EAAEoB,OAAO,CAACvB,WAAW,CAACG,OAAO,IAAI,EAAE;QAC1CC,YAAY,EAAEmB,OAAO,CAACvB,WAAW,CAACI,YAAY,IAAI,EAAE;QACpDG,eAAe,EAAE;UACfC,MAAM,EAAEe,OAAO,CAACvB,WAAW,CAACO,eAAe,EAAEC,MAAM,IAAI,EAAE;UACzDZ,IAAI,EAAE2B,OAAO,CAACvB,WAAW,CAACO,eAAe,EAAEX,IAAI,IAAI,EAAE;UACrDC,KAAK,EAAE0B,OAAO,CAACvB,WAAW,CAACO,eAAe,EAAEV,KAAK,IAAI,EAAE;UACvDY,UAAU,EAAEc,OAAO,CAACvB,WAAW,CAACO,eAAe,EAAEE,UAAU,IAAI,EAAE;UACjEX,OAAO,EAAEyB,OAAO,CAACvB,WAAW,CAACO,eAAe,EAAET,OAAO,IAAI,EAAE;UAC3DY,QAAQ,EAAEa,OAAO,CAACvB,WAAW,CAACO,eAAe,EAAEG,QAAQ,IAAI;;OAE9D;MACDA,QAAQ,EAAEa,OAAO,CAACb,QAAQ;MAC1BG,iBAAiB,EAAE;QACjBC,UAAU,EAAES,OAAO,CAACV,iBAAiB,EAAEC,UAAU,IAAI,IAAI;QACzDG,WAAW,EAAEM,OAAO,CAACV,iBAAiB,EAAEI,WAAW,IAAI,IAAI;QAC3DC,QAAQ,EAAEK,OAAO,CAACV,iBAAiB,EAAEK,QAAQ,IAAI;;KAEpD,CAAC;IAEF;IACA,IAAI,CAACc,eAAe,CAACT,OAAO,CAACvB,WAAW,CAACK,YAAY,IAAI,EAAE,CAAC;IAE5D;IACA,IAAI,CAAC4B,cAAc,CAACV,OAAO,CAACZ,WAAW,IAAI,EAAE,CAAC;IAE9C;IACA,IAAI,CAACuB,SAAS,CAACX,OAAO,CAACX,MAAM,IAAI,EAAE,CAAC;IAEpC;IACA,IAAI,CAACuB,mBAAmB,CAACZ,OAAO,CAACJ,gBAAgB,IAAI,EAAE,CAAC;EAC1D;EAEA;EACA,IAAId,YAAY;IACd,OAAO,IAAI,CAAC3B,WAAW,CAAC0D,GAAG,CAAC,0BAA0B,CAAc;EACtE;EAEQJ,eAAe,CAACK,MAAa;IACnC,MAAMC,UAAU,GAAG,IAAI,CAACjC,YAAY;IACpCiC,UAAU,CAACC,KAAK,EAAE;IAElBF,MAAM,CAACG,OAAO,CAACC,KAAK,IAAG;MACrBH,UAAU,CAACI,IAAI,CAAC,IAAI,CAACrE,WAAW,CAACa,KAAK,CAAC;QACrCyD,EAAE,EAAE,CAACF,KAAK,CAACE,EAAE,CAAC;QACdC,MAAM,EAAE,CAACH,KAAK,CAACG,MAAM,EAAE9E,UAAU,CAACsB,QAAQ,CAAC;QAC3CyD,IAAI,EAAE,CAACJ,KAAK,CAACI,IAAI,EAAE/E,UAAU,CAACsB,QAAQ,CAAC;QACvCsB,QAAQ,EAAE,CAAC+B,KAAK,CAAC/B,QAAQ,CAAC;QAC1BoC,SAAS,EAAE,CAACL,KAAK,CAACK,SAAS;OAC5B,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;EAEAC,cAAc;IACZ,MAAMC,UAAU,GAAG,IAAI,CAAC3E,WAAW,CAACa,KAAK,CAAC;MACxCyD,EAAE,EAAE,CAAC,IAAI,CAAC;MACVC,MAAM,EAAE,CAAC,EAAE,EAAE9E,UAAU,CAACsB,QAAQ,CAAC;MACjCyD,IAAI,EAAE,CAAC,QAAQ,EAAE/E,UAAU,CAACsB,QAAQ,CAAC;MACrCsB,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBoC,SAAS,EAAE,CAAC,KAAK;KAClB,CAAC;IAEF,IAAI,CAACzC,YAAY,CAACqC,IAAI,CAACM,UAAU,CAAC;EACpC;EAEAC,iBAAiB,CAACC,KAAa;IAC7B,IAAI,CAAC7C,YAAY,CAAC8C,QAAQ,CAACD,KAAK,CAAC;EACnC;EAEA;EACA,IAAIvC,WAAW;IACb,OAAO,IAAI,CAACjC,WAAW,CAAC0D,GAAG,CAAC,aAAa,CAAc;EACzD;EAEQH,cAAc,CAACmB,KAAY;IACjC,MAAMC,UAAU,GAAG,IAAI,CAAC1C,WAAW;IACnC0C,UAAU,CAACd,KAAK,EAAE;IAElBa,KAAK,CAACZ,OAAO,CAACc,IAAI,IAAG;MACnBD,UAAU,CAACX,IAAI,CAAC,IAAI,CAACrE,WAAW,CAACa,KAAK,CAAC;QACrCyD,EAAE,EAAE,CAACW,IAAI,CAACX,EAAE,CAAC;QACbY,QAAQ,EAAE,CAACD,IAAI,CAACC,QAAQ,EAAEzF,UAAU,CAACsB,QAAQ,CAAC;QAC9CoE,GAAG,EAAE,CAACF,IAAI,CAACE,GAAG,EAAE,CAAC1F,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAAC2F,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;QACzEC,WAAW,EAAE,CAACJ,IAAI,CAACI,WAAW,CAAC;QAC/BhD,QAAQ,EAAE,CAAC4C,IAAI,CAAC5C,QAAQ;OACzB,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;EAEAiD,aAAa;IACX,MAAMC,SAAS,GAAG,IAAI,CAACvF,WAAW,CAACa,KAAK,CAAC;MACvCyD,EAAE,EAAE,CAAC,IAAI,CAAC;MACVY,QAAQ,EAAE,CAAC,UAAU,EAAEzF,UAAU,CAACsB,QAAQ,CAAC;MAC3CoE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC1F,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAAC2F,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MACnEC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBhD,QAAQ,EAAE,CAAC,IAAI;KAChB,CAAC;IAEF,IAAI,CAACC,WAAW,CAAC+B,IAAI,CAACkB,SAAS,CAAC;EAClC;EAEAC,gBAAgB,CAACX,KAAa;IAC5B,IAAI,CAACvC,WAAW,CAACwC,QAAQ,CAACD,KAAK,CAAC;EAClC;EAEA;EACA,IAAItC,MAAM;IACR,OAAO,IAAI,CAAClC,WAAW,CAAC0D,GAAG,CAAC,QAAQ,CAAc;EACpD;EAEQF,SAAS,CAACtB,MAAsB;IACtC,MAAMkD,UAAU,GAAG,IAAI,CAAClD,MAAM;IAC9BkD,UAAU,CAACvB,KAAK,EAAE;IAElB3B,MAAM,CAAC4B,OAAO,CAACuB,KAAK,IAAG;MACrBD,UAAU,CAACpB,IAAI,CAAC,IAAI,CAACrE,WAAW,CAACa,KAAK,CAAC;QACrCyD,EAAE,EAAE,CAACoB,KAAK,CAACpB,EAAE,CAAC;QACdqB,IAAI,EAAE,CAACD,KAAK,CAACC,IAAI,EAAElG,UAAU,CAACsB,QAAQ,CAAC;QACvC6E,QAAQ,EAAE,CAACF,KAAK,CAACE,QAAQ,IAAI,EAAE,CAAC;QAChCC,gBAAgB,EAAE,CAACH,KAAK,CAACG,gBAAgB,IAAI,cAAc,EAAEpG,UAAU,CAACsB,QAAQ;OACjF,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;EAEA+E,QAAQ;IACN,MAAMC,UAAU,GAAG,IAAI,CAAC/F,WAAW,CAACa,KAAK,CAAC;MACxCyD,EAAE,EAAE,CAAC,IAAI,CAAC;MACVqB,IAAI,EAAE,CAAC,EAAE,EAAElG,UAAU,CAACsB,QAAQ,CAAC;MAC/B6E,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,gBAAgB,EAAE,CAAC,cAAc,EAAEpG,UAAU,CAACsB,QAAQ;KACvD,CAAC;IAEF,IAAI,CAACwB,MAAM,CAAC8B,IAAI,CAAC0B,UAAU,CAAC;EAC9B;EAEAC,WAAW,CAACnB,KAAa;IACvB,IAAI,CAACtC,MAAM,CAACuC,QAAQ,CAACD,KAAK,CAAC;EAC7B;EAEA;EACA,IAAI/B,gBAAgB;IAClB,OAAO,IAAI,CAACzC,WAAW,CAAC0D,GAAG,CAAC,kBAAkB,CAAc;EAC9D;EAEQD,mBAAmB,CAACmC,QAA2B;IACrD,MAAMC,YAAY,GAAG,IAAI,CAACpD,gBAAgB;IAC1CoD,YAAY,CAAChC,KAAK,EAAE;IAEpB+B,QAAQ,CAAC9B,OAAO,CAACgC,OAAO,IAAG;MACzBD,YAAY,CAAC7B,IAAI,CAAC,IAAI,CAACrE,WAAW,CAACa,KAAK,CAAC;QACvCyD,EAAE,EAAE,CAAC6B,OAAO,CAAC7B,EAAE,CAAC;QAChBqB,IAAI,EAAE,CAACQ,OAAO,CAACR,IAAI,EAAElG,UAAU,CAACsB,QAAQ,CAAC;QACzCqF,WAAW,EAAE,CAACD,OAAO,CAACC,WAAW,EAAE3G,UAAU,CAACsB,QAAQ,CAAC;QACvDsF,KAAK,EAAE,CAACF,OAAO,CAACE,KAAK,EAAE,CAAC5G,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACiD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChEG,QAAQ,EAAE,CAACsD,OAAO,CAACtD,QAAQ,IAAI,KAAK,EAAEpD,UAAU,CAACsB,QAAQ,CAAC;QAC1DyC,QAAQ,EAAE,CAAC2C,OAAO,CAAC3C,QAAQ,CAAC;QAC5BoC,QAAQ,EAAE,CAACO,OAAO,CAACP,QAAQ,IAAI,EAAE,CAAC;QAClCU,QAAQ,EAAE,CAACH,OAAO,CAACG,QAAQ,KAAK,KAAK,CAAC,CAAC;OACxC,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;;EAEAC,kBAAkB;IAChB,MAAMC,YAAY,GAAG,IAAI,CAACxG,WAAW,CAACa,KAAK,CAAC;MAC1CyD,EAAE,EAAE,CAAC,IAAI,CAAC;MACVqB,IAAI,EAAE,CAAC,EAAE,EAAElG,UAAU,CAACsB,QAAQ,CAAC;MAC/BqF,WAAW,EAAE,CAAC,EAAE,EAAE3G,UAAU,CAACsB,QAAQ,CAAC;MACtCsF,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC5G,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACiD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACpDG,QAAQ,EAAE,CAAC,KAAK,EAAEpD,UAAU,CAACsB,QAAQ,CAAC;MACtCyC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdoC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdU,QAAQ,EAAE,CAAC,IAAI;KAChB,CAAC;IAEF,IAAI,CAACxD,gBAAgB,CAACuB,IAAI,CAACmC,YAAY,CAAC;EAC1C;EAEAC,qBAAqB,CAAC5B,KAAa;IACjC,IAAI,CAAC/B,gBAAgB,CAACgC,QAAQ,CAACD,KAAK,CAAC;EACvC;EAEA;EACA6B,QAAQ;IACN,IAAI,IAAI,CAACC,YAAY,EAAE,EAAE;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;MAEpB,MAAMC,SAAS,GAAG,IAAI,CAACxG,WAAW,CAACyG,KAAK;MACxC,MAAMC,aAAa,GAAyB;QAC1CjG,SAAS,EAAE+F,SAAS,CAAC/F,SAAS;QAC9BG,QAAQ,EAAE4F,SAAS,CAAC5F,QAAQ;QAC5BC,iBAAiB,EAAE2F,SAAS,CAAC3F,iBAAiB;QAC9CC,QAAQ,EAAE0F,SAAS,CAAC1F,QAAQ;QAC5BG,QAAQ,EAAEuF,SAAS,CAACvF,QAAQ;QAC5BK,WAAW,EAAEkF,SAAS,CAAClF,WAAW;QAClCN,OAAO,EAAEwF,SAAS,CAACxF,OAAO;QAC1BgB,QAAQ,EAAEwE,SAAS,CAACxE,QAAQ;QAC5BE,MAAM,EAAEsE,SAAS,CAACtE,MAAM,IAAI,EAAE;QAC9BC,iBAAiB,EAAEqE,SAAS,CAACrE,iBAAiB;QAC9CM,gBAAgB,EAAE+D,SAAS,CAAC/D,gBAAgB,IAAI;OACjD;MAED,IAAI,CAAC7C,cAAc,CAAC+G,aAAa,CAACD,aAAa,CAAC,CAC7C/D,IAAI,CAACpD,SAAS,CAAC,IAAI,CAACc,QAAQ,CAAC,CAAC,CAC9BuC,SAAS,CAAC;QACTtC,IAAI,EAAGsG,cAAc,IAAI;UACvB,IAAI,CAACL,QAAQ,GAAG,KAAK;UACrB,IAAI,CAACxG,QAAQ,CAACmD,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UAChF,IAAI,CAACrD,MAAM,CAACsD,QAAQ,CAAC,CAAC,UAAU,EAAEwD,cAAc,CAACC,IAAI,CAAC,CAAC;QACzD,CAAC;QACD7D,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACuD,QAAQ,GAAG,KAAK;UACrBtD,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,IAAI,CAACjD,QAAQ,CAACmD,IAAI,CAAC,2CAA2C,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QAC9F;OACD,CAAC;;IAEN;EACF;;EAEA2D,QAAQ;IACN,IAAI,IAAI,CAACjE,OAAO,EAAE;MAChB,IAAI,CAAC/C,MAAM,CAACsD,QAAQ,CAAC,CAAC,UAAU,EAAE,IAAI,CAACP,OAAO,CAACgE,IAAI,CAAC,CAAC;KACtD,MAAM;MACL,IAAI,CAAC/G,MAAM,CAACsD,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEA;EACA2D,sBAAsB,CAACC,KAAU;IAC/B,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,IAAI,CAACG,iBAAiB,CAACH,IAAI,EAAE,SAAS,CAAC,EAAE;QAC3C,IAAI,CAACI,kBAAkB,CAACJ,IAAI,EAAE,SAAS,CAAC;QACxC,IAAI,CAACK,kBAAkB,CAACL,IAAI,CAAC;;;IAGjC;IACAD,KAAK,CAACE,MAAM,CAACT,KAAK,GAAG,EAAE;EACzB;EAEAc,oBAAoB,CAACP,KAAU;IAC7B,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,IAAI,CAACG,iBAAiB,CAACH,IAAI,EAAE,OAAO,CAAC,EAAE;QACzC,IAAI,CAACI,kBAAkB,CAACJ,IAAI,EAAE,OAAO,CAAC;QACtC,IAAI,CAACO,gBAAgB,CAACP,IAAI,CAAC;;;IAG/B;IACAD,KAAK,CAACE,MAAM,CAACT,KAAK,GAAG,EAAE;EACzB;EAEQW,iBAAiB,CAACH,IAAU,EAAE9C,IAAyB;IAC7D;IACA,MAAMsD,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IACxF,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACT,IAAI,CAAC9C,IAAI,CAAC,EAAE;MACrC,IAAI,CAACpE,QAAQ,CAACmD,IAAI,CAAC,4DAA4D,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC7G,OAAO,KAAK;;IAGd;IACA,MAAMwE,OAAO,GAAGxD,IAAI,KAAK,SAAS,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;IACvE,IAAI8C,IAAI,CAACW,IAAI,GAAGD,OAAO,EAAE;MACvB,MAAME,SAAS,GAAGF,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC;MACzC,IAAI,CAAC5H,QAAQ,CAACmD,IAAI,CAAC,+BAA+B2E,SAAS,IAAI,EAAE,OAAO,EAAE;QAAE1E,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC7F,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEQkE,kBAAkB,CAACJ,IAAU,EAAE9C,IAAyB;IAC9D,MAAM2D,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;MACzB,IAAI9D,IAAI,KAAK,SAAS,EAAE;QACtB,IAAI,CAAC+D,mBAAmB,GAAGD,CAAC,CAACf,MAAM,CAACiB,MAAM;OAC3C,MAAM;QACL,IAAI,CAACC,iBAAiB,GAAGH,CAAC,CAACf,MAAM,CAACiB,MAAM;;IAE5C,CAAC;IACDL,MAAM,CAACO,aAAa,CAACpB,IAAI,CAAC;EAC5B;EAEQK,kBAAkB,CAACL,IAAU;IACnC,IAAI,CAACqB,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAAC1I,cAAc,CAAC0H,kBAAkB,CAACL,IAAI,CAAC,CACzCtE,IAAI,CAACpD,SAAS,CAAC,IAAI,CAACc,QAAQ,CAAC,CAAC,CAC9BuC,SAAS,CAAC;MACTtC,IAAI,EAAGiI,QAAQ,IAAI;QACjB,IAAI,CAACD,uBAAuB,GAAG,KAAK;QACpC,IAAI,IAAI,CAACzF,OAAO,EAAE;UAChB,IAAI,CAACA,OAAO,CAAC2F,eAAe,GAAGD,QAAQ,CAACzD,GAAG;;QAE7C,IAAI,CAACoD,mBAAmB,GAAG,IAAI,CAAC,CAAC;QACjC,IAAI,CAACnI,QAAQ,CAACmD,IAAI,CAAC,qCAAqC,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACxF,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACsF,uBAAuB,GAAG,KAAK;QACpC,IAAI,CAACJ,mBAAmB,GAAG,IAAI,CAAC,CAAC;QACjCjF,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAMyF,YAAY,GAAGzF,KAAK,CAACA,KAAK,EAAE0F,OAAO,IAAI,kDAAkD;QAC/F,IAAI,CAAC3I,QAAQ,CAACmD,IAAI,CAACuF,YAAY,EAAE,OAAO,EAAE;UAAEtF,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC/D;KACD,CAAC;EACN;EAEQqE,gBAAgB,CAACP,IAAU;IACjC,IAAI,CAAC0B,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAAC/I,cAAc,CAAC4H,gBAAgB,CAACP,IAAI,CAAC,CACvCtE,IAAI,CAACpD,SAAS,CAAC,IAAI,CAACc,QAAQ,CAAC,CAAC,CAC9BuC,SAAS,CAAC;MACTtC,IAAI,EAAGiI,QAAQ,IAAI;QACjB,IAAI,CAACI,qBAAqB,GAAG,KAAK;QAClC,IAAI,IAAI,CAAC9F,OAAO,EAAE;UAChB,IAAI,CAACA,OAAO,CAAC+F,aAAa,GAAGL,QAAQ,CAACzD,GAAG;;QAE3C,IAAI,CAACsD,iBAAiB,GAAG,IAAI,CAAC,CAAC;QAC/B,IAAI,CAACrI,QAAQ,CAACmD,IAAI,CAAC,mCAAmC,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACtF,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC2F,qBAAqB,GAAG,KAAK;QAClC,IAAI,CAACP,iBAAiB,GAAG,IAAI,CAAC,CAAC;QAC/BnF,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,MAAMyF,YAAY,GAAGzF,KAAK,CAACA,KAAK,EAAE0F,OAAO,IAAI,gDAAgD;QAC7F,IAAI,CAAC3I,QAAQ,CAACmD,IAAI,CAACuF,YAAY,EAAE,OAAO,EAAE;UAAEtF,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC/D;KACD,CAAC;EACN;EAEA;EACQ0F,oBAAoB;IAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/I,WAAW,CAACgJ,QAAQ,CAAC,CAAClF,OAAO,CAACmF,GAAG,IAAG;MACnD,MAAMC,OAAO,GAAG,IAAI,CAAClJ,WAAW,CAAC0D,GAAG,CAACuF,GAAG,CAAC;MACzCC,OAAO,EAAEC,aAAa,EAAE;MAExB,IAAID,OAAO,YAAY7J,SAAS,EAAE;QAChC6J,OAAO,CAACF,QAAQ,CAAClF,OAAO,CAACsF,YAAY,IAAG;UACtC,IAAIA,YAAY,YAAYjK,SAAS,EAAE;YACrC2J,MAAM,CAACC,IAAI,CAACK,YAAY,CAACJ,QAAQ,CAAC,CAAClF,OAAO,CAACuF,QAAQ,IAAG;cACpDD,YAAY,CAAC1F,GAAG,CAAC2F,QAAQ,CAAC,EAAEF,aAAa,EAAE;YAC7C,CAAC,CAAC;;QAEN,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAG,eAAe,CAACC,SAAiB;IAC/B,MAAML,OAAO,GAAG,IAAI,CAAClJ,WAAW,CAAC0D,GAAG,CAAC6F,SAAS,CAAC;IAC/C,IAAI,CAACL,OAAO,IAAI,CAACA,OAAO,CAACM,MAAM,EAAE,OAAO,EAAE;IAE1C,MAAMC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAACH,SAAS,CAAC;IAE5D,IAAIL,OAAO,CAACS,QAAQ,CAAC,UAAU,CAAC,EAAE;MAChC,OAAO,GAAGF,gBAAgB,cAAc;;IAE1C,IAAIP,OAAO,CAACS,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7B,OAAO,oCAAoC;;IAE7C,IAAIT,OAAO,CAACS,QAAQ,CAAC,WAAW,CAAC,EAAE;MACjC,MAAMC,cAAc,GAAGV,OAAO,CAACM,MAAM,CAAC,WAAW,CAAC,CAACI,cAAc;MACjE,OAAO,GAAGH,gBAAgB,qBAAqBG,cAAc,aAAa;;IAE5E,IAAIV,OAAO,CAACS,QAAQ,CAAC,WAAW,CAAC,EAAE;MACjC,MAAM5I,SAAS,GAAGmI,OAAO,CAACM,MAAM,CAAC,WAAW,CAAC,CAACI,cAAc;MAC5D,OAAO,GAAGH,gBAAgB,yBAAyB1I,SAAS,aAAa;;IAE3E,IAAImI,OAAO,CAACS,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC/B,OAAO,0BAA0B;;IAEnC,IAAIT,OAAO,CAACS,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC3B,MAAME,QAAQ,GAAGX,OAAO,CAACM,MAAM,CAAC,KAAK,CAAC,CAACnH,GAAG;MAC1C,OAAO,GAAGoH,gBAAgB,qBAAqBI,QAAQ,EAAE;;IAE3D,IAAIX,OAAO,CAACS,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC3B,MAAMG,QAAQ,GAAGZ,OAAO,CAACM,MAAM,CAAC,KAAK,CAAC,CAAClH,GAAG;MAC1C,OAAO,GAAGmH,gBAAgB,kBAAkBK,QAAQ,EAAE;;IAExD,OAAO,EAAE;EACX;EAEQJ,mBAAmB,CAACH,SAAiB;IAC3C,MAAMQ,UAAU,GAA8B;MAC5C,WAAW,EAAE,YAAY;MACzB,UAAU,EAAE,WAAW;MACvB,mBAAmB,EAAE,oBAAoB;MACzC,UAAU,EAAE,UAAU;MACtB,SAAS,EAAE,SAAS;MACpB,mBAAmB,EAAE,OAAO;MAC5B,qBAAqB,EAAE,SAAS;MAChC,0BAA0B,EAAE,eAAe;MAC3C,eAAe,EAAE,MAAM;MACvB,gBAAgB,EAAE,OAAO;MACzB,kBAAkB,EAAE,SAAS;MAC7B,0BAA0B,EAAE,kBAAkB;MAC9C,8BAA8B,EAAE,aAAa;MAC7C,+BAA+B,EAAE,cAAc;MAC/C,4BAA4B,EAAE;KAC/B;IACD,OAAOA,UAAU,CAACR,SAAS,CAAC,IAAIA,SAAS;EAC3C;EAEA;EACAjD,YAAY;IACV,IAAI,IAAI,CAACtG,WAAW,CAACgK,OAAO,EAAE;MAC5B,IAAI,CAACnB,oBAAoB,EAAE;MAE3B;MACA,MAAMoB,iBAAiB,GAAG,IAAI,CAACC,qBAAqB,EAAE;MACtD,IAAID,iBAAiB,EAAE;QACrBA,iBAAiB,CAACE,KAAK,EAAE;;MAG3B;MACA,MAAMX,MAAM,GAAG,IAAI,CAACY,aAAa,EAAE;MACnC,IAAIZ,MAAM,CAACa,MAAM,GAAG,CAAC,EAAE;QACrB,IAAI,CAACtK,QAAQ,CAACmD,IAAI,CAAC,oCAAoCsG,MAAM,CAACc,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE;UAAEnH,QAAQ,EAAE;QAAI,CAAE,CAAC;;MAG1G,OAAO,KAAK;;IAEd,OAAO,IAAI;EACb;EAEQ+G,qBAAqB;IAC3B,MAAMK,aAAa,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,8GAA8G,CAAC;IAC/J,OAAOF,aAAa,CAACF,MAAM,GAAG,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAgB,GAAG,IAAI;EAC1E;EAEQH,aAAa;IACnB,MAAMZ,MAAM,GAAa,EAAE;IAE3B;IACAV,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/I,WAAW,CAACgJ,QAAQ,CAAC,CAAClF,OAAO,CAACmF,GAAG,IAAG;MACnD,MAAMC,OAAO,GAAG,IAAI,CAAClJ,WAAW,CAAC0D,GAAG,CAACuF,GAAG,CAAC;MACzC,IAAIC,OAAO,IAAIA,OAAO,CAACc,OAAO,IAAId,OAAO,CAACwB,OAAO,EAAE;QACjD,MAAMjC,YAAY,GAAG,IAAI,CAACa,eAAe,CAACL,GAAG,CAAC;QAC9C,IAAIR,YAAY,EAAE;UAChBe,MAAM,CAACxF,IAAI,CAACyE,YAAY,CAAC;;;IAG/B,CAAC,CAAC;IAEF;IACA,MAAMnH,WAAW,GAAG,IAAI,CAACtB,WAAW,CAAC0D,GAAG,CAAC,aAAa,CAAc;IACpE,IAAIpC,WAAW,IAAIA,WAAW,CAAC0I,OAAO,EAAE;MACtClB,MAAM,CAACC,IAAI,CAACzH,WAAW,CAAC0H,QAAQ,CAAC,CAAClF,OAAO,CAACmF,GAAG,IAAG;QAC9C,MAAMC,OAAO,GAAG5H,WAAW,CAACoC,GAAG,CAACuF,GAAG,CAAC;QACpC,IAAIC,OAAO,IAAIA,OAAO,CAACc,OAAO,IAAId,OAAO,CAACwB,OAAO,EAAE;UACjD,MAAMjC,YAAY,GAAG,IAAI,CAACa,eAAe,CAAC,eAAeL,GAAG,EAAE,CAAC;UAC/D,IAAIR,YAAY,EAAE;YAChBe,MAAM,CAACxF,IAAI,CAACyE,YAAY,CAAC;;;MAG/B,CAAC,CAAC;;IAGJ,OAAOe,MAAM,CAACmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC7B;EAEA;EACAC,kBAAkB;IAChB,OAAO,CACL;MAAEnE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,SAAS;MAAEoE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEpE,KAAK,EAAE,QAAQ;MAAEoE,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEpE,KAAK,EAAE,SAAS;MAAEoE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEpE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,WAAW;MAAEoE,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEpE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,SAAS;MAAEoE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEpE,KAAK,EAAE,OAAO;MAAEoE,KAAK,EAAE;IAAO,CAAE,CACnC;EACH;EAEA;EACAC,mBAAmB;IACjB,OAAO,CACL;MAAErE,KAAK,EAAE,QAAQ;MAAEoE,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEpE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,MAAM;MAAEoE,KAAK,EAAE;IAAM,CAAE,CACjC;EACH;EAEA;EACAE,uBAAuB;IACrB,OAAO,CACL;MAAEtE,KAAK,EAAE,WAAW;MAAEoE,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEpE,KAAK,EAAE,iBAAiB;MAAEoE,KAAK,EAAE;IAAiB,CAAE,EACtD;MAAEpE,KAAK,EAAE,WAAW;MAAEoE,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEpE,KAAK,EAAE,sBAAsB;MAAEoE,KAAK,EAAE;IAAsB,CAAE,EAChE;MAAEpE,KAAK,EAAE,YAAY;MAAEoE,KAAK,EAAE;IAAY,CAAE,EAC5C;MAAEpE,KAAK,EAAE,eAAe;MAAEoE,KAAK,EAAE;IAAe,CAAE,EAClD;MAAEpE,KAAK,EAAE,gBAAgB;MAAEoE,KAAK,EAAE;IAAgB,CAAE,EACpD;MAAEpE,KAAK,EAAE,YAAY;MAAEoE,KAAK,EAAE;IAAY,CAAE,EAC5C;MAAEpE,KAAK,EAAE,OAAO;MAAEoE,KAAK,EAAE;IAAO,CAAE,CACnC;EACH;EAEA;EACAG,0BAA0B;IACxB,OAAO,CACL;MAAEvE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,cAAc;MAAEoE,KAAK,EAAE;IAAc,CAAE,EAChD;MAAEpE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,QAAQ;MAAEoE,KAAK,EAAE;IAAQ,CAAE,CACrC;EACH;EAEA;EACAI,yBAAyB;IACvB,OAAO,CACL;MAAExE,KAAK,EAAE,SAAS;MAAEoE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEpE,KAAK,EAAE,cAAc;MAAEoE,KAAK,EAAE;IAAc,CAAE,EAChD;MAAEpE,KAAK,EAAE,SAAS;MAAEoE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEpE,KAAK,EAAE,UAAU;MAAEoE,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEpE,KAAK,EAAE,QAAQ;MAAEoE,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEpE,KAAK,EAAE,OAAO;MAAEoE,KAAK,EAAE;IAAO,CAAE,CACnC;EACH;EAEA;EACAK,kBAAkB;IAChB,OAAO,CACL;MAAEzE,KAAK,EAAE,KAAK;MAAEoE,KAAK,EAAE;IAAqB,CAAE,EAC9C;MAAEpE,KAAK,EAAE,KAAK;MAAEoE,KAAK,EAAE;IAAY,CAAE,EACrC;MAAEpE,KAAK,EAAE,KAAK;MAAEoE,KAAK,EAAE;IAAiB,CAAE,EAC1C;MAAEpE,KAAK,EAAE,KAAK;MAAEoE,KAAK,EAAE;IAAqB,CAAE,CAC/C;EACH;;;uBAxoBWpL,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAA0L;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCfjC/L,wEAwhBM;UAGNA,qEAGM;;;UA9hB+BA,qCAAgB;UA2hBrBA,eAAe;UAAfA,oCAAe", "names": ["FormGroup", "Validators", "FormArray", "Subject", "takeUntil", "i0", "ProfileEditComponent", "constructor", "formBuilder", "profileService", "authService", "router", "snackBar", "profileForm", "createForm", "ngOnInit", "loadProfile", "ngOnDestroy", "destroy$", "next", "complete", "group", "firstName", "required", "<PERSON><PERSON><PERSON><PERSON>", "lastName", "professional<PERSON>itle", "headline", "max<PERSON><PERSON><PERSON>", "summary", "location", "city", "state", "country", "displayLocation", "contactInfo", "email", "isEmailPublic", "website", "portfolioUrl", "phoneNumbers", "array", "businessAddress", "street", "postalCode", "isPublic", "socialLinks", "skills", "consultationRates", "hourlyRate", "min", "max", "sessionRate", "currency", "serviceOfferings", "getCurrentUserProfile", "pipe", "subscribe", "profile", "populateForm", "isLoading", "error", "console", "open", "duration", "navigate", "patchValue", "setPhoneNumbers", "setSocialLinks", "setSkills", "setServiceOfferings", "get", "phones", "phoneArray", "clear", "for<PERSON>ach", "phone", "push", "id", "number", "type", "isPrimary", "addPhoneNumber", "phoneGroup", "removePhoneNumber", "index", "removeAt", "links", "linksArray", "link", "platform", "url", "pattern", "displayName", "addSocialLink", "linkGroup", "removeSocialLink", "skillArray", "skill", "name", "category", "proficiencyLevel", "addSkill", "skillGroup", "removeSkill", "services", "serviceArray", "service", "description", "price", "isActive", "addServiceOffering", "serviceGroup", "removeServiceOffering", "onSubmit", "validateForm", "isSaving", "formValue", "value", "updateRequest", "updateProfile", "updatedProfile", "slug", "onCancel", "onProfilePhotoSelected", "event", "file", "target", "files", "validateImageFile", "createImagePreview", "uploadProfilePhoto", "onCoverPhotoSelected", "uploadCoverPhoto", "allowedTypes", "includes", "maxSize", "size", "maxSizeMB", "reader", "FileReader", "onload", "e", "profilePhotoPreview", "result", "coverPhotoPreview", "readAsDataURL", "isUploadingProfilePhoto", "response", "profilePhotoUrl", "errorMessage", "message", "isUploadingCoverPhoto", "coverPhotoUrl", "markFormGroupTouched", "Object", "keys", "controls", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "arrayControl", "<PERSON><PERSON><PERSON>", "getErrorMessage", "fieldName", "errors", "fieldDisplayName", "getFieldDisplayName", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "minValue", "maxValue", "fieldNames", "invalid", "firstInvalidField", "findFirstInvalidField", "focus", "getFormErrors", "length", "join", "invalidFields", "document", "querySelectorAll", "touched", "slice", "getPlatformOptions", "label", "getPhoneTypeOptions", "getSkillCategoryOptions", "getProficiencyLevelOptions", "getServiceCategoryOptions", "getCurrencyOptions", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\profile-edit\\profile-edit.component.ts", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\profile-edit\\profile-edit.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\nimport { ProfileService } from '../../services/profile.service';\r\nimport { AuthService } from '../../../auth/services/auth.service';\r\nimport { UserProfile, ProfileUpdateRequest, ProfileSkill, ConsultationRates, ServiceOffering } from '../../models/profile.models';\r\n\r\n@Component({\r\n  selector: 'app-profile-edit',\r\n  templateUrl: './profile-edit.component.html',\r\n  styleUrls: ['./profile-edit.component.css']\r\n})\r\nexport class ProfileEditComponent implements OnInit, OnDestroy {\r\n  profileForm: FormGroup;\r\n  profile: UserProfile | null = null;\r\n  isLoading = true;\r\n  isSaving = false;\r\n  isUploadingProfilePhoto = false;\r\n  isUploadingCoverPhoto = false;\r\n  profilePhotoPreview: string | null = null;\r\n  coverPhotoPreview: string | null = null;\r\n\r\n  private destroy$ = new Subject<void>();\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private profileService: ProfileService,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    this.profileForm = this.createForm();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadProfile();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  private createForm(): FormGroup {\r\n    return this.formBuilder.group({\r\n      // Basic Information\r\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\r\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\r\n      professionalTitle: [''],\r\n      headline: ['', Validators.maxLength(220)],\r\n      summary: ['', Validators.maxLength(2000)],\r\n      \r\n      // Location\r\n      location: this.formBuilder.group({\r\n        city: [''],\r\n        state: [''],\r\n        country: [''],\r\n        displayLocation: ['']\r\n      }),\r\n      \r\n      // Contact Information\r\n      contactInfo: this.formBuilder.group({\r\n        email: ['', Validators.email],\r\n        isEmailPublic: [false],\r\n        website: [''],\r\n        portfolioUrl: [''],\r\n        phoneNumbers: this.formBuilder.array([]),\r\n        businessAddress: this.formBuilder.group({\r\n          street: [''],\r\n          city: [''],\r\n          state: [''],\r\n          postalCode: [''],\r\n          country: [''],\r\n          isPublic: [false]\r\n        })\r\n      }),\r\n      \r\n      // Privacy Settings\r\n      isPublic: [true],\r\n\r\n      // Social Links\r\n      socialLinks: this.formBuilder.array([]),\r\n\r\n      // Skills\r\n      skills: this.formBuilder.array([]),\r\n\r\n      // Consultation Rates\r\n      consultationRates: this.formBuilder.group({\r\n        hourlyRate: [null, [Validators.min(0), Validators.max(10000)]],\r\n        sessionRate: [null, [Validators.min(0), Validators.max(10000)]],\r\n        currency: ['BGN', Validators.required]\r\n      }),\r\n\r\n      // Service Offerings\r\n      serviceOfferings: this.formBuilder.array([])\r\n    });\r\n  }\r\n\r\n  private loadProfile(): void {\r\n    this.profileService.getCurrentUserProfile()\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (profile) => {\r\n          this.profile = profile;\r\n          this.populateForm(profile);\r\n          this.isLoading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading profile:', error);\r\n          this.snackBar.open('Error loading profile', 'Close', { duration: 5000 });\r\n          this.router.navigate(['/dashboard']);\r\n          this.isLoading = false;\r\n        }\r\n      });\r\n  }\r\n\r\n  private populateForm(profile: UserProfile): void {\r\n    this.profileForm.patchValue({\r\n      firstName: profile.firstName,\r\n      lastName: profile.lastName,\r\n      professionalTitle: profile.professionalTitle || '',\r\n      headline: profile.headline || '',\r\n      summary: profile.summary || '',\r\n      location: {\r\n        city: profile.location?.city || '',\r\n        state: profile.location?.state || '',\r\n        country: profile.location?.country || '',\r\n        displayLocation: profile.location?.displayLocation || ''\r\n      },\r\n      contactInfo: {\r\n        email: profile.contactInfo.email || '',\r\n        isEmailPublic: profile.contactInfo.isEmailPublic,\r\n        website: profile.contactInfo.website || '',\r\n        portfolioUrl: profile.contactInfo.portfolioUrl || '',\r\n        businessAddress: {\r\n          street: profile.contactInfo.businessAddress?.street || '',\r\n          city: profile.contactInfo.businessAddress?.city || '',\r\n          state: profile.contactInfo.businessAddress?.state || '',\r\n          postalCode: profile.contactInfo.businessAddress?.postalCode || '',\r\n          country: profile.contactInfo.businessAddress?.country || '',\r\n          isPublic: profile.contactInfo.businessAddress?.isPublic || false\r\n        }\r\n      },\r\n      isPublic: profile.isPublic,\r\n      consultationRates: {\r\n        hourlyRate: profile.consultationRates?.hourlyRate || null,\r\n        sessionRate: profile.consultationRates?.sessionRate || null,\r\n        currency: profile.consultationRates?.currency || 'BGN'\r\n      }\r\n    });\r\n\r\n    // Populate phone numbers\r\n    this.setPhoneNumbers(profile.contactInfo.phoneNumbers || []);\r\n\r\n    // Populate social links\r\n    this.setSocialLinks(profile.socialLinks || []);\r\n\r\n    // Populate skills\r\n    this.setSkills(profile.skills || []);\r\n\r\n    // Populate service offerings\r\n    this.setServiceOfferings(profile.serviceOfferings || []);\r\n  }\r\n\r\n  // Phone Numbers Management\r\n  get phoneNumbers(): FormArray {\r\n    return this.profileForm.get('contactInfo.phoneNumbers') as FormArray;\r\n  }\r\n\r\n  private setPhoneNumbers(phones: any[]): void {\r\n    const phoneArray = this.phoneNumbers;\r\n    phoneArray.clear();\r\n    \r\n    phones.forEach(phone => {\r\n      phoneArray.push(this.formBuilder.group({\r\n        id: [phone.id],\r\n        number: [phone.number, Validators.required],\r\n        type: [phone.type, Validators.required],\r\n        isPublic: [phone.isPublic],\r\n        isPrimary: [phone.isPrimary]\r\n      }));\r\n    });\r\n  }\r\n\r\n  addPhoneNumber(): void {\r\n    const phoneGroup = this.formBuilder.group({\r\n      id: [null],\r\n      number: ['', Validators.required],\r\n      type: ['mobile', Validators.required],\r\n      isPublic: [false],\r\n      isPrimary: [false]\r\n    });\r\n    \r\n    this.phoneNumbers.push(phoneGroup);\r\n  }\r\n\r\n  removePhoneNumber(index: number): void {\r\n    this.phoneNumbers.removeAt(index);\r\n  }\r\n\r\n  // Social Links Management\r\n  get socialLinks(): FormArray {\r\n    return this.profileForm.get('socialLinks') as FormArray;\r\n  }\r\n\r\n  private setSocialLinks(links: any[]): void {\r\n    const linksArray = this.socialLinks;\r\n    linksArray.clear();\r\n    \r\n    links.forEach(link => {\r\n      linksArray.push(this.formBuilder.group({\r\n        id: [link.id],\r\n        platform: [link.platform, Validators.required],\r\n        url: [link.url, [Validators.required, Validators.pattern('https?://.+')]],\r\n        displayName: [link.displayName],\r\n        isPublic: [link.isPublic]\r\n      }));\r\n    });\r\n  }\r\n\r\n  addSocialLink(): void {\r\n    const linkGroup = this.formBuilder.group({\r\n      id: [null],\r\n      platform: ['linkedin', Validators.required],\r\n      url: ['', [Validators.required, Validators.pattern('https?://.+')]],\r\n      displayName: [''],\r\n      isPublic: [true]\r\n    });\r\n    \r\n    this.socialLinks.push(linkGroup);\r\n  }\r\n\r\n  removeSocialLink(index: number): void {\r\n    this.socialLinks.removeAt(index);\r\n  }\r\n\r\n  // Skills Management\r\n  get skills(): FormArray {\r\n    return this.profileForm.get('skills') as FormArray;\r\n  }\r\n\r\n  private setSkills(skills: ProfileSkill[]): void {\r\n    const skillArray = this.skills;\r\n    skillArray.clear();\r\n\r\n    skills.forEach(skill => {\r\n      skillArray.push(this.formBuilder.group({\r\n        id: [skill.id],\r\n        name: [skill.name, Validators.required],\r\n        category: [skill.category || ''],\r\n        proficiencyLevel: [skill.proficiencyLevel || 'intermediate', Validators.required]\r\n      }));\r\n    });\r\n  }\r\n\r\n  addSkill(): void {\r\n    const skillGroup = this.formBuilder.group({\r\n      id: [null],\r\n      name: ['', Validators.required],\r\n      category: [''],\r\n      proficiencyLevel: ['intermediate', Validators.required]\r\n    });\r\n\r\n    this.skills.push(skillGroup);\r\n  }\r\n\r\n  removeSkill(index: number): void {\r\n    this.skills.removeAt(index);\r\n  }\r\n\r\n  // Service Offerings Management\r\n  get serviceOfferings(): FormArray {\r\n    return this.profileForm.get('serviceOfferings') as FormArray;\r\n  }\r\n\r\n  private setServiceOfferings(services: ServiceOffering[]): void {\r\n    const serviceArray = this.serviceOfferings;\r\n    serviceArray.clear();\r\n\r\n    services.forEach(service => {\r\n      serviceArray.push(this.formBuilder.group({\r\n        id: [service.id],\r\n        name: [service.name, Validators.required],\r\n        description: [service.description, Validators.required],\r\n        price: [service.price, [Validators.required, Validators.min(0)]],\r\n        currency: [service.currency || 'BGN', Validators.required],\r\n        duration: [service.duration],\r\n        category: [service.category || ''],\r\n        isActive: [service.isActive !== false] // default to true\r\n      }));\r\n    });\r\n  }\r\n\r\n  addServiceOffering(): void {\r\n    const serviceGroup = this.formBuilder.group({\r\n      id: [null],\r\n      name: ['', Validators.required],\r\n      description: ['', Validators.required],\r\n      price: [0, [Validators.required, Validators.min(0)]],\r\n      currency: ['BGN', Validators.required],\r\n      duration: [60], // default 60 minutes\r\n      category: [''],\r\n      isActive: [true]\r\n    });\r\n\r\n    this.serviceOfferings.push(serviceGroup);\r\n  }\r\n\r\n  removeServiceOffering(index: number): void {\r\n    this.serviceOfferings.removeAt(index);\r\n  }\r\n\r\n  // Form Submission\r\n  onSubmit(): void {\r\n    if (this.validateForm()) {\r\n      this.isSaving = true;\r\n      \r\n      const formValue = this.profileForm.value;\r\n      const updateRequest: ProfileUpdateRequest = {\r\n        firstName: formValue.firstName,\r\n        lastName: formValue.lastName,\r\n        professionalTitle: formValue.professionalTitle,\r\n        headline: formValue.headline,\r\n        location: formValue.location,\r\n        contactInfo: formValue.contactInfo,\r\n        summary: formValue.summary,\r\n        isPublic: formValue.isPublic,\r\n        skills: formValue.skills || [],\r\n        consultationRates: formValue.consultationRates,\r\n        serviceOfferings: formValue.serviceOfferings || []\r\n      };\r\n\r\n      this.profileService.updateProfile(updateRequest)\r\n        .pipe(takeUntil(this.destroy$))\r\n        .subscribe({\r\n          next: (updatedProfile) => {\r\n            this.isSaving = false;\r\n            this.snackBar.open('Profile updated successfully!', 'Close', { duration: 3000 });\r\n            this.router.navigate(['/profile', updatedProfile.slug]);\r\n          },\r\n          error: (error) => {\r\n            this.isSaving = false;\r\n            console.error('Error updating profile:', error);\r\n            this.snackBar.open('Error updating profile. Please try again.', 'Close', { duration: 5000 });\r\n          }\r\n        });\r\n    }\r\n    // Validation is now handled in validateForm() method\r\n  }\r\n\r\n  onCancel(): void {\r\n    if (this.profile) {\r\n      this.router.navigate(['/profile', this.profile.slug]);\r\n    } else {\r\n      this.router.navigate(['/dashboard']);\r\n    }\r\n  }\r\n\r\n  // File Upload Methods\r\n  onProfilePhotoSelected(event: any): void {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      if (this.validateImageFile(file, 'profile')) {\r\n        this.createImagePreview(file, 'profile');\r\n        this.uploadProfilePhoto(file);\r\n      }\r\n    }\r\n    // Reset the input to allow selecting the same file again\r\n    event.target.value = '';\r\n  }\r\n\r\n  onCoverPhotoSelected(event: any): void {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      if (this.validateImageFile(file, 'cover')) {\r\n        this.createImagePreview(file, 'cover');\r\n        this.uploadCoverPhoto(file);\r\n      }\r\n    }\r\n    // Reset the input to allow selecting the same file again\r\n    event.target.value = '';\r\n  }\r\n\r\n  private validateImageFile(file: File, type: 'profile' | 'cover'): boolean {\r\n    // Check file type\r\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];\r\n    if (!allowedTypes.includes(file.type)) {\r\n      this.snackBar.open('Please select a valid image file (JPEG, PNG, GIF, or WebP)', 'Close', { duration: 5000 });\r\n      return false;\r\n    }\r\n\r\n    // Check file size (5MB for profile, 10MB for cover)\r\n    const maxSize = type === 'profile' ? 5 * 1024 * 1024 : 10 * 1024 * 1024;\r\n    if (file.size > maxSize) {\r\n      const maxSizeMB = maxSize / (1024 * 1024);\r\n      this.snackBar.open(`File size must be less than ${maxSizeMB}MB`, 'Close', { duration: 5000 });\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  private createImagePreview(file: File, type: 'profile' | 'cover'): void {\r\n    const reader = new FileReader();\r\n    reader.onload = (e: any) => {\r\n      if (type === 'profile') {\r\n        this.profilePhotoPreview = e.target.result;\r\n      } else {\r\n        this.coverPhotoPreview = e.target.result;\r\n      }\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n\r\n  private uploadProfilePhoto(file: File): void {\r\n    this.isUploadingProfilePhoto = true;\r\n    this.profileService.uploadProfilePhoto(file)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (response) => {\r\n          this.isUploadingProfilePhoto = false;\r\n          if (this.profile) {\r\n            this.profile.profilePhotoUrl = response.url;\r\n          }\r\n          this.profilePhotoPreview = null; // Clear preview since we have the actual URL\r\n          this.snackBar.open('Profile photo updated successfully!', 'Close', { duration: 3000 });\r\n        },\r\n        error: (error) => {\r\n          this.isUploadingProfilePhoto = false;\r\n          this.profilePhotoPreview = null; // Clear preview on error\r\n          console.error('Error uploading profile photo:', error);\r\n          const errorMessage = error.error?.message || 'Error uploading profile photo. Please try again.';\r\n          this.snackBar.open(errorMessage, 'Close', { duration: 5000 });\r\n        }\r\n      });\r\n  }\r\n\r\n  private uploadCoverPhoto(file: File): void {\r\n    this.isUploadingCoverPhoto = true;\r\n    this.profileService.uploadCoverPhoto(file)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (response) => {\r\n          this.isUploadingCoverPhoto = false;\r\n          if (this.profile) {\r\n            this.profile.coverPhotoUrl = response.url;\r\n          }\r\n          this.coverPhotoPreview = null; // Clear preview since we have the actual URL\r\n          this.snackBar.open('Cover photo updated successfully!', 'Close', { duration: 3000 });\r\n        },\r\n        error: (error) => {\r\n          this.isUploadingCoverPhoto = false;\r\n          this.coverPhotoPreview = null; // Clear preview on error\r\n          console.error('Error uploading cover photo:', error);\r\n          const errorMessage = error.error?.message || 'Error uploading cover photo. Please try again.';\r\n          this.snackBar.open(errorMessage, 'Close', { duration: 5000 });\r\n        }\r\n      });\r\n  }\r\n\r\n  // Utility Methods\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.profileForm.controls).forEach(key => {\r\n      const control = this.profileForm.get(key);\r\n      control?.markAsTouched();\r\n      \r\n      if (control instanceof FormArray) {\r\n        control.controls.forEach(arrayControl => {\r\n          if (arrayControl instanceof FormGroup) {\r\n            Object.keys(arrayControl.controls).forEach(arrayKey => {\r\n              arrayControl.get(arrayKey)?.markAsTouched();\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  getErrorMessage(fieldName: string): string {\r\n    const control = this.profileForm.get(fieldName);\r\n    if (!control || !control.errors) return '';\r\n\r\n    const fieldDisplayName = this.getFieldDisplayName(fieldName);\r\n\r\n    if (control.hasError('required')) {\r\n      return `${fieldDisplayName} is required`;\r\n    }\r\n    if (control.hasError('email')) {\r\n      return 'Please enter a valid email address';\r\n    }\r\n    if (control.hasError('minlength')) {\r\n      const requiredLength = control.errors['minlength'].requiredLength;\r\n      return `${fieldDisplayName} must be at least ${requiredLength} characters`;\r\n    }\r\n    if (control.hasError('maxlength')) {\r\n      const maxLength = control.errors['maxlength'].requiredLength;\r\n      return `${fieldDisplayName} must be no more than ${maxLength} characters`;\r\n    }\r\n    if (control.hasError('pattern')) {\r\n      return 'Please enter a valid URL';\r\n    }\r\n    if (control.hasError('min')) {\r\n      const minValue = control.errors['min'].min;\r\n      return `${fieldDisplayName} must be at least ${minValue}`;\r\n    }\r\n    if (control.hasError('max')) {\r\n      const maxValue = control.errors['max'].max;\r\n      return `${fieldDisplayName} cannot exceed ${maxValue}`;\r\n    }\r\n    return '';\r\n  }\r\n\r\n  private getFieldDisplayName(fieldName: string): string {\r\n    const fieldNames: { [key: string]: string } = {\r\n      'firstName': 'First Name',\r\n      'lastName': 'Last Name',\r\n      'professionalTitle': 'Professional Title',\r\n      'headline': 'Headline',\r\n      'summary': 'Summary',\r\n      'contactInfo.email': 'Email',\r\n      'contactInfo.website': 'Website',\r\n      'contactInfo.portfolioUrl': 'Portfolio URL',\r\n      'location.city': 'City',\r\n      'location.state': 'State',\r\n      'location.country': 'Country',\r\n      'location.displayLocation': 'Display Location',\r\n      'consultationRates.hourlyRate': 'Hourly Rate',\r\n      'consultationRates.sessionRate': 'Session Rate',\r\n      'consultationRates.currency': 'Currency'\r\n    };\r\n    return fieldNames[fieldName] || fieldName;\r\n  }\r\n\r\n  // Enhanced form validation\r\n  validateForm(): boolean {\r\n    if (this.profileForm.invalid) {\r\n      this.markFormGroupTouched();\r\n\r\n      // Find first invalid field and focus on it\r\n      const firstInvalidField = this.findFirstInvalidField();\r\n      if (firstInvalidField) {\r\n        firstInvalidField.focus();\r\n      }\r\n\r\n      // Show specific error message\r\n      const errors = this.getFormErrors();\r\n      if (errors.length > 0) {\r\n        this.snackBar.open(`Please fix the following errors: ${errors.join(', ')}`, 'Close', { duration: 5000 });\r\n      }\r\n\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n\r\n  private findFirstInvalidField(): HTMLElement | null {\r\n    const invalidFields = document.querySelectorAll('.mat-form-field.ng-invalid input, .mat-form-field.ng-invalid textarea, .mat-form-field.ng-invalid mat-select');\r\n    return invalidFields.length > 0 ? invalidFields[0] as HTMLElement : null;\r\n  }\r\n\r\n  private getFormErrors(): string[] {\r\n    const errors: string[] = [];\r\n\r\n    // Check main form fields\r\n    Object.keys(this.profileForm.controls).forEach(key => {\r\n      const control = this.profileForm.get(key);\r\n      if (control && control.invalid && control.touched) {\r\n        const errorMessage = this.getErrorMessage(key);\r\n        if (errorMessage) {\r\n          errors.push(errorMessage);\r\n        }\r\n      }\r\n    });\r\n\r\n    // Check nested form groups\r\n    const contactInfo = this.profileForm.get('contactInfo') as FormGroup;\r\n    if (contactInfo && contactInfo.invalid) {\r\n      Object.keys(contactInfo.controls).forEach(key => {\r\n        const control = contactInfo.get(key);\r\n        if (control && control.invalid && control.touched) {\r\n          const errorMessage = this.getErrorMessage(`contactInfo.${key}`);\r\n          if (errorMessage) {\r\n            errors.push(errorMessage);\r\n          }\r\n        }\r\n      });\r\n    }\r\n\r\n    return errors.slice(0, 3); // Limit to first 3 errors to avoid overwhelming the user\r\n  }\r\n\r\n  // Platform options for social links\r\n  getPlatformOptions() {\r\n    return [\r\n      { value: 'linkedin', label: 'LinkedIn' },\r\n      { value: 'twitter', label: 'Twitter' },\r\n      { value: 'github', label: 'GitHub' },\r\n      { value: 'behance', label: 'Behance' },\r\n      { value: 'dribbble', label: 'Dribbble' },\r\n      { value: 'instagram', label: 'Instagram' },\r\n      { value: 'facebook', label: 'Facebook' },\r\n      { value: 'youtube', label: 'YouTube' },\r\n      { value: 'other', label: 'Other' }\r\n    ];\r\n  }\r\n\r\n  // Phone type options\r\n  getPhoneTypeOptions() {\r\n    return [\r\n      { value: 'mobile', label: 'Mobile' },\r\n      { value: 'business', label: 'Business' },\r\n      { value: 'home', label: 'Home' }\r\n    ];\r\n  }\r\n\r\n  // Skill category options\r\n  getSkillCategoryOptions() {\r\n    return [\r\n      { value: 'Astrology', label: 'Astrology' },\r\n      { value: 'Crystal Healing', label: 'Crystal Healing' },\r\n      { value: 'Palmistry', label: 'Palmistry' },\r\n      { value: 'Spiritual Counseling', label: 'Spiritual Counseling' },\r\n      { value: 'Numerology', label: 'Numerology' },\r\n      { value: 'Tarot Reading', label: 'Tarot Reading' },\r\n      { value: 'Energy Healing', label: 'Energy Healing' },\r\n      { value: 'Meditation', label: 'Meditation' },\r\n      { value: 'Other', label: 'Other' }\r\n    ];\r\n  }\r\n\r\n  // Proficiency level options\r\n  getProficiencyLevelOptions() {\r\n    return [\r\n      { value: 'beginner', label: 'Beginner' },\r\n      { value: 'intermediate', label: 'Intermediate' },\r\n      { value: 'advanced', label: 'Advanced' },\r\n      { value: 'expert', label: 'Expert' }\r\n    ];\r\n  }\r\n\r\n  // Service category options\r\n  getServiceCategoryOptions() {\r\n    return [\r\n      { value: 'Reading', label: 'Reading' },\r\n      { value: 'Consultation', label: 'Consultation' },\r\n      { value: 'Healing', label: 'Healing' },\r\n      { value: 'Workshop', label: 'Workshop' },\r\n      { value: 'Course', label: 'Course' },\r\n      { value: 'Other', label: 'Other' }\r\n    ];\r\n  }\r\n\r\n  // Currency options\r\n  getCurrencyOptions() {\r\n    return [\r\n      { value: 'BGN', label: 'BGN (Bulgarian Lev)' },\r\n      { value: 'EUR', label: 'EUR (Euro)' },\r\n      { value: 'USD', label: 'USD (US Dollar)' },\r\n      { value: 'GBP', label: 'GBP (British Pound)' }\r\n    ];\r\n  }\r\n}\r\n", "<div class=\"profile-edit-container\" *ngIf=\"!isLoading\">\r\n  <div class=\"edit-header\">\r\n    <h1>\r\n      <mat-icon>edit</mat-icon>\r\n      Edit Profile\r\n    </h1>\r\n    <div class=\"header-actions\">\r\n      <button mat-stroked-button (click)=\"onCancel()\" [disabled]=\"isSaving\">\r\n        Cancel\r\n      </button>\r\n      <button\r\n        mat-raised-button\r\n        color=\"primary\"\r\n        (click)=\"onSubmit()\"\r\n        [disabled]=\"isSaving || profileForm.invalid\">\r\n        <mat-icon *ngIf=\"isSaving\">\r\n          <mat-spinner diameter=\"20\"></mat-spinner>\r\n        </mat-icon>\r\n        <mat-icon *ngIf=\"!isSaving\">save</mat-icon>\r\n        {{ isSaving ? 'Saving...' : 'Save Changes' }}\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <form [formGroup]=\"profileForm\" class=\"profile-form\">\r\n    <!-- Photo Upload Section -->\r\n    <mat-card class=\"photo-section\">\r\n      <mat-card-header>\r\n        <mat-card-title>Profile Photos</mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"photo-uploads\">\r\n          <div class=\"profile-photo-upload\">\r\n            <div class=\"photo-preview\">\r\n              <img [src]=\"profilePhotoPreview || profile?.profilePhotoUrl || '/assets/images/default-avatar.png'\"\r\n                   alt=\"Profile Photo\" class=\"profile-photo\">\r\n              <div class=\"photo-overlay\" [class.uploading]=\"isUploadingProfilePhoto\">\r\n                <mat-spinner *ngIf=\"isUploadingProfilePhoto\" diameter=\"24\"></mat-spinner>\r\n                <mat-icon *ngIf=\"!isUploadingProfilePhoto\">camera_alt</mat-icon>\r\n              </div>\r\n            </div>\r\n            <input type=\"file\" #profilePhotoInput (change)=\"onProfilePhotoSelected($event)\"\r\n                   accept=\"image/jpeg,image/jpg,image/png,image/gif,image/webp\" style=\"display: none;\">\r\n            <button mat-stroked-button\r\n                    (click)=\"profilePhotoInput.click()\"\r\n                    [disabled]=\"isUploadingProfilePhoto\">\r\n              <mat-icon *ngIf=\"isUploadingProfilePhoto\">\r\n                <mat-spinner diameter=\"16\"></mat-spinner>\r\n              </mat-icon>\r\n              <mat-icon *ngIf=\"!isUploadingProfilePhoto\">photo_camera</mat-icon>\r\n              {{ isUploadingProfilePhoto ? 'Uploading...' : 'Change Profile Photo' }}\r\n            </button>\r\n            <p class=\"upload-hint\">Max 5MB • JPEG, PNG, GIF, WebP</p>\r\n          </div>\r\n\r\n          <div class=\"cover-photo-upload\">\r\n            <div class=\"cover-preview\">\r\n              <div class=\"cover-photo\"\r\n                   [style.background-image]=\"coverPhotoPreview ? ('url(' + coverPhotoPreview + ')') :\r\n                   (profile && profile.coverPhotoUrl) ? ('url(' + profile.coverPhotoUrl + ')') : 'var(--theme-gradient-primary)'\">\r\n                <div class=\"photo-overlay\" [class.uploading]=\"isUploadingCoverPhoto\">\r\n                  <mat-spinner *ngIf=\"isUploadingCoverPhoto\" diameter=\"24\"></mat-spinner>\r\n                  <mat-icon *ngIf=\"!isUploadingCoverPhoto\">camera_alt</mat-icon>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <input type=\"file\" #coverPhotoInput (change)=\"onCoverPhotoSelected($event)\"\r\n                   accept=\"image/jpeg,image/jpg,image/png,image/gif,image/webp\" style=\"display: none;\">\r\n            <button mat-stroked-button\r\n                    (click)=\"coverPhotoInput.click()\"\r\n                    [disabled]=\"isUploadingCoverPhoto\">\r\n              <mat-icon *ngIf=\"isUploadingCoverPhoto\">\r\n                <mat-spinner diameter=\"16\"></mat-spinner>\r\n              </mat-icon>\r\n              <mat-icon *ngIf=\"!isUploadingCoverPhoto\">photo_camera</mat-icon>\r\n              {{ isUploadingCoverPhoto ? 'Uploading...' : 'Change Cover Photo' }}\r\n            </button>\r\n            <p class=\"upload-hint\">Max 10MB • JPEG, PNG, GIF, WebP</p>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Basic Information -->\r\n    <mat-card class=\"form-section\">\r\n      <mat-card-header>\r\n        <mat-card-title>Basic Information</mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>First Name</mat-label>\r\n            <input matInput formControlName=\"firstName\" required>\r\n            <mat-error *ngIf=\"profileForm.get('firstName')?.invalid && profileForm.get('firstName')?.touched\">\r\n              {{ getErrorMessage('firstName') }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>Last Name</mat-label>\r\n            <input matInput formControlName=\"lastName\" required>\r\n            <mat-error *ngIf=\"profileForm.get('lastName')?.invalid && profileForm.get('lastName')?.touched\">\r\n              {{ getErrorMessage('lastName') }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n          <mat-label>Professional Title</mat-label>\r\n          <input matInput formControlName=\"professionalTitle\"\r\n                 placeholder=\"e.g., Senior Software Engineer, UX Designer\">\r\n        </mat-form-field>\r\n\r\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n          <mat-label>Professional Headline</mat-label>\r\n          <textarea matInput formControlName=\"headline\" rows=\"2\"\r\n                    placeholder=\"A brief, compelling description of what you do\"\r\n                    maxlength=\"220\"></textarea>\r\n          <mat-hint align=\"end\">{{ profileForm.get('headline')?.value?.length || 0 }}/220</mat-hint>\r\n        </mat-form-field>\r\n\r\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n          <mat-label>Professional Summary</mat-label>\r\n          <textarea matInput formControlName=\"summary\" rows=\"6\"\r\n                    placeholder=\"Describe your expertise, experience, and what makes you unique\"\r\n                    maxlength=\"2000\"></textarea>\r\n          <mat-hint align=\"end\">{{ profileForm.get('summary')?.value?.length || 0 }}/2000</mat-hint>\r\n        </mat-form-field>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Location Information -->\r\n    <mat-card class=\"form-section\" formGroupName=\"location\">\r\n      <mat-card-header>\r\n        <mat-card-title>Location</mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>City</mat-label>\r\n            <input matInput formControlName=\"city\">\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>State/Province</mat-label>\r\n            <input matInput formControlName=\"state\">\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>Country</mat-label>\r\n            <input matInput formControlName=\"country\">\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>Display Location</mat-label>\r\n            <input matInput formControlName=\"displayLocation\"\r\n                   placeholder=\"e.g., San Francisco, CA\">\r\n          </mat-form-field>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Contact Information -->\r\n    <mat-card class=\"form-section\" formGroupName=\"contactInfo\">\r\n      <mat-card-header>\r\n        <mat-card-title>Contact Information</mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>Email</mat-label>\r\n            <input matInput formControlName=\"email\" type=\"email\">\r\n            <mat-error *ngIf=\"profileForm.get('contactInfo.email')?.invalid && profileForm.get('contactInfo.email')?.touched\">\r\n              {{ getErrorMessage('contactInfo.email') }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <div class=\"checkbox-field\">\r\n            <mat-checkbox formControlName=\"isEmailPublic\">\r\n              Make email public\r\n            </mat-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>Website</mat-label>\r\n            <input matInput formControlName=\"website\" placeholder=\"https://yourwebsite.com\">\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>Portfolio URL</mat-label>\r\n            <input matInput formControlName=\"portfolioUrl\" placeholder=\"https://portfolio.com\">\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <!-- Phone Numbers -->\r\n        <div class=\"phone-numbers-section\">\r\n          <h4>Phone Numbers</h4>\r\n          <div formArrayName=\"phoneNumbers\">\r\n            <div *ngFor=\"let phone of phoneNumbers.controls; let i = index\"\r\n                 [formGroupName]=\"i\" class=\"phone-number-item\">\r\n              <div class=\"form-row\">\r\n                <mat-form-field appearance=\"outline\" class=\"phone-input\">\r\n                  <mat-label>Phone Number</mat-label>\r\n                  <input matInput formControlName=\"number\" placeholder=\"+****************\">\r\n                </mat-form-field>\r\n\r\n                <mat-form-field appearance=\"outline\" class=\"phone-type\">\r\n                  <mat-label>Type</mat-label>\r\n                  <mat-select formControlName=\"type\">\r\n                    <mat-option *ngFor=\"let type of getPhoneTypeOptions()\" [value]=\"type.value\">\r\n                      {{ type.label }}\r\n                    </mat-option>\r\n                  </mat-select>\r\n                </mat-form-field>\r\n\r\n                <div class=\"phone-controls\">\r\n                  <mat-checkbox formControlName=\"isPublic\">Public</mat-checkbox>\r\n                  <mat-checkbox formControlName=\"isPrimary\">Primary</mat-checkbox>\r\n                  <button mat-icon-button color=\"warn\" (click)=\"removePhoneNumber(i)\" type=\"button\">\r\n                    <mat-icon>delete</mat-icon>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <button mat-stroked-button (click)=\"addPhoneNumber()\" type=\"button\">\r\n            <mat-icon>add</mat-icon>\r\n            Add Phone Number\r\n          </button>\r\n        </div>\r\n\r\n        <!-- Business Address -->\r\n        <div class=\"business-address-section\" formGroupName=\"businessAddress\">\r\n          <h4>Business Address</h4>\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Street Address</mat-label>\r\n            <input matInput formControlName=\"street\">\r\n          </mat-form-field>\r\n\r\n          <div class=\"form-row\">\r\n            <mat-form-field appearance=\"outline\" class=\"third-width\">\r\n              <mat-label>City</mat-label>\r\n              <input matInput formControlName=\"city\">\r\n            </mat-form-field>\r\n\r\n            <mat-form-field appearance=\"outline\" class=\"third-width\">\r\n              <mat-label>State</mat-label>\r\n              <input matInput formControlName=\"state\">\r\n            </mat-form-field>\r\n\r\n            <mat-form-field appearance=\"outline\" class=\"third-width\">\r\n              <mat-label>Postal Code</mat-label>\r\n              <input matInput formControlName=\"postalCode\">\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n              <mat-label>Country</mat-label>\r\n              <input matInput formControlName=\"country\">\r\n            </mat-form-field>\r\n\r\n            <div class=\"checkbox-field\">\r\n              <mat-checkbox formControlName=\"isPublic\">\r\n                Make business address public\r\n              </mat-checkbox>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Social Links -->\r\n    <mat-card class=\"form-section\">\r\n      <mat-card-header>\r\n        <mat-card-title>Social Links</mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div formArrayName=\"socialLinks\">\r\n          <div *ngFor=\"let link of socialLinks.controls; let i = index\"\r\n               [formGroupName]=\"i\" class=\"social-link-item\">\r\n            <div class=\"form-row\">\r\n              <mat-form-field appearance=\"outline\" class=\"platform-select\">\r\n                <mat-label>Platform</mat-label>\r\n                <mat-select formControlName=\"platform\">\r\n                  <mat-option *ngFor=\"let platform of getPlatformOptions()\" [value]=\"platform.value\">\r\n                    {{ platform.label }}\r\n                  </mat-option>\r\n                </mat-select>\r\n              </mat-form-field>\r\n\r\n              <mat-form-field appearance=\"outline\" class=\"url-input\">\r\n                <mat-label>URL</mat-label>\r\n                <input matInput formControlName=\"url\" placeholder=\"https://linkedin.com/in/yourname\">\r\n              </mat-form-field>\r\n\r\n              <div class=\"link-controls\">\r\n                <mat-checkbox formControlName=\"isPublic\">Public</mat-checkbox>\r\n                <button mat-icon-button color=\"warn\" (click)=\"removeSocialLink(i)\" type=\"button\">\r\n                  <mat-icon>delete</mat-icon>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <button mat-stroked-button (click)=\"addSocialLink()\" type=\"button\">\r\n          <mat-icon>add</mat-icon>\r\n          Add Social Link\r\n        </button>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Skills Management -->\r\n    <mat-card class=\"form-section\">\r\n      <mat-card-header>\r\n        <mat-card-title>Skills & Expertise</mat-card-title>\r\n        <mat-card-subtitle>Add your skills and areas of expertise</mat-card-subtitle>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div formArrayName=\"skills\">\r\n          <div *ngFor=\"let skill of skills.controls; let i = index\" [formGroupName]=\"i\" class=\"skill-item\">\r\n            <div class=\"skill-fields\">\r\n              <mat-form-field appearance=\"outline\" class=\"skill-name\">\r\n                <mat-label>Skill Name</mat-label>\r\n                <input matInput formControlName=\"name\" placeholder=\"e.g., Tarot Reading\">\r\n                <mat-error *ngIf=\"skill.get('name')?.hasError('required')\">\r\n                  Skill name is required\r\n                </mat-error>\r\n              </mat-form-field>\r\n\r\n              <mat-form-field appearance=\"outline\" class=\"skill-category\">\r\n                <mat-label>Category</mat-label>\r\n                <mat-select formControlName=\"category\">\r\n                  <mat-option value=\"\">Select Category</mat-option>\r\n                  <mat-option *ngFor=\"let category of getSkillCategoryOptions()\" [value]=\"category.value\">\r\n                    {{ category.label }}\r\n                  </mat-option>\r\n                </mat-select>\r\n              </mat-form-field>\r\n\r\n              <mat-form-field appearance=\"outline\" class=\"skill-proficiency\">\r\n                <mat-label>Proficiency Level</mat-label>\r\n                <mat-select formControlName=\"proficiencyLevel\">\r\n                  <mat-option *ngFor=\"let level of getProficiencyLevelOptions()\" [value]=\"level.value\">\r\n                    {{ level.label }}\r\n                  </mat-option>\r\n                </mat-select>\r\n                <mat-error *ngIf=\"skill.get('proficiencyLevel')?.hasError('required')\">\r\n                  Proficiency level is required\r\n                </mat-error>\r\n              </mat-form-field>\r\n\r\n              <button mat-icon-button color=\"warn\" (click)=\"removeSkill(i)\" type=\"button\" class=\"remove-skill\">\r\n                <mat-icon>delete</mat-icon>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <button mat-stroked-button (click)=\"addSkill()\" type=\"button\" class=\"add-skill-btn\">\r\n          <mat-icon>add</mat-icon>\r\n          Add Skill\r\n        </button>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Consultation Rates -->\r\n    <mat-card class=\"form-section\">\r\n      <mat-card-header>\r\n        <mat-card-title>Consultation Rates</mat-card-title>\r\n        <mat-card-subtitle>Set your pricing for consultations</mat-card-subtitle>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div formGroupName=\"consultationRates\" class=\"rates-section\">\r\n          <div class=\"rate-fields\">\r\n            <mat-form-field appearance=\"outline\" class=\"rate-field\">\r\n              <mat-label>Hourly Rate</mat-label>\r\n              <input matInput type=\"number\" formControlName=\"hourlyRate\" placeholder=\"0.00\" min=\"0\" max=\"10000\">\r\n              <span matSuffix>{{ profileForm.get('consultationRates.currency')?.value || 'BGN' }}</span>\r\n              <mat-error *ngIf=\"profileForm.get('consultationRates.hourlyRate')?.hasError('min')\">\r\n                Rate must be positive\r\n              </mat-error>\r\n              <mat-error *ngIf=\"profileForm.get('consultationRates.hourlyRate')?.hasError('max')\">\r\n                Rate cannot exceed 10,000\r\n              </mat-error>\r\n            </mat-form-field>\r\n\r\n            <mat-form-field appearance=\"outline\" class=\"rate-field\">\r\n              <mat-label>Session Rate</mat-label>\r\n              <input matInput type=\"number\" formControlName=\"sessionRate\" placeholder=\"0.00\" min=\"0\" max=\"10000\">\r\n              <span matSuffix>{{ profileForm.get('consultationRates.currency')?.value || 'BGN' }}</span>\r\n              <mat-error *ngIf=\"profileForm.get('consultationRates.sessionRate')?.hasError('min')\">\r\n                Rate must be positive\r\n              </mat-error>\r\n              <mat-error *ngIf=\"profileForm.get('consultationRates.sessionRate')?.hasError('max')\">\r\n                Rate cannot exceed 10,000\r\n              </mat-error>\r\n            </mat-form-field>\r\n\r\n            <mat-form-field appearance=\"outline\" class=\"currency-field\">\r\n              <mat-label>Currency</mat-label>\r\n              <mat-select formControlName=\"currency\">\r\n                <mat-option *ngFor=\"let currency of getCurrencyOptions()\" [value]=\"currency.value\">\r\n                  {{ currency.label }}\r\n                </mat-option>\r\n              </mat-select>\r\n              <mat-error *ngIf=\"profileForm.get('consultationRates.currency')?.hasError('required')\">\r\n                Currency is required\r\n              </mat-error>\r\n            </mat-form-field>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Service Offerings -->\r\n    <mat-card class=\"form-section\">\r\n      <mat-card-header>\r\n        <mat-card-title>Service Offerings</mat-card-title>\r\n        <mat-card-subtitle>Define your specific services and packages</mat-card-subtitle>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div formArrayName=\"serviceOfferings\">\r\n          <div *ngFor=\"let service of serviceOfferings.controls; let i = index\" [formGroupName]=\"i\" class=\"service-item\">\r\n            <div class=\"service-header\">\r\n              <h4>Service {{ i + 1 }}</h4>\r\n              <mat-slide-toggle formControlName=\"isActive\" color=\"primary\">\r\n                Active\r\n              </mat-slide-toggle>\r\n            </div>\r\n\r\n            <div class=\"service-fields\">\r\n              <mat-form-field appearance=\"outline\" class=\"service-name\">\r\n                <mat-label>Service Name</mat-label>\r\n                <input matInput formControlName=\"name\" placeholder=\"e.g., Personal Tarot Reading\">\r\n                <mat-error *ngIf=\"service.get('name')?.hasError('required')\">\r\n                  Service name is required\r\n                </mat-error>\r\n              </mat-form-field>\r\n\r\n              <mat-form-field appearance=\"outline\" class=\"service-category\">\r\n                <mat-label>Category</mat-label>\r\n                <mat-select formControlName=\"category\">\r\n                  <mat-option value=\"\">Select Category</mat-option>\r\n                  <mat-option *ngFor=\"let category of getServiceCategoryOptions()\" [value]=\"category.value\">\r\n                    {{ category.label }}\r\n                  </mat-option>\r\n                </mat-select>\r\n              </mat-form-field>\r\n\r\n              <mat-form-field appearance=\"outline\" class=\"service-description\">\r\n                <mat-label>Description</mat-label>\r\n                <textarea matInput formControlName=\"description\" rows=\"3\"\r\n                          placeholder=\"Describe what this service includes...\"></textarea>\r\n                <mat-error *ngIf=\"service.get('description')?.hasError('required')\">\r\n                  Description is required\r\n                </mat-error>\r\n              </mat-form-field>\r\n\r\n              <div class=\"service-pricing\">\r\n                <mat-form-field appearance=\"outline\" class=\"price-field\">\r\n                  <mat-label>Price</mat-label>\r\n                  <input matInput type=\"number\" formControlName=\"price\" placeholder=\"0.00\" min=\"0\">\r\n                  <span matSuffix>{{ service.get('currency')?.value || 'BGN' }}</span>\r\n                  <mat-error *ngIf=\"service.get('price')?.hasError('required')\">\r\n                    Price is required\r\n                  </mat-error>\r\n                  <mat-error *ngIf=\"service.get('price')?.hasError('min')\">\r\n                    Price must be positive\r\n                  </mat-error>\r\n                </mat-form-field>\r\n\r\n                <mat-form-field appearance=\"outline\" class=\"currency-field\">\r\n                  <mat-label>Currency</mat-label>\r\n                  <mat-select formControlName=\"currency\">\r\n                    <mat-option *ngFor=\"let currency of getCurrencyOptions()\" [value]=\"currency.value\">\r\n                      {{ currency.label }}\r\n                    </mat-option>\r\n                  </mat-select>\r\n                </mat-form-field>\r\n\r\n                <mat-form-field appearance=\"outline\" class=\"duration-field\">\r\n                  <mat-label>Duration (minutes)</mat-label>\r\n                  <input matInput type=\"number\" formControlName=\"duration\" placeholder=\"60\" min=\"1\">\r\n                  <mat-hint>Optional - leave blank if not applicable</mat-hint>\r\n                </mat-form-field>\r\n              </div>\r\n\r\n              <button mat-icon-button color=\"warn\" (click)=\"removeServiceOffering(i)\" type=\"button\" class=\"remove-service\">\r\n                <mat-icon>delete</mat-icon>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <button mat-stroked-button (click)=\"addServiceOffering()\" type=\"button\" class=\"add-service-btn\">\r\n          <mat-icon>add</mat-icon>\r\n          Add Service\r\n        </button>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Privacy Settings -->\r\n    <mat-card class=\"form-section\">\r\n      <mat-card-header>\r\n        <mat-card-title>Privacy Settings</mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"privacy-controls\">\r\n          <mat-slide-toggle formControlName=\"isPublic\" color=\"primary\">\r\n            <span class=\"toggle-label\">Make profile public</span>\r\n            <p class=\"toggle-description\">\r\n              When enabled, your profile will be visible to everyone and searchable.\r\n              When disabled, only you can see your profile.\r\n            </p>\r\n          </mat-slide-toggle>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Form Actions -->\r\n    <div class=\"form-actions\">\r\n      <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"isSaving\">\r\n        <mat-icon *ngIf=\"isSaving\">\r\n          <mat-spinner diameter=\"16\"></mat-spinner>\r\n        </mat-icon>\r\n        <mat-icon *ngIf=\"!isSaving\">save</mat-icon>\r\n        {{ isSaving ? 'Saving...' : 'Save Changes' }}\r\n      </button>\r\n      <button mat-stroked-button type=\"button\" routerLink=\"/profile\">\r\n        Cancel\r\n      </button>\r\n    </div>\r\n  </form>\r\n</div>\r\n\r\n<!-- Loading State -->\r\n<div class=\"loading-container\" *ngIf=\"isLoading\">\r\n  <mat-spinner diameter=\"50\"></mat-spinner>\r\n  <p>Loading profile...</p>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}