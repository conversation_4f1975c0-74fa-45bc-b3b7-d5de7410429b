{"ast": null, "code": "/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ViewEncapsulation, Injectable, Inject, RendererFactory2, NgZone, ANIMATION_MODULE_TYPE, NgModule } from '@angular/core';\nexport { ANIMATION_MODULE_TYPE } from '@angular/core';\nimport { ɵDomRendererFactory2, BrowserModule } from '@angular/platform-browser';\nimport { AnimationBuilder, sequence, AnimationFactory } from '@angular/animations';\nimport * as i1 from '@angular/animations/browser';\nimport { ɵAnimationEngine, ɵWebAnimationsStyleNormalizer, ɵAnimationStyleNormalizer, AnimationDriver, ɵWebAnimationsDriver, ɵNoopAnimationDriver } from '@angular/animations/browser';\nimport { DOCUMENT } from '@angular/common';\nclass BrowserAnimationBuilder extends AnimationBuilder {\n  constructor(rootRenderer, doc) {\n    super();\n    this._nextAnimationId = 0;\n    const typeData = {\n      id: '0',\n      encapsulation: ViewEncapsulation.None,\n      styles: [],\n      data: {\n        animation: []\n      }\n    };\n    this._renderer = rootRenderer.createRenderer(doc.body, typeData);\n  }\n  build(animation) {\n    const id = this._nextAnimationId.toString();\n    this._nextAnimationId++;\n    const entry = Array.isArray(animation) ? sequence(animation) : animation;\n    issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n    return new BrowserAnimationFactory(id, this._renderer);\n  }\n}\nBrowserAnimationBuilder.ɵfac = function BrowserAnimationBuilder_Factory(t) {\n  return new (t || BrowserAnimationBuilder)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(DOCUMENT));\n};\nBrowserAnimationBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BrowserAnimationBuilder,\n  factory: BrowserAnimationBuilder.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserAnimationBuilder, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.RendererFactory2\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\nclass BrowserAnimationFactory extends AnimationFactory {\n  constructor(_id, _renderer) {\n    super();\n    this._id = _id;\n    this._renderer = _renderer;\n  }\n  create(element, options) {\n    return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n  }\n}\nclass RendererAnimationPlayer {\n  constructor(id, element, options, _renderer) {\n    this.id = id;\n    this.element = element;\n    this._renderer = _renderer;\n    this.parentPlayer = null;\n    this._started = false;\n    this.totalTime = 0;\n    this._command('create', options);\n  }\n  _listen(eventName, callback) {\n    return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n  }\n  _command(command, ...args) {\n    return issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n  }\n  onDone(fn) {\n    this._listen('done', fn);\n  }\n  onStart(fn) {\n    this._listen('start', fn);\n  }\n  onDestroy(fn) {\n    this._listen('destroy', fn);\n  }\n  init() {\n    this._command('init');\n  }\n  hasStarted() {\n    return this._started;\n  }\n  play() {\n    this._command('play');\n    this._started = true;\n  }\n  pause() {\n    this._command('pause');\n  }\n  restart() {\n    this._command('restart');\n  }\n  finish() {\n    this._command('finish');\n  }\n  destroy() {\n    this._command('destroy');\n  }\n  reset() {\n    this._command('reset');\n    this._started = false;\n  }\n  setPosition(p) {\n    this._command('setPosition', p);\n  }\n  getPosition() {\n    return this._renderer.engine.players[+this.id]?.getPosition() ?? 0;\n  }\n}\nfunction issueAnimationCommand(renderer, element, id, command, args) {\n  return renderer.setProperty(element, `@@${id}:${command}`, args);\n}\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\nclass AnimationRendererFactory {\n  constructor(delegate, engine, _zone) {\n    this.delegate = delegate;\n    this.engine = engine;\n    this._zone = _zone;\n    this._currentId = 0;\n    this._microtaskId = 1;\n    this._animationCallbacksBuffer = [];\n    this._rendererCache = new Map();\n    this._cdRecurDepth = 0;\n    this.promise = Promise.resolve(0);\n    engine.onRemovalComplete = (element, delegate) => {\n      // Note: if a component element has a leave animation, and a host leave animation,\n      // the view engine will call `removeChild` for the parent\n      // component renderer as well as for the child component renderer.\n      // Therefore, we need to check if we already removed the element.\n      const parentNode = delegate?.parentNode(element);\n      if (parentNode) {\n        delegate.removeChild(parentNode, element);\n      }\n    };\n  }\n  createRenderer(hostElement, type) {\n    const EMPTY_NAMESPACE_ID = '';\n    // cache the delegates to find out which cached delegate can\n    // be used by which cached renderer\n    const delegate = this.delegate.createRenderer(hostElement, type);\n    if (!hostElement || !type || !type.data || !type.data['animation']) {\n      let renderer = this._rendererCache.get(delegate);\n      if (!renderer) {\n        // Ensure that the renderer is removed from the cache on destroy\n        // since it may contain references to detached DOM nodes.\n        const onRendererDestroy = () => this._rendererCache.delete(delegate);\n        renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine, onRendererDestroy);\n        // only cache this result when the base renderer is used\n        this._rendererCache.set(delegate, renderer);\n      }\n      return renderer;\n    }\n    const componentId = type.id;\n    const namespaceId = type.id + '-' + this._currentId;\n    this._currentId++;\n    this.engine.register(namespaceId, hostElement);\n    const registerTrigger = trigger => {\n      if (Array.isArray(trigger)) {\n        trigger.forEach(registerTrigger);\n      } else {\n        this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n      }\n    };\n    const animationTriggers = type.data['animation'];\n    animationTriggers.forEach(registerTrigger);\n    return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n  }\n  begin() {\n    this._cdRecurDepth++;\n    if (this.delegate.begin) {\n      this.delegate.begin();\n    }\n  }\n  _scheduleCountTask() {\n    // always use promise to schedule microtask instead of use Zone\n    this.promise.then(() => {\n      this._microtaskId++;\n    });\n  }\n  /** @internal */\n  scheduleListenerCallback(count, fn, data) {\n    if (count >= 0 && count < this._microtaskId) {\n      this._zone.run(() => fn(data));\n      return;\n    }\n    if (this._animationCallbacksBuffer.length == 0) {\n      Promise.resolve(null).then(() => {\n        this._zone.run(() => {\n          this._animationCallbacksBuffer.forEach(tuple => {\n            const [fn, data] = tuple;\n            fn(data);\n          });\n          this._animationCallbacksBuffer = [];\n        });\n      });\n    }\n    this._animationCallbacksBuffer.push([fn, data]);\n  }\n  end() {\n    this._cdRecurDepth--;\n    // this is to prevent animations from running twice when an inner\n    // component does CD when a parent component instead has inserted it\n    if (this._cdRecurDepth == 0) {\n      this._zone.runOutsideAngular(() => {\n        this._scheduleCountTask();\n        this.engine.flush(this._microtaskId);\n      });\n    }\n    if (this.delegate.end) {\n      this.delegate.end();\n    }\n  }\n  whenRenderingDone() {\n    return this.engine.whenRenderingDone();\n  }\n}\nAnimationRendererFactory.ɵfac = function AnimationRendererFactory_Factory(t) {\n  return new (t || AnimationRendererFactory)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(i1.ɵAnimationEngine), i0.ɵɵinject(i0.NgZone));\n};\nAnimationRendererFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: AnimationRendererFactory,\n  factory: AnimationRendererFactory.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AnimationRendererFactory, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.RendererFactory2\n    }, {\n      type: i1.ɵAnimationEngine\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\nclass BaseAnimationRenderer {\n  constructor(namespaceId, delegate, engine, _onDestroy) {\n    this.namespaceId = namespaceId;\n    this.delegate = delegate;\n    this.engine = engine;\n    this._onDestroy = _onDestroy;\n    this.destroyNode = this.delegate.destroyNode ? n => delegate.destroyNode(n) : null;\n  }\n  get data() {\n    return this.delegate.data;\n  }\n  destroy() {\n    this.engine.destroy(this.namespaceId, this.delegate);\n    this.delegate.destroy();\n    this._onDestroy?.();\n  }\n  createElement(name, namespace) {\n    return this.delegate.createElement(name, namespace);\n  }\n  createComment(value) {\n    return this.delegate.createComment(value);\n  }\n  createText(value) {\n    return this.delegate.createText(value);\n  }\n  appendChild(parent, newChild) {\n    this.delegate.appendChild(parent, newChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, false);\n  }\n  insertBefore(parent, newChild, refChild, isMove = true) {\n    this.delegate.insertBefore(parent, newChild, refChild);\n    // If `isMove` true than we should animate this insert.\n    this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n  }\n  removeChild(parent, oldChild, isHostElement) {\n    this.engine.onRemove(this.namespaceId, oldChild, this.delegate, isHostElement);\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n  parentNode(node) {\n    return this.delegate.parentNode(node);\n  }\n  nextSibling(node) {\n    return this.delegate.nextSibling(node);\n  }\n  setAttribute(el, name, value, namespace) {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n  removeAttribute(el, name, namespace) {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n  addClass(el, name) {\n    this.delegate.addClass(el, name);\n  }\n  removeClass(el, name) {\n    this.delegate.removeClass(el, name);\n  }\n  setStyle(el, style, value, flags) {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n  removeStyle(el, style, flags) {\n    this.delegate.removeStyle(el, style, flags);\n  }\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n      this.disableAnimations(el, !!value);\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n  setValue(node, value) {\n    this.delegate.setValue(node, value);\n  }\n  listen(target, eventName, callback) {\n    return this.delegate.listen(target, eventName, callback);\n  }\n  disableAnimations(element, value) {\n    this.engine.disableAnimations(element, value);\n  }\n}\nclass AnimationRenderer extends BaseAnimationRenderer {\n  constructor(factory, namespaceId, delegate, engine, onDestroy) {\n    super(namespaceId, delegate, engine, onDestroy);\n    this.factory = factory;\n    this.namespaceId = namespaceId;\n  }\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX) {\n      if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n        value = value === undefined ? true : !!value;\n        this.disableAnimations(el, value);\n      } else {\n        this.engine.process(this.namespaceId, el, name.slice(1), value);\n      }\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n  listen(target, eventName, callback) {\n    if (eventName.charAt(0) == ANIMATION_PREFIX) {\n      const element = resolveElementFromTarget(target);\n      let name = eventName.slice(1);\n      let phase = '';\n      // @listener.phase is for trigger animation callbacks\n      // @@listener is for animation builder callbacks\n      if (name.charAt(0) != ANIMATION_PREFIX) {\n        [name, phase] = parseTriggerCallbackName(name);\n      }\n      return this.engine.listen(this.namespaceId, element, name, phase, event => {\n        const countId = event['_data'] || -1;\n        this.factory.scheduleListenerCallback(countId, callback, event);\n      });\n    }\n    return this.delegate.listen(target, eventName, callback);\n  }\n}\nfunction resolveElementFromTarget(target) {\n  switch (target) {\n    case 'body':\n      return document.body;\n    case 'document':\n      return document;\n    case 'window':\n      return window;\n    default:\n      return target;\n  }\n}\nfunction parseTriggerCallbackName(triggerName) {\n  const dotIndex = triggerName.indexOf('.');\n  const trigger = triggerName.substring(0, dotIndex);\n  const phase = triggerName.slice(dotIndex + 1);\n  return [trigger, phase];\n}\nclass InjectableAnimationEngine extends ɵAnimationEngine {\n  // The `ApplicationRef` is injected here explicitly to force the dependency ordering.\n  // Since the `ApplicationRef` should be created earlier before the `AnimationEngine`, they\n  // both have `ngOnDestroy` hooks and `flush()` must be called after all views are destroyed.\n  constructor(doc, driver, normalizer, appRef) {\n    super(doc.body, driver, normalizer);\n  }\n  ngOnDestroy() {\n    this.flush();\n  }\n}\nInjectableAnimationEngine.ɵfac = function InjectableAnimationEngine_Factory(t) {\n  return new (t || InjectableAnimationEngine)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.AnimationDriver), i0.ɵɵinject(i1.ɵAnimationStyleNormalizer), i0.ɵɵinject(i0.ApplicationRef));\n};\nInjectableAnimationEngine.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: InjectableAnimationEngine,\n  factory: InjectableAnimationEngine.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InjectableAnimationEngine, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1.AnimationDriver\n    }, {\n      type: i1.ɵAnimationStyleNormalizer\n    }, {\n      type: i0.ApplicationRef\n    }];\n  }, null);\n})();\nfunction instantiateDefaultStyleNormalizer() {\n  return new ɵWebAnimationsStyleNormalizer();\n}\nfunction instantiateRendererFactory(renderer, engine, zone) {\n  return new AnimationRendererFactory(renderer, engine, zone);\n}\nconst SHARED_ANIMATION_PROVIDERS = [{\n  provide: AnimationBuilder,\n  useClass: BrowserAnimationBuilder\n}, {\n  provide: ɵAnimationStyleNormalizer,\n  useFactory: instantiateDefaultStyleNormalizer\n}, {\n  provide: ɵAnimationEngine,\n  useClass: InjectableAnimationEngine\n}, {\n  provide: RendererFactory2,\n  useFactory: instantiateRendererFactory,\n  deps: [ɵDomRendererFactory2, ɵAnimationEngine, NgZone]\n}];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\nconst BROWSER_ANIMATIONS_PROVIDERS = [{\n  provide: AnimationDriver,\n  useFactory: () => new ɵWebAnimationsDriver()\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useValue: 'BrowserAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\nconst BROWSER_NOOP_ANIMATIONS_PROVIDERS = [{\n  provide: AnimationDriver,\n  useClass: ɵNoopAnimationDriver\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useValue: 'NoopAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n\n/**\n * Exports `BrowserModule` with additional [dependency-injection providers](guide/glossary#provider)\n * for use with animations. See [Animations](guide/animations).\n * @publicApi\n */\nclass BrowserAnimationsModule {\n  /**\n   * Configures the module based on the specified object.\n   *\n   * @param config Object used to configure the behavior of the `BrowserAnimationsModule`.\n   * @see `BrowserAnimationsModuleConfig`\n   *\n   * @usageNotes\n   * When registering the `BrowserAnimationsModule`, you can use the `withConfig`\n   * function as follows:\n   * ```\n   * @NgModule({\n   *   imports: [BrowserAnimationsModule.withConfig(config)]\n   * })\n   * class MyNgModule {}\n   * ```\n   */\n  static withConfig(config) {\n    return {\n      ngModule: BrowserAnimationsModule,\n      providers: config.disableAnimations ? BROWSER_NOOP_ANIMATIONS_PROVIDERS : BROWSER_ANIMATIONS_PROVIDERS\n    };\n  }\n}\nBrowserAnimationsModule.ɵfac = function BrowserAnimationsModule_Factory(t) {\n  return new (t || BrowserAnimationsModule)();\n};\nBrowserAnimationsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BrowserAnimationsModule,\n  exports: [BrowserModule]\n});\nBrowserAnimationsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: BROWSER_ANIMATIONS_PROVIDERS,\n  imports: [BrowserModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserAnimationsModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: BROWSER_ANIMATIONS_PROVIDERS\n    }]\n  }], null, null);\n})();\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to enable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to enable animations in an application\n * bootstrapped using the `bootstrapApplication` function. In this scenario there\n * is no need to import the `BrowserAnimationsModule` NgModule at all, just add\n * providers returned by this function to the `providers` list as show below.\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nfunction provideAnimations() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideAnimations` call results in app code.\n  return [...BROWSER_ANIMATIONS_PROVIDERS];\n}\n/**\n * A null player that must be imported to allow disabling of animations.\n * @publicApi\n */\nclass NoopAnimationsModule {}\nNoopAnimationsModule.ɵfac = function NoopAnimationsModule_Factory(t) {\n  return new (t || NoopAnimationsModule)();\n};\nNoopAnimationsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NoopAnimationsModule,\n  exports: [BrowserModule]\n});\nNoopAnimationsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n  imports: [BrowserModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoopAnimationsModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS\n    }]\n  }], null, null);\n})();\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to disable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to bootstrap an application using\n * the `bootstrapApplication` function, but you need to disable animations\n * (for example, when running tests).\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideNoopAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nfunction provideNoopAnimations() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideNoopAnimations` call results in app code.\n  return [...BROWSER_NOOP_ANIMATIONS_PROVIDERS];\n}\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation browser package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserAnimationsModule, NoopAnimationsModule, provideAnimations, provideNoopAnimations, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, BrowserAnimationBuilder as ɵBrowserAnimationBuilder, BrowserAnimationFactory as ɵBrowserAnimationFactory, InjectableAnimationEngine as ɵInjectableAnimationEngine };", "map": {"version": 3, "names": ["i0", "ViewEncapsulation", "Injectable", "Inject", "RendererFactory2", "NgZone", "ANIMATION_MODULE_TYPE", "NgModule", "ɵDomRendererFactory2", "BrowserModule", "AnimationBuilder", "sequence", "AnimationFactory", "i1", "ɵAnimationEngine", "ɵWebAnimationsStyleNormalizer", "ɵAnimationStyleNormalizer", "AnimationDriver", "ɵWebAnimationsDriver", "ɵNoopAnimationDriver", "DOCUMENT", "BrowserAnimationBuilder", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "doc", "_nextAnimationId", "typeData", "id", "encapsulation", "None", "styles", "data", "animation", "_renderer", "<PERSON><PERSON><PERSON><PERSON>", "body", "build", "toString", "entry", "Array", "isArray", "issueAnimationCommand", "BrowserAnimationFactory", "ɵfac", "ɵprov", "type", "undefined", "decorators", "args", "_id", "create", "element", "options", "RendererAnimationPlayer", "parentPlayer", "_started", "totalTime", "_command", "_listen", "eventName", "callback", "listen", "command", "onDone", "fn", "onStart", "onDestroy", "init", "hasStarted", "play", "pause", "restart", "finish", "destroy", "reset", "setPosition", "p", "getPosition", "engine", "players", "renderer", "setProperty", "ANIMATION_PREFIX", "DISABLE_ANIMATIONS_FLAG", "AnimationRendererFactory", "delegate", "_zone", "_currentId", "_microtaskId", "_animationCallbacksBuffer", "_rendererCache", "Map", "_cdRecurDepth", "promise", "Promise", "resolve", "onRemovalComplete", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "hostElement", "EMPTY_NAMESPACE_ID", "get", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delete", "BaseAnimationRenderer", "set", "componentId", "namespaceId", "register", "registerTrigger", "trigger", "for<PERSON>ach", "name", "animationTriggers", "<PERSON><PERSON><PERSON><PERSON>", "begin", "_scheduleCountTask", "then", "scheduleListenerCallback", "count", "run", "length", "tuple", "push", "end", "runOutsideAngular", "flush", "whenRenderingDone", "_onD<PERSON>roy", "destroyNode", "n", "createElement", "namespace", "createComment", "value", "createText", "append<PERSON><PERSON><PERSON>", "parent", "<PERSON><PERSON><PERSON><PERSON>", "onInsert", "insertBefore", "refChild", "isMove", "<PERSON><PERSON><PERSON><PERSON>", "isHostElement", "onRemove", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "node", "nextS<PERSON>ling", "setAttribute", "el", "removeAttribute", "addClass", "removeClass", "setStyle", "style", "flags", "removeStyle", "char<PERSON>t", "disableAnimations", "setValue", "target", "factory", "process", "slice", "resolveElementFromTarget", "phase", "parseTriggerCallbackName", "event", "countId", "document", "window", "triggerName", "dotIndex", "indexOf", "substring", "InjectableAnimationEngine", "driver", "normalizer", "appRef", "ngOnDestroy", "ApplicationRef", "instantiateDefaultStyleNormalizer", "instantiateRendererFactory", "zone", "SHARED_ANIMATION_PROVIDERS", "provide", "useClass", "useFactory", "deps", "BROWSER_ANIMATIONS_PROVIDERS", "useValue", "BROWSER_NOOP_ANIMATIONS_PROVIDERS", "BrowserAnimationsModule", "withConfig", "config", "ngModule", "providers", "ɵmod", "ɵinj", "exports", "provideAnimations", "NoopAnimationsModule", "provideNoopAnimations", "ɵAnimationRenderer", "ɵAnimationRendererFactory", "ɵBrowserAnimationBuilder", "ɵBrowserAnimationFactory", "ɵInjectableAnimationEngine"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/platform-browser/fesm2020/animations.mjs"], "sourcesContent": ["/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ViewEncapsulation, Injectable, Inject, RendererFactory2, NgZone, ANIMATION_MODULE_TYPE, NgModule } from '@angular/core';\nexport { ANIMATION_MODULE_TYPE } from '@angular/core';\nimport { ɵDomRendererFactory2, BrowserModule } from '@angular/platform-browser';\nimport { AnimationBuilder, sequence, AnimationFactory } from '@angular/animations';\nimport * as i1 from '@angular/animations/browser';\nimport { ɵAnimationEngine, ɵWebAnimationsStyleNormalizer, ɵAnimationStyleNormalizer, AnimationDriver, ɵWebAnimationsDriver, ɵNoopAnimationDriver } from '@angular/animations/browser';\nimport { DOCUMENT } from '@angular/common';\n\nclass BrowserAnimationBuilder extends AnimationBuilder {\n    constructor(rootRenderer, doc) {\n        super();\n        this._nextAnimationId = 0;\n        const typeData = { id: '0', encapsulation: ViewEncapsulation.None, styles: [], data: { animation: [] } };\n        this._renderer = rootRenderer.createRenderer(doc.body, typeData);\n    }\n    build(animation) {\n        const id = this._nextAnimationId.toString();\n        this._nextAnimationId++;\n        const entry = Array.isArray(animation) ? sequence(animation) : animation;\n        issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n        return new BrowserAnimationFactory(id, this._renderer);\n    }\n}\nBrowserAnimationBuilder.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserAnimationBuilder, deps: [{ token: i0.RendererFactory2 }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nBrowserAnimationBuilder.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserAnimationBuilder });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserAnimationBuilder, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i0.RendererFactory2 }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\nclass BrowserAnimationFactory extends AnimationFactory {\n    constructor(_id, _renderer) {\n        super();\n        this._id = _id;\n        this._renderer = _renderer;\n    }\n    create(element, options) {\n        return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n    }\n}\nclass RendererAnimationPlayer {\n    constructor(id, element, options, _renderer) {\n        this.id = id;\n        this.element = element;\n        this._renderer = _renderer;\n        this.parentPlayer = null;\n        this._started = false;\n        this.totalTime = 0;\n        this._command('create', options);\n    }\n    _listen(eventName, callback) {\n        return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n    }\n    _command(command, ...args) {\n        return issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n    }\n    onDone(fn) {\n        this._listen('done', fn);\n    }\n    onStart(fn) {\n        this._listen('start', fn);\n    }\n    onDestroy(fn) {\n        this._listen('destroy', fn);\n    }\n    init() {\n        this._command('init');\n    }\n    hasStarted() {\n        return this._started;\n    }\n    play() {\n        this._command('play');\n        this._started = true;\n    }\n    pause() {\n        this._command('pause');\n    }\n    restart() {\n        this._command('restart');\n    }\n    finish() {\n        this._command('finish');\n    }\n    destroy() {\n        this._command('destroy');\n    }\n    reset() {\n        this._command('reset');\n        this._started = false;\n    }\n    setPosition(p) {\n        this._command('setPosition', p);\n    }\n    getPosition() {\n        return this._renderer.engine.players[+this.id]?.getPosition() ?? 0;\n    }\n}\nfunction issueAnimationCommand(renderer, element, id, command, args) {\n    return renderer.setProperty(element, `@@${id}:${command}`, args);\n}\n\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\nclass AnimationRendererFactory {\n    constructor(delegate, engine, _zone) {\n        this.delegate = delegate;\n        this.engine = engine;\n        this._zone = _zone;\n        this._currentId = 0;\n        this._microtaskId = 1;\n        this._animationCallbacksBuffer = [];\n        this._rendererCache = new Map();\n        this._cdRecurDepth = 0;\n        this.promise = Promise.resolve(0);\n        engine.onRemovalComplete = (element, delegate) => {\n            // Note: if a component element has a leave animation, and a host leave animation,\n            // the view engine will call `removeChild` for the parent\n            // component renderer as well as for the child component renderer.\n            // Therefore, we need to check if we already removed the element.\n            const parentNode = delegate?.parentNode(element);\n            if (parentNode) {\n                delegate.removeChild(parentNode, element);\n            }\n        };\n    }\n    createRenderer(hostElement, type) {\n        const EMPTY_NAMESPACE_ID = '';\n        // cache the delegates to find out which cached delegate can\n        // be used by which cached renderer\n        const delegate = this.delegate.createRenderer(hostElement, type);\n        if (!hostElement || !type || !type.data || !type.data['animation']) {\n            let renderer = this._rendererCache.get(delegate);\n            if (!renderer) {\n                // Ensure that the renderer is removed from the cache on destroy\n                // since it may contain references to detached DOM nodes.\n                const onRendererDestroy = () => this._rendererCache.delete(delegate);\n                renderer =\n                    new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine, onRendererDestroy);\n                // only cache this result when the base renderer is used\n                this._rendererCache.set(delegate, renderer);\n            }\n            return renderer;\n        }\n        const componentId = type.id;\n        const namespaceId = type.id + '-' + this._currentId;\n        this._currentId++;\n        this.engine.register(namespaceId, hostElement);\n        const registerTrigger = (trigger) => {\n            if (Array.isArray(trigger)) {\n                trigger.forEach(registerTrigger);\n            }\n            else {\n                this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n            }\n        };\n        const animationTriggers = type.data['animation'];\n        animationTriggers.forEach(registerTrigger);\n        return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n    }\n    begin() {\n        this._cdRecurDepth++;\n        if (this.delegate.begin) {\n            this.delegate.begin();\n        }\n    }\n    _scheduleCountTask() {\n        // always use promise to schedule microtask instead of use Zone\n        this.promise.then(() => {\n            this._microtaskId++;\n        });\n    }\n    /** @internal */\n    scheduleListenerCallback(count, fn, data) {\n        if (count >= 0 && count < this._microtaskId) {\n            this._zone.run(() => fn(data));\n            return;\n        }\n        if (this._animationCallbacksBuffer.length == 0) {\n            Promise.resolve(null).then(() => {\n                this._zone.run(() => {\n                    this._animationCallbacksBuffer.forEach(tuple => {\n                        const [fn, data] = tuple;\n                        fn(data);\n                    });\n                    this._animationCallbacksBuffer = [];\n                });\n            });\n        }\n        this._animationCallbacksBuffer.push([fn, data]);\n    }\n    end() {\n        this._cdRecurDepth--;\n        // this is to prevent animations from running twice when an inner\n        // component does CD when a parent component instead has inserted it\n        if (this._cdRecurDepth == 0) {\n            this._zone.runOutsideAngular(() => {\n                this._scheduleCountTask();\n                this.engine.flush(this._microtaskId);\n            });\n        }\n        if (this.delegate.end) {\n            this.delegate.end();\n        }\n    }\n    whenRenderingDone() {\n        return this.engine.whenRenderingDone();\n    }\n}\nAnimationRendererFactory.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: AnimationRendererFactory, deps: [{ token: i0.RendererFactory2 }, { token: i1.ɵAnimationEngine }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\nAnimationRendererFactory.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: AnimationRendererFactory });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: AnimationRendererFactory, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i0.RendererFactory2 }, { type: i1.ɵAnimationEngine }, { type: i0.NgZone }]; } });\nclass BaseAnimationRenderer {\n    constructor(namespaceId, delegate, engine, _onDestroy) {\n        this.namespaceId = namespaceId;\n        this.delegate = delegate;\n        this.engine = engine;\n        this._onDestroy = _onDestroy;\n        this.destroyNode = this.delegate.destroyNode ? (n) => delegate.destroyNode(n) : null;\n    }\n    get data() {\n        return this.delegate.data;\n    }\n    destroy() {\n        this.engine.destroy(this.namespaceId, this.delegate);\n        this.delegate.destroy();\n        this._onDestroy?.();\n    }\n    createElement(name, namespace) {\n        return this.delegate.createElement(name, namespace);\n    }\n    createComment(value) {\n        return this.delegate.createComment(value);\n    }\n    createText(value) {\n        return this.delegate.createText(value);\n    }\n    appendChild(parent, newChild) {\n        this.delegate.appendChild(parent, newChild);\n        this.engine.onInsert(this.namespaceId, newChild, parent, false);\n    }\n    insertBefore(parent, newChild, refChild, isMove = true) {\n        this.delegate.insertBefore(parent, newChild, refChild);\n        // If `isMove` true than we should animate this insert.\n        this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n    }\n    removeChild(parent, oldChild, isHostElement) {\n        this.engine.onRemove(this.namespaceId, oldChild, this.delegate, isHostElement);\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n    }\n    parentNode(node) {\n        return this.delegate.parentNode(node);\n    }\n    nextSibling(node) {\n        return this.delegate.nextSibling(node);\n    }\n    setAttribute(el, name, value, namespace) {\n        this.delegate.setAttribute(el, name, value, namespace);\n    }\n    removeAttribute(el, name, namespace) {\n        this.delegate.removeAttribute(el, name, namespace);\n    }\n    addClass(el, name) {\n        this.delegate.addClass(el, name);\n    }\n    removeClass(el, name) {\n        this.delegate.removeClass(el, name);\n    }\n    setStyle(el, style, value, flags) {\n        this.delegate.setStyle(el, style, value, flags);\n    }\n    removeStyle(el, style, flags) {\n        this.delegate.removeStyle(el, style, flags);\n    }\n    setProperty(el, name, value) {\n        if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n            this.disableAnimations(el, !!value);\n        }\n        else {\n            this.delegate.setProperty(el, name, value);\n        }\n    }\n    setValue(node, value) {\n        this.delegate.setValue(node, value);\n    }\n    listen(target, eventName, callback) {\n        return this.delegate.listen(target, eventName, callback);\n    }\n    disableAnimations(element, value) {\n        this.engine.disableAnimations(element, value);\n    }\n}\nclass AnimationRenderer extends BaseAnimationRenderer {\n    constructor(factory, namespaceId, delegate, engine, onDestroy) {\n        super(namespaceId, delegate, engine, onDestroy);\n        this.factory = factory;\n        this.namespaceId = namespaceId;\n    }\n    setProperty(el, name, value) {\n        if (name.charAt(0) == ANIMATION_PREFIX) {\n            if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n                value = value === undefined ? true : !!value;\n                this.disableAnimations(el, value);\n            }\n            else {\n                this.engine.process(this.namespaceId, el, name.slice(1), value);\n            }\n        }\n        else {\n            this.delegate.setProperty(el, name, value);\n        }\n    }\n    listen(target, eventName, callback) {\n        if (eventName.charAt(0) == ANIMATION_PREFIX) {\n            const element = resolveElementFromTarget(target);\n            let name = eventName.slice(1);\n            let phase = '';\n            // @listener.phase is for trigger animation callbacks\n            // @@listener is for animation builder callbacks\n            if (name.charAt(0) != ANIMATION_PREFIX) {\n                [name, phase] = parseTriggerCallbackName(name);\n            }\n            return this.engine.listen(this.namespaceId, element, name, phase, event => {\n                const countId = event['_data'] || -1;\n                this.factory.scheduleListenerCallback(countId, callback, event);\n            });\n        }\n        return this.delegate.listen(target, eventName, callback);\n    }\n}\nfunction resolveElementFromTarget(target) {\n    switch (target) {\n        case 'body':\n            return document.body;\n        case 'document':\n            return document;\n        case 'window':\n            return window;\n        default:\n            return target;\n    }\n}\nfunction parseTriggerCallbackName(triggerName) {\n    const dotIndex = triggerName.indexOf('.');\n    const trigger = triggerName.substring(0, dotIndex);\n    const phase = triggerName.slice(dotIndex + 1);\n    return [trigger, phase];\n}\n\nclass InjectableAnimationEngine extends ɵAnimationEngine {\n    // The `ApplicationRef` is injected here explicitly to force the dependency ordering.\n    // Since the `ApplicationRef` should be created earlier before the `AnimationEngine`, they\n    // both have `ngOnDestroy` hooks and `flush()` must be called after all views are destroyed.\n    constructor(doc, driver, normalizer, appRef) {\n        super(doc.body, driver, normalizer);\n    }\n    ngOnDestroy() {\n        this.flush();\n    }\n}\nInjectableAnimationEngine.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: InjectableAnimationEngine, deps: [{ token: DOCUMENT }, { token: i1.AnimationDriver }, { token: i1.ɵAnimationStyleNormalizer }, { token: i0.ApplicationRef }], target: i0.ɵɵFactoryTarget.Injectable });\nInjectableAnimationEngine.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: InjectableAnimationEngine });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: InjectableAnimationEngine, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1.AnimationDriver }, { type: i1.ɵAnimationStyleNormalizer }, { type: i0.ApplicationRef }]; } });\nfunction instantiateDefaultStyleNormalizer() {\n    return new ɵWebAnimationsStyleNormalizer();\n}\nfunction instantiateRendererFactory(renderer, engine, zone) {\n    return new AnimationRendererFactory(renderer, engine, zone);\n}\nconst SHARED_ANIMATION_PROVIDERS = [\n    { provide: AnimationBuilder, useClass: BrowserAnimationBuilder },\n    { provide: ɵAnimationStyleNormalizer, useFactory: instantiateDefaultStyleNormalizer },\n    { provide: ɵAnimationEngine, useClass: InjectableAnimationEngine }, {\n        provide: RendererFactory2,\n        useFactory: instantiateRendererFactory,\n        deps: [ɵDomRendererFactory2, ɵAnimationEngine, NgZone]\n    }\n];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\nconst BROWSER_ANIMATIONS_PROVIDERS = [\n    { provide: AnimationDriver, useFactory: () => new ɵWebAnimationsDriver() },\n    { provide: ANIMATION_MODULE_TYPE, useValue: 'BrowserAnimations' }, ...SHARED_ANIMATION_PROVIDERS\n];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\nconst BROWSER_NOOP_ANIMATIONS_PROVIDERS = [\n    { provide: AnimationDriver, useClass: ɵNoopAnimationDriver },\n    { provide: ANIMATION_MODULE_TYPE, useValue: 'NoopAnimations' }, ...SHARED_ANIMATION_PROVIDERS\n];\n\n/**\n * Exports `BrowserModule` with additional [dependency-injection providers](guide/glossary#provider)\n * for use with animations. See [Animations](guide/animations).\n * @publicApi\n */\nclass BrowserAnimationsModule {\n    /**\n     * Configures the module based on the specified object.\n     *\n     * @param config Object used to configure the behavior of the `BrowserAnimationsModule`.\n     * @see `BrowserAnimationsModuleConfig`\n     *\n     * @usageNotes\n     * When registering the `BrowserAnimationsModule`, you can use the `withConfig`\n     * function as follows:\n     * ```\n     * @NgModule({\n     *   imports: [BrowserAnimationsModule.withConfig(config)]\n     * })\n     * class MyNgModule {}\n     * ```\n     */\n    static withConfig(config) {\n        return {\n            ngModule: BrowserAnimationsModule,\n            providers: config.disableAnimations ? BROWSER_NOOP_ANIMATIONS_PROVIDERS :\n                BROWSER_ANIMATIONS_PROVIDERS\n        };\n    }\n}\nBrowserAnimationsModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserAnimationsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nBrowserAnimationsModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserAnimationsModule, exports: [BrowserModule] });\nBrowserAnimationsModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserAnimationsModule, providers: BROWSER_ANIMATIONS_PROVIDERS, imports: [BrowserModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserAnimationsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserModule],\n                    providers: BROWSER_ANIMATIONS_PROVIDERS,\n                }]\n        }] });\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to enable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to enable animations in an application\n * bootstrapped using the `bootstrapApplication` function. In this scenario there\n * is no need to import the `BrowserAnimationsModule` NgModule at all, just add\n * providers returned by this function to the `providers` list as show below.\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nfunction provideAnimations() {\n    // Return a copy to prevent changes to the original array in case any in-place\n    // alterations are performed to the `provideAnimations` call results in app code.\n    return [...BROWSER_ANIMATIONS_PROVIDERS];\n}\n/**\n * A null player that must be imported to allow disabling of animations.\n * @publicApi\n */\nclass NoopAnimationsModule {\n}\nNoopAnimationsModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: NoopAnimationsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nNoopAnimationsModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: NoopAnimationsModule, exports: [BrowserModule] });\nNoopAnimationsModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: NoopAnimationsModule, providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS, imports: [BrowserModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: NoopAnimationsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserModule],\n                    providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n                }]\n        }] });\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to disable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to bootstrap an application using\n * the `bootstrapApplication` function, but you need to disable animations\n * (for example, when running tests).\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideNoopAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nfunction provideNoopAnimations() {\n    // Return a copy to prevent changes to the original array in case any in-place\n    // alterations are performed to the `provideNoopAnimations` call results in app code.\n    return [...BROWSER_NOOP_ANIMATIONS_PROVIDERS];\n}\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation browser package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserAnimationsModule, NoopAnimationsModule, provideAnimations, provideNoopAnimations, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, BrowserAnimationBuilder as ɵBrowserAnimationBuilder, BrowserAnimationFactory as ɵBrowserAnimationFactory, InjectableAnimationEngine as ɵInjectableAnimationEngine };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,iBAAiB,EAAEC,UAAU,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,qBAAqB,EAAEC,QAAQ,QAAQ,eAAe;AAChI,SAASD,qBAAqB,QAAQ,eAAe;AACrD,SAASE,oBAAoB,EAAEC,aAAa,QAAQ,2BAA2B;AAC/E,SAASC,gBAAgB,EAAEC,QAAQ,EAAEC,gBAAgB,QAAQ,qBAAqB;AAClF,OAAO,KAAKC,EAAE,MAAM,6BAA6B;AACjD,SAASC,gBAAgB,EAAEC,6BAA6B,EAAEC,yBAAyB,EAAEC,eAAe,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,6BAA6B;AACrL,SAASC,QAAQ,QAAQ,iBAAiB;AAE1C,MAAMC,uBAAuB,SAASX,gBAAgB,CAAC;EACnDY,WAAW,CAACC,YAAY,EAAEC,GAAG,EAAE;IAC3B,KAAK,EAAE;IACP,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,MAAMC,QAAQ,GAAG;MAAEC,EAAE,EAAE,GAAG;MAAEC,aAAa,EAAE3B,iBAAiB,CAAC4B,IAAI;MAAEC,MAAM,EAAE,EAAE;MAAEC,IAAI,EAAE;QAAEC,SAAS,EAAE;MAAG;IAAE,CAAC;IACxG,IAAI,CAACC,SAAS,GAAGV,YAAY,CAACW,cAAc,CAACV,GAAG,CAACW,IAAI,EAAET,QAAQ,CAAC;EACpE;EACAU,KAAK,CAACJ,SAAS,EAAE;IACb,MAAML,EAAE,GAAG,IAAI,CAACF,gBAAgB,CAACY,QAAQ,EAAE;IAC3C,IAAI,CAACZ,gBAAgB,EAAE;IACvB,MAAMa,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACR,SAAS,CAAC,GAAGrB,QAAQ,CAACqB,SAAS,CAAC,GAAGA,SAAS;IACxES,qBAAqB,CAAC,IAAI,CAACR,SAAS,EAAE,IAAI,EAAEN,EAAE,EAAE,UAAU,EAAE,CAACW,KAAK,CAAC,CAAC;IACpE,OAAO,IAAII,uBAAuB,CAACf,EAAE,EAAE,IAAI,CAACM,SAAS,CAAC;EAC1D;AACJ;AACAZ,uBAAuB,CAACsB,IAAI;EAAA,iBAAyFtB,uBAAuB,EAAjCrB,EAAE,UAAiDA,EAAE,CAACI,gBAAgB,GAAtEJ,EAAE,UAAiFoB,QAAQ;AAAA,CAA6C;AACnPC,uBAAuB,CAACuB,KAAK,kBAD8E5C,EAAE;EAAA,OACYqB,uBAAuB;EAAA,SAAvBA,uBAAuB;AAAA,EAAG;AACnJ;EAAA,mDAF2GrB,EAAE,mBAEjBqB,uBAAuB,EAAc,CAAC;IACtHwB,IAAI,EAAE3C;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE2C,IAAI,EAAE7C,EAAE,CAACI;IAAiB,CAAC,EAAE;MAAEyC,IAAI,EAAEC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC7FF,IAAI,EAAE1C,MAAM;QACZ6C,IAAI,EAAE,CAAC5B,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB,MAAMsB,uBAAuB,SAAS9B,gBAAgB,CAAC;EACnDU,WAAW,CAAC2B,GAAG,EAAEhB,SAAS,EAAE;IACxB,KAAK,EAAE;IACP,IAAI,CAACgB,GAAG,GAAGA,GAAG;IACd,IAAI,CAAChB,SAAS,GAAGA,SAAS;EAC9B;EACAiB,MAAM,CAACC,OAAO,EAAEC,OAAO,EAAE;IACrB,OAAO,IAAIC,uBAAuB,CAAC,IAAI,CAACJ,GAAG,EAAEE,OAAO,EAAEC,OAAO,IAAI,CAAC,CAAC,EAAE,IAAI,CAACnB,SAAS,CAAC;EACxF;AACJ;AACA,MAAMoB,uBAAuB,CAAC;EAC1B/B,WAAW,CAACK,EAAE,EAAEwB,OAAO,EAAEC,OAAO,EAAEnB,SAAS,EAAE;IACzC,IAAI,CAACN,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACwB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAClB,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACqB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,QAAQ,CAAC,QAAQ,EAAEL,OAAO,CAAC;EACpC;EACAM,OAAO,CAACC,SAAS,EAAEC,QAAQ,EAAE;IACzB,OAAO,IAAI,CAAC3B,SAAS,CAAC4B,MAAM,CAAC,IAAI,CAACV,OAAO,EAAG,KAAI,IAAI,CAACxB,EAAG,IAAGgC,SAAU,EAAC,EAAEC,QAAQ,CAAC;EACrF;EACAH,QAAQ,CAACK,OAAO,EAAE,GAAGd,IAAI,EAAE;IACvB,OAAOP,qBAAqB,CAAC,IAAI,CAACR,SAAS,EAAE,IAAI,CAACkB,OAAO,EAAE,IAAI,CAACxB,EAAE,EAAEmC,OAAO,EAAEd,IAAI,CAAC;EACtF;EACAe,MAAM,CAACC,EAAE,EAAE;IACP,IAAI,CAACN,OAAO,CAAC,MAAM,EAAEM,EAAE,CAAC;EAC5B;EACAC,OAAO,CAACD,EAAE,EAAE;IACR,IAAI,CAACN,OAAO,CAAC,OAAO,EAAEM,EAAE,CAAC;EAC7B;EACAE,SAAS,CAACF,EAAE,EAAE;IACV,IAAI,CAACN,OAAO,CAAC,SAAS,EAAEM,EAAE,CAAC;EAC/B;EACAG,IAAI,GAAG;IACH,IAAI,CAACV,QAAQ,CAAC,MAAM,CAAC;EACzB;EACAW,UAAU,GAAG;IACT,OAAO,IAAI,CAACb,QAAQ;EACxB;EACAc,IAAI,GAAG;IACH,IAAI,CAACZ,QAAQ,CAAC,MAAM,CAAC;IACrB,IAAI,CAACF,QAAQ,GAAG,IAAI;EACxB;EACAe,KAAK,GAAG;IACJ,IAAI,CAACb,QAAQ,CAAC,OAAO,CAAC;EAC1B;EACAc,OAAO,GAAG;IACN,IAAI,CAACd,QAAQ,CAAC,SAAS,CAAC;EAC5B;EACAe,MAAM,GAAG;IACL,IAAI,CAACf,QAAQ,CAAC,QAAQ,CAAC;EAC3B;EACAgB,OAAO,GAAG;IACN,IAAI,CAAChB,QAAQ,CAAC,SAAS,CAAC;EAC5B;EACAiB,KAAK,GAAG;IACJ,IAAI,CAACjB,QAAQ,CAAC,OAAO,CAAC;IACtB,IAAI,CAACF,QAAQ,GAAG,KAAK;EACzB;EACAoB,WAAW,CAACC,CAAC,EAAE;IACX,IAAI,CAACnB,QAAQ,CAAC,aAAa,EAAEmB,CAAC,CAAC;EACnC;EACAC,WAAW,GAAG;IACV,OAAO,IAAI,CAAC5C,SAAS,CAAC6C,MAAM,CAACC,OAAO,CAAC,CAAC,IAAI,CAACpD,EAAE,CAAC,EAAEkD,WAAW,EAAE,IAAI,CAAC;EACtE;AACJ;AACA,SAASpC,qBAAqB,CAACuC,QAAQ,EAAE7B,OAAO,EAAExB,EAAE,EAAEmC,OAAO,EAAEd,IAAI,EAAE;EACjE,OAAOgC,QAAQ,CAACC,WAAW,CAAC9B,OAAO,EAAG,KAAIxB,EAAG,IAAGmC,OAAQ,EAAC,EAAEd,IAAI,CAAC;AACpE;AAEA,MAAMkC,gBAAgB,GAAG,GAAG;AAC5B,MAAMC,uBAAuB,GAAG,YAAY;AAC5C,MAAMC,wBAAwB,CAAC;EAC3B9D,WAAW,CAAC+D,QAAQ,EAAEP,MAAM,EAAEQ,KAAK,EAAE;IACjC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACP,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACQ,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAACC,cAAc,GAAG,IAAIC,GAAG,EAAE;IAC/B,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,OAAO,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;IACjCjB,MAAM,CAACkB,iBAAiB,GAAG,CAAC7C,OAAO,EAAEkC,QAAQ,KAAK;MAC9C;MACA;MACA;MACA;MACA,MAAMY,UAAU,GAAGZ,QAAQ,EAAEY,UAAU,CAAC9C,OAAO,CAAC;MAChD,IAAI8C,UAAU,EAAE;QACZZ,QAAQ,CAACa,WAAW,CAACD,UAAU,EAAE9C,OAAO,CAAC;MAC7C;IACJ,CAAC;EACL;EACAjB,cAAc,CAACiE,WAAW,EAAEtD,IAAI,EAAE;IAC9B,MAAMuD,kBAAkB,GAAG,EAAE;IAC7B;IACA;IACA,MAAMf,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACnD,cAAc,CAACiE,WAAW,EAAEtD,IAAI,CAAC;IAChE,IAAI,CAACsD,WAAW,IAAI,CAACtD,IAAI,IAAI,CAACA,IAAI,CAACd,IAAI,IAAI,CAACc,IAAI,CAACd,IAAI,CAAC,WAAW,CAAC,EAAE;MAChE,IAAIiD,QAAQ,GAAG,IAAI,CAACU,cAAc,CAACW,GAAG,CAAChB,QAAQ,CAAC;MAChD,IAAI,CAACL,QAAQ,EAAE;QACX;QACA;QACA,MAAMsB,iBAAiB,GAAG,MAAM,IAAI,CAACZ,cAAc,CAACa,MAAM,CAAClB,QAAQ,CAAC;QACpEL,QAAQ,GACJ,IAAIwB,qBAAqB,CAACJ,kBAAkB,EAAEf,QAAQ,EAAE,IAAI,CAACP,MAAM,EAAEwB,iBAAiB,CAAC;QAC3F;QACA,IAAI,CAACZ,cAAc,CAACe,GAAG,CAACpB,QAAQ,EAAEL,QAAQ,CAAC;MAC/C;MACA,OAAOA,QAAQ;IACnB;IACA,MAAM0B,WAAW,GAAG7D,IAAI,CAAClB,EAAE;IAC3B,MAAMgF,WAAW,GAAG9D,IAAI,CAAClB,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC4D,UAAU;IACnD,IAAI,CAACA,UAAU,EAAE;IACjB,IAAI,CAACT,MAAM,CAAC8B,QAAQ,CAACD,WAAW,EAAER,WAAW,CAAC;IAC9C,MAAMU,eAAe,GAAIC,OAAO,IAAK;MACjC,IAAIvE,KAAK,CAACC,OAAO,CAACsE,OAAO,CAAC,EAAE;QACxBA,OAAO,CAACC,OAAO,CAACF,eAAe,CAAC;MACpC,CAAC,MACI;QACD,IAAI,CAAC/B,MAAM,CAAC+B,eAAe,CAACH,WAAW,EAAEC,WAAW,EAAER,WAAW,EAAEW,OAAO,CAACE,IAAI,EAAEF,OAAO,CAAC;MAC7F;IACJ,CAAC;IACD,MAAMG,iBAAiB,GAAGpE,IAAI,CAACd,IAAI,CAAC,WAAW,CAAC;IAChDkF,iBAAiB,CAACF,OAAO,CAACF,eAAe,CAAC;IAC1C,OAAO,IAAIK,iBAAiB,CAAC,IAAI,EAAEP,WAAW,EAAEtB,QAAQ,EAAE,IAAI,CAACP,MAAM,CAAC;EAC1E;EACAqC,KAAK,GAAG;IACJ,IAAI,CAACvB,aAAa,EAAE;IACpB,IAAI,IAAI,CAACP,QAAQ,CAAC8B,KAAK,EAAE;MACrB,IAAI,CAAC9B,QAAQ,CAAC8B,KAAK,EAAE;IACzB;EACJ;EACAC,kBAAkB,GAAG;IACjB;IACA,IAAI,CAACvB,OAAO,CAACwB,IAAI,CAAC,MAAM;MACpB,IAAI,CAAC7B,YAAY,EAAE;IACvB,CAAC,CAAC;EACN;EACA;EACA8B,wBAAwB,CAACC,KAAK,EAAEvD,EAAE,EAAEjC,IAAI,EAAE;IACtC,IAAIwF,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAAC/B,YAAY,EAAE;MACzC,IAAI,CAACF,KAAK,CAACkC,GAAG,CAAC,MAAMxD,EAAE,CAACjC,IAAI,CAAC,CAAC;MAC9B;IACJ;IACA,IAAI,IAAI,CAAC0D,yBAAyB,CAACgC,MAAM,IAAI,CAAC,EAAE;MAC5C3B,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC,CAACsB,IAAI,CAAC,MAAM;QAC7B,IAAI,CAAC/B,KAAK,CAACkC,GAAG,CAAC,MAAM;UACjB,IAAI,CAAC/B,yBAAyB,CAACsB,OAAO,CAACW,KAAK,IAAI;YAC5C,MAAM,CAAC1D,EAAE,EAAEjC,IAAI,CAAC,GAAG2F,KAAK;YACxB1D,EAAE,CAACjC,IAAI,CAAC;UACZ,CAAC,CAAC;UACF,IAAI,CAAC0D,yBAAyB,GAAG,EAAE;QACvC,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACA,IAAI,CAACA,yBAAyB,CAACkC,IAAI,CAAC,CAAC3D,EAAE,EAAEjC,IAAI,CAAC,CAAC;EACnD;EACA6F,GAAG,GAAG;IACF,IAAI,CAAChC,aAAa,EAAE;IACpB;IACA;IACA,IAAI,IAAI,CAACA,aAAa,IAAI,CAAC,EAAE;MACzB,IAAI,CAACN,KAAK,CAACuC,iBAAiB,CAAC,MAAM;QAC/B,IAAI,CAACT,kBAAkB,EAAE;QACzB,IAAI,CAACtC,MAAM,CAACgD,KAAK,CAAC,IAAI,CAACtC,YAAY,CAAC;MACxC,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACH,QAAQ,CAACuC,GAAG,EAAE;MACnB,IAAI,CAACvC,QAAQ,CAACuC,GAAG,EAAE;IACvB;EACJ;EACAG,iBAAiB,GAAG;IAChB,OAAO,IAAI,CAACjD,MAAM,CAACiD,iBAAiB,EAAE;EAC1C;AACJ;AACA3C,wBAAwB,CAACzC,IAAI;EAAA,iBAAyFyC,wBAAwB,EA3LnCpF,EAAE,UA2LmDA,EAAE,CAACI,gBAAgB,GA3LxEJ,EAAE,UA2LmFa,EAAE,CAACC,gBAAgB,GA3LxGd,EAAE,UA2LmHA,EAAE,CAACK,MAAM;AAAA,CAA6C;AACtR+E,wBAAwB,CAACxC,KAAK,kBA5L6E5C,EAAE;EAAA,OA4LaoF,wBAAwB;EAAA,SAAxBA,wBAAwB;AAAA,EAAG;AACrJ;EAAA,mDA7L2GpF,EAAE,mBA6LjBoF,wBAAwB,EAAc,CAAC;IACvHvC,IAAI,EAAE3C;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE2C,IAAI,EAAE7C,EAAE,CAACI;IAAiB,CAAC,EAAE;MAAEyC,IAAI,EAAEhC,EAAE,CAACC;IAAiB,CAAC,EAAE;MAAE+B,IAAI,EAAE7C,EAAE,CAACK;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AACvI,MAAMmG,qBAAqB,CAAC;EACxBlF,WAAW,CAACqF,WAAW,EAAEtB,QAAQ,EAAEP,MAAM,EAAEkD,UAAU,EAAE;IACnD,IAAI,CAACrB,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACtB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACP,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACkD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,WAAW,GAAG,IAAI,CAAC5C,QAAQ,CAAC4C,WAAW,GAAIC,CAAC,IAAK7C,QAAQ,CAAC4C,WAAW,CAACC,CAAC,CAAC,GAAG,IAAI;EACxF;EACA,IAAInG,IAAI,GAAG;IACP,OAAO,IAAI,CAACsD,QAAQ,CAACtD,IAAI;EAC7B;EACA0C,OAAO,GAAG;IACN,IAAI,CAACK,MAAM,CAACL,OAAO,CAAC,IAAI,CAACkC,WAAW,EAAE,IAAI,CAACtB,QAAQ,CAAC;IACpD,IAAI,CAACA,QAAQ,CAACZ,OAAO,EAAE;IACvB,IAAI,CAACuD,UAAU,IAAI;EACvB;EACAG,aAAa,CAACnB,IAAI,EAAEoB,SAAS,EAAE;IAC3B,OAAO,IAAI,CAAC/C,QAAQ,CAAC8C,aAAa,CAACnB,IAAI,EAAEoB,SAAS,CAAC;EACvD;EACAC,aAAa,CAACC,KAAK,EAAE;IACjB,OAAO,IAAI,CAACjD,QAAQ,CAACgD,aAAa,CAACC,KAAK,CAAC;EAC7C;EACAC,UAAU,CAACD,KAAK,EAAE;IACd,OAAO,IAAI,CAACjD,QAAQ,CAACkD,UAAU,CAACD,KAAK,CAAC;EAC1C;EACAE,WAAW,CAACC,MAAM,EAAEC,QAAQ,EAAE;IAC1B,IAAI,CAACrD,QAAQ,CAACmD,WAAW,CAACC,MAAM,EAAEC,QAAQ,CAAC;IAC3C,IAAI,CAAC5D,MAAM,CAAC6D,QAAQ,CAAC,IAAI,CAAChC,WAAW,EAAE+B,QAAQ,EAAED,MAAM,EAAE,KAAK,CAAC;EACnE;EACAG,YAAY,CAACH,MAAM,EAAEC,QAAQ,EAAEG,QAAQ,EAAEC,MAAM,GAAG,IAAI,EAAE;IACpD,IAAI,CAACzD,QAAQ,CAACuD,YAAY,CAACH,MAAM,EAAEC,QAAQ,EAAEG,QAAQ,CAAC;IACtD;IACA,IAAI,CAAC/D,MAAM,CAAC6D,QAAQ,CAAC,IAAI,CAAChC,WAAW,EAAE+B,QAAQ,EAAED,MAAM,EAAEK,MAAM,CAAC;EACpE;EACA5C,WAAW,CAACuC,MAAM,EAAEM,QAAQ,EAAEC,aAAa,EAAE;IACzC,IAAI,CAAClE,MAAM,CAACmE,QAAQ,CAAC,IAAI,CAACtC,WAAW,EAAEoC,QAAQ,EAAE,IAAI,CAAC1D,QAAQ,EAAE2D,aAAa,CAAC;EAClF;EACAE,iBAAiB,CAACC,cAAc,EAAEC,eAAe,EAAE;IAC/C,OAAO,IAAI,CAAC/D,QAAQ,CAAC6D,iBAAiB,CAACC,cAAc,EAAEC,eAAe,CAAC;EAC3E;EACAnD,UAAU,CAACoD,IAAI,EAAE;IACb,OAAO,IAAI,CAAChE,QAAQ,CAACY,UAAU,CAACoD,IAAI,CAAC;EACzC;EACAC,WAAW,CAACD,IAAI,EAAE;IACd,OAAO,IAAI,CAAChE,QAAQ,CAACiE,WAAW,CAACD,IAAI,CAAC;EAC1C;EACAE,YAAY,CAACC,EAAE,EAAExC,IAAI,EAAEsB,KAAK,EAAEF,SAAS,EAAE;IACrC,IAAI,CAAC/C,QAAQ,CAACkE,YAAY,CAACC,EAAE,EAAExC,IAAI,EAAEsB,KAAK,EAAEF,SAAS,CAAC;EAC1D;EACAqB,eAAe,CAACD,EAAE,EAAExC,IAAI,EAAEoB,SAAS,EAAE;IACjC,IAAI,CAAC/C,QAAQ,CAACoE,eAAe,CAACD,EAAE,EAAExC,IAAI,EAAEoB,SAAS,CAAC;EACtD;EACAsB,QAAQ,CAACF,EAAE,EAAExC,IAAI,EAAE;IACf,IAAI,CAAC3B,QAAQ,CAACqE,QAAQ,CAACF,EAAE,EAAExC,IAAI,CAAC;EACpC;EACA2C,WAAW,CAACH,EAAE,EAAExC,IAAI,EAAE;IAClB,IAAI,CAAC3B,QAAQ,CAACsE,WAAW,CAACH,EAAE,EAAExC,IAAI,CAAC;EACvC;EACA4C,QAAQ,CAACJ,EAAE,EAAEK,KAAK,EAAEvB,KAAK,EAAEwB,KAAK,EAAE;IAC9B,IAAI,CAACzE,QAAQ,CAACuE,QAAQ,CAACJ,EAAE,EAAEK,KAAK,EAAEvB,KAAK,EAAEwB,KAAK,CAAC;EACnD;EACAC,WAAW,CAACP,EAAE,EAAEK,KAAK,EAAEC,KAAK,EAAE;IAC1B,IAAI,CAACzE,QAAQ,CAAC0E,WAAW,CAACP,EAAE,EAAEK,KAAK,EAAEC,KAAK,CAAC;EAC/C;EACA7E,WAAW,CAACuE,EAAE,EAAExC,IAAI,EAAEsB,KAAK,EAAE;IACzB,IAAItB,IAAI,CAACgD,MAAM,CAAC,CAAC,CAAC,IAAI9E,gBAAgB,IAAI8B,IAAI,IAAI7B,uBAAuB,EAAE;MACvE,IAAI,CAAC8E,iBAAiB,CAACT,EAAE,EAAE,CAAC,CAAClB,KAAK,CAAC;IACvC,CAAC,MACI;MACD,IAAI,CAACjD,QAAQ,CAACJ,WAAW,CAACuE,EAAE,EAAExC,IAAI,EAAEsB,KAAK,CAAC;IAC9C;EACJ;EACA4B,QAAQ,CAACb,IAAI,EAAEf,KAAK,EAAE;IAClB,IAAI,CAACjD,QAAQ,CAAC6E,QAAQ,CAACb,IAAI,EAAEf,KAAK,CAAC;EACvC;EACAzE,MAAM,CAACsG,MAAM,EAAExG,SAAS,EAAEC,QAAQ,EAAE;IAChC,OAAO,IAAI,CAACyB,QAAQ,CAACxB,MAAM,CAACsG,MAAM,EAAExG,SAAS,EAAEC,QAAQ,CAAC;EAC5D;EACAqG,iBAAiB,CAAC9G,OAAO,EAAEmF,KAAK,EAAE;IAC9B,IAAI,CAACxD,MAAM,CAACmF,iBAAiB,CAAC9G,OAAO,EAAEmF,KAAK,CAAC;EACjD;AACJ;AACA,MAAMpB,iBAAiB,SAASV,qBAAqB,CAAC;EAClDlF,WAAW,CAAC8I,OAAO,EAAEzD,WAAW,EAAEtB,QAAQ,EAAEP,MAAM,EAAEZ,SAAS,EAAE;IAC3D,KAAK,CAACyC,WAAW,EAAEtB,QAAQ,EAAEP,MAAM,EAAEZ,SAAS,CAAC;IAC/C,IAAI,CAACkG,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACzD,WAAW,GAAGA,WAAW;EAClC;EACA1B,WAAW,CAACuE,EAAE,EAAExC,IAAI,EAAEsB,KAAK,EAAE;IACzB,IAAItB,IAAI,CAACgD,MAAM,CAAC,CAAC,CAAC,IAAI9E,gBAAgB,EAAE;MACpC,IAAI8B,IAAI,CAACgD,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAIhD,IAAI,IAAI7B,uBAAuB,EAAE;QAC1DmD,KAAK,GAAGA,KAAK,KAAKxF,SAAS,GAAG,IAAI,GAAG,CAAC,CAACwF,KAAK;QAC5C,IAAI,CAAC2B,iBAAiB,CAACT,EAAE,EAAElB,KAAK,CAAC;MACrC,CAAC,MACI;QACD,IAAI,CAACxD,MAAM,CAACuF,OAAO,CAAC,IAAI,CAAC1D,WAAW,EAAE6C,EAAE,EAAExC,IAAI,CAACsD,KAAK,CAAC,CAAC,CAAC,EAAEhC,KAAK,CAAC;MACnE;IACJ,CAAC,MACI;MACD,IAAI,CAACjD,QAAQ,CAACJ,WAAW,CAACuE,EAAE,EAAExC,IAAI,EAAEsB,KAAK,CAAC;IAC9C;EACJ;EACAzE,MAAM,CAACsG,MAAM,EAAExG,SAAS,EAAEC,QAAQ,EAAE;IAChC,IAAID,SAAS,CAACqG,MAAM,CAAC,CAAC,CAAC,IAAI9E,gBAAgB,EAAE;MACzC,MAAM/B,OAAO,GAAGoH,wBAAwB,CAACJ,MAAM,CAAC;MAChD,IAAInD,IAAI,GAAGrD,SAAS,CAAC2G,KAAK,CAAC,CAAC,CAAC;MAC7B,IAAIE,KAAK,GAAG,EAAE;MACd;MACA;MACA,IAAIxD,IAAI,CAACgD,MAAM,CAAC,CAAC,CAAC,IAAI9E,gBAAgB,EAAE;QACpC,CAAC8B,IAAI,EAAEwD,KAAK,CAAC,GAAGC,wBAAwB,CAACzD,IAAI,CAAC;MAClD;MACA,OAAO,IAAI,CAAClC,MAAM,CAACjB,MAAM,CAAC,IAAI,CAAC8C,WAAW,EAAExD,OAAO,EAAE6D,IAAI,EAAEwD,KAAK,EAAEE,KAAK,IAAI;QACvE,MAAMC,OAAO,GAAGD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAACN,OAAO,CAAC9C,wBAAwB,CAACqD,OAAO,EAAE/G,QAAQ,EAAE8G,KAAK,CAAC;MACnE,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAACrF,QAAQ,CAACxB,MAAM,CAACsG,MAAM,EAAExG,SAAS,EAAEC,QAAQ,CAAC;EAC5D;AACJ;AACA,SAAS2G,wBAAwB,CAACJ,MAAM,EAAE;EACtC,QAAQA,MAAM;IACV,KAAK,MAAM;MACP,OAAOS,QAAQ,CAACzI,IAAI;IACxB,KAAK,UAAU;MACX,OAAOyI,QAAQ;IACnB,KAAK,QAAQ;MACT,OAAOC,MAAM;IACjB;MACI,OAAOV,MAAM;EAAC;AAE1B;AACA,SAASM,wBAAwB,CAACK,WAAW,EAAE;EAC3C,MAAMC,QAAQ,GAAGD,WAAW,CAACE,OAAO,CAAC,GAAG,CAAC;EACzC,MAAMlE,OAAO,GAAGgE,WAAW,CAACG,SAAS,CAAC,CAAC,EAAEF,QAAQ,CAAC;EAClD,MAAMP,KAAK,GAAGM,WAAW,CAACR,KAAK,CAACS,QAAQ,GAAG,CAAC,CAAC;EAC7C,OAAO,CAACjE,OAAO,EAAE0D,KAAK,CAAC;AAC3B;AAEA,MAAMU,yBAAyB,SAASpK,gBAAgB,CAAC;EACrD;EACA;EACA;EACAQ,WAAW,CAACE,GAAG,EAAE2J,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAE;IACzC,KAAK,CAAC7J,GAAG,CAACW,IAAI,EAAEgJ,MAAM,EAAEC,UAAU,CAAC;EACvC;EACAE,WAAW,GAAG;IACV,IAAI,CAACxD,KAAK,EAAE;EAChB;AACJ;AACAoD,yBAAyB,CAACvI,IAAI;EAAA,iBAAyFuI,yBAAyB,EAtVrClL,EAAE,UAsVqDoB,QAAQ,GAtV/DpB,EAAE,UAsV0Ea,EAAE,CAACI,eAAe,GAtV9FjB,EAAE,UAsVyGa,EAAE,CAACG,yBAAyB,GAtVvIhB,EAAE,UAsVkJA,EAAE,CAACuL,cAAc;AAAA,CAA6C;AAC7TL,yBAAyB,CAACtI,KAAK,kBAvV4E5C,EAAE;EAAA,OAuVckL,yBAAyB;EAAA,SAAzBA,yBAAyB;AAAA,EAAG;AACvJ;EAAA,mDAxV2GlL,EAAE,mBAwVjBkL,yBAAyB,EAAc,CAAC;IACxHrI,IAAI,EAAE3C;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE2C,IAAI,EAAEC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DF,IAAI,EAAE1C,MAAM;QACZ6C,IAAI,EAAE,CAAC5B,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEyB,IAAI,EAAEhC,EAAE,CAACI;IAAgB,CAAC,EAAE;MAAE4B,IAAI,EAAEhC,EAAE,CAACG;IAA0B,CAAC,EAAE;MAAE6B,IAAI,EAAE7C,EAAE,CAACuL;IAAe,CAAC,CAAC;EAAE,CAAC;AAAA;AAC3H,SAASC,iCAAiC,GAAG;EACzC,OAAO,IAAIzK,6BAA6B,EAAE;AAC9C;AACA,SAAS0K,0BAA0B,CAACzG,QAAQ,EAAEF,MAAM,EAAE4G,IAAI,EAAE;EACxD,OAAO,IAAItG,wBAAwB,CAACJ,QAAQ,EAAEF,MAAM,EAAE4G,IAAI,CAAC;AAC/D;AACA,MAAMC,0BAA0B,GAAG,CAC/B;EAAEC,OAAO,EAAElL,gBAAgB;EAAEmL,QAAQ,EAAExK;AAAwB,CAAC,EAChE;EAAEuK,OAAO,EAAE5K,yBAAyB;EAAE8K,UAAU,EAAEN;AAAkC,CAAC,EACrF;EAAEI,OAAO,EAAE9K,gBAAgB;EAAE+K,QAAQ,EAAEX;AAA0B,CAAC,EAAE;EAChEU,OAAO,EAAExL,gBAAgB;EACzB0L,UAAU,EAAEL,0BAA0B;EACtCM,IAAI,EAAE,CAACvL,oBAAoB,EAAEM,gBAAgB,EAAET,MAAM;AACzD,CAAC,CACJ;AACD;AACA;AACA;AACA;AACA,MAAM2L,4BAA4B,GAAG,CACjC;EAAEJ,OAAO,EAAE3K,eAAe;EAAE6K,UAAU,EAAE,MAAM,IAAI5K,oBAAoB;AAAG,CAAC,EAC1E;EAAE0K,OAAO,EAAEtL,qBAAqB;EAAE2L,QAAQ,EAAE;AAAoB,CAAC,EAAE,GAAGN,0BAA0B,CACnG;AACD;AACA;AACA;AACA;AACA,MAAMO,iCAAiC,GAAG,CACtC;EAAEN,OAAO,EAAE3K,eAAe;EAAE4K,QAAQ,EAAE1K;AAAqB,CAAC,EAC5D;EAAEyK,OAAO,EAAEtL,qBAAqB;EAAE2L,QAAQ,EAAE;AAAiB,CAAC,EAAE,GAAGN,0BAA0B,CAChG;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMQ,uBAAuB,CAAC;EAC1B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,UAAU,CAACC,MAAM,EAAE;IACtB,OAAO;MACHC,QAAQ,EAAEH,uBAAuB;MACjCI,SAAS,EAAEF,MAAM,CAACpC,iBAAiB,GAAGiC,iCAAiC,GACnEF;IACR,CAAC;EACL;AACJ;AACAG,uBAAuB,CAACxJ,IAAI;EAAA,iBAAyFwJ,uBAAuB;AAAA,CAAkD;AAC9LA,uBAAuB,CAACK,IAAI,kBA7Z+ExM,EAAE;EAAA,MA6ZSmM,uBAAuB;EAAA,UAAY1L,aAAa;AAAA,EAAI;AAC1K0L,uBAAuB,CAACM,IAAI,kBA9Z+EzM,EAAE;EAAA,WA8Z6CgM,4BAA4B;EAAA,UAAYvL,aAAa;AAAA,EAAI;AACnN;EAAA,mDA/Z2GT,EAAE,mBA+ZjBmM,uBAAuB,EAAc,CAAC;IACtHtJ,IAAI,EAAEtC,QAAQ;IACdyC,IAAI,EAAE,CAAC;MACC0J,OAAO,EAAE,CAACjM,aAAa,CAAC;MACxB8L,SAAS,EAAEP;IACf,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,iBAAiB,GAAG;EACzB;EACA;EACA,OAAO,CAAC,GAAGX,4BAA4B,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA,MAAMY,oBAAoB,CAAC;AAE3BA,oBAAoB,CAACjK,IAAI;EAAA,iBAAyFiK,oBAAoB;AAAA,CAAkD;AACxLA,oBAAoB,CAACJ,IAAI,kBAxckFxM,EAAE;EAAA,MAwcM4M,oBAAoB;EAAA,UAAYnM,aAAa;AAAA,EAAI;AACpKmM,oBAAoB,CAACH,IAAI,kBAzckFzM,EAAE;EAAA,WAycuCkM,iCAAiC;EAAA,UAAYzL,aAAa;AAAA,EAAI;AAClN;EAAA,mDA1c2GT,EAAE,mBA0cjB4M,oBAAoB,EAAc,CAAC;IACnH/J,IAAI,EAAEtC,QAAQ;IACdyC,IAAI,EAAE,CAAC;MACC0J,OAAO,EAAE,CAACjM,aAAa,CAAC;MACxB8L,SAAS,EAAEL;IACf,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,qBAAqB,GAAG;EAC7B;EACA;EACA,OAAO,CAAC,GAAGX,iCAAiC,CAAC;AACjD;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASC,uBAAuB,EAAES,oBAAoB,EAAED,iBAAiB,EAAEE,qBAAqB,EAAE3F,iBAAiB,IAAI4F,kBAAkB,EAAE1H,wBAAwB,IAAI2H,yBAAyB,EAAE1L,uBAAuB,IAAI2L,wBAAwB,EAAEtK,uBAAuB,IAAIuK,wBAAwB,EAAE/B,yBAAyB,IAAIgC,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}