﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35931.197
MinimumVisualStudioVersion = 10.0.40219.1
Project("{54A90642-561A-4BB1-A94E-469ADEE60C69}") = "oracul.client", "oracul.client\oracul.client.esproj", "{2B81A976-17F1-2E69-EA10-1DD1FC7A0473}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Oracul.Server", "Oracul.Server\Oracul.Server.csproj", "{4016A170-9C4A-43DD-8577-6E1C573E7D79}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Oracul.Data", "Oracul.Data\Oracul.Data.csproj", "{8B2A1F3C-9D4E-4A5B-8C7F-1E2D3F4A5B6C}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{2B81A976-17F1-2E69-EA10-1DD1FC7A0473}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2B81A976-17F1-2E69-EA10-1DD1FC7A0473}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2B81A976-17F1-2E69-EA10-1DD1FC7A0473}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{2B81A976-17F1-2E69-EA10-1DD1FC7A0473}.Debug|x64.ActiveCfg = Debug|x64
		{2B81A976-17F1-2E69-EA10-1DD1FC7A0473}.Debug|x86.ActiveCfg = Debug|x86
		{2B81A976-17F1-2E69-EA10-1DD1FC7A0473}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2B81A976-17F1-2E69-EA10-1DD1FC7A0473}.Release|Any CPU.Build.0 = Release|Any CPU
		{2B81A976-17F1-2E69-EA10-1DD1FC7A0473}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{2B81A976-17F1-2E69-EA10-1DD1FC7A0473}.Release|x64.ActiveCfg = Release|x64
		{2B81A976-17F1-2E69-EA10-1DD1FC7A0473}.Release|x86.ActiveCfg = Release|x86
		{4016A170-9C4A-43DD-8577-6E1C573E7D79}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4016A170-9C4A-43DD-8577-6E1C573E7D79}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4016A170-9C4A-43DD-8577-6E1C573E7D79}.Debug|x64.ActiveCfg = Debug|Any CPU
		{4016A170-9C4A-43DD-8577-6E1C573E7D79}.Debug|x64.Build.0 = Debug|Any CPU
		{4016A170-9C4A-43DD-8577-6E1C573E7D79}.Debug|x86.ActiveCfg = Debug|Any CPU
		{4016A170-9C4A-43DD-8577-6E1C573E7D79}.Debug|x86.Build.0 = Debug|Any CPU
		{4016A170-9C4A-43DD-8577-6E1C573E7D79}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4016A170-9C4A-43DD-8577-6E1C573E7D79}.Release|Any CPU.Build.0 = Release|Any CPU
		{4016A170-9C4A-43DD-8577-6E1C573E7D79}.Release|x64.ActiveCfg = Release|Any CPU
		{4016A170-9C4A-43DD-8577-6E1C573E7D79}.Release|x64.Build.0 = Release|Any CPU
		{4016A170-9C4A-43DD-8577-6E1C573E7D79}.Release|x86.ActiveCfg = Release|Any CPU
		{4016A170-9C4A-43DD-8577-6E1C573E7D79}.Release|x86.Build.0 = Release|Any CPU
		{8B2A1F3C-9D4E-4A5B-8C7F-1E2D3F4A5B6C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B2A1F3C-9D4E-4A5B-8C7F-1E2D3F4A5B6C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B2A1F3C-9D4E-4A5B-8C7F-1E2D3F4A5B6C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8B2A1F3C-9D4E-4A5B-8C7F-1E2D3F4A5B6C}.Debug|x64.Build.0 = Debug|Any CPU
		{8B2A1F3C-9D4E-4A5B-8C7F-1E2D3F4A5B6C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{8B2A1F3C-9D4E-4A5B-8C7F-1E2D3F4A5B6C}.Debug|x86.Build.0 = Debug|Any CPU
		{8B2A1F3C-9D4E-4A5B-8C7F-1E2D3F4A5B6C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B2A1F3C-9D4E-4A5B-8C7F-1E2D3F4A5B6C}.Release|Any CPU.Build.0 = Release|Any CPU
		{8B2A1F3C-9D4E-4A5B-8C7F-1E2D3F4A5B6C}.Release|x64.ActiveCfg = Release|Any CPU
		{8B2A1F3C-9D4E-4A5B-8C7F-1E2D3F4A5B6C}.Release|x64.Build.0 = Release|Any CPU
		{8B2A1F3C-9D4E-4A5B-8C7F-1E2D3F4A5B6C}.Release|x86.ActiveCfg = Release|Any CPU
		{8B2A1F3C-9D4E-4A5B-8C7F-1E2D3F4A5B6C}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {59AC851C-B26B-4251-B6D4-74D65E0F28FC}
	EndGlobalSection
EndGlobal
