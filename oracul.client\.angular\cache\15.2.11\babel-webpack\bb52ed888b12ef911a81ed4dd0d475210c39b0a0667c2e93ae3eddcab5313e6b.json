{"ast": null, "code": "'use strict';\n\n/**\n * @license Angular v15.1.0-next.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nconst NEWLINE = '\\n';\nconst IGNORE_FRAMES = {};\nconst creationTrace = '__creationTrace__';\nconst ERROR_TAG = 'STACKTRACE TRACKING';\nconst SEP_TAG = '__SEP_TAG__';\nlet sepTemplate = SEP_TAG + '@[native]';\nclass LongStackTrace {\n  constructor() {\n    this.error = getStacktrace();\n    this.timestamp = new Date();\n  }\n}\nfunction getStacktraceWithUncaughtError() {\n  return new Error(ERROR_TAG);\n}\nfunction getStacktraceWithCaughtError() {\n  try {\n    throw getStacktraceWithUncaughtError();\n  } catch (err) {\n    return err;\n  }\n}\n// Some implementations of exception handling don't create a stack trace if the exception\n// isn't thrown, however it's faster not to actually throw the exception.\nconst error = getStacktraceWithUncaughtError();\nconst caughtError = getStacktraceWithCaughtError();\nconst getStacktrace = error.stack ? getStacktraceWithUncaughtError : caughtError.stack ? getStacktraceWithCaughtError : getStacktraceWithUncaughtError;\nfunction getFrames(error) {\n  return error.stack ? error.stack.split(NEWLINE) : [];\n}\nfunction addErrorStack(lines, error) {\n  let trace = getFrames(error);\n  for (let i = 0; i < trace.length; i++) {\n    const frame = trace[i];\n    // Filter out the Frames which are part of stack capturing.\n    if (!IGNORE_FRAMES.hasOwnProperty(frame)) {\n      lines.push(trace[i]);\n    }\n  }\n}\nfunction renderLongStackTrace(frames, stack) {\n  const longTrace = [stack ? stack.trim() : ''];\n  if (frames) {\n    let timestamp = new Date().getTime();\n    for (let i = 0; i < frames.length; i++) {\n      const traceFrames = frames[i];\n      const lastTime = traceFrames.timestamp;\n      let separator = `____________________Elapsed ${timestamp - lastTime.getTime()} ms; At: ${lastTime}`;\n      separator = separator.replace(/[^\\w\\d]/g, '_');\n      longTrace.push(sepTemplate.replace(SEP_TAG, separator));\n      addErrorStack(longTrace, traceFrames.error);\n      timestamp = lastTime.getTime();\n    }\n  }\n  return longTrace.join(NEWLINE);\n}\n// if Error.stackTraceLimit is 0, means stack trace\n// is disabled, so we don't need to generate long stack trace\n// this will improve performance in some test(some test will\n// set stackTraceLimit to 0, https://github.com/angular/zone.js/issues/698\nfunction stackTracesEnabled() {\n  // Cast through any since this property only exists on Error in the nodejs\n  // typings.\n  return Error.stackTraceLimit > 0;\n}\nZone['longStackTraceZoneSpec'] = {\n  name: 'long-stack-trace',\n  longStackTraceLimit: 10,\n  // add a getLongStackTrace method in spec to\n  // handle handled reject promise error.\n  getLongStackTrace: function (error) {\n    if (!error) {\n      return undefined;\n    }\n    const trace = error[Zone.__symbol__('currentTaskTrace')];\n    if (!trace) {\n      return error.stack;\n    }\n    return renderLongStackTrace(trace, error.stack);\n  },\n  onScheduleTask: function (parentZoneDelegate, currentZone, targetZone, task) {\n    if (stackTracesEnabled()) {\n      const currentTask = Zone.currentTask;\n      let trace = currentTask && currentTask.data && currentTask.data[creationTrace] || [];\n      trace = [new LongStackTrace()].concat(trace);\n      if (trace.length > this.longStackTraceLimit) {\n        trace.length = this.longStackTraceLimit;\n      }\n      if (!task.data) task.data = {};\n      if (task.type === 'eventTask') {\n        // Fix issue https://github.com/angular/zone.js/issues/1195,\n        // For event task of browser, by default, all task will share a\n        // singleton instance of data object, we should create a new one here\n        // The cast to `any` is required to workaround a closure bug which wrongly applies\n        // URL sanitization rules to .data access.\n        task.data = Object.assign({}, task.data);\n      }\n      task.data[creationTrace] = trace;\n    }\n    return parentZoneDelegate.scheduleTask(targetZone, task);\n  },\n  onHandleError: function (parentZoneDelegate, currentZone, targetZone, error) {\n    if (stackTracesEnabled()) {\n      const parentTask = Zone.currentTask || error.task;\n      if (error instanceof Error && parentTask) {\n        const longStack = renderLongStackTrace(parentTask.data && parentTask.data[creationTrace], error.stack);\n        try {\n          error.stack = error.longStack = longStack;\n        } catch (err) {}\n      }\n    }\n    return parentZoneDelegate.handleError(targetZone, error);\n  }\n};\nfunction captureStackTraces(stackTraces, count) {\n  if (count > 0) {\n    stackTraces.push(getFrames(new LongStackTrace().error));\n    captureStackTraces(stackTraces, count - 1);\n  }\n}\nfunction computeIgnoreFrames() {\n  if (!stackTracesEnabled()) {\n    return;\n  }\n  const frames = [];\n  captureStackTraces(frames, 2);\n  const frames1 = frames[0];\n  const frames2 = frames[1];\n  for (let i = 0; i < frames1.length; i++) {\n    const frame1 = frames1[i];\n    if (frame1.indexOf(ERROR_TAG) == -1) {\n      let match = frame1.match(/^\\s*at\\s+/);\n      if (match) {\n        sepTemplate = match[0] + SEP_TAG + ' (http://localhost)';\n        break;\n      }\n    }\n  }\n  for (let i = 0; i < frames1.length; i++) {\n    const frame1 = frames1[i];\n    const frame2 = frames2[i];\n    if (frame1 === frame2) {\n      IGNORE_FRAMES[frame1] = true;\n    } else {\n      break;\n    }\n  }\n}\ncomputeIgnoreFrames();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass ProxyZoneSpec {\n  constructor(defaultSpecDelegate = null) {\n    this.defaultSpecDelegate = defaultSpecDelegate;\n    this.name = 'ProxyZone';\n    this._delegateSpec = null;\n    this.properties = {\n      'ProxyZoneSpec': this\n    };\n    this.propertyKeys = null;\n    this.lastTaskState = null;\n    this.isNeedToTriggerHasTask = false;\n    this.tasks = [];\n    this.setDelegate(defaultSpecDelegate);\n  }\n  static get() {\n    return Zone.current.get('ProxyZoneSpec');\n  }\n  static isLoaded() {\n    return ProxyZoneSpec.get() instanceof ProxyZoneSpec;\n  }\n  static assertPresent() {\n    if (!ProxyZoneSpec.isLoaded()) {\n      throw new Error(`Expected to be running in 'ProxyZone', but it was not found.`);\n    }\n    return ProxyZoneSpec.get();\n  }\n  setDelegate(delegateSpec) {\n    const isNewDelegate = this._delegateSpec !== delegateSpec;\n    this._delegateSpec = delegateSpec;\n    this.propertyKeys && this.propertyKeys.forEach(key => delete this.properties[key]);\n    this.propertyKeys = null;\n    if (delegateSpec && delegateSpec.properties) {\n      this.propertyKeys = Object.keys(delegateSpec.properties);\n      this.propertyKeys.forEach(k => this.properties[k] = delegateSpec.properties[k]);\n    }\n    // if a new delegateSpec was set, check if we need to trigger hasTask\n    if (isNewDelegate && this.lastTaskState && (this.lastTaskState.macroTask || this.lastTaskState.microTask)) {\n      this.isNeedToTriggerHasTask = true;\n    }\n  }\n  getDelegate() {\n    return this._delegateSpec;\n  }\n  resetDelegate() {\n    this.getDelegate();\n    this.setDelegate(this.defaultSpecDelegate);\n  }\n  tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone) {\n    if (this.isNeedToTriggerHasTask && this.lastTaskState) {\n      // last delegateSpec has microTask or macroTask\n      // should call onHasTask in current delegateSpec\n      this.isNeedToTriggerHasTask = false;\n      this.onHasTask(parentZoneDelegate, currentZone, targetZone, this.lastTaskState);\n    }\n  }\n  removeFromTasks(task) {\n    if (!this.tasks) {\n      return;\n    }\n    for (let i = 0; i < this.tasks.length; i++) {\n      if (this.tasks[i] === task) {\n        this.tasks.splice(i, 1);\n        return;\n      }\n    }\n  }\n  getAndClearPendingTasksInfo() {\n    if (this.tasks.length === 0) {\n      return '';\n    }\n    const taskInfo = this.tasks.map(task => {\n      const dataInfo = task.data && Object.keys(task.data).map(key => {\n        return key + ':' + task.data[key];\n      }).join(',');\n      return `type: ${task.type}, source: ${task.source}, args: {${dataInfo}}`;\n    });\n    const pendingTasksInfo = '--Pending async tasks are: [' + taskInfo + ']';\n    // clear tasks\n    this.tasks = [];\n    return pendingTasksInfo;\n  }\n  onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec) {\n    if (this._delegateSpec && this._delegateSpec.onFork) {\n      return this._delegateSpec.onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec);\n    } else {\n      return parentZoneDelegate.fork(targetZone, zoneSpec);\n    }\n  }\n  onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source) {\n    if (this._delegateSpec && this._delegateSpec.onIntercept) {\n      return this._delegateSpec.onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source);\n    } else {\n      return parentZoneDelegate.intercept(targetZone, delegate, source);\n    }\n  }\n  onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n    this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n    if (this._delegateSpec && this._delegateSpec.onInvoke) {\n      return this._delegateSpec.onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source);\n    } else {\n      return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n    }\n  }\n  onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n    if (this._delegateSpec && this._delegateSpec.onHandleError) {\n      return this._delegateSpec.onHandleError(parentZoneDelegate, currentZone, targetZone, error);\n    } else {\n      return parentZoneDelegate.handleError(targetZone, error);\n    }\n  }\n  onScheduleTask(parentZoneDelegate, currentZone, targetZone, task) {\n    if (task.type !== 'eventTask') {\n      this.tasks.push(task);\n    }\n    if (this._delegateSpec && this._delegateSpec.onScheduleTask) {\n      return this._delegateSpec.onScheduleTask(parentZoneDelegate, currentZone, targetZone, task);\n    } else {\n      return parentZoneDelegate.scheduleTask(targetZone, task);\n    }\n  }\n  onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs) {\n    if (task.type !== 'eventTask') {\n      this.removeFromTasks(task);\n    }\n    this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n    if (this._delegateSpec && this._delegateSpec.onInvokeTask) {\n      return this._delegateSpec.onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs);\n    } else {\n      return parentZoneDelegate.invokeTask(targetZone, task, applyThis, applyArgs);\n    }\n  }\n  onCancelTask(parentZoneDelegate, currentZone, targetZone, task) {\n    if (task.type !== 'eventTask') {\n      this.removeFromTasks(task);\n    }\n    this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n    if (this._delegateSpec && this._delegateSpec.onCancelTask) {\n      return this._delegateSpec.onCancelTask(parentZoneDelegate, currentZone, targetZone, task);\n    } else {\n      return parentZoneDelegate.cancelTask(targetZone, task);\n    }\n  }\n  onHasTask(delegate, current, target, hasTaskState) {\n    this.lastTaskState = hasTaskState;\n    if (this._delegateSpec && this._delegateSpec.onHasTask) {\n      this._delegateSpec.onHasTask(delegate, current, target, hasTaskState);\n    } else {\n      delegate.hasTask(target, hasTaskState);\n    }\n  }\n}\n// Export the class so that new instances can be created with proper\n// constructor params.\nZone['ProxyZoneSpec'] = ProxyZoneSpec;\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass SyncTestZoneSpec {\n  constructor(namePrefix) {\n    this.runZone = Zone.current;\n    this.name = 'syncTestZone for ' + namePrefix;\n  }\n  onScheduleTask(delegate, current, target, task) {\n    switch (task.type) {\n      case 'microTask':\n      case 'macroTask':\n        throw new Error(`Cannot call ${task.source} from within a sync test (${this.name}).`);\n      case 'eventTask':\n        task = delegate.scheduleTask(target, task);\n        break;\n    }\n    return task;\n  }\n}\n// Export the class so that new instances can be created with proper\n// constructor params.\nZone['SyncTestZoneSpec'] = SyncTestZoneSpec;\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nZone.__load_patch('jasmine', (global, Zone, api) => {\n  const __extends = function (d, b) {\n    for (const p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n  // Patch jasmine's describe/it/beforeEach/afterEach functions so test code always runs\n  // in a testZone (ProxyZone). (See: angular/zone.js#91 & angular/angular#10503)\n  if (!Zone) throw new Error('Missing: zone.js');\n  if (typeof jest !== 'undefined') {\n    // return if jasmine is a light implementation inside jest\n    // in this case, we are running inside jest not jasmine\n    return;\n  }\n  if (typeof jasmine == 'undefined' || jasmine['__zone_patch__']) {\n    return;\n  }\n  jasmine['__zone_patch__'] = true;\n  const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n  const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n  if (!SyncTestZoneSpec) throw new Error('Missing: SyncTestZoneSpec');\n  if (!ProxyZoneSpec) throw new Error('Missing: ProxyZoneSpec');\n  const ambientZone = Zone.current;\n  const symbol = Zone.__symbol__;\n  // whether patch jasmine clock when in fakeAsync\n  const disablePatchingJasmineClock = global[symbol('fakeAsyncDisablePatchingClock')] === true;\n  // the original variable name fakeAsyncPatchLock is not accurate, so the name will be\n  // fakeAsyncAutoFakeAsyncWhenClockPatched and if this enablePatchingJasmineClock is false, we also\n  // automatically disable the auto jump into fakeAsync feature\n  const enableAutoFakeAsyncWhenClockPatched = !disablePatchingJasmineClock && (global[symbol('fakeAsyncPatchLock')] === true || global[symbol('fakeAsyncAutoFakeAsyncWhenClockPatched')] === true);\n  const ignoreUnhandledRejection = global[symbol('ignoreUnhandledRejection')] === true;\n  if (!ignoreUnhandledRejection) {\n    const globalErrors = jasmine.GlobalErrors;\n    if (globalErrors && !jasmine[symbol('GlobalErrors')]) {\n      jasmine[symbol('GlobalErrors')] = globalErrors;\n      jasmine.GlobalErrors = function () {\n        const instance = new globalErrors();\n        const originalInstall = instance.install;\n        if (originalInstall && !instance[symbol('install')]) {\n          instance[symbol('install')] = originalInstall;\n          instance.install = function () {\n            const isNode = typeof process !== 'undefined' && !!process.on;\n            // Note: Jasmine checks internally if `process` and `process.on` is defined. Otherwise,\n            // it installs the browser rejection handler through the `global.addEventListener`.\n            // This code may be run in the browser environment where `process` is not defined, and\n            // this will lead to a runtime exception since Webpack 5 removed automatic Node.js\n            // polyfills. Note, that events are named differently, it's `unhandledRejection` in\n            // Node.js and `unhandledrejection` in the browser.\n            const originalHandlers = isNode ? process.listeners('unhandledRejection') : global.eventListeners('unhandledrejection');\n            const result = originalInstall.apply(this, arguments);\n            isNode ? process.removeAllListeners('unhandledRejection') : global.removeAllListeners('unhandledrejection');\n            if (originalHandlers) {\n              originalHandlers.forEach(handler => {\n                if (isNode) {\n                  process.on('unhandledRejection', handler);\n                } else {\n                  global.addEventListener('unhandledrejection', handler);\n                }\n              });\n            }\n            return result;\n          };\n        }\n        return instance;\n      };\n    }\n  }\n  // Monkey patch all of the jasmine DSL so that each function runs in appropriate zone.\n  const jasmineEnv = jasmine.getEnv();\n  ['describe', 'xdescribe', 'fdescribe'].forEach(methodName => {\n    let originalJasmineFn = jasmineEnv[methodName];\n    jasmineEnv[methodName] = function (description, specDefinitions) {\n      return originalJasmineFn.call(this, description, wrapDescribeInZone(description, specDefinitions));\n    };\n  });\n  ['it', 'xit', 'fit'].forEach(methodName => {\n    let originalJasmineFn = jasmineEnv[methodName];\n    jasmineEnv[symbol(methodName)] = originalJasmineFn;\n    jasmineEnv[methodName] = function (description, specDefinitions, timeout) {\n      arguments[1] = wrapTestInZone(specDefinitions);\n      return originalJasmineFn.apply(this, arguments);\n    };\n  });\n  ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach(methodName => {\n    let originalJasmineFn = jasmineEnv[methodName];\n    jasmineEnv[symbol(methodName)] = originalJasmineFn;\n    jasmineEnv[methodName] = function (specDefinitions, timeout) {\n      arguments[0] = wrapTestInZone(specDefinitions);\n      return originalJasmineFn.apply(this, arguments);\n    };\n  });\n  if (!disablePatchingJasmineClock) {\n    // need to patch jasmine.clock().mockDate and jasmine.clock().tick() so\n    // they can work properly in FakeAsyncTest\n    const originalClockFn = jasmine[symbol('clock')] = jasmine['clock'];\n    jasmine['clock'] = function () {\n      const clock = originalClockFn.apply(this, arguments);\n      if (!clock[symbol('patched')]) {\n        clock[symbol('patched')] = symbol('patched');\n        const originalTick = clock[symbol('tick')] = clock.tick;\n        clock.tick = function () {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            return fakeAsyncZoneSpec.tick.apply(fakeAsyncZoneSpec, arguments);\n          }\n          return originalTick.apply(this, arguments);\n        };\n        const originalMockDate = clock[symbol('mockDate')] = clock.mockDate;\n        clock.mockDate = function () {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            const dateTime = arguments.length > 0 ? arguments[0] : new Date();\n            return fakeAsyncZoneSpec.setFakeBaseSystemTime.apply(fakeAsyncZoneSpec, dateTime && typeof dateTime.getTime === 'function' ? [dateTime.getTime()] : arguments);\n          }\n          return originalMockDate.apply(this, arguments);\n        };\n        // for auto go into fakeAsync feature, we need the flag to enable it\n        if (enableAutoFakeAsyncWhenClockPatched) {\n          ['install', 'uninstall'].forEach(methodName => {\n            const originalClockFn = clock[symbol(methodName)] = clock[methodName];\n            clock[methodName] = function () {\n              const FakeAsyncTestZoneSpec = Zone['FakeAsyncTestZoneSpec'];\n              if (FakeAsyncTestZoneSpec) {\n                jasmine[symbol('clockInstalled')] = 'install' === methodName;\n                return;\n              }\n              return originalClockFn.apply(this, arguments);\n            };\n          });\n        }\n      }\n      return clock;\n    };\n  }\n  // monkey patch createSpyObj to make properties enumerable to true\n  if (!jasmine[Zone.__symbol__('createSpyObj')]) {\n    const originalCreateSpyObj = jasmine.createSpyObj;\n    jasmine[Zone.__symbol__('createSpyObj')] = originalCreateSpyObj;\n    jasmine.createSpyObj = function () {\n      const args = Array.prototype.slice.call(arguments);\n      const propertyNames = args.length >= 3 ? args[2] : null;\n      let spyObj;\n      if (propertyNames) {\n        const defineProperty = Object.defineProperty;\n        Object.defineProperty = function (obj, p, attributes) {\n          return defineProperty.call(this, obj, p, Object.assign(Object.assign({}, attributes), {\n            configurable: true,\n            enumerable: true\n          }));\n        };\n        try {\n          spyObj = originalCreateSpyObj.apply(this, args);\n        } finally {\n          Object.defineProperty = defineProperty;\n        }\n      } else {\n        spyObj = originalCreateSpyObj.apply(this, args);\n      }\n      return spyObj;\n    };\n  }\n  /**\n   * Gets a function wrapping the body of a Jasmine `describe` block to execute in a\n   * synchronous-only zone.\n   */\n  function wrapDescribeInZone(description, describeBody) {\n    return function () {\n      // Create a synchronous-only zone in which to run `describe` blocks in order to raise an\n      // error if any asynchronous operations are attempted inside of a `describe`.\n      const syncZone = ambientZone.fork(new SyncTestZoneSpec(`jasmine.describe#${description}`));\n      return syncZone.run(describeBody, this, arguments);\n    };\n  }\n  function runInTestZone(testBody, applyThis, queueRunner, done) {\n    const isClockInstalled = !!jasmine[symbol('clockInstalled')];\n    queueRunner.testProxyZoneSpec;\n    const testProxyZone = queueRunner.testProxyZone;\n    if (isClockInstalled && enableAutoFakeAsyncWhenClockPatched) {\n      // auto run a fakeAsync\n      const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n      if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n        testBody = fakeAsyncModule.fakeAsync(testBody);\n      }\n    }\n    if (done) {\n      return testProxyZone.run(testBody, applyThis, [done]);\n    } else {\n      return testProxyZone.run(testBody, applyThis);\n    }\n  }\n  /**\n   * Gets a function wrapping the body of a Jasmine `it/beforeEach/afterEach` block to\n   * execute in a ProxyZone zone.\n   * This will run in `testProxyZone`. The `testProxyZone` will be reset by the `ZoneQueueRunner`\n   */\n  function wrapTestInZone(testBody) {\n    // The `done` callback is only passed through if the function expects at least one argument.\n    // Note we have to make a function with correct number of arguments, otherwise jasmine will\n    // think that all functions are sync or async.\n    return testBody && (testBody.length ? function (done) {\n      return runInTestZone(testBody, this, this.queueRunner, done);\n    } : function () {\n      return runInTestZone(testBody, this, this.queueRunner);\n    });\n  }\n  const QueueRunner = jasmine.QueueRunner;\n  jasmine.QueueRunner = function (_super) {\n    __extends(ZoneQueueRunner, _super);\n    function ZoneQueueRunner(attrs) {\n      if (attrs.onComplete) {\n        attrs.onComplete = (fn => () => {\n          // All functions are done, clear the test zone.\n          this.testProxyZone = null;\n          this.testProxyZoneSpec = null;\n          ambientZone.scheduleMicroTask('jasmine.onComplete', fn);\n        })(attrs.onComplete);\n      }\n      const nativeSetTimeout = global[Zone.__symbol__('setTimeout')];\n      const nativeClearTimeout = global[Zone.__symbol__('clearTimeout')];\n      if (nativeSetTimeout) {\n        // should run setTimeout inside jasmine outside of zone\n        attrs.timeout = {\n          setTimeout: nativeSetTimeout ? nativeSetTimeout : global.setTimeout,\n          clearTimeout: nativeClearTimeout ? nativeClearTimeout : global.clearTimeout\n        };\n      }\n      // create a userContext to hold the queueRunner itself\n      // so we can access the testProxy in it/xit/beforeEach ...\n      if (jasmine.UserContext) {\n        if (!attrs.userContext) {\n          attrs.userContext = new jasmine.UserContext();\n        }\n        attrs.userContext.queueRunner = this;\n      } else {\n        if (!attrs.userContext) {\n          attrs.userContext = {};\n        }\n        attrs.userContext.queueRunner = this;\n      }\n      // patch attrs.onException\n      const onException = attrs.onException;\n      attrs.onException = function (error) {\n        if (error && error.message === 'Timeout - Async callback was not invoked within timeout specified by jasmine.DEFAULT_TIMEOUT_INTERVAL.') {\n          // jasmine timeout, we can make the error message more\n          // reasonable to tell what tasks are pending\n          const proxyZoneSpec = this && this.testProxyZoneSpec;\n          if (proxyZoneSpec) {\n            const pendingTasksInfo = proxyZoneSpec.getAndClearPendingTasksInfo();\n            try {\n              // try catch here in case error.message is not writable\n              error.message += pendingTasksInfo;\n            } catch (err) {}\n          }\n        }\n        if (onException) {\n          onException.call(this, error);\n        }\n      };\n      _super.call(this, attrs);\n    }\n    ZoneQueueRunner.prototype.execute = function () {\n      let zone = Zone.current;\n      let isChildOfAmbientZone = false;\n      while (zone) {\n        if (zone === ambientZone) {\n          isChildOfAmbientZone = true;\n          break;\n        }\n        zone = zone.parent;\n      }\n      if (!isChildOfAmbientZone) throw new Error('Unexpected Zone: ' + Zone.current.name);\n      // This is the zone which will be used for running individual tests.\n      // It will be a proxy zone, so that the tests function can retroactively install\n      // different zones.\n      // Example:\n      //   - In beforeEach() do childZone = Zone.current.fork(...);\n      //   - In it() try to do fakeAsync(). The issue is that because the beforeEach forked the\n      //     zone outside of fakeAsync it will be able to escape the fakeAsync rules.\n      //   - Because ProxyZone is parent fo `childZone` fakeAsync can retroactively add\n      //     fakeAsync behavior to the childZone.\n      this.testProxyZoneSpec = new ProxyZoneSpec();\n      this.testProxyZone = ambientZone.fork(this.testProxyZoneSpec);\n      if (!Zone.currentTask) {\n        // if we are not running in a task then if someone would register a\n        // element.addEventListener and then calling element.click() the\n        // addEventListener callback would think that it is the top most task and would\n        // drain the microtask queue on element.click() which would be incorrect.\n        // For this reason we always force a task when running jasmine tests.\n        Zone.current.scheduleMicroTask('jasmine.execute().forceTask', () => QueueRunner.prototype.execute.call(this));\n      } else {\n        _super.prototype.execute.call(this);\n      }\n    };\n    return ZoneQueueRunner;\n  }(QueueRunner);\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nZone.__load_patch('jest', (context, Zone, api) => {\n  if (typeof jest === 'undefined' || jest['__zone_patch__']) {\n    return;\n  }\n  jest['__zone_patch__'] = true;\n  const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n  const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n  if (!ProxyZoneSpec) {\n    throw new Error('Missing ProxyZoneSpec');\n  }\n  const rootZone = Zone.current;\n  const syncZone = rootZone.fork(new SyncTestZoneSpec('jest.describe'));\n  const proxyZoneSpec = new ProxyZoneSpec();\n  const proxyZone = rootZone.fork(proxyZoneSpec);\n  function wrapDescribeFactoryInZone(originalJestFn) {\n    return function (...tableArgs) {\n      const originalDescribeFn = originalJestFn.apply(this, tableArgs);\n      return function (...args) {\n        args[1] = wrapDescribeInZone(args[1]);\n        return originalDescribeFn.apply(this, args);\n      };\n    };\n  }\n  function wrapTestFactoryInZone(originalJestFn) {\n    return function (...tableArgs) {\n      return function (...args) {\n        args[1] = wrapTestInZone(args[1]);\n        return originalJestFn.apply(this, tableArgs).apply(this, args);\n      };\n    };\n  }\n  /**\n   * Gets a function wrapping the body of a jest `describe` block to execute in a\n   * synchronous-only zone.\n   */\n  function wrapDescribeInZone(describeBody) {\n    return function (...args) {\n      return syncZone.run(describeBody, this, args);\n    };\n  }\n  /**\n   * Gets a function wrapping the body of a jest `it/beforeEach/afterEach` block to\n   * execute in a ProxyZone zone.\n   * This will run in the `proxyZone`.\n   */\n  function wrapTestInZone(testBody, isTestFunc = false) {\n    if (typeof testBody !== 'function') {\n      return testBody;\n    }\n    const wrappedFunc = function () {\n      if (Zone[api.symbol('useFakeTimersCalled')] === true && testBody && !testBody.isFakeAsync) {\n        // jest.useFakeTimers is called, run into fakeAsyncTest automatically.\n        const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n        if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n          testBody = fakeAsyncModule.fakeAsync(testBody);\n        }\n      }\n      proxyZoneSpec.isTestFunc = isTestFunc;\n      return proxyZone.run(testBody, null, arguments);\n    };\n    // Update the length of wrappedFunc to be the same as the length of the testBody\n    // So jest core can handle whether the test function has `done()` or not correctly\n    Object.defineProperty(wrappedFunc, 'length', {\n      configurable: true,\n      writable: true,\n      enumerable: false\n    });\n    wrappedFunc.length = testBody.length;\n    return wrappedFunc;\n  }\n  ['describe', 'xdescribe', 'fdescribe'].forEach(methodName => {\n    let originalJestFn = context[methodName];\n    if (context[Zone.__symbol__(methodName)]) {\n      return;\n    }\n    context[Zone.__symbol__(methodName)] = originalJestFn;\n    context[methodName] = function (...args) {\n      args[1] = wrapDescribeInZone(args[1]);\n      return originalJestFn.apply(this, args);\n    };\n    context[methodName].each = wrapDescribeFactoryInZone(originalJestFn.each);\n  });\n  context.describe.only = context.fdescribe;\n  context.describe.skip = context.xdescribe;\n  ['it', 'xit', 'fit', 'test', 'xtest'].forEach(methodName => {\n    let originalJestFn = context[methodName];\n    if (context[Zone.__symbol__(methodName)]) {\n      return;\n    }\n    context[Zone.__symbol__(methodName)] = originalJestFn;\n    context[methodName] = function (...args) {\n      args[1] = wrapTestInZone(args[1], true);\n      return originalJestFn.apply(this, args);\n    };\n    context[methodName].each = wrapTestFactoryInZone(originalJestFn.each);\n    context[methodName].todo = originalJestFn.todo;\n  });\n  context.it.only = context.fit;\n  context.it.skip = context.xit;\n  context.test.only = context.fit;\n  context.test.skip = context.xit;\n  ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach(methodName => {\n    let originalJestFn = context[methodName];\n    if (context[Zone.__symbol__(methodName)]) {\n      return;\n    }\n    context[Zone.__symbol__(methodName)] = originalJestFn;\n    context[methodName] = function (...args) {\n      args[0] = wrapTestInZone(args[0]);\n      return originalJestFn.apply(this, args);\n    };\n  });\n  Zone.patchJestObject = function patchJestObject(Timer, isModern = false) {\n    // check whether currently the test is inside fakeAsync()\n    function isPatchingFakeTimer() {\n      const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n      return !!fakeAsyncZoneSpec;\n    }\n    // check whether the current function is inside `test/it` or other methods\n    // such as `describe/beforeEach`\n    function isInTestFunc() {\n      const proxyZoneSpec = Zone.current.get('ProxyZoneSpec');\n      return proxyZoneSpec && proxyZoneSpec.isTestFunc;\n    }\n    if (Timer[api.symbol('fakeTimers')]) {\n      return;\n    }\n    Timer[api.symbol('fakeTimers')] = true;\n    // patch jest fakeTimer internal method to make sure no console.warn print out\n    api.patchMethod(Timer, '_checkFakeTimers', delegate => {\n      return function (self, args) {\n        if (isPatchingFakeTimer()) {\n          return true;\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch useFakeTimers(), set useFakeTimersCalled flag, and make test auto run into fakeAsync\n    api.patchMethod(Timer, 'useFakeTimers', delegate => {\n      return function (self, args) {\n        Zone[api.symbol('useFakeTimersCalled')] = true;\n        if (isModern || isInTestFunc()) {\n          return delegate.apply(self, args);\n        }\n        return self;\n      };\n    });\n    // patch useRealTimers(), unset useFakeTimers flag\n    api.patchMethod(Timer, 'useRealTimers', delegate => {\n      return function (self, args) {\n        Zone[api.symbol('useFakeTimersCalled')] = false;\n        if (isModern || isInTestFunc()) {\n          return delegate.apply(self, args);\n        }\n        return self;\n      };\n    });\n    // patch setSystemTime(), call setCurrentRealTime() in the fakeAsyncTest\n    api.patchMethod(Timer, 'setSystemTime', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n          fakeAsyncZoneSpec.setFakeBaseSystemTime(args[0]);\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch getSystemTime(), call getCurrentRealTime() in the fakeAsyncTest\n    api.patchMethod(Timer, 'getRealSystemTime', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n          return fakeAsyncZoneSpec.getRealSystemTime();\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch runAllTicks(), run all microTasks inside fakeAsync\n    api.patchMethod(Timer, 'runAllTicks', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec) {\n          fakeAsyncZoneSpec.flushMicrotasks();\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch runAllTimers(), run all macroTasks inside fakeAsync\n    api.patchMethod(Timer, 'runAllTimers', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec) {\n          fakeAsyncZoneSpec.flush(100, true);\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch advanceTimersByTime(), call tick() in the fakeAsyncTest\n    api.patchMethod(Timer, 'advanceTimersByTime', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec) {\n          fakeAsyncZoneSpec.tick(args[0]);\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch runOnlyPendingTimers(), call flushOnlyPendingTimers() in the fakeAsyncTest\n    api.patchMethod(Timer, 'runOnlyPendingTimers', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec) {\n          fakeAsyncZoneSpec.flushOnlyPendingTimers();\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch advanceTimersToNextTimer(), call tickToNext() in the fakeAsyncTest\n    api.patchMethod(Timer, 'advanceTimersToNextTimer', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec) {\n          fakeAsyncZoneSpec.tickToNext(args[0]);\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch clearAllTimers(), call removeAllTimers() in the fakeAsyncTest\n    api.patchMethod(Timer, 'clearAllTimers', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec) {\n          fakeAsyncZoneSpec.removeAllTimers();\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch getTimerCount(), call getTimerCount() in the fakeAsyncTest\n    api.patchMethod(Timer, 'getTimerCount', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec) {\n          return fakeAsyncZoneSpec.getTimerCount();\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n  };\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nZone.__load_patch('mocha', (global, Zone) => {\n  const Mocha = global.Mocha;\n  if (typeof Mocha === 'undefined') {\n    // return if Mocha is not available, because now zone-testing\n    // will load mocha patch with jasmine/jest patch\n    return;\n  }\n  if (typeof Zone === 'undefined') {\n    throw new Error('Missing Zone.js');\n  }\n  const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n  const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n  if (!ProxyZoneSpec) {\n    throw new Error('Missing ProxyZoneSpec');\n  }\n  if (Mocha['__zone_patch__']) {\n    throw new Error('\"Mocha\" has already been patched with \"Zone\".');\n  }\n  Mocha['__zone_patch__'] = true;\n  const rootZone = Zone.current;\n  const syncZone = rootZone.fork(new SyncTestZoneSpec('Mocha.describe'));\n  let testZone = null;\n  const suiteZone = rootZone.fork(new ProxyZoneSpec());\n  const mochaOriginal = {\n    after: global.after,\n    afterEach: global.afterEach,\n    before: global.before,\n    beforeEach: global.beforeEach,\n    describe: global.describe,\n    it: global.it\n  };\n  function modifyArguments(args, syncTest, asyncTest) {\n    for (let i = 0; i < args.length; i++) {\n      let arg = args[i];\n      if (typeof arg === 'function') {\n        // The `done` callback is only passed through if the function expects at\n        // least one argument.\n        // Note we have to make a function with correct number of arguments,\n        // otherwise mocha will\n        // think that all functions are sync or async.\n        args[i] = arg.length === 0 ? syncTest(arg) : asyncTest(arg);\n        // Mocha uses toString to view the test body in the result list, make sure we return the\n        // correct function body\n        args[i].toString = function () {\n          return arg.toString();\n        };\n      }\n    }\n    return args;\n  }\n  function wrapDescribeInZone(args) {\n    const syncTest = function (fn) {\n      return function () {\n        return syncZone.run(fn, this, arguments);\n      };\n    };\n    return modifyArguments(args, syncTest);\n  }\n  function wrapTestInZone(args) {\n    const asyncTest = function (fn) {\n      return function (done) {\n        return testZone.run(fn, this, [done]);\n      };\n    };\n    const syncTest = function (fn) {\n      return function () {\n        return testZone.run(fn, this);\n      };\n    };\n    return modifyArguments(args, syncTest, asyncTest);\n  }\n  function wrapSuiteInZone(args) {\n    const asyncTest = function (fn) {\n      return function (done) {\n        return suiteZone.run(fn, this, [done]);\n      };\n    };\n    const syncTest = function (fn) {\n      return function () {\n        return suiteZone.run(fn, this);\n      };\n    };\n    return modifyArguments(args, syncTest, asyncTest);\n  }\n  global.describe = global.suite = function () {\n    return mochaOriginal.describe.apply(this, wrapDescribeInZone(arguments));\n  };\n  global.xdescribe = global.suite.skip = function () {\n    return mochaOriginal.describe.skip.apply(this, wrapDescribeInZone(arguments));\n  };\n  global.describe.only = global.suite.only = function () {\n    return mochaOriginal.describe.only.apply(this, wrapDescribeInZone(arguments));\n  };\n  global.it = global.specify = global.test = function () {\n    return mochaOriginal.it.apply(this, wrapTestInZone(arguments));\n  };\n  global.xit = global.xspecify = function () {\n    return mochaOriginal.it.skip.apply(this, wrapTestInZone(arguments));\n  };\n  global.it.only = global.test.only = function () {\n    return mochaOriginal.it.only.apply(this, wrapTestInZone(arguments));\n  };\n  global.after = global.suiteTeardown = function () {\n    return mochaOriginal.after.apply(this, wrapSuiteInZone(arguments));\n  };\n  global.afterEach = global.teardown = function () {\n    return mochaOriginal.afterEach.apply(this, wrapTestInZone(arguments));\n  };\n  global.before = global.suiteSetup = function () {\n    return mochaOriginal.before.apply(this, wrapSuiteInZone(arguments));\n  };\n  global.beforeEach = global.setup = function () {\n    return mochaOriginal.beforeEach.apply(this, wrapTestInZone(arguments));\n  };\n  ((originalRunTest, originalRun) => {\n    Mocha.Runner.prototype.runTest = function (fn) {\n      Zone.current.scheduleMicroTask('mocha.forceTask', () => {\n        originalRunTest.call(this, fn);\n      });\n    };\n    Mocha.Runner.prototype.run = function (fn) {\n      this.on('test', e => {\n        testZone = rootZone.fork(new ProxyZoneSpec());\n      });\n      this.on('fail', (test, err) => {\n        const proxyZoneSpec = testZone && testZone.get('ProxyZoneSpec');\n        if (proxyZoneSpec && err) {\n          try {\n            // try catch here in case err.message is not writable\n            err.message += proxyZoneSpec.getAndClearPendingTasksInfo();\n          } catch (error) {}\n        }\n      });\n      return originalRun.call(this, fn);\n    };\n  })(Mocha.Runner.prototype.runTest, Mocha.Runner.prototype.run);\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (_global) {\n  class AsyncTestZoneSpec {\n    constructor(finishCallback, failCallback, namePrefix) {\n      this.finishCallback = finishCallback;\n      this.failCallback = failCallback;\n      this._pendingMicroTasks = false;\n      this._pendingMacroTasks = false;\n      this._alreadyErrored = false;\n      this._isSync = false;\n      this._existingFinishTimer = null;\n      this.entryFunction = null;\n      this.runZone = Zone.current;\n      this.unresolvedChainedPromiseCount = 0;\n      this.supportWaitUnresolvedChainedPromise = false;\n      this.name = 'asyncTestZone for ' + namePrefix;\n      this.properties = {\n        'AsyncTestZoneSpec': this\n      };\n      this.supportWaitUnresolvedChainedPromise = _global[Zone.__symbol__('supportWaitUnResolvedChainedPromise')] === true;\n    }\n    isUnresolvedChainedPromisePending() {\n      return this.unresolvedChainedPromiseCount > 0;\n    }\n    _finishCallbackIfDone() {\n      // NOTE: Technically the `onHasTask` could fire together with the initial synchronous\n      // completion in `onInvoke`. `onHasTask` might call this method when it captured e.g.\n      // microtasks in the proxy zone that now complete as part of this async zone run.\n      // Consider the following scenario:\n      //    1. A test `beforeEach` schedules a microtask in the ProxyZone.\n      //    2. An actual empty `it` spec executes in the AsyncTestZone` (using e.g. `waitForAsync`).\n      //    3. The `onInvoke` invokes `_finishCallbackIfDone` because the spec runs synchronously.\n      //    4. We wait the scheduled timeout (see below) to account for unhandled promises.\n      //    5. The microtask from (1) finishes and `onHasTask` is invoked.\n      //    --> We register a second `_finishCallbackIfDone` even though we have scheduled a timeout.\n      // If the finish timeout from below is already scheduled, terminate the existing scheduled\n      // finish invocation, avoiding calling `jasmine` `done` multiple times. *Note* that we would\n      // want to schedule a new finish callback in case the task state changes again.\n      if (this._existingFinishTimer !== null) {\n        clearTimeout(this._existingFinishTimer);\n        this._existingFinishTimer = null;\n      }\n      if (!(this._pendingMicroTasks || this._pendingMacroTasks || this.supportWaitUnresolvedChainedPromise && this.isUnresolvedChainedPromisePending())) {\n        // We wait until the next tick because we would like to catch unhandled promises which could\n        // cause test logic to be executed. In such cases we cannot finish with tasks pending then.\n        this.runZone.run(() => {\n          this._existingFinishTimer = setTimeout(() => {\n            if (!this._alreadyErrored && !(this._pendingMicroTasks || this._pendingMacroTasks)) {\n              this.finishCallback();\n            }\n          }, 0);\n        });\n      }\n    }\n    patchPromiseForTest() {\n      if (!this.supportWaitUnresolvedChainedPromise) {\n        return;\n      }\n      const patchPromiseForTest = Promise[Zone.__symbol__('patchPromiseForTest')];\n      if (patchPromiseForTest) {\n        patchPromiseForTest();\n      }\n    }\n    unPatchPromiseForTest() {\n      if (!this.supportWaitUnresolvedChainedPromise) {\n        return;\n      }\n      const unPatchPromiseForTest = Promise[Zone.__symbol__('unPatchPromiseForTest')];\n      if (unPatchPromiseForTest) {\n        unPatchPromiseForTest();\n      }\n    }\n    onScheduleTask(delegate, current, target, task) {\n      if (task.type !== 'eventTask') {\n        this._isSync = false;\n      }\n      if (task.type === 'microTask' && task.data && task.data instanceof Promise) {\n        // check whether the promise is a chained promise\n        if (task.data[AsyncTestZoneSpec.symbolParentUnresolved] === true) {\n          // chained promise is being scheduled\n          this.unresolvedChainedPromiseCount--;\n        }\n      }\n      return delegate.scheduleTask(target, task);\n    }\n    onInvokeTask(delegate, current, target, task, applyThis, applyArgs) {\n      if (task.type !== 'eventTask') {\n        this._isSync = false;\n      }\n      return delegate.invokeTask(target, task, applyThis, applyArgs);\n    }\n    onCancelTask(delegate, current, target, task) {\n      if (task.type !== 'eventTask') {\n        this._isSync = false;\n      }\n      return delegate.cancelTask(target, task);\n    }\n    // Note - we need to use onInvoke at the moment to call finish when a test is\n    // fully synchronous. TODO(juliemr): remove this when the logic for\n    // onHasTask changes and it calls whenever the task queues are dirty.\n    // updated by(JiaLiPassion), only call finish callback when no task\n    // was scheduled/invoked/canceled.\n    onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n      if (!this.entryFunction) {\n        this.entryFunction = delegate;\n      }\n      try {\n        this._isSync = true;\n        return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n      } finally {\n        // We need to check the delegate is the same as entryFunction or not.\n        // Consider the following case.\n        //\n        // asyncTestZone.run(() => { // Here the delegate will be the entryFunction\n        //   Zone.current.run(() => { // Here the delegate will not be the entryFunction\n        //   });\n        // });\n        //\n        // We only want to check whether there are async tasks scheduled\n        // for the entry function.\n        if (this._isSync && this.entryFunction === delegate) {\n          this._finishCallbackIfDone();\n        }\n      }\n    }\n    onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n      // Let the parent try to handle the error.\n      const result = parentZoneDelegate.handleError(targetZone, error);\n      if (result) {\n        this.failCallback(error);\n        this._alreadyErrored = true;\n      }\n      return false;\n    }\n    onHasTask(delegate, current, target, hasTaskState) {\n      delegate.hasTask(target, hasTaskState);\n      // We should only trigger finishCallback when the target zone is the AsyncTestZone\n      // Consider the following cases.\n      //\n      // const childZone = asyncTestZone.fork({\n      //   name: 'child',\n      //   onHasTask: ...\n      // });\n      //\n      // So we have nested zones declared the onHasTask hook, in this case,\n      // the onHasTask will be triggered twice, and cause the finishCallbackIfDone()\n      // is also be invoked twice. So we need to only trigger the finishCallbackIfDone()\n      // when the current zone is the same as the target zone.\n      if (current !== target) {\n        return;\n      }\n      if (hasTaskState.change == 'microTask') {\n        this._pendingMicroTasks = hasTaskState.microTask;\n        this._finishCallbackIfDone();\n      } else if (hasTaskState.change == 'macroTask') {\n        this._pendingMacroTasks = hasTaskState.macroTask;\n        this._finishCallbackIfDone();\n      }\n    }\n  }\n  AsyncTestZoneSpec.symbolParentUnresolved = Zone.__symbol__('parentUnresolved');\n  // Export the class so that new instances can be created with proper\n  // constructor params.\n  Zone['AsyncTestZoneSpec'] = AsyncTestZoneSpec;\n})(typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global);\nZone.__load_patch('asynctest', (global, Zone, api) => {\n  /**\n   * Wraps a test function in an asynchronous test zone. The test will automatically\n   * complete when all asynchronous calls within this zone are done.\n   */\n  Zone[api.symbol('asyncTest')] = function asyncTest(fn) {\n    // If we're running using the Jasmine test framework, adapt to call the 'done'\n    // function when asynchronous activity is finished.\n    if (global.jasmine) {\n      // Not using an arrow function to preserve context passed from call site\n      return function (done) {\n        if (!done) {\n          // if we run beforeEach in @angular/core/testing/testing_internal then we get no done\n          // fake it here and assume sync.\n          done = function () {};\n          done.fail = function (e) {\n            throw e;\n          };\n        }\n        runInTestZone(fn, this, done, err => {\n          if (typeof err === 'string') {\n            return done.fail(new Error(err));\n          } else {\n            done.fail(err);\n          }\n        });\n      };\n    }\n    // Otherwise, return a promise which will resolve when asynchronous activity\n    // is finished. This will be correctly consumed by the Mocha framework with\n    // it('...', async(myFn)); or can be used in a custom framework.\n    // Not using an arrow function to preserve context passed from call site\n    return function () {\n      return new Promise((finishCallback, failCallback) => {\n        runInTestZone(fn, this, finishCallback, failCallback);\n      });\n    };\n  };\n  function runInTestZone(fn, context, finishCallback, failCallback) {\n    const currentZone = Zone.current;\n    const AsyncTestZoneSpec = Zone['AsyncTestZoneSpec'];\n    if (AsyncTestZoneSpec === undefined) {\n      throw new Error('AsyncTestZoneSpec is needed for the async() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js/plugins/async-test');\n    }\n    const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n    if (!ProxyZoneSpec) {\n      throw new Error('ProxyZoneSpec is needed for the async() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js/plugins/proxy');\n    }\n    const proxyZoneSpec = ProxyZoneSpec.get();\n    ProxyZoneSpec.assertPresent();\n    // We need to create the AsyncTestZoneSpec outside the ProxyZone.\n    // If we do it in ProxyZone then we will get to infinite recursion.\n    const proxyZone = Zone.current.getZoneWith('ProxyZoneSpec');\n    const previousDelegate = proxyZoneSpec.getDelegate();\n    proxyZone.parent.run(() => {\n      const testZoneSpec = new AsyncTestZoneSpec(() => {\n        // Need to restore the original zone.\n        if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n          // Only reset the zone spec if it's\n          // still this one. Otherwise, assume\n          // it's OK.\n          proxyZoneSpec.setDelegate(previousDelegate);\n        }\n        testZoneSpec.unPatchPromiseForTest();\n        currentZone.run(() => {\n          finishCallback();\n        });\n      }, error => {\n        // Need to restore the original zone.\n        if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n          // Only reset the zone spec if it's sill this one. Otherwise, assume it's OK.\n          proxyZoneSpec.setDelegate(previousDelegate);\n        }\n        testZoneSpec.unPatchPromiseForTest();\n        currentZone.run(() => {\n          failCallback(error);\n        });\n      }, 'test');\n      proxyZoneSpec.setDelegate(testZoneSpec);\n      testZoneSpec.patchPromiseForTest();\n    });\n    return Zone.current.runGuarded(fn, context);\n  }\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (global) {\n  const OriginalDate = global.Date;\n  // Since when we compile this file to `es2015`, and if we define\n  // this `FakeDate` as `class FakeDate`, and then set `FakeDate.prototype`\n  // there will be an error which is `Cannot assign to read only property 'prototype'`\n  // so we need to use function implementation here.\n  function FakeDate() {\n    if (arguments.length === 0) {\n      const d = new OriginalDate();\n      d.setTime(FakeDate.now());\n      return d;\n    } else {\n      const args = Array.prototype.slice.call(arguments);\n      return new OriginalDate(...args);\n    }\n  }\n  FakeDate.now = function () {\n    const fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n    if (fakeAsyncTestZoneSpec) {\n      return fakeAsyncTestZoneSpec.getFakeSystemTime();\n    }\n    return OriginalDate.now.apply(this, arguments);\n  };\n  FakeDate.UTC = OriginalDate.UTC;\n  FakeDate.parse = OriginalDate.parse;\n  // keep a reference for zone patched timer function\n  const timers = {\n    setTimeout: global.setTimeout,\n    setInterval: global.setInterval,\n    clearTimeout: global.clearTimeout,\n    clearInterval: global.clearInterval\n  };\n  class Scheduler {\n    constructor() {\n      // Scheduler queue with the tuple of end time and callback function - sorted by end time.\n      this._schedulerQueue = [];\n      // Current simulated time in millis.\n      this._currentTickTime = 0;\n      // Current fake system base time in millis.\n      this._currentFakeBaseSystemTime = OriginalDate.now();\n      // track requeuePeriodicTimer\n      this._currentTickRequeuePeriodicEntries = [];\n    }\n    getCurrentTickTime() {\n      return this._currentTickTime;\n    }\n    getFakeSystemTime() {\n      return this._currentFakeBaseSystemTime + this._currentTickTime;\n    }\n    setFakeBaseSystemTime(fakeBaseSystemTime) {\n      this._currentFakeBaseSystemTime = fakeBaseSystemTime;\n    }\n    getRealSystemTime() {\n      return OriginalDate.now();\n    }\n    scheduleFunction(cb, delay, options) {\n      options = Object.assign({\n        args: [],\n        isPeriodic: false,\n        isRequestAnimationFrame: false,\n        id: -1,\n        isRequeuePeriodic: false\n      }, options);\n      let currentId = options.id < 0 ? Scheduler.nextId++ : options.id;\n      let endTime = this._currentTickTime + delay;\n      // Insert so that scheduler queue remains sorted by end time.\n      let newEntry = {\n        endTime: endTime,\n        id: currentId,\n        func: cb,\n        args: options.args,\n        delay: delay,\n        isPeriodic: options.isPeriodic,\n        isRequestAnimationFrame: options.isRequestAnimationFrame\n      };\n      if (options.isRequeuePeriodic) {\n        this._currentTickRequeuePeriodicEntries.push(newEntry);\n      }\n      let i = 0;\n      for (; i < this._schedulerQueue.length; i++) {\n        let currentEntry = this._schedulerQueue[i];\n        if (newEntry.endTime < currentEntry.endTime) {\n          break;\n        }\n      }\n      this._schedulerQueue.splice(i, 0, newEntry);\n      return currentId;\n    }\n    removeScheduledFunctionWithId(id) {\n      for (let i = 0; i < this._schedulerQueue.length; i++) {\n        if (this._schedulerQueue[i].id == id) {\n          this._schedulerQueue.splice(i, 1);\n          break;\n        }\n      }\n    }\n    removeAll() {\n      this._schedulerQueue = [];\n    }\n    getTimerCount() {\n      return this._schedulerQueue.length;\n    }\n    tickToNext(step = 1, doTick, tickOptions) {\n      if (this._schedulerQueue.length < step) {\n        return;\n      }\n      // Find the last task currently queued in the scheduler queue and tick\n      // till that time.\n      const startTime = this._currentTickTime;\n      const targetTask = this._schedulerQueue[step - 1];\n      this.tick(targetTask.endTime - startTime, doTick, tickOptions);\n    }\n    tick(millis = 0, doTick, tickOptions) {\n      let finalTime = this._currentTickTime + millis;\n      let lastCurrentTime = 0;\n      tickOptions = Object.assign({\n        processNewMacroTasksSynchronously: true\n      }, tickOptions);\n      // we need to copy the schedulerQueue so nested timeout\n      // will not be wrongly called in the current tick\n      // https://github.com/angular/angular/issues/33799\n      const schedulerQueue = tickOptions.processNewMacroTasksSynchronously ? this._schedulerQueue : this._schedulerQueue.slice();\n      if (schedulerQueue.length === 0 && doTick) {\n        doTick(millis);\n        return;\n      }\n      while (schedulerQueue.length > 0) {\n        // clear requeueEntries before each loop\n        this._currentTickRequeuePeriodicEntries = [];\n        let current = schedulerQueue[0];\n        if (finalTime < current.endTime) {\n          // Done processing the queue since it's sorted by endTime.\n          break;\n        } else {\n          // Time to run scheduled function. Remove it from the head of queue.\n          let current = schedulerQueue.shift();\n          if (!tickOptions.processNewMacroTasksSynchronously) {\n            const idx = this._schedulerQueue.indexOf(current);\n            if (idx >= 0) {\n              this._schedulerQueue.splice(idx, 1);\n            }\n          }\n          lastCurrentTime = this._currentTickTime;\n          this._currentTickTime = current.endTime;\n          if (doTick) {\n            doTick(this._currentTickTime - lastCurrentTime);\n          }\n          let retval = current.func.apply(global, current.isRequestAnimationFrame ? [this._currentTickTime] : current.args);\n          if (!retval) {\n            // Uncaught exception in the current scheduled function. Stop processing the queue.\n            break;\n          }\n          // check is there any requeue periodic entry is added in\n          // current loop, if there is, we need to add to current loop\n          if (!tickOptions.processNewMacroTasksSynchronously) {\n            this._currentTickRequeuePeriodicEntries.forEach(newEntry => {\n              let i = 0;\n              for (; i < schedulerQueue.length; i++) {\n                const currentEntry = schedulerQueue[i];\n                if (newEntry.endTime < currentEntry.endTime) {\n                  break;\n                }\n              }\n              schedulerQueue.splice(i, 0, newEntry);\n            });\n          }\n        }\n      }\n      lastCurrentTime = this._currentTickTime;\n      this._currentTickTime = finalTime;\n      if (doTick) {\n        doTick(this._currentTickTime - lastCurrentTime);\n      }\n    }\n    flushOnlyPendingTimers(doTick) {\n      if (this._schedulerQueue.length === 0) {\n        return 0;\n      }\n      // Find the last task currently queued in the scheduler queue and tick\n      // till that time.\n      const startTime = this._currentTickTime;\n      const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n      this.tick(lastTask.endTime - startTime, doTick, {\n        processNewMacroTasksSynchronously: false\n      });\n      return this._currentTickTime - startTime;\n    }\n    flush(limit = 20, flushPeriodic = false, doTick) {\n      if (flushPeriodic) {\n        return this.flushPeriodic(doTick);\n      } else {\n        return this.flushNonPeriodic(limit, doTick);\n      }\n    }\n    flushPeriodic(doTick) {\n      if (this._schedulerQueue.length === 0) {\n        return 0;\n      }\n      // Find the last task currently queued in the scheduler queue and tick\n      // till that time.\n      const startTime = this._currentTickTime;\n      const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n      this.tick(lastTask.endTime - startTime, doTick);\n      return this._currentTickTime - startTime;\n    }\n    flushNonPeriodic(limit, doTick) {\n      const startTime = this._currentTickTime;\n      let lastCurrentTime = 0;\n      let count = 0;\n      while (this._schedulerQueue.length > 0) {\n        count++;\n        if (count > limit) {\n          throw new Error('flush failed after reaching the limit of ' + limit + ' tasks. Does your code use a polling timeout?');\n        }\n        // flush only non-periodic timers.\n        // If the only remaining tasks are periodic(or requestAnimationFrame), finish flushing.\n        if (this._schedulerQueue.filter(task => !task.isPeriodic && !task.isRequestAnimationFrame).length === 0) {\n          break;\n        }\n        const current = this._schedulerQueue.shift();\n        lastCurrentTime = this._currentTickTime;\n        this._currentTickTime = current.endTime;\n        if (doTick) {\n          // Update any secondary schedulers like Jasmine mock Date.\n          doTick(this._currentTickTime - lastCurrentTime);\n        }\n        const retval = current.func.apply(global, current.args);\n        if (!retval) {\n          // Uncaught exception in the current scheduled function. Stop processing the queue.\n          break;\n        }\n      }\n      return this._currentTickTime - startTime;\n    }\n  }\n  // Next scheduler id.\n  Scheduler.nextId = 1;\n  class FakeAsyncTestZoneSpec {\n    constructor(namePrefix, trackPendingRequestAnimationFrame = false, macroTaskOptions) {\n      this.trackPendingRequestAnimationFrame = trackPendingRequestAnimationFrame;\n      this.macroTaskOptions = macroTaskOptions;\n      this._scheduler = new Scheduler();\n      this._microtasks = [];\n      this._lastError = null;\n      this._uncaughtPromiseErrors = Promise[Zone.__symbol__('uncaughtPromiseErrors')];\n      this.pendingPeriodicTimers = [];\n      this.pendingTimers = [];\n      this.patchDateLocked = false;\n      this.properties = {\n        'FakeAsyncTestZoneSpec': this\n      };\n      this.name = 'fakeAsyncTestZone for ' + namePrefix;\n      // in case user can't access the construction of FakeAsyncTestSpec\n      // user can also define macroTaskOptions by define a global variable.\n      if (!this.macroTaskOptions) {\n        this.macroTaskOptions = global[Zone.__symbol__('FakeAsyncTestMacroTask')];\n      }\n    }\n    static assertInZone() {\n      if (Zone.current.get('FakeAsyncTestZoneSpec') == null) {\n        throw new Error('The code should be running in the fakeAsync zone to call this function');\n      }\n    }\n    _fnAndFlush(fn, completers) {\n      return (...args) => {\n        fn.apply(global, args);\n        if (this._lastError === null) {\n          // Success\n          if (completers.onSuccess != null) {\n            completers.onSuccess.apply(global);\n          }\n          // Flush microtasks only on success.\n          this.flushMicrotasks();\n        } else {\n          // Failure\n          if (completers.onError != null) {\n            completers.onError.apply(global);\n          }\n        }\n        // Return true if there were no errors, false otherwise.\n        return this._lastError === null;\n      };\n    }\n    static _removeTimer(timers, id) {\n      let index = timers.indexOf(id);\n      if (index > -1) {\n        timers.splice(index, 1);\n      }\n    }\n    _dequeueTimer(id) {\n      return () => {\n        FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n      };\n    }\n    _requeuePeriodicTimer(fn, interval, args, id) {\n      return () => {\n        // Requeue the timer callback if it's not been canceled.\n        if (this.pendingPeriodicTimers.indexOf(id) !== -1) {\n          this._scheduler.scheduleFunction(fn, interval, {\n            args,\n            isPeriodic: true,\n            id,\n            isRequeuePeriodic: true\n          });\n        }\n      };\n    }\n    _dequeuePeriodicTimer(id) {\n      return () => {\n        FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n      };\n    }\n    _setTimeout(fn, delay, args, isTimer = true) {\n      let removeTimerFn = this._dequeueTimer(Scheduler.nextId);\n      // Queue the callback and dequeue the timer on success and error.\n      let cb = this._fnAndFlush(fn, {\n        onSuccess: removeTimerFn,\n        onError: removeTimerFn\n      });\n      let id = this._scheduler.scheduleFunction(cb, delay, {\n        args,\n        isRequestAnimationFrame: !isTimer\n      });\n      if (isTimer) {\n        this.pendingTimers.push(id);\n      }\n      return id;\n    }\n    _clearTimeout(id) {\n      FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n      this._scheduler.removeScheduledFunctionWithId(id);\n    }\n    _setInterval(fn, interval, args) {\n      let id = Scheduler.nextId;\n      let completers = {\n        onSuccess: null,\n        onError: this._dequeuePeriodicTimer(id)\n      };\n      let cb = this._fnAndFlush(fn, completers);\n      // Use the callback created above to requeue on success.\n      completers.onSuccess = this._requeuePeriodicTimer(cb, interval, args, id);\n      // Queue the callback and dequeue the periodic timer only on error.\n      this._scheduler.scheduleFunction(cb, interval, {\n        args,\n        isPeriodic: true\n      });\n      this.pendingPeriodicTimers.push(id);\n      return id;\n    }\n    _clearInterval(id) {\n      FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n      this._scheduler.removeScheduledFunctionWithId(id);\n    }\n    _resetLastErrorAndThrow() {\n      let error = this._lastError || this._uncaughtPromiseErrors[0];\n      this._uncaughtPromiseErrors.length = 0;\n      this._lastError = null;\n      throw error;\n    }\n    getCurrentTickTime() {\n      return this._scheduler.getCurrentTickTime();\n    }\n    getFakeSystemTime() {\n      return this._scheduler.getFakeSystemTime();\n    }\n    setFakeBaseSystemTime(realTime) {\n      this._scheduler.setFakeBaseSystemTime(realTime);\n    }\n    getRealSystemTime() {\n      return this._scheduler.getRealSystemTime();\n    }\n    static patchDate() {\n      if (!!global[Zone.__symbol__('disableDatePatching')]) {\n        // we don't want to patch global Date\n        // because in some case, global Date\n        // is already being patched, we need to provide\n        // an option to let user still use their\n        // own version of Date.\n        return;\n      }\n      if (global['Date'] === FakeDate) {\n        // already patched\n        return;\n      }\n      global['Date'] = FakeDate;\n      FakeDate.prototype = OriginalDate.prototype;\n      // try check and reset timers\n      // because jasmine.clock().install() may\n      // have replaced the global timer\n      FakeAsyncTestZoneSpec.checkTimerPatch();\n    }\n    static resetDate() {\n      if (global['Date'] === FakeDate) {\n        global['Date'] = OriginalDate;\n      }\n    }\n    static checkTimerPatch() {\n      if (global.setTimeout !== timers.setTimeout) {\n        global.setTimeout = timers.setTimeout;\n        global.clearTimeout = timers.clearTimeout;\n      }\n      if (global.setInterval !== timers.setInterval) {\n        global.setInterval = timers.setInterval;\n        global.clearInterval = timers.clearInterval;\n      }\n    }\n    lockDatePatch() {\n      this.patchDateLocked = true;\n      FakeAsyncTestZoneSpec.patchDate();\n    }\n    unlockDatePatch() {\n      this.patchDateLocked = false;\n      FakeAsyncTestZoneSpec.resetDate();\n    }\n    tickToNext(steps = 1, doTick, tickOptions = {\n      processNewMacroTasksSynchronously: true\n    }) {\n      if (steps <= 0) {\n        return;\n      }\n      FakeAsyncTestZoneSpec.assertInZone();\n      this.flushMicrotasks();\n      this._scheduler.tickToNext(steps, doTick, tickOptions);\n      if (this._lastError !== null) {\n        this._resetLastErrorAndThrow();\n      }\n    }\n    tick(millis = 0, doTick, tickOptions = {\n      processNewMacroTasksSynchronously: true\n    }) {\n      FakeAsyncTestZoneSpec.assertInZone();\n      this.flushMicrotasks();\n      this._scheduler.tick(millis, doTick, tickOptions);\n      if (this._lastError !== null) {\n        this._resetLastErrorAndThrow();\n      }\n    }\n    flushMicrotasks() {\n      FakeAsyncTestZoneSpec.assertInZone();\n      const flushErrors = () => {\n        if (this._lastError !== null || this._uncaughtPromiseErrors.length) {\n          // If there is an error stop processing the microtask queue and rethrow the error.\n          this._resetLastErrorAndThrow();\n        }\n      };\n      while (this._microtasks.length > 0) {\n        let microtask = this._microtasks.shift();\n        microtask.func.apply(microtask.target, microtask.args);\n      }\n      flushErrors();\n    }\n    flush(limit, flushPeriodic, doTick) {\n      FakeAsyncTestZoneSpec.assertInZone();\n      this.flushMicrotasks();\n      const elapsed = this._scheduler.flush(limit, flushPeriodic, doTick);\n      if (this._lastError !== null) {\n        this._resetLastErrorAndThrow();\n      }\n      return elapsed;\n    }\n    flushOnlyPendingTimers(doTick) {\n      FakeAsyncTestZoneSpec.assertInZone();\n      this.flushMicrotasks();\n      const elapsed = this._scheduler.flushOnlyPendingTimers(doTick);\n      if (this._lastError !== null) {\n        this._resetLastErrorAndThrow();\n      }\n      return elapsed;\n    }\n    removeAllTimers() {\n      FakeAsyncTestZoneSpec.assertInZone();\n      this._scheduler.removeAll();\n      this.pendingPeriodicTimers = [];\n      this.pendingTimers = [];\n    }\n    getTimerCount() {\n      return this._scheduler.getTimerCount() + this._microtasks.length;\n    }\n    onScheduleTask(delegate, current, target, task) {\n      switch (task.type) {\n        case 'microTask':\n          let args = task.data && task.data.args;\n          // should pass additional arguments to callback if have any\n          // currently we know process.nextTick will have such additional\n          // arguments\n          let additionalArgs;\n          if (args) {\n            let callbackIndex = task.data.cbIdx;\n            if (typeof args.length === 'number' && args.length > callbackIndex + 1) {\n              additionalArgs = Array.prototype.slice.call(args, callbackIndex + 1);\n            }\n          }\n          this._microtasks.push({\n            func: task.invoke,\n            args: additionalArgs,\n            target: task.data && task.data.target\n          });\n          break;\n        case 'macroTask':\n          switch (task.source) {\n            case 'setTimeout':\n              task.data['handleId'] = this._setTimeout(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n              break;\n            case 'setImmediate':\n              task.data['handleId'] = this._setTimeout(task.invoke, 0, Array.prototype.slice.call(task.data['args'], 1));\n              break;\n            case 'setInterval':\n              task.data['handleId'] = this._setInterval(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n              break;\n            case 'XMLHttpRequest.send':\n              throw new Error('Cannot make XHRs from within a fake async test. Request URL: ' + task.data['url']);\n            case 'requestAnimationFrame':\n            case 'webkitRequestAnimationFrame':\n            case 'mozRequestAnimationFrame':\n              // Simulate a requestAnimationFrame by using a setTimeout with 16 ms.\n              // (60 frames per second)\n              task.data['handleId'] = this._setTimeout(task.invoke, 16, task.data['args'], this.trackPendingRequestAnimationFrame);\n              break;\n            default:\n              // user can define which macroTask they want to support by passing\n              // macroTaskOptions\n              const macroTaskOption = this.findMacroTaskOption(task);\n              if (macroTaskOption) {\n                const args = task.data && task.data['args'];\n                const delay = args && args.length > 1 ? args[1] : 0;\n                let callbackArgs = macroTaskOption.callbackArgs ? macroTaskOption.callbackArgs : args;\n                if (!!macroTaskOption.isPeriodic) {\n                  // periodic macroTask, use setInterval to simulate\n                  task.data['handleId'] = this._setInterval(task.invoke, delay, callbackArgs);\n                  task.data.isPeriodic = true;\n                } else {\n                  // not periodic, use setTimeout to simulate\n                  task.data['handleId'] = this._setTimeout(task.invoke, delay, callbackArgs);\n                }\n                break;\n              }\n              throw new Error('Unknown macroTask scheduled in fake async test: ' + task.source);\n          }\n          break;\n        case 'eventTask':\n          task = delegate.scheduleTask(target, task);\n          break;\n      }\n      return task;\n    }\n    onCancelTask(delegate, current, target, task) {\n      switch (task.source) {\n        case 'setTimeout':\n        case 'requestAnimationFrame':\n        case 'webkitRequestAnimationFrame':\n        case 'mozRequestAnimationFrame':\n          return this._clearTimeout(task.data['handleId']);\n        case 'setInterval':\n          return this._clearInterval(task.data['handleId']);\n        default:\n          // user can define which macroTask they want to support by passing\n          // macroTaskOptions\n          const macroTaskOption = this.findMacroTaskOption(task);\n          if (macroTaskOption) {\n            const handleId = task.data['handleId'];\n            return macroTaskOption.isPeriodic ? this._clearInterval(handleId) : this._clearTimeout(handleId);\n          }\n          return delegate.cancelTask(target, task);\n      }\n    }\n    onInvoke(delegate, current, target, callback, applyThis, applyArgs, source) {\n      try {\n        FakeAsyncTestZoneSpec.patchDate();\n        return delegate.invoke(target, callback, applyThis, applyArgs, source);\n      } finally {\n        if (!this.patchDateLocked) {\n          FakeAsyncTestZoneSpec.resetDate();\n        }\n      }\n    }\n    findMacroTaskOption(task) {\n      if (!this.macroTaskOptions) {\n        return null;\n      }\n      for (let i = 0; i < this.macroTaskOptions.length; i++) {\n        const macroTaskOption = this.macroTaskOptions[i];\n        if (macroTaskOption.source === task.source) {\n          return macroTaskOption;\n        }\n      }\n      return null;\n    }\n    onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n      this._lastError = error;\n      return false; // Don't propagate error to parent zone.\n    }\n  }\n  // Export the class so that new instances can be created with proper\n  // constructor params.\n  Zone['FakeAsyncTestZoneSpec'] = FakeAsyncTestZoneSpec;\n})(typeof window === 'object' && window || typeof self === 'object' && self || global);\nZone.__load_patch('fakeasync', (global, Zone, api) => {\n  const FakeAsyncTestZoneSpec = Zone && Zone['FakeAsyncTestZoneSpec'];\n  function getProxyZoneSpec() {\n    return Zone && Zone['ProxyZoneSpec'];\n  }\n  let _fakeAsyncTestZoneSpec = null;\n  /**\n   * Clears out the shared fake async zone for a test.\n   * To be called in a global `beforeEach`.\n   *\n   * @experimental\n   */\n  function resetFakeAsyncZone() {\n    if (_fakeAsyncTestZoneSpec) {\n      _fakeAsyncTestZoneSpec.unlockDatePatch();\n    }\n    _fakeAsyncTestZoneSpec = null;\n    // in node.js testing we may not have ProxyZoneSpec in which case there is nothing to reset.\n    getProxyZoneSpec() && getProxyZoneSpec().assertPresent().resetDelegate();\n  }\n  /**\n   * Wraps a function to be executed in the fakeAsync zone:\n   * - microtasks are manually executed by calling `flushMicrotasks()`,\n   * - timers are synchronous, `tick()` simulates the asynchronous passage of time.\n   *\n   * If there are any pending timers at the end of the function, an exception will be thrown.\n   *\n   * Can be used to wrap inject() calls.\n   *\n   * ## Example\n   *\n   * {@example core/testing/ts/fake_async.ts region='basic'}\n   *\n   * @param fn\n   * @returns The function wrapped to be executed in the fakeAsync zone\n   *\n   * @experimental\n   */\n  function fakeAsync(fn) {\n    // Not using an arrow function to preserve context passed from call site\n    const fakeAsyncFn = function (...args) {\n      const ProxyZoneSpec = getProxyZoneSpec();\n      if (!ProxyZoneSpec) {\n        throw new Error('ProxyZoneSpec is needed for the async() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js/plugins/proxy');\n      }\n      const proxyZoneSpec = ProxyZoneSpec.assertPresent();\n      if (Zone.current.get('FakeAsyncTestZoneSpec')) {\n        throw new Error('fakeAsync() calls can not be nested');\n      }\n      try {\n        // in case jasmine.clock init a fakeAsyncTestZoneSpec\n        if (!_fakeAsyncTestZoneSpec) {\n          if (proxyZoneSpec.getDelegate() instanceof FakeAsyncTestZoneSpec) {\n            throw new Error('fakeAsync() calls can not be nested');\n          }\n          _fakeAsyncTestZoneSpec = new FakeAsyncTestZoneSpec();\n        }\n        let res;\n        const lastProxyZoneSpec = proxyZoneSpec.getDelegate();\n        proxyZoneSpec.setDelegate(_fakeAsyncTestZoneSpec);\n        _fakeAsyncTestZoneSpec.lockDatePatch();\n        try {\n          res = fn.apply(this, args);\n          flushMicrotasks();\n        } finally {\n          proxyZoneSpec.setDelegate(lastProxyZoneSpec);\n        }\n        if (_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length > 0) {\n          throw new Error(`${_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length} ` + `periodic timer(s) still in the queue.`);\n        }\n        if (_fakeAsyncTestZoneSpec.pendingTimers.length > 0) {\n          throw new Error(`${_fakeAsyncTestZoneSpec.pendingTimers.length} timer(s) still in the queue.`);\n        }\n        return res;\n      } finally {\n        resetFakeAsyncZone();\n      }\n    };\n    fakeAsyncFn.isFakeAsync = true;\n    return fakeAsyncFn;\n  }\n  function _getFakeAsyncZoneSpec() {\n    if (_fakeAsyncTestZoneSpec == null) {\n      _fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n      if (_fakeAsyncTestZoneSpec == null) {\n        throw new Error('The code should be running in the fakeAsync zone to call this function');\n      }\n    }\n    return _fakeAsyncTestZoneSpec;\n  }\n  /**\n   * Simulates the asynchronous passage of time for the timers in the fakeAsync zone.\n   *\n   * The microtasks queue is drained at the very start of this function and after any timer callback\n   * has been executed.\n   *\n   * ## Example\n   *\n   * {@example core/testing/ts/fake_async.ts region='basic'}\n   *\n   * @experimental\n   */\n  function tick(millis = 0, ignoreNestedTimeout = false) {\n    _getFakeAsyncZoneSpec().tick(millis, null, ignoreNestedTimeout);\n  }\n  /**\n   * Simulates the asynchronous passage of time for the timers in the fakeAsync zone by\n   * draining the macrotask queue until it is empty. The returned value is the milliseconds\n   * of time that would have been elapsed.\n   *\n   * @param maxTurns\n   * @returns The simulated time elapsed, in millis.\n   *\n   * @experimental\n   */\n  function flush(maxTurns) {\n    return _getFakeAsyncZoneSpec().flush(maxTurns);\n  }\n  /**\n   * Discard all remaining periodic tasks.\n   *\n   * @experimental\n   */\n  function discardPeriodicTasks() {\n    const zoneSpec = _getFakeAsyncZoneSpec();\n    zoneSpec.pendingPeriodicTimers;\n    zoneSpec.pendingPeriodicTimers.length = 0;\n  }\n  /**\n   * Flush any pending microtasks.\n   *\n   * @experimental\n   */\n  function flushMicrotasks() {\n    _getFakeAsyncZoneSpec().flushMicrotasks();\n  }\n  Zone[api.symbol('fakeAsyncTest')] = {\n    resetFakeAsyncZone,\n    flushMicrotasks,\n    discardPeriodicTasks,\n    tick,\n    flush,\n    fakeAsync\n  };\n}, true);\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Promise for async/fakeAsync zoneSpec test\n * can support async operation which not supported by zone.js\n * such as\n * it ('test jsonp in AsyncZone', async() => {\n *   new Promise(res => {\n *     jsonp(url, (data) => {\n *       // success callback\n *       res(data);\n *     });\n *   }).then((jsonpResult) => {\n *     // get jsonp result.\n *\n *     // user will expect AsyncZoneSpec wait for\n *     // then, but because jsonp is not zone aware\n *     // AsyncZone will finish before then is called.\n *   });\n * });\n */\nZone.__load_patch('promisefortest', (global, Zone, api) => {\n  const symbolState = api.symbol('state');\n  const UNRESOLVED = null;\n  const symbolParentUnresolved = api.symbol('parentUnresolved');\n  // patch Promise.prototype.then to keep an internal\n  // number for tracking unresolved chained promise\n  // we will decrease this number when the parent promise\n  // being resolved/rejected and chained promise was\n  // scheduled as a microTask.\n  // so we can know such kind of chained promise still\n  // not resolved in AsyncTestZone\n  Promise[api.symbol('patchPromiseForTest')] = function patchPromiseForTest() {\n    let oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n    if (oriThen) {\n      return;\n    }\n    oriThen = Promise[Zone.__symbol__('ZonePromiseThen')] = Promise.prototype.then;\n    Promise.prototype.then = function () {\n      const chained = oriThen.apply(this, arguments);\n      if (this[symbolState] === UNRESOLVED) {\n        // parent promise is unresolved.\n        const asyncTestZoneSpec = Zone.current.get('AsyncTestZoneSpec');\n        if (asyncTestZoneSpec) {\n          asyncTestZoneSpec.unresolvedChainedPromiseCount++;\n          chained[symbolParentUnresolved] = true;\n        }\n      }\n      return chained;\n    };\n  };\n  Promise[api.symbol('unPatchPromiseForTest')] = function unpatchPromiseForTest() {\n    // restore origin then\n    const oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n    if (oriThen) {\n      Promise.prototype.then = oriThen;\n      Promise[Zone.__symbol__('ZonePromiseThen')] = undefined;\n    }\n  };\n});", "map": {"version": 3, "names": ["NEWLINE", "IGNORE_FRAMES", "creationTrace", "ERROR_TAG", "SEP_TAG", "sepTemplate", "LongStackTrace", "constructor", "error", "getStacktrace", "timestamp", "Date", "getStacktraceWithUncaughtError", "Error", "getStacktraceWithCaughtError", "err", "caughtError", "stack", "getFrames", "split", "addErrorStack", "lines", "trace", "i", "length", "frame", "hasOwnProperty", "push", "renderLongStackTrace", "frames", "longTrace", "trim", "getTime", "traceFrames", "lastTime", "separator", "replace", "join", "stackTracesEnabled", "stackTraceLimit", "Zone", "name", "longStackTraceLimit", "getLongStackTrace", "undefined", "__symbol__", "onScheduleTask", "parentZoneDelegate", "currentZone", "targetZone", "task", "currentTask", "data", "concat", "type", "Object", "assign", "scheduleTask", "onHandleError", "parentTask", "longStack", "handleError", "captureStackTraces", "stackTraces", "count", "computeIgnoreFrames", "frames1", "frames2", "frame1", "indexOf", "match", "frame2", "ProxyZoneSpec", "defaultSpecDelegate", "_delegateSpec", "properties", "propertyKeys", "lastTaskState", "isNeedToTriggerHasTask", "tasks", "setDelegate", "get", "current", "isLoaded", "assertPresent", "delegateSpec", "isNewDelegate", "for<PERSON>ach", "key", "keys", "k", "macroTask", "microTask", "getDelegate", "resetDelegate", "tryTriggerHasTask", "onHasTask", "removeFromTasks", "splice", "getAndClearPendingTasksInfo", "taskInfo", "map", "dataInfo", "source", "pendingTasksInfo", "onFork", "zoneSpec", "fork", "onIntercept", "delegate", "intercept", "onInvoke", "applyThis", "applyArgs", "invoke", "onInvokeTask", "invokeTask", "onCancelTask", "cancelTask", "target", "hasTaskState", "hasTask", "SyncTestZoneSpec", "namePrefix", "runZone", "__load_patch", "global", "api", "__extends", "d", "b", "p", "__", "prototype", "create", "jest", "jasmine", "ambientZone", "symbol", "disablePatchingJasmineClock", "enableAutoFakeAsyncWhenClockPatched", "ignoreUnhandledRejection", "globalErrors", "GlobalErrors", "instance", "originalInstall", "install", "isNode", "process", "on", "originalHandlers", "listeners", "eventListeners", "result", "apply", "arguments", "removeAllListeners", "handler", "addEventListener", "jasmineEnv", "getEnv", "methodName", "originalJasmineFn", "description", "specDefinitions", "call", "wrapDescribeInZone", "timeout", "wrapTestInZone", "originalClockFn", "clock", "originalTick", "tick", "fakeAsyncZoneSpec", "originalMockDate", "mockDate", "dateTime", "setFakeBaseSystemTime", "FakeAsyncTestZoneSpec", "originalCreateSpyObj", "createSpyObj", "args", "Array", "slice", "propertyNames", "spyObj", "defineProperty", "obj", "attributes", "configurable", "enumerable", "describeBody", "syncZone", "run", "runInTestZone", "testBody", "queueRunner", "done", "isClockInstalled", "testProxyZoneSpec", "testProxyZone", "fakeAsyncModule", "fakeAsync", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_super", "ZoneQueueRunner", "attrs", "onComplete", "fn", "scheduleMicroTask", "nativeSetTimeout", "nativeClearTimeout", "setTimeout", "clearTimeout", "UserContext", "userContext", "onException", "message", "proxyZoneSpec", "execute", "zone", "isChildOfAmbientZone", "parent", "context", "rootZone", "proxyZone", "wrapDescribeFactoryInZone", "originalJestFn", "tableArgs", "originalDescribeFn", "wrapTestFactoryInZone", "isTestFunc", "wrappedFunc", "isFakeAsync", "writable", "each", "describe", "only", "fdescribe", "skip", "xdescribe", "todo", "it", "fit", "xit", "test", "patchJestObject", "Timer", "isModern", "isPatchingFakeTimer", "isInTestFunc", "patchMethod", "self", "getRealSystemTime", "flushMicrotasks", "flush", "flushOnlyPendingTimers", "tickToNext", "removeAllTimers", "getTimerCount", "<PERSON><PERSON>", "testZone", "suiteZone", "mochaOriginal", "after", "after<PERSON>ach", "before", "beforeEach", "modifyArguments", "syncTest", "asyncTest", "arg", "toString", "wrapSuiteInZone", "suite", "specify", "xspecify", "suiteTeardown", "teardown", "suiteSetup", "setup", "originalRunTest", "originalRun", "Runner", "runTest", "e", "_global", "AsyncTestZoneSpec", "finishCallback", "fail<PERSON><PERSON>back", "_pendingMicroTasks", "_pendingMacroTasks", "_alreadyErrored", "_isSync", "_existingFinishTimer", "entryFunction", "unresolvedChainedPromiseCount", "supportWaitUnresolvedChainedPromise", "isUnresolvedChainedPromisePending", "_finishCallbackIfDone", "patchPromiseForTest", "Promise", "unPatchPromiseForTest", "symbolParentUnresolved", "change", "window", "fail", "getZoneWith", "previousDelegate", "testZoneSpec", "runGuarded", "OriginalDate", "FakeDate", "setTime", "now", "fakeAsyncTestZoneSpec", "getFakeSystemTime", "UTC", "parse", "timers", "setInterval", "clearInterval", "Scheduler", "_schedulerQueue", "_currentTickTime", "_currentFakeBaseSystemTime", "_currentTickRequeuePeriodicEntries", "getCurrentTickTime", "fakeBaseSystemTime", "scheduleFunction", "cb", "delay", "options", "isPeriodic", "isRequestAnimationFrame", "id", "isRequeuePeriodic", "currentId", "nextId", "endTime", "newEntry", "func", "currentEntry", "removeScheduledFunctionWithId", "removeAll", "step", "doTick", "tickOptions", "startTime", "targetTask", "millis", "finalTime", "lastCurrentTime", "processNewMacroTasksSynchronously", "schedulerQueue", "shift", "idx", "retval", "lastTask", "limit", "flushPeriodic", "flushNonPeriodic", "filter", "trackPendingRequestAnimationFrame", "macroTaskOptions", "_scheduler", "_microtasks", "_lastError", "_uncaughtPromiseErrors", "pendingPeriodicTimers", "pendingTimers", "patchDateLocked", "assertInZone", "_fnAndFlush", "completers", "onSuccess", "onError", "_removeTimer", "index", "_dequeueTimer", "_requeuePeriodicTimer", "interval", "_dequeuePeriodicTimer", "_setTimeout", "isTimer", "removeTimerFn", "_clearTimeout", "_setInterval", "_clearInterval", "_resetLastErrorAndThrow", "realTime", "patchDate", "checkTimerPatch", "resetDate", "lockDatePatch", "unlockDatePatch", "steps", "flushErrors", "microtask", "elapsed", "additionalArgs", "callbackIndex", "cbIdx", "macroTaskOption", "findMacroTaskOption", "callback<PERSON><PERSON><PERSON>", "handleId", "callback", "getProxyZoneSpec", "_fakeAsyncTestZoneSpec", "resetFakeAsyncZone", "fakeAsyncFn", "res", "lastProxyZoneSpec", "_getFakeAsyncZoneSpec", "ignoreNestedTimeout", "maxTurns", "discardPeriodicTasks", "symbolState", "UNRESOLVED", "ori<PERSON><PERSON>", "then", "chained", "asyncTestZoneSpec", "unpatchPromiseForTest"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/zone.js/fesm2015/zone-testing.js"], "sourcesContent": ["'use strict';\n/**\n * @license Angular v15.1.0-next.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nconst NEWLINE = '\\n';\nconst IGNORE_FRAMES = {};\nconst creationTrace = '__creationTrace__';\nconst ERROR_TAG = 'STACKTRACE TRACKING';\nconst SEP_TAG = '__SEP_TAG__';\nlet sepTemplate = SEP_TAG + '@[native]';\nclass LongStackTrace {\n    constructor() {\n        this.error = getStacktrace();\n        this.timestamp = new Date();\n    }\n}\nfunction getStacktraceWithUncaughtError() {\n    return new Error(ERROR_TAG);\n}\nfunction getStacktraceWithCaughtError() {\n    try {\n        throw getStacktraceWithUncaughtError();\n    }\n    catch (err) {\n        return err;\n    }\n}\n// Some implementations of exception handling don't create a stack trace if the exception\n// isn't thrown, however it's faster not to actually throw the exception.\nconst error = getStacktraceWithUncaughtError();\nconst caughtError = getStacktraceWithCaughtError();\nconst getStacktrace = error.stack ?\n    getStacktraceWithUncaughtError :\n    (caughtError.stack ? getStacktraceWithCaughtError : getStacktraceWithUncaughtError);\nfunction getFrames(error) {\n    return error.stack ? error.stack.split(NEWLINE) : [];\n}\nfunction addErrorStack(lines, error) {\n    let trace = getFrames(error);\n    for (let i = 0; i < trace.length; i++) {\n        const frame = trace[i];\n        // Filter out the Frames which are part of stack capturing.\n        if (!IGNORE_FRAMES.hasOwnProperty(frame)) {\n            lines.push(trace[i]);\n        }\n    }\n}\nfunction renderLongStackTrace(frames, stack) {\n    const longTrace = [stack ? stack.trim() : ''];\n    if (frames) {\n        let timestamp = new Date().getTime();\n        for (let i = 0; i < frames.length; i++) {\n            const traceFrames = frames[i];\n            const lastTime = traceFrames.timestamp;\n            let separator = `____________________Elapsed ${timestamp - lastTime.getTime()} ms; At: ${lastTime}`;\n            separator = separator.replace(/[^\\w\\d]/g, '_');\n            longTrace.push(sepTemplate.replace(SEP_TAG, separator));\n            addErrorStack(longTrace, traceFrames.error);\n            timestamp = lastTime.getTime();\n        }\n    }\n    return longTrace.join(NEWLINE);\n}\n// if Error.stackTraceLimit is 0, means stack trace\n// is disabled, so we don't need to generate long stack trace\n// this will improve performance in some test(some test will\n// set stackTraceLimit to 0, https://github.com/angular/zone.js/issues/698\nfunction stackTracesEnabled() {\n    // Cast through any since this property only exists on Error in the nodejs\n    // typings.\n    return Error.stackTraceLimit > 0;\n}\nZone['longStackTraceZoneSpec'] = {\n    name: 'long-stack-trace',\n    longStackTraceLimit: 10,\n    // add a getLongStackTrace method in spec to\n    // handle handled reject promise error.\n    getLongStackTrace: function (error) {\n        if (!error) {\n            return undefined;\n        }\n        const trace = error[Zone.__symbol__('currentTaskTrace')];\n        if (!trace) {\n            return error.stack;\n        }\n        return renderLongStackTrace(trace, error.stack);\n    },\n    onScheduleTask: function (parentZoneDelegate, currentZone, targetZone, task) {\n        if (stackTracesEnabled()) {\n            const currentTask = Zone.currentTask;\n            let trace = currentTask && currentTask.data && currentTask.data[creationTrace] || [];\n            trace = [new LongStackTrace()].concat(trace);\n            if (trace.length > this.longStackTraceLimit) {\n                trace.length = this.longStackTraceLimit;\n            }\n            if (!task.data)\n                task.data = {};\n            if (task.type === 'eventTask') {\n                // Fix issue https://github.com/angular/zone.js/issues/1195,\n                // For event task of browser, by default, all task will share a\n                // singleton instance of data object, we should create a new one here\n                // The cast to `any` is required to workaround a closure bug which wrongly applies\n                // URL sanitization rules to .data access.\n                task.data = Object.assign({}, task.data);\n            }\n            task.data[creationTrace] = trace;\n        }\n        return parentZoneDelegate.scheduleTask(targetZone, task);\n    },\n    onHandleError: function (parentZoneDelegate, currentZone, targetZone, error) {\n        if (stackTracesEnabled()) {\n            const parentTask = Zone.currentTask || error.task;\n            if (error instanceof Error && parentTask) {\n                const longStack = renderLongStackTrace(parentTask.data && parentTask.data[creationTrace], error.stack);\n                try {\n                    error.stack = error.longStack = longStack;\n                }\n                catch (err) {\n                }\n            }\n        }\n        return parentZoneDelegate.handleError(targetZone, error);\n    }\n};\nfunction captureStackTraces(stackTraces, count) {\n    if (count > 0) {\n        stackTraces.push(getFrames((new LongStackTrace()).error));\n        captureStackTraces(stackTraces, count - 1);\n    }\n}\nfunction computeIgnoreFrames() {\n    if (!stackTracesEnabled()) {\n        return;\n    }\n    const frames = [];\n    captureStackTraces(frames, 2);\n    const frames1 = frames[0];\n    const frames2 = frames[1];\n    for (let i = 0; i < frames1.length; i++) {\n        const frame1 = frames1[i];\n        if (frame1.indexOf(ERROR_TAG) == -1) {\n            let match = frame1.match(/^\\s*at\\s+/);\n            if (match) {\n                sepTemplate = match[0] + SEP_TAG + ' (http://localhost)';\n                break;\n            }\n        }\n    }\n    for (let i = 0; i < frames1.length; i++) {\n        const frame1 = frames1[i];\n        const frame2 = frames2[i];\n        if (frame1 === frame2) {\n            IGNORE_FRAMES[frame1] = true;\n        }\n        else {\n            break;\n        }\n    }\n}\ncomputeIgnoreFrames();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass ProxyZoneSpec {\n    constructor(defaultSpecDelegate = null) {\n        this.defaultSpecDelegate = defaultSpecDelegate;\n        this.name = 'ProxyZone';\n        this._delegateSpec = null;\n        this.properties = { 'ProxyZoneSpec': this };\n        this.propertyKeys = null;\n        this.lastTaskState = null;\n        this.isNeedToTriggerHasTask = false;\n        this.tasks = [];\n        this.setDelegate(defaultSpecDelegate);\n    }\n    static get() {\n        return Zone.current.get('ProxyZoneSpec');\n    }\n    static isLoaded() {\n        return ProxyZoneSpec.get() instanceof ProxyZoneSpec;\n    }\n    static assertPresent() {\n        if (!ProxyZoneSpec.isLoaded()) {\n            throw new Error(`Expected to be running in 'ProxyZone', but it was not found.`);\n        }\n        return ProxyZoneSpec.get();\n    }\n    setDelegate(delegateSpec) {\n        const isNewDelegate = this._delegateSpec !== delegateSpec;\n        this._delegateSpec = delegateSpec;\n        this.propertyKeys && this.propertyKeys.forEach((key) => delete this.properties[key]);\n        this.propertyKeys = null;\n        if (delegateSpec && delegateSpec.properties) {\n            this.propertyKeys = Object.keys(delegateSpec.properties);\n            this.propertyKeys.forEach((k) => this.properties[k] = delegateSpec.properties[k]);\n        }\n        // if a new delegateSpec was set, check if we need to trigger hasTask\n        if (isNewDelegate && this.lastTaskState &&\n            (this.lastTaskState.macroTask || this.lastTaskState.microTask)) {\n            this.isNeedToTriggerHasTask = true;\n        }\n    }\n    getDelegate() {\n        return this._delegateSpec;\n    }\n    resetDelegate() {\n        this.getDelegate();\n        this.setDelegate(this.defaultSpecDelegate);\n    }\n    tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone) {\n        if (this.isNeedToTriggerHasTask && this.lastTaskState) {\n            // last delegateSpec has microTask or macroTask\n            // should call onHasTask in current delegateSpec\n            this.isNeedToTriggerHasTask = false;\n            this.onHasTask(parentZoneDelegate, currentZone, targetZone, this.lastTaskState);\n        }\n    }\n    removeFromTasks(task) {\n        if (!this.tasks) {\n            return;\n        }\n        for (let i = 0; i < this.tasks.length; i++) {\n            if (this.tasks[i] === task) {\n                this.tasks.splice(i, 1);\n                return;\n            }\n        }\n    }\n    getAndClearPendingTasksInfo() {\n        if (this.tasks.length === 0) {\n            return '';\n        }\n        const taskInfo = this.tasks.map((task) => {\n            const dataInfo = task.data &&\n                Object.keys(task.data)\n                    .map((key) => {\n                    return key + ':' + task.data[key];\n                })\n                    .join(',');\n            return `type: ${task.type}, source: ${task.source}, args: {${dataInfo}}`;\n        });\n        const pendingTasksInfo = '--Pending async tasks are: [' + taskInfo + ']';\n        // clear tasks\n        this.tasks = [];\n        return pendingTasksInfo;\n    }\n    onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec) {\n        if (this._delegateSpec && this._delegateSpec.onFork) {\n            return this._delegateSpec.onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec);\n        }\n        else {\n            return parentZoneDelegate.fork(targetZone, zoneSpec);\n        }\n    }\n    onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source) {\n        if (this._delegateSpec && this._delegateSpec.onIntercept) {\n            return this._delegateSpec.onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source);\n        }\n        else {\n            return parentZoneDelegate.intercept(targetZone, delegate, source);\n        }\n    }\n    onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n        this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n        if (this._delegateSpec && this._delegateSpec.onInvoke) {\n            return this._delegateSpec.onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source);\n        }\n        else {\n            return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n        }\n    }\n    onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n        if (this._delegateSpec && this._delegateSpec.onHandleError) {\n            return this._delegateSpec.onHandleError(parentZoneDelegate, currentZone, targetZone, error);\n        }\n        else {\n            return parentZoneDelegate.handleError(targetZone, error);\n        }\n    }\n    onScheduleTask(parentZoneDelegate, currentZone, targetZone, task) {\n        if (task.type !== 'eventTask') {\n            this.tasks.push(task);\n        }\n        if (this._delegateSpec && this._delegateSpec.onScheduleTask) {\n            return this._delegateSpec.onScheduleTask(parentZoneDelegate, currentZone, targetZone, task);\n        }\n        else {\n            return parentZoneDelegate.scheduleTask(targetZone, task);\n        }\n    }\n    onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs) {\n        if (task.type !== 'eventTask') {\n            this.removeFromTasks(task);\n        }\n        this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n        if (this._delegateSpec && this._delegateSpec.onInvokeTask) {\n            return this._delegateSpec.onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs);\n        }\n        else {\n            return parentZoneDelegate.invokeTask(targetZone, task, applyThis, applyArgs);\n        }\n    }\n    onCancelTask(parentZoneDelegate, currentZone, targetZone, task) {\n        if (task.type !== 'eventTask') {\n            this.removeFromTasks(task);\n        }\n        this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n        if (this._delegateSpec && this._delegateSpec.onCancelTask) {\n            return this._delegateSpec.onCancelTask(parentZoneDelegate, currentZone, targetZone, task);\n        }\n        else {\n            return parentZoneDelegate.cancelTask(targetZone, task);\n        }\n    }\n    onHasTask(delegate, current, target, hasTaskState) {\n        this.lastTaskState = hasTaskState;\n        if (this._delegateSpec && this._delegateSpec.onHasTask) {\n            this._delegateSpec.onHasTask(delegate, current, target, hasTaskState);\n        }\n        else {\n            delegate.hasTask(target, hasTaskState);\n        }\n    }\n}\n// Export the class so that new instances can be created with proper\n// constructor params.\nZone['ProxyZoneSpec'] = ProxyZoneSpec;\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass SyncTestZoneSpec {\n    constructor(namePrefix) {\n        this.runZone = Zone.current;\n        this.name = 'syncTestZone for ' + namePrefix;\n    }\n    onScheduleTask(delegate, current, target, task) {\n        switch (task.type) {\n            case 'microTask':\n            case 'macroTask':\n                throw new Error(`Cannot call ${task.source} from within a sync test (${this.name}).`);\n            case 'eventTask':\n                task = delegate.scheduleTask(target, task);\n                break;\n        }\n        return task;\n    }\n}\n// Export the class so that new instances can be created with proper\n// constructor params.\nZone['SyncTestZoneSpec'] = SyncTestZoneSpec;\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nZone.__load_patch('jasmine', (global, Zone, api) => {\n    const __extends = function (d, b) {\n        for (const p in b)\n            if (b.hasOwnProperty(p))\n                d[p] = b[p];\n        function __() {\n            this.constructor = d;\n        }\n        d.prototype = b === null ? Object.create(b) : ((__.prototype = b.prototype), new __());\n    };\n    // Patch jasmine's describe/it/beforeEach/afterEach functions so test code always runs\n    // in a testZone (ProxyZone). (See: angular/zone.js#91 & angular/angular#10503)\n    if (!Zone)\n        throw new Error('Missing: zone.js');\n    if (typeof jest !== 'undefined') {\n        // return if jasmine is a light implementation inside jest\n        // in this case, we are running inside jest not jasmine\n        return;\n    }\n    if (typeof jasmine == 'undefined' || jasmine['__zone_patch__']) {\n        return;\n    }\n    jasmine['__zone_patch__'] = true;\n    const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n    const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n    if (!SyncTestZoneSpec)\n        throw new Error('Missing: SyncTestZoneSpec');\n    if (!ProxyZoneSpec)\n        throw new Error('Missing: ProxyZoneSpec');\n    const ambientZone = Zone.current;\n    const symbol = Zone.__symbol__;\n    // whether patch jasmine clock when in fakeAsync\n    const disablePatchingJasmineClock = global[symbol('fakeAsyncDisablePatchingClock')] === true;\n    // the original variable name fakeAsyncPatchLock is not accurate, so the name will be\n    // fakeAsyncAutoFakeAsyncWhenClockPatched and if this enablePatchingJasmineClock is false, we also\n    // automatically disable the auto jump into fakeAsync feature\n    const enableAutoFakeAsyncWhenClockPatched = !disablePatchingJasmineClock &&\n        ((global[symbol('fakeAsyncPatchLock')] === true) ||\n            (global[symbol('fakeAsyncAutoFakeAsyncWhenClockPatched')] === true));\n    const ignoreUnhandledRejection = global[symbol('ignoreUnhandledRejection')] === true;\n    if (!ignoreUnhandledRejection) {\n        const globalErrors = jasmine.GlobalErrors;\n        if (globalErrors && !jasmine[symbol('GlobalErrors')]) {\n            jasmine[symbol('GlobalErrors')] = globalErrors;\n            jasmine.GlobalErrors = function () {\n                const instance = new globalErrors();\n                const originalInstall = instance.install;\n                if (originalInstall && !instance[symbol('install')]) {\n                    instance[symbol('install')] = originalInstall;\n                    instance.install = function () {\n                        const isNode = typeof process !== 'undefined' && !!process.on;\n                        // Note: Jasmine checks internally if `process` and `process.on` is defined. Otherwise,\n                        // it installs the browser rejection handler through the `global.addEventListener`.\n                        // This code may be run in the browser environment where `process` is not defined, and\n                        // this will lead to a runtime exception since Webpack 5 removed automatic Node.js\n                        // polyfills. Note, that events are named differently, it's `unhandledRejection` in\n                        // Node.js and `unhandledrejection` in the browser.\n                        const originalHandlers = isNode ? process.listeners('unhandledRejection') :\n                            global.eventListeners('unhandledrejection');\n                        const result = originalInstall.apply(this, arguments);\n                        isNode ? process.removeAllListeners('unhandledRejection') :\n                            global.removeAllListeners('unhandledrejection');\n                        if (originalHandlers) {\n                            originalHandlers.forEach(handler => {\n                                if (isNode) {\n                                    process.on('unhandledRejection', handler);\n                                }\n                                else {\n                                    global.addEventListener('unhandledrejection', handler);\n                                }\n                            });\n                        }\n                        return result;\n                    };\n                }\n                return instance;\n            };\n        }\n    }\n    // Monkey patch all of the jasmine DSL so that each function runs in appropriate zone.\n    const jasmineEnv = jasmine.getEnv();\n    ['describe', 'xdescribe', 'fdescribe'].forEach(methodName => {\n        let originalJasmineFn = jasmineEnv[methodName];\n        jasmineEnv[methodName] = function (description, specDefinitions) {\n            return originalJasmineFn.call(this, description, wrapDescribeInZone(description, specDefinitions));\n        };\n    });\n    ['it', 'xit', 'fit'].forEach(methodName => {\n        let originalJasmineFn = jasmineEnv[methodName];\n        jasmineEnv[symbol(methodName)] = originalJasmineFn;\n        jasmineEnv[methodName] = function (description, specDefinitions, timeout) {\n            arguments[1] = wrapTestInZone(specDefinitions);\n            return originalJasmineFn.apply(this, arguments);\n        };\n    });\n    ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach(methodName => {\n        let originalJasmineFn = jasmineEnv[methodName];\n        jasmineEnv[symbol(methodName)] = originalJasmineFn;\n        jasmineEnv[methodName] = function (specDefinitions, timeout) {\n            arguments[0] = wrapTestInZone(specDefinitions);\n            return originalJasmineFn.apply(this, arguments);\n        };\n    });\n    if (!disablePatchingJasmineClock) {\n        // need to patch jasmine.clock().mockDate and jasmine.clock().tick() so\n        // they can work properly in FakeAsyncTest\n        const originalClockFn = (jasmine[symbol('clock')] = jasmine['clock']);\n        jasmine['clock'] = function () {\n            const clock = originalClockFn.apply(this, arguments);\n            if (!clock[symbol('patched')]) {\n                clock[symbol('patched')] = symbol('patched');\n                const originalTick = (clock[symbol('tick')] = clock.tick);\n                clock.tick = function () {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        return fakeAsyncZoneSpec.tick.apply(fakeAsyncZoneSpec, arguments);\n                    }\n                    return originalTick.apply(this, arguments);\n                };\n                const originalMockDate = (clock[symbol('mockDate')] = clock.mockDate);\n                clock.mockDate = function () {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        const dateTime = arguments.length > 0 ? arguments[0] : new Date();\n                        return fakeAsyncZoneSpec.setFakeBaseSystemTime.apply(fakeAsyncZoneSpec, dateTime && typeof dateTime.getTime === 'function' ? [dateTime.getTime()] :\n                            arguments);\n                    }\n                    return originalMockDate.apply(this, arguments);\n                };\n                // for auto go into fakeAsync feature, we need the flag to enable it\n                if (enableAutoFakeAsyncWhenClockPatched) {\n                    ['install', 'uninstall'].forEach(methodName => {\n                        const originalClockFn = (clock[symbol(methodName)] = clock[methodName]);\n                        clock[methodName] = function () {\n                            const FakeAsyncTestZoneSpec = Zone['FakeAsyncTestZoneSpec'];\n                            if (FakeAsyncTestZoneSpec) {\n                                jasmine[symbol('clockInstalled')] = 'install' === methodName;\n                                return;\n                            }\n                            return originalClockFn.apply(this, arguments);\n                        };\n                    });\n                }\n            }\n            return clock;\n        };\n    }\n    // monkey patch createSpyObj to make properties enumerable to true\n    if (!jasmine[Zone.__symbol__('createSpyObj')]) {\n        const originalCreateSpyObj = jasmine.createSpyObj;\n        jasmine[Zone.__symbol__('createSpyObj')] = originalCreateSpyObj;\n        jasmine.createSpyObj = function () {\n            const args = Array.prototype.slice.call(arguments);\n            const propertyNames = args.length >= 3 ? args[2] : null;\n            let spyObj;\n            if (propertyNames) {\n                const defineProperty = Object.defineProperty;\n                Object.defineProperty = function (obj, p, attributes) {\n                    return defineProperty.call(this, obj, p, Object.assign(Object.assign({}, attributes), { configurable: true, enumerable: true }));\n                };\n                try {\n                    spyObj = originalCreateSpyObj.apply(this, args);\n                }\n                finally {\n                    Object.defineProperty = defineProperty;\n                }\n            }\n            else {\n                spyObj = originalCreateSpyObj.apply(this, args);\n            }\n            return spyObj;\n        };\n    }\n    /**\n     * Gets a function wrapping the body of a Jasmine `describe` block to execute in a\n     * synchronous-only zone.\n     */\n    function wrapDescribeInZone(description, describeBody) {\n        return function () {\n            // Create a synchronous-only zone in which to run `describe` blocks in order to raise an\n            // error if any asynchronous operations are attempted inside of a `describe`.\n            const syncZone = ambientZone.fork(new SyncTestZoneSpec(`jasmine.describe#${description}`));\n            return syncZone.run(describeBody, this, arguments);\n        };\n    }\n    function runInTestZone(testBody, applyThis, queueRunner, done) {\n        const isClockInstalled = !!jasmine[symbol('clockInstalled')];\n        queueRunner.testProxyZoneSpec;\n        const testProxyZone = queueRunner.testProxyZone;\n        if (isClockInstalled && enableAutoFakeAsyncWhenClockPatched) {\n            // auto run a fakeAsync\n            const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n            if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n                testBody = fakeAsyncModule.fakeAsync(testBody);\n            }\n        }\n        if (done) {\n            return testProxyZone.run(testBody, applyThis, [done]);\n        }\n        else {\n            return testProxyZone.run(testBody, applyThis);\n        }\n    }\n    /**\n     * Gets a function wrapping the body of a Jasmine `it/beforeEach/afterEach` block to\n     * execute in a ProxyZone zone.\n     * This will run in `testProxyZone`. The `testProxyZone` will be reset by the `ZoneQueueRunner`\n     */\n    function wrapTestInZone(testBody) {\n        // The `done` callback is only passed through if the function expects at least one argument.\n        // Note we have to make a function with correct number of arguments, otherwise jasmine will\n        // think that all functions are sync or async.\n        return (testBody && (testBody.length ? function (done) {\n            return runInTestZone(testBody, this, this.queueRunner, done);\n        } : function () {\n            return runInTestZone(testBody, this, this.queueRunner);\n        }));\n    }\n    const QueueRunner = jasmine.QueueRunner;\n    jasmine.QueueRunner = (function (_super) {\n        __extends(ZoneQueueRunner, _super);\n        function ZoneQueueRunner(attrs) {\n            if (attrs.onComplete) {\n                attrs.onComplete = (fn => () => {\n                    // All functions are done, clear the test zone.\n                    this.testProxyZone = null;\n                    this.testProxyZoneSpec = null;\n                    ambientZone.scheduleMicroTask('jasmine.onComplete', fn);\n                })(attrs.onComplete);\n            }\n            const nativeSetTimeout = global[Zone.__symbol__('setTimeout')];\n            const nativeClearTimeout = global[Zone.__symbol__('clearTimeout')];\n            if (nativeSetTimeout) {\n                // should run setTimeout inside jasmine outside of zone\n                attrs.timeout = {\n                    setTimeout: nativeSetTimeout ? nativeSetTimeout : global.setTimeout,\n                    clearTimeout: nativeClearTimeout ? nativeClearTimeout : global.clearTimeout\n                };\n            }\n            // create a userContext to hold the queueRunner itself\n            // so we can access the testProxy in it/xit/beforeEach ...\n            if (jasmine.UserContext) {\n                if (!attrs.userContext) {\n                    attrs.userContext = new jasmine.UserContext();\n                }\n                attrs.userContext.queueRunner = this;\n            }\n            else {\n                if (!attrs.userContext) {\n                    attrs.userContext = {};\n                }\n                attrs.userContext.queueRunner = this;\n            }\n            // patch attrs.onException\n            const onException = attrs.onException;\n            attrs.onException = function (error) {\n                if (error &&\n                    error.message ===\n                        'Timeout - Async callback was not invoked within timeout specified by jasmine.DEFAULT_TIMEOUT_INTERVAL.') {\n                    // jasmine timeout, we can make the error message more\n                    // reasonable to tell what tasks are pending\n                    const proxyZoneSpec = this && this.testProxyZoneSpec;\n                    if (proxyZoneSpec) {\n                        const pendingTasksInfo = proxyZoneSpec.getAndClearPendingTasksInfo();\n                        try {\n                            // try catch here in case error.message is not writable\n                            error.message += pendingTasksInfo;\n                        }\n                        catch (err) {\n                        }\n                    }\n                }\n                if (onException) {\n                    onException.call(this, error);\n                }\n            };\n            _super.call(this, attrs);\n        }\n        ZoneQueueRunner.prototype.execute = function () {\n            let zone = Zone.current;\n            let isChildOfAmbientZone = false;\n            while (zone) {\n                if (zone === ambientZone) {\n                    isChildOfAmbientZone = true;\n                    break;\n                }\n                zone = zone.parent;\n            }\n            if (!isChildOfAmbientZone)\n                throw new Error('Unexpected Zone: ' + Zone.current.name);\n            // This is the zone which will be used for running individual tests.\n            // It will be a proxy zone, so that the tests function can retroactively install\n            // different zones.\n            // Example:\n            //   - In beforeEach() do childZone = Zone.current.fork(...);\n            //   - In it() try to do fakeAsync(). The issue is that because the beforeEach forked the\n            //     zone outside of fakeAsync it will be able to escape the fakeAsync rules.\n            //   - Because ProxyZone is parent fo `childZone` fakeAsync can retroactively add\n            //     fakeAsync behavior to the childZone.\n            this.testProxyZoneSpec = new ProxyZoneSpec();\n            this.testProxyZone = ambientZone.fork(this.testProxyZoneSpec);\n            if (!Zone.currentTask) {\n                // if we are not running in a task then if someone would register a\n                // element.addEventListener and then calling element.click() the\n                // addEventListener callback would think that it is the top most task and would\n                // drain the microtask queue on element.click() which would be incorrect.\n                // For this reason we always force a task when running jasmine tests.\n                Zone.current.scheduleMicroTask('jasmine.execute().forceTask', () => QueueRunner.prototype.execute.call(this));\n            }\n            else {\n                _super.prototype.execute.call(this);\n            }\n        };\n        return ZoneQueueRunner;\n    })(QueueRunner);\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nZone.__load_patch('jest', (context, Zone, api) => {\n    if (typeof jest === 'undefined' || jest['__zone_patch__']) {\n        return;\n    }\n    jest['__zone_patch__'] = true;\n    const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n    const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n    if (!ProxyZoneSpec) {\n        throw new Error('Missing ProxyZoneSpec');\n    }\n    const rootZone = Zone.current;\n    const syncZone = rootZone.fork(new SyncTestZoneSpec('jest.describe'));\n    const proxyZoneSpec = new ProxyZoneSpec();\n    const proxyZone = rootZone.fork(proxyZoneSpec);\n    function wrapDescribeFactoryInZone(originalJestFn) {\n        return function (...tableArgs) {\n            const originalDescribeFn = originalJestFn.apply(this, tableArgs);\n            return function (...args) {\n                args[1] = wrapDescribeInZone(args[1]);\n                return originalDescribeFn.apply(this, args);\n            };\n        };\n    }\n    function wrapTestFactoryInZone(originalJestFn) {\n        return function (...tableArgs) {\n            return function (...args) {\n                args[1] = wrapTestInZone(args[1]);\n                return originalJestFn.apply(this, tableArgs).apply(this, args);\n            };\n        };\n    }\n    /**\n     * Gets a function wrapping the body of a jest `describe` block to execute in a\n     * synchronous-only zone.\n     */\n    function wrapDescribeInZone(describeBody) {\n        return function (...args) {\n            return syncZone.run(describeBody, this, args);\n        };\n    }\n    /**\n     * Gets a function wrapping the body of a jest `it/beforeEach/afterEach` block to\n     * execute in a ProxyZone zone.\n     * This will run in the `proxyZone`.\n     */\n    function wrapTestInZone(testBody, isTestFunc = false) {\n        if (typeof testBody !== 'function') {\n            return testBody;\n        }\n        const wrappedFunc = function () {\n            if (Zone[api.symbol('useFakeTimersCalled')] === true && testBody &&\n                !testBody.isFakeAsync) {\n                // jest.useFakeTimers is called, run into fakeAsyncTest automatically.\n                const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n                if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n                    testBody = fakeAsyncModule.fakeAsync(testBody);\n                }\n            }\n            proxyZoneSpec.isTestFunc = isTestFunc;\n            return proxyZone.run(testBody, null, arguments);\n        };\n        // Update the length of wrappedFunc to be the same as the length of the testBody\n        // So jest core can handle whether the test function has `done()` or not correctly\n        Object.defineProperty(wrappedFunc, 'length', { configurable: true, writable: true, enumerable: false });\n        wrappedFunc.length = testBody.length;\n        return wrappedFunc;\n    }\n    ['describe', 'xdescribe', 'fdescribe'].forEach(methodName => {\n        let originalJestFn = context[methodName];\n        if (context[Zone.__symbol__(methodName)]) {\n            return;\n        }\n        context[Zone.__symbol__(methodName)] = originalJestFn;\n        context[methodName] = function (...args) {\n            args[1] = wrapDescribeInZone(args[1]);\n            return originalJestFn.apply(this, args);\n        };\n        context[methodName].each = wrapDescribeFactoryInZone(originalJestFn.each);\n    });\n    context.describe.only = context.fdescribe;\n    context.describe.skip = context.xdescribe;\n    ['it', 'xit', 'fit', 'test', 'xtest'].forEach(methodName => {\n        let originalJestFn = context[methodName];\n        if (context[Zone.__symbol__(methodName)]) {\n            return;\n        }\n        context[Zone.__symbol__(methodName)] = originalJestFn;\n        context[methodName] = function (...args) {\n            args[1] = wrapTestInZone(args[1], true);\n            return originalJestFn.apply(this, args);\n        };\n        context[methodName].each = wrapTestFactoryInZone(originalJestFn.each);\n        context[methodName].todo = originalJestFn.todo;\n    });\n    context.it.only = context.fit;\n    context.it.skip = context.xit;\n    context.test.only = context.fit;\n    context.test.skip = context.xit;\n    ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach(methodName => {\n        let originalJestFn = context[methodName];\n        if (context[Zone.__symbol__(methodName)]) {\n            return;\n        }\n        context[Zone.__symbol__(methodName)] = originalJestFn;\n        context[methodName] = function (...args) {\n            args[0] = wrapTestInZone(args[0]);\n            return originalJestFn.apply(this, args);\n        };\n    });\n    Zone.patchJestObject = function patchJestObject(Timer, isModern = false) {\n        // check whether currently the test is inside fakeAsync()\n        function isPatchingFakeTimer() {\n            const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n            return !!fakeAsyncZoneSpec;\n        }\n        // check whether the current function is inside `test/it` or other methods\n        // such as `describe/beforeEach`\n        function isInTestFunc() {\n            const proxyZoneSpec = Zone.current.get('ProxyZoneSpec');\n            return proxyZoneSpec && proxyZoneSpec.isTestFunc;\n        }\n        if (Timer[api.symbol('fakeTimers')]) {\n            return;\n        }\n        Timer[api.symbol('fakeTimers')] = true;\n        // patch jest fakeTimer internal method to make sure no console.warn print out\n        api.patchMethod(Timer, '_checkFakeTimers', delegate => {\n            return function (self, args) {\n                if (isPatchingFakeTimer()) {\n                    return true;\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch useFakeTimers(), set useFakeTimersCalled flag, and make test auto run into fakeAsync\n        api.patchMethod(Timer, 'useFakeTimers', delegate => {\n            return function (self, args) {\n                Zone[api.symbol('useFakeTimersCalled')] = true;\n                if (isModern || isInTestFunc()) {\n                    return delegate.apply(self, args);\n                }\n                return self;\n            };\n        });\n        // patch useRealTimers(), unset useFakeTimers flag\n        api.patchMethod(Timer, 'useRealTimers', delegate => {\n            return function (self, args) {\n                Zone[api.symbol('useFakeTimersCalled')] = false;\n                if (isModern || isInTestFunc()) {\n                    return delegate.apply(self, args);\n                }\n                return self;\n            };\n        });\n        // patch setSystemTime(), call setCurrentRealTime() in the fakeAsyncTest\n        api.patchMethod(Timer, 'setSystemTime', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n                    fakeAsyncZoneSpec.setFakeBaseSystemTime(args[0]);\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch getSystemTime(), call getCurrentRealTime() in the fakeAsyncTest\n        api.patchMethod(Timer, 'getRealSystemTime', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n                    return fakeAsyncZoneSpec.getRealSystemTime();\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch runAllTicks(), run all microTasks inside fakeAsync\n        api.patchMethod(Timer, 'runAllTicks', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec) {\n                    fakeAsyncZoneSpec.flushMicrotasks();\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch runAllTimers(), run all macroTasks inside fakeAsync\n        api.patchMethod(Timer, 'runAllTimers', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec) {\n                    fakeAsyncZoneSpec.flush(100, true);\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch advanceTimersByTime(), call tick() in the fakeAsyncTest\n        api.patchMethod(Timer, 'advanceTimersByTime', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec) {\n                    fakeAsyncZoneSpec.tick(args[0]);\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch runOnlyPendingTimers(), call flushOnlyPendingTimers() in the fakeAsyncTest\n        api.patchMethod(Timer, 'runOnlyPendingTimers', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec) {\n                    fakeAsyncZoneSpec.flushOnlyPendingTimers();\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch advanceTimersToNextTimer(), call tickToNext() in the fakeAsyncTest\n        api.patchMethod(Timer, 'advanceTimersToNextTimer', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec) {\n                    fakeAsyncZoneSpec.tickToNext(args[0]);\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch clearAllTimers(), call removeAllTimers() in the fakeAsyncTest\n        api.patchMethod(Timer, 'clearAllTimers', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec) {\n                    fakeAsyncZoneSpec.removeAllTimers();\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch getTimerCount(), call getTimerCount() in the fakeAsyncTest\n        api.patchMethod(Timer, 'getTimerCount', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec) {\n                    return fakeAsyncZoneSpec.getTimerCount();\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n    };\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nZone.__load_patch('mocha', (global, Zone) => {\n    const Mocha = global.Mocha;\n    if (typeof Mocha === 'undefined') {\n        // return if Mocha is not available, because now zone-testing\n        // will load mocha patch with jasmine/jest patch\n        return;\n    }\n    if (typeof Zone === 'undefined') {\n        throw new Error('Missing Zone.js');\n    }\n    const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n    const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n    if (!ProxyZoneSpec) {\n        throw new Error('Missing ProxyZoneSpec');\n    }\n    if (Mocha['__zone_patch__']) {\n        throw new Error('\"Mocha\" has already been patched with \"Zone\".');\n    }\n    Mocha['__zone_patch__'] = true;\n    const rootZone = Zone.current;\n    const syncZone = rootZone.fork(new SyncTestZoneSpec('Mocha.describe'));\n    let testZone = null;\n    const suiteZone = rootZone.fork(new ProxyZoneSpec());\n    const mochaOriginal = {\n        after: global.after,\n        afterEach: global.afterEach,\n        before: global.before,\n        beforeEach: global.beforeEach,\n        describe: global.describe,\n        it: global.it\n    };\n    function modifyArguments(args, syncTest, asyncTest) {\n        for (let i = 0; i < args.length; i++) {\n            let arg = args[i];\n            if (typeof arg === 'function') {\n                // The `done` callback is only passed through if the function expects at\n                // least one argument.\n                // Note we have to make a function with correct number of arguments,\n                // otherwise mocha will\n                // think that all functions are sync or async.\n                args[i] = (arg.length === 0) ? syncTest(arg) : asyncTest(arg);\n                // Mocha uses toString to view the test body in the result list, make sure we return the\n                // correct function body\n                args[i].toString = function () {\n                    return arg.toString();\n                };\n            }\n        }\n        return args;\n    }\n    function wrapDescribeInZone(args) {\n        const syncTest = function (fn) {\n            return function () {\n                return syncZone.run(fn, this, arguments);\n            };\n        };\n        return modifyArguments(args, syncTest);\n    }\n    function wrapTestInZone(args) {\n        const asyncTest = function (fn) {\n            return function (done) {\n                return testZone.run(fn, this, [done]);\n            };\n        };\n        const syncTest = function (fn) {\n            return function () {\n                return testZone.run(fn, this);\n            };\n        };\n        return modifyArguments(args, syncTest, asyncTest);\n    }\n    function wrapSuiteInZone(args) {\n        const asyncTest = function (fn) {\n            return function (done) {\n                return suiteZone.run(fn, this, [done]);\n            };\n        };\n        const syncTest = function (fn) {\n            return function () {\n                return suiteZone.run(fn, this);\n            };\n        };\n        return modifyArguments(args, syncTest, asyncTest);\n    }\n    global.describe = global.suite = function () {\n        return mochaOriginal.describe.apply(this, wrapDescribeInZone(arguments));\n    };\n    global.xdescribe = global.suite.skip = function () {\n        return mochaOriginal.describe.skip.apply(this, wrapDescribeInZone(arguments));\n    };\n    global.describe.only = global.suite.only = function () {\n        return mochaOriginal.describe.only.apply(this, wrapDescribeInZone(arguments));\n    };\n    global.it = global.specify = global.test = function () {\n        return mochaOriginal.it.apply(this, wrapTestInZone(arguments));\n    };\n    global.xit = global.xspecify = function () {\n        return mochaOriginal.it.skip.apply(this, wrapTestInZone(arguments));\n    };\n    global.it.only = global.test.only = function () {\n        return mochaOriginal.it.only.apply(this, wrapTestInZone(arguments));\n    };\n    global.after = global.suiteTeardown = function () {\n        return mochaOriginal.after.apply(this, wrapSuiteInZone(arguments));\n    };\n    global.afterEach = global.teardown = function () {\n        return mochaOriginal.afterEach.apply(this, wrapTestInZone(arguments));\n    };\n    global.before = global.suiteSetup = function () {\n        return mochaOriginal.before.apply(this, wrapSuiteInZone(arguments));\n    };\n    global.beforeEach = global.setup = function () {\n        return mochaOriginal.beforeEach.apply(this, wrapTestInZone(arguments));\n    };\n    ((originalRunTest, originalRun) => {\n        Mocha.Runner.prototype.runTest = function (fn) {\n            Zone.current.scheduleMicroTask('mocha.forceTask', () => {\n                originalRunTest.call(this, fn);\n            });\n        };\n        Mocha.Runner.prototype.run = function (fn) {\n            this.on('test', (e) => {\n                testZone = rootZone.fork(new ProxyZoneSpec());\n            });\n            this.on('fail', (test, err) => {\n                const proxyZoneSpec = testZone && testZone.get('ProxyZoneSpec');\n                if (proxyZoneSpec && err) {\n                    try {\n                        // try catch here in case err.message is not writable\n                        err.message += proxyZoneSpec.getAndClearPendingTasksInfo();\n                    }\n                    catch (error) {\n                    }\n                }\n            });\n            return originalRun.call(this, fn);\n        };\n    })(Mocha.Runner.prototype.runTest, Mocha.Runner.prototype.run);\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (_global) {\n    class AsyncTestZoneSpec {\n        constructor(finishCallback, failCallback, namePrefix) {\n            this.finishCallback = finishCallback;\n            this.failCallback = failCallback;\n            this._pendingMicroTasks = false;\n            this._pendingMacroTasks = false;\n            this._alreadyErrored = false;\n            this._isSync = false;\n            this._existingFinishTimer = null;\n            this.entryFunction = null;\n            this.runZone = Zone.current;\n            this.unresolvedChainedPromiseCount = 0;\n            this.supportWaitUnresolvedChainedPromise = false;\n            this.name = 'asyncTestZone for ' + namePrefix;\n            this.properties = { 'AsyncTestZoneSpec': this };\n            this.supportWaitUnresolvedChainedPromise =\n                _global[Zone.__symbol__('supportWaitUnResolvedChainedPromise')] === true;\n        }\n        isUnresolvedChainedPromisePending() {\n            return this.unresolvedChainedPromiseCount > 0;\n        }\n        _finishCallbackIfDone() {\n            // NOTE: Technically the `onHasTask` could fire together with the initial synchronous\n            // completion in `onInvoke`. `onHasTask` might call this method when it captured e.g.\n            // microtasks in the proxy zone that now complete as part of this async zone run.\n            // Consider the following scenario:\n            //    1. A test `beforeEach` schedules a microtask in the ProxyZone.\n            //    2. An actual empty `it` spec executes in the AsyncTestZone` (using e.g. `waitForAsync`).\n            //    3. The `onInvoke` invokes `_finishCallbackIfDone` because the spec runs synchronously.\n            //    4. We wait the scheduled timeout (see below) to account for unhandled promises.\n            //    5. The microtask from (1) finishes and `onHasTask` is invoked.\n            //    --> We register a second `_finishCallbackIfDone` even though we have scheduled a timeout.\n            // If the finish timeout from below is already scheduled, terminate the existing scheduled\n            // finish invocation, avoiding calling `jasmine` `done` multiple times. *Note* that we would\n            // want to schedule a new finish callback in case the task state changes again.\n            if (this._existingFinishTimer !== null) {\n                clearTimeout(this._existingFinishTimer);\n                this._existingFinishTimer = null;\n            }\n            if (!(this._pendingMicroTasks || this._pendingMacroTasks ||\n                (this.supportWaitUnresolvedChainedPromise && this.isUnresolvedChainedPromisePending()))) {\n                // We wait until the next tick because we would like to catch unhandled promises which could\n                // cause test logic to be executed. In such cases we cannot finish with tasks pending then.\n                this.runZone.run(() => {\n                    this._existingFinishTimer = setTimeout(() => {\n                        if (!this._alreadyErrored && !(this._pendingMicroTasks || this._pendingMacroTasks)) {\n                            this.finishCallback();\n                        }\n                    }, 0);\n                });\n            }\n        }\n        patchPromiseForTest() {\n            if (!this.supportWaitUnresolvedChainedPromise) {\n                return;\n            }\n            const patchPromiseForTest = Promise[Zone.__symbol__('patchPromiseForTest')];\n            if (patchPromiseForTest) {\n                patchPromiseForTest();\n            }\n        }\n        unPatchPromiseForTest() {\n            if (!this.supportWaitUnresolvedChainedPromise) {\n                return;\n            }\n            const unPatchPromiseForTest = Promise[Zone.__symbol__('unPatchPromiseForTest')];\n            if (unPatchPromiseForTest) {\n                unPatchPromiseForTest();\n            }\n        }\n        onScheduleTask(delegate, current, target, task) {\n            if (task.type !== 'eventTask') {\n                this._isSync = false;\n            }\n            if (task.type === 'microTask' && task.data && task.data instanceof Promise) {\n                // check whether the promise is a chained promise\n                if (task.data[AsyncTestZoneSpec.symbolParentUnresolved] === true) {\n                    // chained promise is being scheduled\n                    this.unresolvedChainedPromiseCount--;\n                }\n            }\n            return delegate.scheduleTask(target, task);\n        }\n        onInvokeTask(delegate, current, target, task, applyThis, applyArgs) {\n            if (task.type !== 'eventTask') {\n                this._isSync = false;\n            }\n            return delegate.invokeTask(target, task, applyThis, applyArgs);\n        }\n        onCancelTask(delegate, current, target, task) {\n            if (task.type !== 'eventTask') {\n                this._isSync = false;\n            }\n            return delegate.cancelTask(target, task);\n        }\n        // Note - we need to use onInvoke at the moment to call finish when a test is\n        // fully synchronous. TODO(juliemr): remove this when the logic for\n        // onHasTask changes and it calls whenever the task queues are dirty.\n        // updated by(JiaLiPassion), only call finish callback when no task\n        // was scheduled/invoked/canceled.\n        onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n            if (!this.entryFunction) {\n                this.entryFunction = delegate;\n            }\n            try {\n                this._isSync = true;\n                return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n            }\n            finally {\n                // We need to check the delegate is the same as entryFunction or not.\n                // Consider the following case.\n                //\n                // asyncTestZone.run(() => { // Here the delegate will be the entryFunction\n                //   Zone.current.run(() => { // Here the delegate will not be the entryFunction\n                //   });\n                // });\n                //\n                // We only want to check whether there are async tasks scheduled\n                // for the entry function.\n                if (this._isSync && this.entryFunction === delegate) {\n                    this._finishCallbackIfDone();\n                }\n            }\n        }\n        onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n            // Let the parent try to handle the error.\n            const result = parentZoneDelegate.handleError(targetZone, error);\n            if (result) {\n                this.failCallback(error);\n                this._alreadyErrored = true;\n            }\n            return false;\n        }\n        onHasTask(delegate, current, target, hasTaskState) {\n            delegate.hasTask(target, hasTaskState);\n            // We should only trigger finishCallback when the target zone is the AsyncTestZone\n            // Consider the following cases.\n            //\n            // const childZone = asyncTestZone.fork({\n            //   name: 'child',\n            //   onHasTask: ...\n            // });\n            //\n            // So we have nested zones declared the onHasTask hook, in this case,\n            // the onHasTask will be triggered twice, and cause the finishCallbackIfDone()\n            // is also be invoked twice. So we need to only trigger the finishCallbackIfDone()\n            // when the current zone is the same as the target zone.\n            if (current !== target) {\n                return;\n            }\n            if (hasTaskState.change == 'microTask') {\n                this._pendingMicroTasks = hasTaskState.microTask;\n                this._finishCallbackIfDone();\n            }\n            else if (hasTaskState.change == 'macroTask') {\n                this._pendingMacroTasks = hasTaskState.macroTask;\n                this._finishCallbackIfDone();\n            }\n        }\n    }\n    AsyncTestZoneSpec.symbolParentUnresolved = Zone.__symbol__('parentUnresolved');\n    // Export the class so that new instances can be created with proper\n    // constructor params.\n    Zone['AsyncTestZoneSpec'] = AsyncTestZoneSpec;\n})(typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global);\nZone.__load_patch('asynctest', (global, Zone, api) => {\n    /**\n     * Wraps a test function in an asynchronous test zone. The test will automatically\n     * complete when all asynchronous calls within this zone are done.\n     */\n    Zone[api.symbol('asyncTest')] = function asyncTest(fn) {\n        // If we're running using the Jasmine test framework, adapt to call the 'done'\n        // function when asynchronous activity is finished.\n        if (global.jasmine) {\n            // Not using an arrow function to preserve context passed from call site\n            return function (done) {\n                if (!done) {\n                    // if we run beforeEach in @angular/core/testing/testing_internal then we get no done\n                    // fake it here and assume sync.\n                    done = function () { };\n                    done.fail = function (e) {\n                        throw e;\n                    };\n                }\n                runInTestZone(fn, this, done, (err) => {\n                    if (typeof err === 'string') {\n                        return done.fail(new Error(err));\n                    }\n                    else {\n                        done.fail(err);\n                    }\n                });\n            };\n        }\n        // Otherwise, return a promise which will resolve when asynchronous activity\n        // is finished. This will be correctly consumed by the Mocha framework with\n        // it('...', async(myFn)); or can be used in a custom framework.\n        // Not using an arrow function to preserve context passed from call site\n        return function () {\n            return new Promise((finishCallback, failCallback) => {\n                runInTestZone(fn, this, finishCallback, failCallback);\n            });\n        };\n    };\n    function runInTestZone(fn, context, finishCallback, failCallback) {\n        const currentZone = Zone.current;\n        const AsyncTestZoneSpec = Zone['AsyncTestZoneSpec'];\n        if (AsyncTestZoneSpec === undefined) {\n            throw new Error('AsyncTestZoneSpec is needed for the async() test helper but could not be found. ' +\n                'Please make sure that your environment includes zone.js/plugins/async-test');\n        }\n        const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n        if (!ProxyZoneSpec) {\n            throw new Error('ProxyZoneSpec is needed for the async() test helper but could not be found. ' +\n                'Please make sure that your environment includes zone.js/plugins/proxy');\n        }\n        const proxyZoneSpec = ProxyZoneSpec.get();\n        ProxyZoneSpec.assertPresent();\n        // We need to create the AsyncTestZoneSpec outside the ProxyZone.\n        // If we do it in ProxyZone then we will get to infinite recursion.\n        const proxyZone = Zone.current.getZoneWith('ProxyZoneSpec');\n        const previousDelegate = proxyZoneSpec.getDelegate();\n        proxyZone.parent.run(() => {\n            const testZoneSpec = new AsyncTestZoneSpec(() => {\n                // Need to restore the original zone.\n                if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n                    // Only reset the zone spec if it's\n                    // still this one. Otherwise, assume\n                    // it's OK.\n                    proxyZoneSpec.setDelegate(previousDelegate);\n                }\n                testZoneSpec.unPatchPromiseForTest();\n                currentZone.run(() => {\n                    finishCallback();\n                });\n            }, (error) => {\n                // Need to restore the original zone.\n                if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n                    // Only reset the zone spec if it's sill this one. Otherwise, assume it's OK.\n                    proxyZoneSpec.setDelegate(previousDelegate);\n                }\n                testZoneSpec.unPatchPromiseForTest();\n                currentZone.run(() => {\n                    failCallback(error);\n                });\n            }, 'test');\n            proxyZoneSpec.setDelegate(testZoneSpec);\n            testZoneSpec.patchPromiseForTest();\n        });\n        return Zone.current.runGuarded(fn, context);\n    }\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n(function (global) {\n    const OriginalDate = global.Date;\n    // Since when we compile this file to `es2015`, and if we define\n    // this `FakeDate` as `class FakeDate`, and then set `FakeDate.prototype`\n    // there will be an error which is `Cannot assign to read only property 'prototype'`\n    // so we need to use function implementation here.\n    function FakeDate() {\n        if (arguments.length === 0) {\n            const d = new OriginalDate();\n            d.setTime(FakeDate.now());\n            return d;\n        }\n        else {\n            const args = Array.prototype.slice.call(arguments);\n            return new OriginalDate(...args);\n        }\n    }\n    FakeDate.now = function () {\n        const fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncTestZoneSpec) {\n            return fakeAsyncTestZoneSpec.getFakeSystemTime();\n        }\n        return OriginalDate.now.apply(this, arguments);\n    };\n    FakeDate.UTC = OriginalDate.UTC;\n    FakeDate.parse = OriginalDate.parse;\n    // keep a reference for zone patched timer function\n    const timers = {\n        setTimeout: global.setTimeout,\n        setInterval: global.setInterval,\n        clearTimeout: global.clearTimeout,\n        clearInterval: global.clearInterval\n    };\n    class Scheduler {\n        constructor() {\n            // Scheduler queue with the tuple of end time and callback function - sorted by end time.\n            this._schedulerQueue = [];\n            // Current simulated time in millis.\n            this._currentTickTime = 0;\n            // Current fake system base time in millis.\n            this._currentFakeBaseSystemTime = OriginalDate.now();\n            // track requeuePeriodicTimer\n            this._currentTickRequeuePeriodicEntries = [];\n        }\n        getCurrentTickTime() {\n            return this._currentTickTime;\n        }\n        getFakeSystemTime() {\n            return this._currentFakeBaseSystemTime + this._currentTickTime;\n        }\n        setFakeBaseSystemTime(fakeBaseSystemTime) {\n            this._currentFakeBaseSystemTime = fakeBaseSystemTime;\n        }\n        getRealSystemTime() {\n            return OriginalDate.now();\n        }\n        scheduleFunction(cb, delay, options) {\n            options = Object.assign({\n                args: [],\n                isPeriodic: false,\n                isRequestAnimationFrame: false,\n                id: -1,\n                isRequeuePeriodic: false\n            }, options);\n            let currentId = options.id < 0 ? Scheduler.nextId++ : options.id;\n            let endTime = this._currentTickTime + delay;\n            // Insert so that scheduler queue remains sorted by end time.\n            let newEntry = {\n                endTime: endTime,\n                id: currentId,\n                func: cb,\n                args: options.args,\n                delay: delay,\n                isPeriodic: options.isPeriodic,\n                isRequestAnimationFrame: options.isRequestAnimationFrame\n            };\n            if (options.isRequeuePeriodic) {\n                this._currentTickRequeuePeriodicEntries.push(newEntry);\n            }\n            let i = 0;\n            for (; i < this._schedulerQueue.length; i++) {\n                let currentEntry = this._schedulerQueue[i];\n                if (newEntry.endTime < currentEntry.endTime) {\n                    break;\n                }\n            }\n            this._schedulerQueue.splice(i, 0, newEntry);\n            return currentId;\n        }\n        removeScheduledFunctionWithId(id) {\n            for (let i = 0; i < this._schedulerQueue.length; i++) {\n                if (this._schedulerQueue[i].id == id) {\n                    this._schedulerQueue.splice(i, 1);\n                    break;\n                }\n            }\n        }\n        removeAll() {\n            this._schedulerQueue = [];\n        }\n        getTimerCount() {\n            return this._schedulerQueue.length;\n        }\n        tickToNext(step = 1, doTick, tickOptions) {\n            if (this._schedulerQueue.length < step) {\n                return;\n            }\n            // Find the last task currently queued in the scheduler queue and tick\n            // till that time.\n            const startTime = this._currentTickTime;\n            const targetTask = this._schedulerQueue[step - 1];\n            this.tick(targetTask.endTime - startTime, doTick, tickOptions);\n        }\n        tick(millis = 0, doTick, tickOptions) {\n            let finalTime = this._currentTickTime + millis;\n            let lastCurrentTime = 0;\n            tickOptions = Object.assign({ processNewMacroTasksSynchronously: true }, tickOptions);\n            // we need to copy the schedulerQueue so nested timeout\n            // will not be wrongly called in the current tick\n            // https://github.com/angular/angular/issues/33799\n            const schedulerQueue = tickOptions.processNewMacroTasksSynchronously ?\n                this._schedulerQueue :\n                this._schedulerQueue.slice();\n            if (schedulerQueue.length === 0 && doTick) {\n                doTick(millis);\n                return;\n            }\n            while (schedulerQueue.length > 0) {\n                // clear requeueEntries before each loop\n                this._currentTickRequeuePeriodicEntries = [];\n                let current = schedulerQueue[0];\n                if (finalTime < current.endTime) {\n                    // Done processing the queue since it's sorted by endTime.\n                    break;\n                }\n                else {\n                    // Time to run scheduled function. Remove it from the head of queue.\n                    let current = schedulerQueue.shift();\n                    if (!tickOptions.processNewMacroTasksSynchronously) {\n                        const idx = this._schedulerQueue.indexOf(current);\n                        if (idx >= 0) {\n                            this._schedulerQueue.splice(idx, 1);\n                        }\n                    }\n                    lastCurrentTime = this._currentTickTime;\n                    this._currentTickTime = current.endTime;\n                    if (doTick) {\n                        doTick(this._currentTickTime - lastCurrentTime);\n                    }\n                    let retval = current.func.apply(global, current.isRequestAnimationFrame ? [this._currentTickTime] : current.args);\n                    if (!retval) {\n                        // Uncaught exception in the current scheduled function. Stop processing the queue.\n                        break;\n                    }\n                    // check is there any requeue periodic entry is added in\n                    // current loop, if there is, we need to add to current loop\n                    if (!tickOptions.processNewMacroTasksSynchronously) {\n                        this._currentTickRequeuePeriodicEntries.forEach(newEntry => {\n                            let i = 0;\n                            for (; i < schedulerQueue.length; i++) {\n                                const currentEntry = schedulerQueue[i];\n                                if (newEntry.endTime < currentEntry.endTime) {\n                                    break;\n                                }\n                            }\n                            schedulerQueue.splice(i, 0, newEntry);\n                        });\n                    }\n                }\n            }\n            lastCurrentTime = this._currentTickTime;\n            this._currentTickTime = finalTime;\n            if (doTick) {\n                doTick(this._currentTickTime - lastCurrentTime);\n            }\n        }\n        flushOnlyPendingTimers(doTick) {\n            if (this._schedulerQueue.length === 0) {\n                return 0;\n            }\n            // Find the last task currently queued in the scheduler queue and tick\n            // till that time.\n            const startTime = this._currentTickTime;\n            const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n            this.tick(lastTask.endTime - startTime, doTick, { processNewMacroTasksSynchronously: false });\n            return this._currentTickTime - startTime;\n        }\n        flush(limit = 20, flushPeriodic = false, doTick) {\n            if (flushPeriodic) {\n                return this.flushPeriodic(doTick);\n            }\n            else {\n                return this.flushNonPeriodic(limit, doTick);\n            }\n        }\n        flushPeriodic(doTick) {\n            if (this._schedulerQueue.length === 0) {\n                return 0;\n            }\n            // Find the last task currently queued in the scheduler queue and tick\n            // till that time.\n            const startTime = this._currentTickTime;\n            const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n            this.tick(lastTask.endTime - startTime, doTick);\n            return this._currentTickTime - startTime;\n        }\n        flushNonPeriodic(limit, doTick) {\n            const startTime = this._currentTickTime;\n            let lastCurrentTime = 0;\n            let count = 0;\n            while (this._schedulerQueue.length > 0) {\n                count++;\n                if (count > limit) {\n                    throw new Error('flush failed after reaching the limit of ' + limit +\n                        ' tasks. Does your code use a polling timeout?');\n                }\n                // flush only non-periodic timers.\n                // If the only remaining tasks are periodic(or requestAnimationFrame), finish flushing.\n                if (this._schedulerQueue.filter(task => !task.isPeriodic && !task.isRequestAnimationFrame)\n                    .length === 0) {\n                    break;\n                }\n                const current = this._schedulerQueue.shift();\n                lastCurrentTime = this._currentTickTime;\n                this._currentTickTime = current.endTime;\n                if (doTick) {\n                    // Update any secondary schedulers like Jasmine mock Date.\n                    doTick(this._currentTickTime - lastCurrentTime);\n                }\n                const retval = current.func.apply(global, current.args);\n                if (!retval) {\n                    // Uncaught exception in the current scheduled function. Stop processing the queue.\n                    break;\n                }\n            }\n            return this._currentTickTime - startTime;\n        }\n    }\n    // Next scheduler id.\n    Scheduler.nextId = 1;\n    class FakeAsyncTestZoneSpec {\n        constructor(namePrefix, trackPendingRequestAnimationFrame = false, macroTaskOptions) {\n            this.trackPendingRequestAnimationFrame = trackPendingRequestAnimationFrame;\n            this.macroTaskOptions = macroTaskOptions;\n            this._scheduler = new Scheduler();\n            this._microtasks = [];\n            this._lastError = null;\n            this._uncaughtPromiseErrors = Promise[Zone.__symbol__('uncaughtPromiseErrors')];\n            this.pendingPeriodicTimers = [];\n            this.pendingTimers = [];\n            this.patchDateLocked = false;\n            this.properties = { 'FakeAsyncTestZoneSpec': this };\n            this.name = 'fakeAsyncTestZone for ' + namePrefix;\n            // in case user can't access the construction of FakeAsyncTestSpec\n            // user can also define macroTaskOptions by define a global variable.\n            if (!this.macroTaskOptions) {\n                this.macroTaskOptions = global[Zone.__symbol__('FakeAsyncTestMacroTask')];\n            }\n        }\n        static assertInZone() {\n            if (Zone.current.get('FakeAsyncTestZoneSpec') == null) {\n                throw new Error('The code should be running in the fakeAsync zone to call this function');\n            }\n        }\n        _fnAndFlush(fn, completers) {\n            return (...args) => {\n                fn.apply(global, args);\n                if (this._lastError === null) { // Success\n                    if (completers.onSuccess != null) {\n                        completers.onSuccess.apply(global);\n                    }\n                    // Flush microtasks only on success.\n                    this.flushMicrotasks();\n                }\n                else { // Failure\n                    if (completers.onError != null) {\n                        completers.onError.apply(global);\n                    }\n                }\n                // Return true if there were no errors, false otherwise.\n                return this._lastError === null;\n            };\n        }\n        static _removeTimer(timers, id) {\n            let index = timers.indexOf(id);\n            if (index > -1) {\n                timers.splice(index, 1);\n            }\n        }\n        _dequeueTimer(id) {\n            return () => {\n                FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n            };\n        }\n        _requeuePeriodicTimer(fn, interval, args, id) {\n            return () => {\n                // Requeue the timer callback if it's not been canceled.\n                if (this.pendingPeriodicTimers.indexOf(id) !== -1) {\n                    this._scheduler.scheduleFunction(fn, interval, { args, isPeriodic: true, id, isRequeuePeriodic: true });\n                }\n            };\n        }\n        _dequeuePeriodicTimer(id) {\n            return () => {\n                FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n            };\n        }\n        _setTimeout(fn, delay, args, isTimer = true) {\n            let removeTimerFn = this._dequeueTimer(Scheduler.nextId);\n            // Queue the callback and dequeue the timer on success and error.\n            let cb = this._fnAndFlush(fn, { onSuccess: removeTimerFn, onError: removeTimerFn });\n            let id = this._scheduler.scheduleFunction(cb, delay, { args, isRequestAnimationFrame: !isTimer });\n            if (isTimer) {\n                this.pendingTimers.push(id);\n            }\n            return id;\n        }\n        _clearTimeout(id) {\n            FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n            this._scheduler.removeScheduledFunctionWithId(id);\n        }\n        _setInterval(fn, interval, args) {\n            let id = Scheduler.nextId;\n            let completers = { onSuccess: null, onError: this._dequeuePeriodicTimer(id) };\n            let cb = this._fnAndFlush(fn, completers);\n            // Use the callback created above to requeue on success.\n            completers.onSuccess = this._requeuePeriodicTimer(cb, interval, args, id);\n            // Queue the callback and dequeue the periodic timer only on error.\n            this._scheduler.scheduleFunction(cb, interval, { args, isPeriodic: true });\n            this.pendingPeriodicTimers.push(id);\n            return id;\n        }\n        _clearInterval(id) {\n            FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n            this._scheduler.removeScheduledFunctionWithId(id);\n        }\n        _resetLastErrorAndThrow() {\n            let error = this._lastError || this._uncaughtPromiseErrors[0];\n            this._uncaughtPromiseErrors.length = 0;\n            this._lastError = null;\n            throw error;\n        }\n        getCurrentTickTime() {\n            return this._scheduler.getCurrentTickTime();\n        }\n        getFakeSystemTime() {\n            return this._scheduler.getFakeSystemTime();\n        }\n        setFakeBaseSystemTime(realTime) {\n            this._scheduler.setFakeBaseSystemTime(realTime);\n        }\n        getRealSystemTime() {\n            return this._scheduler.getRealSystemTime();\n        }\n        static patchDate() {\n            if (!!global[Zone.__symbol__('disableDatePatching')]) {\n                // we don't want to patch global Date\n                // because in some case, global Date\n                // is already being patched, we need to provide\n                // an option to let user still use their\n                // own version of Date.\n                return;\n            }\n            if (global['Date'] === FakeDate) {\n                // already patched\n                return;\n            }\n            global['Date'] = FakeDate;\n            FakeDate.prototype = OriginalDate.prototype;\n            // try check and reset timers\n            // because jasmine.clock().install() may\n            // have replaced the global timer\n            FakeAsyncTestZoneSpec.checkTimerPatch();\n        }\n        static resetDate() {\n            if (global['Date'] === FakeDate) {\n                global['Date'] = OriginalDate;\n            }\n        }\n        static checkTimerPatch() {\n            if (global.setTimeout !== timers.setTimeout) {\n                global.setTimeout = timers.setTimeout;\n                global.clearTimeout = timers.clearTimeout;\n            }\n            if (global.setInterval !== timers.setInterval) {\n                global.setInterval = timers.setInterval;\n                global.clearInterval = timers.clearInterval;\n            }\n        }\n        lockDatePatch() {\n            this.patchDateLocked = true;\n            FakeAsyncTestZoneSpec.patchDate();\n        }\n        unlockDatePatch() {\n            this.patchDateLocked = false;\n            FakeAsyncTestZoneSpec.resetDate();\n        }\n        tickToNext(steps = 1, doTick, tickOptions = { processNewMacroTasksSynchronously: true }) {\n            if (steps <= 0) {\n                return;\n            }\n            FakeAsyncTestZoneSpec.assertInZone();\n            this.flushMicrotasks();\n            this._scheduler.tickToNext(steps, doTick, tickOptions);\n            if (this._lastError !== null) {\n                this._resetLastErrorAndThrow();\n            }\n        }\n        tick(millis = 0, doTick, tickOptions = { processNewMacroTasksSynchronously: true }) {\n            FakeAsyncTestZoneSpec.assertInZone();\n            this.flushMicrotasks();\n            this._scheduler.tick(millis, doTick, tickOptions);\n            if (this._lastError !== null) {\n                this._resetLastErrorAndThrow();\n            }\n        }\n        flushMicrotasks() {\n            FakeAsyncTestZoneSpec.assertInZone();\n            const flushErrors = () => {\n                if (this._lastError !== null || this._uncaughtPromiseErrors.length) {\n                    // If there is an error stop processing the microtask queue and rethrow the error.\n                    this._resetLastErrorAndThrow();\n                }\n            };\n            while (this._microtasks.length > 0) {\n                let microtask = this._microtasks.shift();\n                microtask.func.apply(microtask.target, microtask.args);\n            }\n            flushErrors();\n        }\n        flush(limit, flushPeriodic, doTick) {\n            FakeAsyncTestZoneSpec.assertInZone();\n            this.flushMicrotasks();\n            const elapsed = this._scheduler.flush(limit, flushPeriodic, doTick);\n            if (this._lastError !== null) {\n                this._resetLastErrorAndThrow();\n            }\n            return elapsed;\n        }\n        flushOnlyPendingTimers(doTick) {\n            FakeAsyncTestZoneSpec.assertInZone();\n            this.flushMicrotasks();\n            const elapsed = this._scheduler.flushOnlyPendingTimers(doTick);\n            if (this._lastError !== null) {\n                this._resetLastErrorAndThrow();\n            }\n            return elapsed;\n        }\n        removeAllTimers() {\n            FakeAsyncTestZoneSpec.assertInZone();\n            this._scheduler.removeAll();\n            this.pendingPeriodicTimers = [];\n            this.pendingTimers = [];\n        }\n        getTimerCount() {\n            return this._scheduler.getTimerCount() + this._microtasks.length;\n        }\n        onScheduleTask(delegate, current, target, task) {\n            switch (task.type) {\n                case 'microTask':\n                    let args = task.data && task.data.args;\n                    // should pass additional arguments to callback if have any\n                    // currently we know process.nextTick will have such additional\n                    // arguments\n                    let additionalArgs;\n                    if (args) {\n                        let callbackIndex = task.data.cbIdx;\n                        if (typeof args.length === 'number' && args.length > callbackIndex + 1) {\n                            additionalArgs = Array.prototype.slice.call(args, callbackIndex + 1);\n                        }\n                    }\n                    this._microtasks.push({\n                        func: task.invoke,\n                        args: additionalArgs,\n                        target: task.data && task.data.target\n                    });\n                    break;\n                case 'macroTask':\n                    switch (task.source) {\n                        case 'setTimeout':\n                            task.data['handleId'] = this._setTimeout(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n                            break;\n                        case 'setImmediate':\n                            task.data['handleId'] = this._setTimeout(task.invoke, 0, Array.prototype.slice.call(task.data['args'], 1));\n                            break;\n                        case 'setInterval':\n                            task.data['handleId'] = this._setInterval(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n                            break;\n                        case 'XMLHttpRequest.send':\n                            throw new Error('Cannot make XHRs from within a fake async test. Request URL: ' +\n                                task.data['url']);\n                        case 'requestAnimationFrame':\n                        case 'webkitRequestAnimationFrame':\n                        case 'mozRequestAnimationFrame':\n                            // Simulate a requestAnimationFrame by using a setTimeout with 16 ms.\n                            // (60 frames per second)\n                            task.data['handleId'] = this._setTimeout(task.invoke, 16, task.data['args'], this.trackPendingRequestAnimationFrame);\n                            break;\n                        default:\n                            // user can define which macroTask they want to support by passing\n                            // macroTaskOptions\n                            const macroTaskOption = this.findMacroTaskOption(task);\n                            if (macroTaskOption) {\n                                const args = task.data && task.data['args'];\n                                const delay = args && args.length > 1 ? args[1] : 0;\n                                let callbackArgs = macroTaskOption.callbackArgs ? macroTaskOption.callbackArgs : args;\n                                if (!!macroTaskOption.isPeriodic) {\n                                    // periodic macroTask, use setInterval to simulate\n                                    task.data['handleId'] = this._setInterval(task.invoke, delay, callbackArgs);\n                                    task.data.isPeriodic = true;\n                                }\n                                else {\n                                    // not periodic, use setTimeout to simulate\n                                    task.data['handleId'] = this._setTimeout(task.invoke, delay, callbackArgs);\n                                }\n                                break;\n                            }\n                            throw new Error('Unknown macroTask scheduled in fake async test: ' + task.source);\n                    }\n                    break;\n                case 'eventTask':\n                    task = delegate.scheduleTask(target, task);\n                    break;\n            }\n            return task;\n        }\n        onCancelTask(delegate, current, target, task) {\n            switch (task.source) {\n                case 'setTimeout':\n                case 'requestAnimationFrame':\n                case 'webkitRequestAnimationFrame':\n                case 'mozRequestAnimationFrame':\n                    return this._clearTimeout(task.data['handleId']);\n                case 'setInterval':\n                    return this._clearInterval(task.data['handleId']);\n                default:\n                    // user can define which macroTask they want to support by passing\n                    // macroTaskOptions\n                    const macroTaskOption = this.findMacroTaskOption(task);\n                    if (macroTaskOption) {\n                        const handleId = task.data['handleId'];\n                        return macroTaskOption.isPeriodic ? this._clearInterval(handleId) :\n                            this._clearTimeout(handleId);\n                    }\n                    return delegate.cancelTask(target, task);\n            }\n        }\n        onInvoke(delegate, current, target, callback, applyThis, applyArgs, source) {\n            try {\n                FakeAsyncTestZoneSpec.patchDate();\n                return delegate.invoke(target, callback, applyThis, applyArgs, source);\n            }\n            finally {\n                if (!this.patchDateLocked) {\n                    FakeAsyncTestZoneSpec.resetDate();\n                }\n            }\n        }\n        findMacroTaskOption(task) {\n            if (!this.macroTaskOptions) {\n                return null;\n            }\n            for (let i = 0; i < this.macroTaskOptions.length; i++) {\n                const macroTaskOption = this.macroTaskOptions[i];\n                if (macroTaskOption.source === task.source) {\n                    return macroTaskOption;\n                }\n            }\n            return null;\n        }\n        onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n            this._lastError = error;\n            return false; // Don't propagate error to parent zone.\n        }\n    }\n    // Export the class so that new instances can be created with proper\n    // constructor params.\n    Zone['FakeAsyncTestZoneSpec'] = FakeAsyncTestZoneSpec;\n})(typeof window === 'object' && window || typeof self === 'object' && self || global);\nZone.__load_patch('fakeasync', (global, Zone, api) => {\n    const FakeAsyncTestZoneSpec = Zone && Zone['FakeAsyncTestZoneSpec'];\n    function getProxyZoneSpec() {\n        return Zone && Zone['ProxyZoneSpec'];\n    }\n    let _fakeAsyncTestZoneSpec = null;\n    /**\n     * Clears out the shared fake async zone for a test.\n     * To be called in a global `beforeEach`.\n     *\n     * @experimental\n     */\n    function resetFakeAsyncZone() {\n        if (_fakeAsyncTestZoneSpec) {\n            _fakeAsyncTestZoneSpec.unlockDatePatch();\n        }\n        _fakeAsyncTestZoneSpec = null;\n        // in node.js testing we may not have ProxyZoneSpec in which case there is nothing to reset.\n        getProxyZoneSpec() && getProxyZoneSpec().assertPresent().resetDelegate();\n    }\n    /**\n     * Wraps a function to be executed in the fakeAsync zone:\n     * - microtasks are manually executed by calling `flushMicrotasks()`,\n     * - timers are synchronous, `tick()` simulates the asynchronous passage of time.\n     *\n     * If there are any pending timers at the end of the function, an exception will be thrown.\n     *\n     * Can be used to wrap inject() calls.\n     *\n     * ## Example\n     *\n     * {@example core/testing/ts/fake_async.ts region='basic'}\n     *\n     * @param fn\n     * @returns The function wrapped to be executed in the fakeAsync zone\n     *\n     * @experimental\n     */\n    function fakeAsync(fn) {\n        // Not using an arrow function to preserve context passed from call site\n        const fakeAsyncFn = function (...args) {\n            const ProxyZoneSpec = getProxyZoneSpec();\n            if (!ProxyZoneSpec) {\n                throw new Error('ProxyZoneSpec is needed for the async() test helper but could not be found. ' +\n                    'Please make sure that your environment includes zone.js/plugins/proxy');\n            }\n            const proxyZoneSpec = ProxyZoneSpec.assertPresent();\n            if (Zone.current.get('FakeAsyncTestZoneSpec')) {\n                throw new Error('fakeAsync() calls can not be nested');\n            }\n            try {\n                // in case jasmine.clock init a fakeAsyncTestZoneSpec\n                if (!_fakeAsyncTestZoneSpec) {\n                    if (proxyZoneSpec.getDelegate() instanceof FakeAsyncTestZoneSpec) {\n                        throw new Error('fakeAsync() calls can not be nested');\n                    }\n                    _fakeAsyncTestZoneSpec = new FakeAsyncTestZoneSpec();\n                }\n                let res;\n                const lastProxyZoneSpec = proxyZoneSpec.getDelegate();\n                proxyZoneSpec.setDelegate(_fakeAsyncTestZoneSpec);\n                _fakeAsyncTestZoneSpec.lockDatePatch();\n                try {\n                    res = fn.apply(this, args);\n                    flushMicrotasks();\n                }\n                finally {\n                    proxyZoneSpec.setDelegate(lastProxyZoneSpec);\n                }\n                if (_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length > 0) {\n                    throw new Error(`${_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length} ` +\n                        `periodic timer(s) still in the queue.`);\n                }\n                if (_fakeAsyncTestZoneSpec.pendingTimers.length > 0) {\n                    throw new Error(`${_fakeAsyncTestZoneSpec.pendingTimers.length} timer(s) still in the queue.`);\n                }\n                return res;\n            }\n            finally {\n                resetFakeAsyncZone();\n            }\n        };\n        fakeAsyncFn.isFakeAsync = true;\n        return fakeAsyncFn;\n    }\n    function _getFakeAsyncZoneSpec() {\n        if (_fakeAsyncTestZoneSpec == null) {\n            _fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n            if (_fakeAsyncTestZoneSpec == null) {\n                throw new Error('The code should be running in the fakeAsync zone to call this function');\n            }\n        }\n        return _fakeAsyncTestZoneSpec;\n    }\n    /**\n     * Simulates the asynchronous passage of time for the timers in the fakeAsync zone.\n     *\n     * The microtasks queue is drained at the very start of this function and after any timer callback\n     * has been executed.\n     *\n     * ## Example\n     *\n     * {@example core/testing/ts/fake_async.ts region='basic'}\n     *\n     * @experimental\n     */\n    function tick(millis = 0, ignoreNestedTimeout = false) {\n        _getFakeAsyncZoneSpec().tick(millis, null, ignoreNestedTimeout);\n    }\n    /**\n     * Simulates the asynchronous passage of time for the timers in the fakeAsync zone by\n     * draining the macrotask queue until it is empty. The returned value is the milliseconds\n     * of time that would have been elapsed.\n     *\n     * @param maxTurns\n     * @returns The simulated time elapsed, in millis.\n     *\n     * @experimental\n     */\n    function flush(maxTurns) {\n        return _getFakeAsyncZoneSpec().flush(maxTurns);\n    }\n    /**\n     * Discard all remaining periodic tasks.\n     *\n     * @experimental\n     */\n    function discardPeriodicTasks() {\n        const zoneSpec = _getFakeAsyncZoneSpec();\n        zoneSpec.pendingPeriodicTimers;\n        zoneSpec.pendingPeriodicTimers.length = 0;\n    }\n    /**\n     * Flush any pending microtasks.\n     *\n     * @experimental\n     */\n    function flushMicrotasks() {\n        _getFakeAsyncZoneSpec().flushMicrotasks();\n    }\n    Zone[api.symbol('fakeAsyncTest')] =\n        { resetFakeAsyncZone, flushMicrotasks, discardPeriodicTasks, tick, flush, fakeAsync };\n}, true);\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Promise for async/fakeAsync zoneSpec test\n * can support async operation which not supported by zone.js\n * such as\n * it ('test jsonp in AsyncZone', async() => {\n *   new Promise(res => {\n *     jsonp(url, (data) => {\n *       // success callback\n *       res(data);\n *     });\n *   }).then((jsonpResult) => {\n *     // get jsonp result.\n *\n *     // user will expect AsyncZoneSpec wait for\n *     // then, but because jsonp is not zone aware\n *     // AsyncZone will finish before then is called.\n *   });\n * });\n */\nZone.__load_patch('promisefortest', (global, Zone, api) => {\n    const symbolState = api.symbol('state');\n    const UNRESOLVED = null;\n    const symbolParentUnresolved = api.symbol('parentUnresolved');\n    // patch Promise.prototype.then to keep an internal\n    // number for tracking unresolved chained promise\n    // we will decrease this number when the parent promise\n    // being resolved/rejected and chained promise was\n    // scheduled as a microTask.\n    // so we can know such kind of chained promise still\n    // not resolved in AsyncTestZone\n    Promise[api.symbol('patchPromiseForTest')] = function patchPromiseForTest() {\n        let oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n        if (oriThen) {\n            return;\n        }\n        oriThen = Promise[Zone.__symbol__('ZonePromiseThen')] = Promise.prototype.then;\n        Promise.prototype.then = function () {\n            const chained = oriThen.apply(this, arguments);\n            if (this[symbolState] === UNRESOLVED) {\n                // parent promise is unresolved.\n                const asyncTestZoneSpec = Zone.current.get('AsyncTestZoneSpec');\n                if (asyncTestZoneSpec) {\n                    asyncTestZoneSpec.unresolvedChainedPromiseCount++;\n                    chained[symbolParentUnresolved] = true;\n                }\n            }\n            return chained;\n        };\n    };\n    Promise[api.symbol('unPatchPromiseForTest')] = function unpatchPromiseForTest() {\n        // restore origin then\n        const oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n        if (oriThen) {\n            Promise.prototype.then = oriThen;\n            Promise[Zone.__symbol__('ZonePromiseThen')] = undefined;\n        }\n    };\n});\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,OAAO,GAAG,IAAI;AACpB,MAAMC,aAAa,GAAG,CAAC,CAAC;AACxB,MAAMC,aAAa,GAAG,mBAAmB;AACzC,MAAMC,SAAS,GAAG,qBAAqB;AACvC,MAAMC,OAAO,GAAG,aAAa;AAC7B,IAAIC,WAAW,GAAGD,OAAO,GAAG,WAAW;AACvC,MAAME,cAAc,CAAC;EACjBC,WAAW,GAAG;IACV,IAAI,CAACC,KAAK,GAAGC,aAAa,EAAE;IAC5B,IAAI,CAACC,SAAS,GAAG,IAAIC,IAAI,EAAE;EAC/B;AACJ;AACA,SAASC,8BAA8B,GAAG;EACtC,OAAO,IAAIC,KAAK,CAACV,SAAS,CAAC;AAC/B;AACA,SAASW,4BAA4B,GAAG;EACpC,IAAI;IACA,MAAMF,8BAA8B,EAAE;EAC1C,CAAC,CACD,OAAOG,GAAG,EAAE;IACR,OAAOA,GAAG;EACd;AACJ;AACA;AACA;AACA,MAAMP,KAAK,GAAGI,8BAA8B,EAAE;AAC9C,MAAMI,WAAW,GAAGF,4BAA4B,EAAE;AAClD,MAAML,aAAa,GAAGD,KAAK,CAACS,KAAK,GAC7BL,8BAA8B,GAC7BI,WAAW,CAACC,KAAK,GAAGH,4BAA4B,GAAGF,8BAA+B;AACvF,SAASM,SAAS,CAACV,KAAK,EAAE;EACtB,OAAOA,KAAK,CAACS,KAAK,GAAGT,KAAK,CAACS,KAAK,CAACE,KAAK,CAACnB,OAAO,CAAC,GAAG,EAAE;AACxD;AACA,SAASoB,aAAa,CAACC,KAAK,EAAEb,KAAK,EAAE;EACjC,IAAIc,KAAK,GAAGJ,SAAS,CAACV,KAAK,CAAC;EAC5B,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,MAAME,KAAK,GAAGH,KAAK,CAACC,CAAC,CAAC;IACtB;IACA,IAAI,CAACtB,aAAa,CAACyB,cAAc,CAACD,KAAK,CAAC,EAAE;MACtCJ,KAAK,CAACM,IAAI,CAACL,KAAK,CAACC,CAAC,CAAC,CAAC;IACxB;EACJ;AACJ;AACA,SAASK,oBAAoB,CAACC,MAAM,EAAEZ,KAAK,EAAE;EACzC,MAAMa,SAAS,GAAG,CAACb,KAAK,GAAGA,KAAK,CAACc,IAAI,EAAE,GAAG,EAAE,CAAC;EAC7C,IAAIF,MAAM,EAAE;IACR,IAAInB,SAAS,GAAG,IAAIC,IAAI,EAAE,CAACqB,OAAO,EAAE;IACpC,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,MAAM,CAACL,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,MAAMU,WAAW,GAAGJ,MAAM,CAACN,CAAC,CAAC;MAC7B,MAAMW,QAAQ,GAAGD,WAAW,CAACvB,SAAS;MACtC,IAAIyB,SAAS,GAAI,+BAA8BzB,SAAS,GAAGwB,QAAQ,CAACF,OAAO,EAAG,YAAWE,QAAS,EAAC;MACnGC,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC;MAC9CN,SAAS,CAACH,IAAI,CAACtB,WAAW,CAAC+B,OAAO,CAAChC,OAAO,EAAE+B,SAAS,CAAC,CAAC;MACvDf,aAAa,CAACU,SAAS,EAAEG,WAAW,CAACzB,KAAK,CAAC;MAC3CE,SAAS,GAAGwB,QAAQ,CAACF,OAAO,EAAE;IAClC;EACJ;EACA,OAAOF,SAAS,CAACO,IAAI,CAACrC,OAAO,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA,SAASsC,kBAAkB,GAAG;EAC1B;EACA;EACA,OAAOzB,KAAK,CAAC0B,eAAe,GAAG,CAAC;AACpC;AACAC,IAAI,CAAC,wBAAwB,CAAC,GAAG;EAC7BC,IAAI,EAAE,kBAAkB;EACxBC,mBAAmB,EAAE,EAAE;EACvB;EACA;EACAC,iBAAiB,EAAE,UAAUnC,KAAK,EAAE;IAChC,IAAI,CAACA,KAAK,EAAE;MACR,OAAOoC,SAAS;IACpB;IACA,MAAMtB,KAAK,GAAGd,KAAK,CAACgC,IAAI,CAACK,UAAU,CAAC,kBAAkB,CAAC,CAAC;IACxD,IAAI,CAACvB,KAAK,EAAE;MACR,OAAOd,KAAK,CAACS,KAAK;IACtB;IACA,OAAOW,oBAAoB,CAACN,KAAK,EAAEd,KAAK,CAACS,KAAK,CAAC;EACnD,CAAC;EACD6B,cAAc,EAAE,UAAUC,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAE;IACzE,IAAIZ,kBAAkB,EAAE,EAAE;MACtB,MAAMa,WAAW,GAAGX,IAAI,CAACW,WAAW;MACpC,IAAI7B,KAAK,GAAG6B,WAAW,IAAIA,WAAW,CAACC,IAAI,IAAID,WAAW,CAACC,IAAI,CAAClD,aAAa,CAAC,IAAI,EAAE;MACpFoB,KAAK,GAAG,CAAC,IAAIhB,cAAc,EAAE,CAAC,CAAC+C,MAAM,CAAC/B,KAAK,CAAC;MAC5C,IAAIA,KAAK,CAACE,MAAM,GAAG,IAAI,CAACkB,mBAAmB,EAAE;QACzCpB,KAAK,CAACE,MAAM,GAAG,IAAI,CAACkB,mBAAmB;MAC3C;MACA,IAAI,CAACQ,IAAI,CAACE,IAAI,EACVF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC;MAClB,IAAIF,IAAI,CAACI,IAAI,KAAK,WAAW,EAAE;QAC3B;QACA;QACA;QACA;QACA;QACAJ,IAAI,CAACE,IAAI,GAAGG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACE,IAAI,CAAC;MAC5C;MACAF,IAAI,CAACE,IAAI,CAAClD,aAAa,CAAC,GAAGoB,KAAK;IACpC;IACA,OAAOyB,kBAAkB,CAACU,YAAY,CAACR,UAAU,EAAEC,IAAI,CAAC;EAC5D,CAAC;EACDQ,aAAa,EAAE,UAAUX,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEzC,KAAK,EAAE;IACzE,IAAI8B,kBAAkB,EAAE,EAAE;MACtB,MAAMqB,UAAU,GAAGnB,IAAI,CAACW,WAAW,IAAI3C,KAAK,CAAC0C,IAAI;MACjD,IAAI1C,KAAK,YAAYK,KAAK,IAAI8C,UAAU,EAAE;QACtC,MAAMC,SAAS,GAAGhC,oBAAoB,CAAC+B,UAAU,CAACP,IAAI,IAAIO,UAAU,CAACP,IAAI,CAAClD,aAAa,CAAC,EAAEM,KAAK,CAACS,KAAK,CAAC;QACtG,IAAI;UACAT,KAAK,CAACS,KAAK,GAAGT,KAAK,CAACoD,SAAS,GAAGA,SAAS;QAC7C,CAAC,CACD,OAAO7C,GAAG,EAAE,CACZ;MACJ;IACJ;IACA,OAAOgC,kBAAkB,CAACc,WAAW,CAACZ,UAAU,EAAEzC,KAAK,CAAC;EAC5D;AACJ,CAAC;AACD,SAASsD,kBAAkB,CAACC,WAAW,EAAEC,KAAK,EAAE;EAC5C,IAAIA,KAAK,GAAG,CAAC,EAAE;IACXD,WAAW,CAACpC,IAAI,CAACT,SAAS,CAAE,IAAIZ,cAAc,EAAE,CAAEE,KAAK,CAAC,CAAC;IACzDsD,kBAAkB,CAACC,WAAW,EAAEC,KAAK,GAAG,CAAC,CAAC;EAC9C;AACJ;AACA,SAASC,mBAAmB,GAAG;EAC3B,IAAI,CAAC3B,kBAAkB,EAAE,EAAE;IACvB;EACJ;EACA,MAAMT,MAAM,GAAG,EAAE;EACjBiC,kBAAkB,CAACjC,MAAM,EAAE,CAAC,CAAC;EAC7B,MAAMqC,OAAO,GAAGrC,MAAM,CAAC,CAAC,CAAC;EACzB,MAAMsC,OAAO,GAAGtC,MAAM,CAAC,CAAC,CAAC;EACzB,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,OAAO,CAAC1C,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAM6C,MAAM,GAAGF,OAAO,CAAC3C,CAAC,CAAC;IACzB,IAAI6C,MAAM,CAACC,OAAO,CAAClE,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;MACjC,IAAImE,KAAK,GAAGF,MAAM,CAACE,KAAK,CAAC,WAAW,CAAC;MACrC,IAAIA,KAAK,EAAE;QACPjE,WAAW,GAAGiE,KAAK,CAAC,CAAC,CAAC,GAAGlE,OAAO,GAAG,qBAAqB;QACxD;MACJ;IACJ;EACJ;EACA,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,OAAO,CAAC1C,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAM6C,MAAM,GAAGF,OAAO,CAAC3C,CAAC,CAAC;IACzB,MAAMgD,MAAM,GAAGJ,OAAO,CAAC5C,CAAC,CAAC;IACzB,IAAI6C,MAAM,KAAKG,MAAM,EAAE;MACnBtE,aAAa,CAACmE,MAAM,CAAC,GAAG,IAAI;IAChC,CAAC,MACI;MACD;IACJ;EACJ;AACJ;AACAH,mBAAmB,EAAE;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,aAAa,CAAC;EAChBjE,WAAW,CAACkE,mBAAmB,GAAG,IAAI,EAAE;IACpC,IAAI,CAACA,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAChC,IAAI,GAAG,WAAW;IACvB,IAAI,CAACiC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,UAAU,GAAG;MAAE,eAAe,EAAE;IAAK,CAAC;IAC3C,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,WAAW,CAACP,mBAAmB,CAAC;EACzC;EACA,OAAOQ,GAAG,GAAG;IACT,OAAOzC,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,eAAe,CAAC;EAC5C;EACA,OAAOE,QAAQ,GAAG;IACd,OAAOX,aAAa,CAACS,GAAG,EAAE,YAAYT,aAAa;EACvD;EACA,OAAOY,aAAa,GAAG;IACnB,IAAI,CAACZ,aAAa,CAACW,QAAQ,EAAE,EAAE;MAC3B,MAAM,IAAItE,KAAK,CAAE,8DAA6D,CAAC;IACnF;IACA,OAAO2D,aAAa,CAACS,GAAG,EAAE;EAC9B;EACAD,WAAW,CAACK,YAAY,EAAE;IACtB,MAAMC,aAAa,GAAG,IAAI,CAACZ,aAAa,KAAKW,YAAY;IACzD,IAAI,CAACX,aAAa,GAAGW,YAAY;IACjC,IAAI,CAACT,YAAY,IAAI,IAAI,CAACA,YAAY,CAACW,OAAO,CAAEC,GAAG,IAAK,OAAO,IAAI,CAACb,UAAU,CAACa,GAAG,CAAC,CAAC;IACpF,IAAI,CAACZ,YAAY,GAAG,IAAI;IACxB,IAAIS,YAAY,IAAIA,YAAY,CAACV,UAAU,EAAE;MACzC,IAAI,CAACC,YAAY,GAAGrB,MAAM,CAACkC,IAAI,CAACJ,YAAY,CAACV,UAAU,CAAC;MACxD,IAAI,CAACC,YAAY,CAACW,OAAO,CAAEG,CAAC,IAAK,IAAI,CAACf,UAAU,CAACe,CAAC,CAAC,GAAGL,YAAY,CAACV,UAAU,CAACe,CAAC,CAAC,CAAC;IACrF;IACA;IACA,IAAIJ,aAAa,IAAI,IAAI,CAACT,aAAa,KAClC,IAAI,CAACA,aAAa,CAACc,SAAS,IAAI,IAAI,CAACd,aAAa,CAACe,SAAS,CAAC,EAAE;MAChE,IAAI,CAACd,sBAAsB,GAAG,IAAI;IACtC;EACJ;EACAe,WAAW,GAAG;IACV,OAAO,IAAI,CAACnB,aAAa;EAC7B;EACAoB,aAAa,GAAG;IACZ,IAAI,CAACD,WAAW,EAAE;IAClB,IAAI,CAACb,WAAW,CAAC,IAAI,CAACP,mBAAmB,CAAC;EAC9C;EACAsB,iBAAiB,CAAChD,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE;IAC3D,IAAI,IAAI,CAAC6B,sBAAsB,IAAI,IAAI,CAACD,aAAa,EAAE;MACnD;MACA;MACA,IAAI,CAACC,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAACkB,SAAS,CAACjD,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE,IAAI,CAAC4B,aAAa,CAAC;IACnF;EACJ;EACAoB,eAAe,CAAC/C,IAAI,EAAE;IAClB,IAAI,CAAC,IAAI,CAAC6B,KAAK,EAAE;MACb;IACJ;IACA,KAAK,IAAIxD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACwD,KAAK,CAACvD,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,IAAI,IAAI,CAACwD,KAAK,CAACxD,CAAC,CAAC,KAAK2B,IAAI,EAAE;QACxB,IAAI,CAAC6B,KAAK,CAACmB,MAAM,CAAC3E,CAAC,EAAE,CAAC,CAAC;QACvB;MACJ;IACJ;EACJ;EACA4E,2BAA2B,GAAG;IAC1B,IAAI,IAAI,CAACpB,KAAK,CAACvD,MAAM,KAAK,CAAC,EAAE;MACzB,OAAO,EAAE;IACb;IACA,MAAM4E,QAAQ,GAAG,IAAI,CAACrB,KAAK,CAACsB,GAAG,CAAEnD,IAAI,IAAK;MACtC,MAAMoD,QAAQ,GAAGpD,IAAI,CAACE,IAAI,IACtBG,MAAM,CAACkC,IAAI,CAACvC,IAAI,CAACE,IAAI,CAAC,CACjBiD,GAAG,CAAEb,GAAG,IAAK;QACd,OAAOA,GAAG,GAAG,GAAG,GAAGtC,IAAI,CAACE,IAAI,CAACoC,GAAG,CAAC;MACrC,CAAC,CAAC,CACGnD,IAAI,CAAC,GAAG,CAAC;MAClB,OAAQ,SAAQa,IAAI,CAACI,IAAK,aAAYJ,IAAI,CAACqD,MAAO,YAAWD,QAAS,GAAE;IAC5E,CAAC,CAAC;IACF,MAAME,gBAAgB,GAAG,8BAA8B,GAAGJ,QAAQ,GAAG,GAAG;IACxE;IACA,IAAI,CAACrB,KAAK,GAAG,EAAE;IACf,OAAOyB,gBAAgB;EAC3B;EACAC,MAAM,CAAC1D,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEyD,QAAQ,EAAE;IAC1D,IAAI,IAAI,CAAChC,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC+B,MAAM,EAAE;MACjD,OAAO,IAAI,CAAC/B,aAAa,CAAC+B,MAAM,CAAC1D,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEyD,QAAQ,CAAC;IAC3F,CAAC,MACI;MACD,OAAO3D,kBAAkB,CAAC4D,IAAI,CAAC1D,UAAU,EAAEyD,QAAQ,CAAC;IACxD;EACJ;EACAE,WAAW,CAAC7D,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE4D,QAAQ,EAAEN,MAAM,EAAE;IACvE,IAAI,IAAI,CAAC7B,aAAa,IAAI,IAAI,CAACA,aAAa,CAACkC,WAAW,EAAE;MACtD,OAAO,IAAI,CAAClC,aAAa,CAACkC,WAAW,CAAC7D,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE4D,QAAQ,EAAEN,MAAM,CAAC;IACxG,CAAC,MACI;MACD,OAAOxD,kBAAkB,CAAC+D,SAAS,CAAC7D,UAAU,EAAE4D,QAAQ,EAAEN,MAAM,CAAC;IACrE;EACJ;EACAQ,QAAQ,CAAChE,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE4D,QAAQ,EAAEG,SAAS,EAAEC,SAAS,EAAEV,MAAM,EAAE;IAC1F,IAAI,CAACR,iBAAiB,CAAChD,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,CAAC;IACnE,IAAI,IAAI,CAACyB,aAAa,IAAI,IAAI,CAACA,aAAa,CAACqC,QAAQ,EAAE;MACnD,OAAO,IAAI,CAACrC,aAAa,CAACqC,QAAQ,CAAChE,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE4D,QAAQ,EAAEG,SAAS,EAAEC,SAAS,EAAEV,MAAM,CAAC;IAC3H,CAAC,MACI;MACD,OAAOxD,kBAAkB,CAACmE,MAAM,CAACjE,UAAU,EAAE4D,QAAQ,EAAEG,SAAS,EAAEC,SAAS,EAAEV,MAAM,CAAC;IACxF;EACJ;EACA7C,aAAa,CAACX,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEzC,KAAK,EAAE;IAC9D,IAAI,IAAI,CAACkE,aAAa,IAAI,IAAI,CAACA,aAAa,CAAChB,aAAa,EAAE;MACxD,OAAO,IAAI,CAACgB,aAAa,CAAChB,aAAa,CAACX,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEzC,KAAK,CAAC;IAC/F,CAAC,MACI;MACD,OAAOuC,kBAAkB,CAACc,WAAW,CAACZ,UAAU,EAAEzC,KAAK,CAAC;IAC5D;EACJ;EACAsC,cAAc,CAACC,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAE;IAC9D,IAAIA,IAAI,CAACI,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAACyB,KAAK,CAACpD,IAAI,CAACuB,IAAI,CAAC;IACzB;IACA,IAAI,IAAI,CAACwB,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC5B,cAAc,EAAE;MACzD,OAAO,IAAI,CAAC4B,aAAa,CAAC5B,cAAc,CAACC,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,CAAC;IAC/F,CAAC,MACI;MACD,OAAOH,kBAAkB,CAACU,YAAY,CAACR,UAAU,EAAEC,IAAI,CAAC;IAC5D;EACJ;EACAiE,YAAY,CAACpE,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAE8D,SAAS,EAAEC,SAAS,EAAE;IAClF,IAAI/D,IAAI,CAACI,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAAC2C,eAAe,CAAC/C,IAAI,CAAC;IAC9B;IACA,IAAI,CAAC6C,iBAAiB,CAAChD,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,CAAC;IACnE,IAAI,IAAI,CAACyB,aAAa,IAAI,IAAI,CAACA,aAAa,CAACyC,YAAY,EAAE;MACvD,OAAO,IAAI,CAACzC,aAAa,CAACyC,YAAY,CAACpE,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAE8D,SAAS,EAAEC,SAAS,CAAC;IACnH,CAAC,MACI;MACD,OAAOlE,kBAAkB,CAACqE,UAAU,CAACnE,UAAU,EAAEC,IAAI,EAAE8D,SAAS,EAAEC,SAAS,CAAC;IAChF;EACJ;EACAI,YAAY,CAACtE,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAE;IAC5D,IAAIA,IAAI,CAACI,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAAC2C,eAAe,CAAC/C,IAAI,CAAC;IAC9B;IACA,IAAI,CAAC6C,iBAAiB,CAAChD,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,CAAC;IACnE,IAAI,IAAI,CAACyB,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC2C,YAAY,EAAE;MACvD,OAAO,IAAI,CAAC3C,aAAa,CAAC2C,YAAY,CAACtE,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,CAAC;IAC7F,CAAC,MACI;MACD,OAAOH,kBAAkB,CAACuE,UAAU,CAACrE,UAAU,EAAEC,IAAI,CAAC;IAC1D;EACJ;EACA8C,SAAS,CAACa,QAAQ,EAAE3B,OAAO,EAAEqC,MAAM,EAAEC,YAAY,EAAE;IAC/C,IAAI,CAAC3C,aAAa,GAAG2C,YAAY;IACjC,IAAI,IAAI,CAAC9C,aAAa,IAAI,IAAI,CAACA,aAAa,CAACsB,SAAS,EAAE;MACpD,IAAI,CAACtB,aAAa,CAACsB,SAAS,CAACa,QAAQ,EAAE3B,OAAO,EAAEqC,MAAM,EAAEC,YAAY,CAAC;IACzE,CAAC,MACI;MACDX,QAAQ,CAACY,OAAO,CAACF,MAAM,EAAEC,YAAY,CAAC;IAC1C;EACJ;AACJ;AACA;AACA;AACAhF,IAAI,CAAC,eAAe,CAAC,GAAGgC,aAAa;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkD,gBAAgB,CAAC;EACnBnH,WAAW,CAACoH,UAAU,EAAE;IACpB,IAAI,CAACC,OAAO,GAAGpF,IAAI,CAAC0C,OAAO;IAC3B,IAAI,CAACzC,IAAI,GAAG,mBAAmB,GAAGkF,UAAU;EAChD;EACA7E,cAAc,CAAC+D,QAAQ,EAAE3B,OAAO,EAAEqC,MAAM,EAAErE,IAAI,EAAE;IAC5C,QAAQA,IAAI,CAACI,IAAI;MACb,KAAK,WAAW;MAChB,KAAK,WAAW;QACZ,MAAM,IAAIzC,KAAK,CAAE,eAAcqC,IAAI,CAACqD,MAAO,6BAA4B,IAAI,CAAC9D,IAAK,IAAG,CAAC;MACzF,KAAK,WAAW;QACZS,IAAI,GAAG2D,QAAQ,CAACpD,YAAY,CAAC8D,MAAM,EAAErE,IAAI,CAAC;QAC1C;IAAM;IAEd,OAAOA,IAAI;EACf;AACJ;AACA;AACA;AACAV,IAAI,CAAC,kBAAkB,CAAC,GAAGkF,gBAAgB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACAlF,IAAI,CAACqF,YAAY,CAAC,SAAS,EAAE,CAACC,MAAM,EAAEtF,IAAI,EAAEuF,GAAG,KAAK;EAChD,MAAMC,SAAS,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAC9B,KAAK,MAAMC,CAAC,IAAID,CAAC,EACb,IAAIA,CAAC,CAACxG,cAAc,CAACyG,CAAC,CAAC,EACnBF,CAAC,CAACE,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC;IACnB,SAASC,EAAE,GAAG;MACV,IAAI,CAAC7H,WAAW,GAAG0H,CAAC;IACxB;IACAA,CAAC,CAACI,SAAS,GAAGH,CAAC,KAAK,IAAI,GAAG3E,MAAM,CAAC+E,MAAM,CAACJ,CAAC,CAAC,IAAKE,EAAE,CAACC,SAAS,GAAGH,CAAC,CAACG,SAAS,EAAG,IAAID,EAAE,EAAE,CAAC;EAC1F,CAAC;EACD;EACA;EACA,IAAI,CAAC5F,IAAI,EACL,MAAM,IAAI3B,KAAK,CAAC,kBAAkB,CAAC;EACvC,IAAI,OAAO0H,IAAI,KAAK,WAAW,EAAE;IAC7B;IACA;IACA;EACJ;EACA,IAAI,OAAOC,OAAO,IAAI,WAAW,IAAIA,OAAO,CAAC,gBAAgB,CAAC,EAAE;IAC5D;EACJ;EACAA,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI;EAChC,MAAMd,gBAAgB,GAAGlF,IAAI,CAAC,kBAAkB,CAAC;EACjD,MAAMgC,aAAa,GAAGhC,IAAI,CAAC,eAAe,CAAC;EAC3C,IAAI,CAACkF,gBAAgB,EACjB,MAAM,IAAI7G,KAAK,CAAC,2BAA2B,CAAC;EAChD,IAAI,CAAC2D,aAAa,EACd,MAAM,IAAI3D,KAAK,CAAC,wBAAwB,CAAC;EAC7C,MAAM4H,WAAW,GAAGjG,IAAI,CAAC0C,OAAO;EAChC,MAAMwD,MAAM,GAAGlG,IAAI,CAACK,UAAU;EAC9B;EACA,MAAM8F,2BAA2B,GAAGb,MAAM,CAACY,MAAM,CAAC,+BAA+B,CAAC,CAAC,KAAK,IAAI;EAC5F;EACA;EACA;EACA,MAAME,mCAAmC,GAAG,CAACD,2BAA2B,KAClEb,MAAM,CAACY,MAAM,CAAC,oBAAoB,CAAC,CAAC,KAAK,IAAI,IAC1CZ,MAAM,CAACY,MAAM,CAAC,wCAAwC,CAAC,CAAC,KAAK,IAAK,CAAC;EAC5E,MAAMG,wBAAwB,GAAGf,MAAM,CAACY,MAAM,CAAC,0BAA0B,CAAC,CAAC,KAAK,IAAI;EACpF,IAAI,CAACG,wBAAwB,EAAE;IAC3B,MAAMC,YAAY,GAAGN,OAAO,CAACO,YAAY;IACzC,IAAID,YAAY,IAAI,CAACN,OAAO,CAACE,MAAM,CAAC,cAAc,CAAC,CAAC,EAAE;MAClDF,OAAO,CAACE,MAAM,CAAC,cAAc,CAAC,CAAC,GAAGI,YAAY;MAC9CN,OAAO,CAACO,YAAY,GAAG,YAAY;QAC/B,MAAMC,QAAQ,GAAG,IAAIF,YAAY,EAAE;QACnC,MAAMG,eAAe,GAAGD,QAAQ,CAACE,OAAO;QACxC,IAAID,eAAe,IAAI,CAACD,QAAQ,CAACN,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE;UACjDM,QAAQ,CAACN,MAAM,CAAC,SAAS,CAAC,CAAC,GAAGO,eAAe;UAC7CD,QAAQ,CAACE,OAAO,GAAG,YAAY;YAC3B,MAAMC,MAAM,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAI,CAAC,CAACA,OAAO,CAACC,EAAE;YAC7D;YACA;YACA;YACA;YACA;YACA;YACA,MAAMC,gBAAgB,GAAGH,MAAM,GAAGC,OAAO,CAACG,SAAS,CAAC,oBAAoB,CAAC,GACrEzB,MAAM,CAAC0B,cAAc,CAAC,oBAAoB,CAAC;YAC/C,MAAMC,MAAM,GAAGR,eAAe,CAACS,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;YACrDR,MAAM,GAAGC,OAAO,CAACQ,kBAAkB,CAAC,oBAAoB,CAAC,GACrD9B,MAAM,CAAC8B,kBAAkB,CAAC,oBAAoB,CAAC;YACnD,IAAIN,gBAAgB,EAAE;cAClBA,gBAAgB,CAAC/D,OAAO,CAACsE,OAAO,IAAI;gBAChC,IAAIV,MAAM,EAAE;kBACRC,OAAO,CAACC,EAAE,CAAC,oBAAoB,EAAEQ,OAAO,CAAC;gBAC7C,CAAC,MACI;kBACD/B,MAAM,CAACgC,gBAAgB,CAAC,oBAAoB,EAAED,OAAO,CAAC;gBAC1D;cACJ,CAAC,CAAC;YACN;YACA,OAAOJ,MAAM;UACjB,CAAC;QACL;QACA,OAAOT,QAAQ;MACnB,CAAC;IACL;EACJ;EACA;EACA,MAAMe,UAAU,GAAGvB,OAAO,CAACwB,MAAM,EAAE;EACnC,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAACzE,OAAO,CAAC0E,UAAU,IAAI;IACzD,IAAIC,iBAAiB,GAAGH,UAAU,CAACE,UAAU,CAAC;IAC9CF,UAAU,CAACE,UAAU,CAAC,GAAG,UAAUE,WAAW,EAAEC,eAAe,EAAE;MAC7D,OAAOF,iBAAiB,CAACG,IAAI,CAAC,IAAI,EAAEF,WAAW,EAAEG,kBAAkB,CAACH,WAAW,EAAEC,eAAe,CAAC,CAAC;IACtG,CAAC;EACL,CAAC,CAAC;EACF,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC7E,OAAO,CAAC0E,UAAU,IAAI;IACvC,IAAIC,iBAAiB,GAAGH,UAAU,CAACE,UAAU,CAAC;IAC9CF,UAAU,CAACrB,MAAM,CAACuB,UAAU,CAAC,CAAC,GAAGC,iBAAiB;IAClDH,UAAU,CAACE,UAAU,CAAC,GAAG,UAAUE,WAAW,EAAEC,eAAe,EAAEG,OAAO,EAAE;MACtEZ,SAAS,CAAC,CAAC,CAAC,GAAGa,cAAc,CAACJ,eAAe,CAAC;MAC9C,OAAOF,iBAAiB,CAACR,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACnD,CAAC;EACL,CAAC,CAAC;EACF,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC,CAACpE,OAAO,CAAC0E,UAAU,IAAI;IACvE,IAAIC,iBAAiB,GAAGH,UAAU,CAACE,UAAU,CAAC;IAC9CF,UAAU,CAACrB,MAAM,CAACuB,UAAU,CAAC,CAAC,GAAGC,iBAAiB;IAClDH,UAAU,CAACE,UAAU,CAAC,GAAG,UAAUG,eAAe,EAAEG,OAAO,EAAE;MACzDZ,SAAS,CAAC,CAAC,CAAC,GAAGa,cAAc,CAACJ,eAAe,CAAC;MAC9C,OAAOF,iBAAiB,CAACR,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACnD,CAAC;EACL,CAAC,CAAC;EACF,IAAI,CAAChB,2BAA2B,EAAE;IAC9B;IACA;IACA,MAAM8B,eAAe,GAAIjC,OAAO,CAACE,MAAM,CAAC,OAAO,CAAC,CAAC,GAAGF,OAAO,CAAC,OAAO,CAAE;IACrEA,OAAO,CAAC,OAAO,CAAC,GAAG,YAAY;MAC3B,MAAMkC,KAAK,GAAGD,eAAe,CAACf,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACpD,IAAI,CAACe,KAAK,CAAChC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE;QAC3BgC,KAAK,CAAChC,MAAM,CAAC,SAAS,CAAC,CAAC,GAAGA,MAAM,CAAC,SAAS,CAAC;QAC5C,MAAMiC,YAAY,GAAID,KAAK,CAAChC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAGgC,KAAK,CAACE,IAAK;QACzDF,KAAK,CAACE,IAAI,GAAG,YAAY;UACrB,MAAMC,iBAAiB,GAAGrI,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAI4F,iBAAiB,EAAE;YACnB,OAAOA,iBAAiB,CAACD,IAAI,CAAClB,KAAK,CAACmB,iBAAiB,EAAElB,SAAS,CAAC;UACrE;UACA,OAAOgB,YAAY,CAACjB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;QAC9C,CAAC;QACD,MAAMmB,gBAAgB,GAAIJ,KAAK,CAAChC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAGgC,KAAK,CAACK,QAAS;QACrEL,KAAK,CAACK,QAAQ,GAAG,YAAY;UACzB,MAAMF,iBAAiB,GAAGrI,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAI4F,iBAAiB,EAAE;YACnB,MAAMG,QAAQ,GAAGrB,SAAS,CAACnI,MAAM,GAAG,CAAC,GAAGmI,SAAS,CAAC,CAAC,CAAC,GAAG,IAAIhJ,IAAI,EAAE;YACjE,OAAOkK,iBAAiB,CAACI,qBAAqB,CAACvB,KAAK,CAACmB,iBAAiB,EAAEG,QAAQ,IAAI,OAAOA,QAAQ,CAAChJ,OAAO,KAAK,UAAU,GAAG,CAACgJ,QAAQ,CAAChJ,OAAO,EAAE,CAAC,GAC7I2H,SAAS,CAAC;UAClB;UACA,OAAOmB,gBAAgB,CAACpB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;QAClD,CAAC;QACD;QACA,IAAIf,mCAAmC,EAAE;UACrC,CAAC,SAAS,EAAE,WAAW,CAAC,CAACrD,OAAO,CAAC0E,UAAU,IAAI;YAC3C,MAAMQ,eAAe,GAAIC,KAAK,CAAChC,MAAM,CAACuB,UAAU,CAAC,CAAC,GAAGS,KAAK,CAACT,UAAU,CAAE;YACvES,KAAK,CAACT,UAAU,CAAC,GAAG,YAAY;cAC5B,MAAMiB,qBAAqB,GAAG1I,IAAI,CAAC,uBAAuB,CAAC;cAC3D,IAAI0I,qBAAqB,EAAE;gBACvB1C,OAAO,CAACE,MAAM,CAAC,gBAAgB,CAAC,CAAC,GAAG,SAAS,KAAKuB,UAAU;gBAC5D;cACJ;cACA,OAAOQ,eAAe,CAACf,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;YACjD,CAAC;UACL,CAAC,CAAC;QACN;MACJ;MACA,OAAOe,KAAK;IAChB,CAAC;EACL;EACA;EACA,IAAI,CAAClC,OAAO,CAAChG,IAAI,CAACK,UAAU,CAAC,cAAc,CAAC,CAAC,EAAE;IAC3C,MAAMsI,oBAAoB,GAAG3C,OAAO,CAAC4C,YAAY;IACjD5C,OAAO,CAAChG,IAAI,CAACK,UAAU,CAAC,cAAc,CAAC,CAAC,GAAGsI,oBAAoB;IAC/D3C,OAAO,CAAC4C,YAAY,GAAG,YAAY;MAC/B,MAAMC,IAAI,GAAGC,KAAK,CAACjD,SAAS,CAACkD,KAAK,CAAClB,IAAI,CAACV,SAAS,CAAC;MAClD,MAAM6B,aAAa,GAAGH,IAAI,CAAC7J,MAAM,IAAI,CAAC,GAAG6J,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;MACvD,IAAII,MAAM;MACV,IAAID,aAAa,EAAE;QACf,MAAME,cAAc,GAAGnI,MAAM,CAACmI,cAAc;QAC5CnI,MAAM,CAACmI,cAAc,GAAG,UAAUC,GAAG,EAAExD,CAAC,EAAEyD,UAAU,EAAE;UAClD,OAAOF,cAAc,CAACrB,IAAI,CAAC,IAAI,EAAEsB,GAAG,EAAExD,CAAC,EAAE5E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEoI,UAAU,CAAC,EAAE;YAAEC,YAAY,EAAE,IAAI;YAAEC,UAAU,EAAE;UAAK,CAAC,CAAC,CAAC;QACpI,CAAC;QACD,IAAI;UACAL,MAAM,GAAGN,oBAAoB,CAACzB,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;QACnD,CAAC,SACO;UACJ9H,MAAM,CAACmI,cAAc,GAAGA,cAAc;QAC1C;MACJ,CAAC,MACI;QACDD,MAAM,GAAGN,oBAAoB,CAACzB,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;MACnD;MACA,OAAOI,MAAM;IACjB,CAAC;EACL;EACA;AACJ;AACA;AACA;EACI,SAASnB,kBAAkB,CAACH,WAAW,EAAE4B,YAAY,EAAE;IACnD,OAAO,YAAY;MACf;MACA;MACA,MAAMC,QAAQ,GAAGvD,WAAW,CAAC9B,IAAI,CAAC,IAAIe,gBAAgB,CAAE,oBAAmByC,WAAY,EAAC,CAAC,CAAC;MAC1F,OAAO6B,QAAQ,CAACC,GAAG,CAACF,YAAY,EAAE,IAAI,EAAEpC,SAAS,CAAC;IACtD,CAAC;EACL;EACA,SAASuC,aAAa,CAACC,QAAQ,EAAEnF,SAAS,EAAEoF,WAAW,EAAEC,IAAI,EAAE;IAC3D,MAAMC,gBAAgB,GAAG,CAAC,CAAC9D,OAAO,CAACE,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAC5D0D,WAAW,CAACG,iBAAiB;IAC7B,MAAMC,aAAa,GAAGJ,WAAW,CAACI,aAAa;IAC/C,IAAIF,gBAAgB,IAAI1D,mCAAmC,EAAE;MACzD;MACA,MAAM6D,eAAe,GAAGjK,IAAI,CAACA,IAAI,CAACK,UAAU,CAAC,eAAe,CAAC,CAAC;MAC9D,IAAI4J,eAAe,IAAI,OAAOA,eAAe,CAACC,SAAS,KAAK,UAAU,EAAE;QACpEP,QAAQ,GAAGM,eAAe,CAACC,SAAS,CAACP,QAAQ,CAAC;MAClD;IACJ;IACA,IAAIE,IAAI,EAAE;MACN,OAAOG,aAAa,CAACP,GAAG,CAACE,QAAQ,EAAEnF,SAAS,EAAE,CAACqF,IAAI,CAAC,CAAC;IACzD,CAAC,MACI;MACD,OAAOG,aAAa,CAACP,GAAG,CAACE,QAAQ,EAAEnF,SAAS,CAAC;IACjD;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI,SAASwD,cAAc,CAAC2B,QAAQ,EAAE;IAC9B;IACA;IACA;IACA,OAAQA,QAAQ,KAAKA,QAAQ,CAAC3K,MAAM,GAAG,UAAU6K,IAAI,EAAE;MACnD,OAAOH,aAAa,CAACC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACC,WAAW,EAAEC,IAAI,CAAC;IAChE,CAAC,GAAG,YAAY;MACZ,OAAOH,aAAa,CAACC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACC,WAAW,CAAC;IAC1D,CAAC,CAAC;EACN;EACA,MAAMO,WAAW,GAAGnE,OAAO,CAACmE,WAAW;EACvCnE,OAAO,CAACmE,WAAW,GAAI,UAAUC,MAAM,EAAE;IACrC5E,SAAS,CAAC6E,eAAe,EAAED,MAAM,CAAC;IAClC,SAASC,eAAe,CAACC,KAAK,EAAE;MAC5B,IAAIA,KAAK,CAACC,UAAU,EAAE;QAClBD,KAAK,CAACC,UAAU,GAAG,CAACC,EAAE,IAAI,MAAM;UAC5B;UACA,IAAI,CAACR,aAAa,GAAG,IAAI;UACzB,IAAI,CAACD,iBAAiB,GAAG,IAAI;UAC7B9D,WAAW,CAACwE,iBAAiB,CAAC,oBAAoB,EAAED,EAAE,CAAC;QAC3D,CAAC,EAAEF,KAAK,CAACC,UAAU,CAAC;MACxB;MACA,MAAMG,gBAAgB,GAAGpF,MAAM,CAACtF,IAAI,CAACK,UAAU,CAAC,YAAY,CAAC,CAAC;MAC9D,MAAMsK,kBAAkB,GAAGrF,MAAM,CAACtF,IAAI,CAACK,UAAU,CAAC,cAAc,CAAC,CAAC;MAClE,IAAIqK,gBAAgB,EAAE;QAClB;QACAJ,KAAK,CAACvC,OAAO,GAAG;UACZ6C,UAAU,EAAEF,gBAAgB,GAAGA,gBAAgB,GAAGpF,MAAM,CAACsF,UAAU;UACnEC,YAAY,EAAEF,kBAAkB,GAAGA,kBAAkB,GAAGrF,MAAM,CAACuF;QACnE,CAAC;MACL;MACA;MACA;MACA,IAAI7E,OAAO,CAAC8E,WAAW,EAAE;QACrB,IAAI,CAACR,KAAK,CAACS,WAAW,EAAE;UACpBT,KAAK,CAACS,WAAW,GAAG,IAAI/E,OAAO,CAAC8E,WAAW,EAAE;QACjD;QACAR,KAAK,CAACS,WAAW,CAACnB,WAAW,GAAG,IAAI;MACxC,CAAC,MACI;QACD,IAAI,CAACU,KAAK,CAACS,WAAW,EAAE;UACpBT,KAAK,CAACS,WAAW,GAAG,CAAC,CAAC;QAC1B;QACAT,KAAK,CAACS,WAAW,CAACnB,WAAW,GAAG,IAAI;MACxC;MACA;MACA,MAAMoB,WAAW,GAAGV,KAAK,CAACU,WAAW;MACrCV,KAAK,CAACU,WAAW,GAAG,UAAUhN,KAAK,EAAE;QACjC,IAAIA,KAAK,IACLA,KAAK,CAACiN,OAAO,KACT,wGAAwG,EAAE;UAC9G;UACA;UACA,MAAMC,aAAa,GAAG,IAAI,IAAI,IAAI,CAACnB,iBAAiB;UACpD,IAAImB,aAAa,EAAE;YACf,MAAMlH,gBAAgB,GAAGkH,aAAa,CAACvH,2BAA2B,EAAE;YACpE,IAAI;cACA;cACA3F,KAAK,CAACiN,OAAO,IAAIjH,gBAAgB;YACrC,CAAC,CACD,OAAOzF,GAAG,EAAE,CACZ;UACJ;QACJ;QACA,IAAIyM,WAAW,EAAE;UACbA,WAAW,CAACnD,IAAI,CAAC,IAAI,EAAE7J,KAAK,CAAC;QACjC;MACJ,CAAC;MACDoM,MAAM,CAACvC,IAAI,CAAC,IAAI,EAAEyC,KAAK,CAAC;IAC5B;IACAD,eAAe,CAACxE,SAAS,CAACsF,OAAO,GAAG,YAAY;MAC5C,IAAIC,IAAI,GAAGpL,IAAI,CAAC0C,OAAO;MACvB,IAAI2I,oBAAoB,GAAG,KAAK;MAChC,OAAOD,IAAI,EAAE;QACT,IAAIA,IAAI,KAAKnF,WAAW,EAAE;UACtBoF,oBAAoB,GAAG,IAAI;UAC3B;QACJ;QACAD,IAAI,GAAGA,IAAI,CAACE,MAAM;MACtB;MACA,IAAI,CAACD,oBAAoB,EACrB,MAAM,IAAIhN,KAAK,CAAC,mBAAmB,GAAG2B,IAAI,CAAC0C,OAAO,CAACzC,IAAI,CAAC;MAC5D;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC8J,iBAAiB,GAAG,IAAI/H,aAAa,EAAE;MAC5C,IAAI,CAACgI,aAAa,GAAG/D,WAAW,CAAC9B,IAAI,CAAC,IAAI,CAAC4F,iBAAiB,CAAC;MAC7D,IAAI,CAAC/J,IAAI,CAACW,WAAW,EAAE;QACnB;QACA;QACA;QACA;QACA;QACAX,IAAI,CAAC0C,OAAO,CAAC+H,iBAAiB,CAAC,6BAA6B,EAAE,MAAMN,WAAW,CAACtE,SAAS,CAACsF,OAAO,CAACtD,IAAI,CAAC,IAAI,CAAC,CAAC;MACjH,CAAC,MACI;QACDuC,MAAM,CAACvE,SAAS,CAACsF,OAAO,CAACtD,IAAI,CAAC,IAAI,CAAC;MACvC;IACJ,CAAC;IACD,OAAOwC,eAAe;EAC1B,CAAC,CAAEF,WAAW,CAAC;AACnB,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACAnK,IAAI,CAACqF,YAAY,CAAC,MAAM,EAAE,CAACkG,OAAO,EAAEvL,IAAI,EAAEuF,GAAG,KAAK;EAC9C,IAAI,OAAOQ,IAAI,KAAK,WAAW,IAAIA,IAAI,CAAC,gBAAgB,CAAC,EAAE;IACvD;EACJ;EACAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI;EAC7B,MAAM/D,aAAa,GAAGhC,IAAI,CAAC,eAAe,CAAC;EAC3C,MAAMkF,gBAAgB,GAAGlF,IAAI,CAAC,kBAAkB,CAAC;EACjD,IAAI,CAACgC,aAAa,EAAE;IAChB,MAAM,IAAI3D,KAAK,CAAC,uBAAuB,CAAC;EAC5C;EACA,MAAMmN,QAAQ,GAAGxL,IAAI,CAAC0C,OAAO;EAC7B,MAAM8G,QAAQ,GAAGgC,QAAQ,CAACrH,IAAI,CAAC,IAAIe,gBAAgB,CAAC,eAAe,CAAC,CAAC;EACrE,MAAMgG,aAAa,GAAG,IAAIlJ,aAAa,EAAE;EACzC,MAAMyJ,SAAS,GAAGD,QAAQ,CAACrH,IAAI,CAAC+G,aAAa,CAAC;EAC9C,SAASQ,yBAAyB,CAACC,cAAc,EAAE;IAC/C,OAAO,UAAU,GAAGC,SAAS,EAAE;MAC3B,MAAMC,kBAAkB,GAAGF,cAAc,CAACzE,KAAK,CAAC,IAAI,EAAE0E,SAAS,CAAC;MAChE,OAAO,UAAU,GAAG/C,IAAI,EAAE;QACtBA,IAAI,CAAC,CAAC,CAAC,GAAGf,kBAAkB,CAACe,IAAI,CAAC,CAAC,CAAC,CAAC;QACrC,OAAOgD,kBAAkB,CAAC3E,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;MAC/C,CAAC;IACL,CAAC;EACL;EACA,SAASiD,qBAAqB,CAACH,cAAc,EAAE;IAC3C,OAAO,UAAU,GAAGC,SAAS,EAAE;MAC3B,OAAO,UAAU,GAAG/C,IAAI,EAAE;QACtBA,IAAI,CAAC,CAAC,CAAC,GAAGb,cAAc,CAACa,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,OAAO8C,cAAc,CAACzE,KAAK,CAAC,IAAI,EAAE0E,SAAS,CAAC,CAAC1E,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;MAClE,CAAC;IACL,CAAC;EACL;EACA;AACJ;AACA;AACA;EACI,SAASf,kBAAkB,CAACyB,YAAY,EAAE;IACtC,OAAO,UAAU,GAAGV,IAAI,EAAE;MACtB,OAAOW,QAAQ,CAACC,GAAG,CAACF,YAAY,EAAE,IAAI,EAAEV,IAAI,CAAC;IACjD,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACI,SAASb,cAAc,CAAC2B,QAAQ,EAAEoC,UAAU,GAAG,KAAK,EAAE;IAClD,IAAI,OAAOpC,QAAQ,KAAK,UAAU,EAAE;MAChC,OAAOA,QAAQ;IACnB;IACA,MAAMqC,WAAW,GAAG,YAAY;MAC5B,IAAIhM,IAAI,CAACuF,GAAG,CAACW,MAAM,CAAC,qBAAqB,CAAC,CAAC,KAAK,IAAI,IAAIyD,QAAQ,IAC5D,CAACA,QAAQ,CAACsC,WAAW,EAAE;QACvB;QACA,MAAMhC,eAAe,GAAGjK,IAAI,CAACA,IAAI,CAACK,UAAU,CAAC,eAAe,CAAC,CAAC;QAC9D,IAAI4J,eAAe,IAAI,OAAOA,eAAe,CAACC,SAAS,KAAK,UAAU,EAAE;UACpEP,QAAQ,GAAGM,eAAe,CAACC,SAAS,CAACP,QAAQ,CAAC;QAClD;MACJ;MACAuB,aAAa,CAACa,UAAU,GAAGA,UAAU;MACrC,OAAON,SAAS,CAAChC,GAAG,CAACE,QAAQ,EAAE,IAAI,EAAExC,SAAS,CAAC;IACnD,CAAC;IACD;IACA;IACApG,MAAM,CAACmI,cAAc,CAAC8C,WAAW,EAAE,QAAQ,EAAE;MAAE3C,YAAY,EAAE,IAAI;MAAE6C,QAAQ,EAAE,IAAI;MAAE5C,UAAU,EAAE;IAAM,CAAC,CAAC;IACvG0C,WAAW,CAAChN,MAAM,GAAG2K,QAAQ,CAAC3K,MAAM;IACpC,OAAOgN,WAAW;EACtB;EACA,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAACjJ,OAAO,CAAC0E,UAAU,IAAI;IACzD,IAAIkE,cAAc,GAAGJ,OAAO,CAAC9D,UAAU,CAAC;IACxC,IAAI8D,OAAO,CAACvL,IAAI,CAACK,UAAU,CAACoH,UAAU,CAAC,CAAC,EAAE;MACtC;IACJ;IACA8D,OAAO,CAACvL,IAAI,CAACK,UAAU,CAACoH,UAAU,CAAC,CAAC,GAAGkE,cAAc;IACrDJ,OAAO,CAAC9D,UAAU,CAAC,GAAG,UAAU,GAAGoB,IAAI,EAAE;MACrCA,IAAI,CAAC,CAAC,CAAC,GAAGf,kBAAkB,CAACe,IAAI,CAAC,CAAC,CAAC,CAAC;MACrC,OAAO8C,cAAc,CAACzE,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;IAC3C,CAAC;IACD0C,OAAO,CAAC9D,UAAU,CAAC,CAAC0E,IAAI,GAAGT,yBAAyB,CAACC,cAAc,CAACQ,IAAI,CAAC;EAC7E,CAAC,CAAC;EACFZ,OAAO,CAACa,QAAQ,CAACC,IAAI,GAAGd,OAAO,CAACe,SAAS;EACzCf,OAAO,CAACa,QAAQ,CAACG,IAAI,GAAGhB,OAAO,CAACiB,SAAS;EACzC,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAACzJ,OAAO,CAAC0E,UAAU,IAAI;IACxD,IAAIkE,cAAc,GAAGJ,OAAO,CAAC9D,UAAU,CAAC;IACxC,IAAI8D,OAAO,CAACvL,IAAI,CAACK,UAAU,CAACoH,UAAU,CAAC,CAAC,EAAE;MACtC;IACJ;IACA8D,OAAO,CAACvL,IAAI,CAACK,UAAU,CAACoH,UAAU,CAAC,CAAC,GAAGkE,cAAc;IACrDJ,OAAO,CAAC9D,UAAU,CAAC,GAAG,UAAU,GAAGoB,IAAI,EAAE;MACrCA,IAAI,CAAC,CAAC,CAAC,GAAGb,cAAc,CAACa,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;MACvC,OAAO8C,cAAc,CAACzE,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;IAC3C,CAAC;IACD0C,OAAO,CAAC9D,UAAU,CAAC,CAAC0E,IAAI,GAAGL,qBAAqB,CAACH,cAAc,CAACQ,IAAI,CAAC;IACrEZ,OAAO,CAAC9D,UAAU,CAAC,CAACgF,IAAI,GAAGd,cAAc,CAACc,IAAI;EAClD,CAAC,CAAC;EACFlB,OAAO,CAACmB,EAAE,CAACL,IAAI,GAAGd,OAAO,CAACoB,GAAG;EAC7BpB,OAAO,CAACmB,EAAE,CAACH,IAAI,GAAGhB,OAAO,CAACqB,GAAG;EAC7BrB,OAAO,CAACsB,IAAI,CAACR,IAAI,GAAGd,OAAO,CAACoB,GAAG;EAC/BpB,OAAO,CAACsB,IAAI,CAACN,IAAI,GAAGhB,OAAO,CAACqB,GAAG;EAC/B,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC7J,OAAO,CAAC0E,UAAU,IAAI;IACvE,IAAIkE,cAAc,GAAGJ,OAAO,CAAC9D,UAAU,CAAC;IACxC,IAAI8D,OAAO,CAACvL,IAAI,CAACK,UAAU,CAACoH,UAAU,CAAC,CAAC,EAAE;MACtC;IACJ;IACA8D,OAAO,CAACvL,IAAI,CAACK,UAAU,CAACoH,UAAU,CAAC,CAAC,GAAGkE,cAAc;IACrDJ,OAAO,CAAC9D,UAAU,CAAC,GAAG,UAAU,GAAGoB,IAAI,EAAE;MACrCA,IAAI,CAAC,CAAC,CAAC,GAAGb,cAAc,CAACa,IAAI,CAAC,CAAC,CAAC,CAAC;MACjC,OAAO8C,cAAc,CAACzE,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;IAC3C,CAAC;EACL,CAAC,CAAC;EACF7I,IAAI,CAAC8M,eAAe,GAAG,SAASA,eAAe,CAACC,KAAK,EAAEC,QAAQ,GAAG,KAAK,EAAE;IACrE;IACA,SAASC,mBAAmB,GAAG;MAC3B,MAAM5E,iBAAiB,GAAGrI,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;MACnE,OAAO,CAAC,CAAC4F,iBAAiB;IAC9B;IACA;IACA;IACA,SAAS6E,YAAY,GAAG;MACpB,MAAMhC,aAAa,GAAGlL,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,eAAe,CAAC;MACvD,OAAOyI,aAAa,IAAIA,aAAa,CAACa,UAAU;IACpD;IACA,IAAIgB,KAAK,CAACxH,GAAG,CAACW,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE;MACjC;IACJ;IACA6G,KAAK,CAACxH,GAAG,CAACW,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI;IACtC;IACAX,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,kBAAkB,EAAE1I,QAAQ,IAAI;MACnD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,IAAIoE,mBAAmB,EAAE,EAAE;UACvB,OAAO,IAAI;QACf,CAAC,MACI;UACD,OAAO5I,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAE1I,QAAQ,IAAI;MAChD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB7I,IAAI,CAACuF,GAAG,CAACW,MAAM,CAAC,qBAAqB,CAAC,CAAC,GAAG,IAAI;QAC9C,IAAI8G,QAAQ,IAAIE,YAAY,EAAE,EAAE;UAC5B,OAAO7I,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;QACA,OAAOuE,IAAI;MACf,CAAC;IACL,CAAC,CAAC;IACF;IACA7H,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAE1I,QAAQ,IAAI;MAChD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB7I,IAAI,CAACuF,GAAG,CAACW,MAAM,CAAC,qBAAqB,CAAC,CAAC,GAAG,KAAK;QAC/C,IAAI8G,QAAQ,IAAIE,YAAY,EAAE,EAAE;UAC5B,OAAO7I,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;QACA,OAAOuE,IAAI;MACf,CAAC;IACL,CAAC,CAAC;IACF;IACA7H,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAE1I,QAAQ,IAAI;MAChD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGrI,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAI4F,iBAAiB,IAAI4E,mBAAmB,EAAE,EAAE;UAC5C5E,iBAAiB,CAACI,qBAAqB,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,MACI;UACD,OAAOxE,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,mBAAmB,EAAE1I,QAAQ,IAAI;MACpD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGrI,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAI4F,iBAAiB,IAAI4E,mBAAmB,EAAE,EAAE;UAC5C,OAAO5E,iBAAiB,CAACgF,iBAAiB,EAAE;QAChD,CAAC,MACI;UACD,OAAOhJ,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,aAAa,EAAE1I,QAAQ,IAAI;MAC9C,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGrI,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAI4F,iBAAiB,EAAE;UACnBA,iBAAiB,CAACiF,eAAe,EAAE;QACvC,CAAC,MACI;UACD,OAAOjJ,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,cAAc,EAAE1I,QAAQ,IAAI;MAC/C,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGrI,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAI4F,iBAAiB,EAAE;UACnBA,iBAAiB,CAACkF,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC;QACtC,CAAC,MACI;UACD,OAAOlJ,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,qBAAqB,EAAE1I,QAAQ,IAAI;MACtD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGrI,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAI4F,iBAAiB,EAAE;UACnBA,iBAAiB,CAACD,IAAI,CAACS,IAAI,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,MACI;UACD,OAAOxE,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,sBAAsB,EAAE1I,QAAQ,IAAI;MACvD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGrI,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAI4F,iBAAiB,EAAE;UACnBA,iBAAiB,CAACmF,sBAAsB,EAAE;QAC9C,CAAC,MACI;UACD,OAAOnJ,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,0BAA0B,EAAE1I,QAAQ,IAAI;MAC3D,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGrI,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAI4F,iBAAiB,EAAE;UACnBA,iBAAiB,CAACoF,UAAU,CAAC5E,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,MACI;UACD,OAAOxE,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,gBAAgB,EAAE1I,QAAQ,IAAI;MACjD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGrI,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAI4F,iBAAiB,EAAE;UACnBA,iBAAiB,CAACqF,eAAe,EAAE;QACvC,CAAC,MACI;UACD,OAAOrJ,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAE1I,QAAQ,IAAI;MAChD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGrI,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAI4F,iBAAiB,EAAE;UACnB,OAAOA,iBAAiB,CAACsF,aAAa,EAAE;QAC5C,CAAC,MACI;UACD,OAAOtJ,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;EACN,CAAC;AACL,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA7I,IAAI,CAACqF,YAAY,CAAC,OAAO,EAAE,CAACC,MAAM,EAAEtF,IAAI,KAAK;EACzC,MAAM4N,KAAK,GAAGtI,MAAM,CAACsI,KAAK;EAC1B,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;IAC9B;IACA;IACA;EACJ;EACA,IAAI,OAAO5N,IAAI,KAAK,WAAW,EAAE;IAC7B,MAAM,IAAI3B,KAAK,CAAC,iBAAiB,CAAC;EACtC;EACA,MAAM2D,aAAa,GAAGhC,IAAI,CAAC,eAAe,CAAC;EAC3C,MAAMkF,gBAAgB,GAAGlF,IAAI,CAAC,kBAAkB,CAAC;EACjD,IAAI,CAACgC,aAAa,EAAE;IAChB,MAAM,IAAI3D,KAAK,CAAC,uBAAuB,CAAC;EAC5C;EACA,IAAIuP,KAAK,CAAC,gBAAgB,CAAC,EAAE;IACzB,MAAM,IAAIvP,KAAK,CAAC,+CAA+C,CAAC;EACpE;EACAuP,KAAK,CAAC,gBAAgB,CAAC,GAAG,IAAI;EAC9B,MAAMpC,QAAQ,GAAGxL,IAAI,CAAC0C,OAAO;EAC7B,MAAM8G,QAAQ,GAAGgC,QAAQ,CAACrH,IAAI,CAAC,IAAIe,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;EACtE,IAAI2I,QAAQ,GAAG,IAAI;EACnB,MAAMC,SAAS,GAAGtC,QAAQ,CAACrH,IAAI,CAAC,IAAInC,aAAa,EAAE,CAAC;EACpD,MAAM+L,aAAa,GAAG;IAClBC,KAAK,EAAE1I,MAAM,CAAC0I,KAAK;IACnBC,SAAS,EAAE3I,MAAM,CAAC2I,SAAS;IAC3BC,MAAM,EAAE5I,MAAM,CAAC4I,MAAM;IACrBC,UAAU,EAAE7I,MAAM,CAAC6I,UAAU;IAC7B/B,QAAQ,EAAE9G,MAAM,CAAC8G,QAAQ;IACzBM,EAAE,EAAEpH,MAAM,CAACoH;EACf,CAAC;EACD,SAAS0B,eAAe,CAACvF,IAAI,EAAEwF,QAAQ,EAAEC,SAAS,EAAE;IAChD,KAAK,IAAIvP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8J,IAAI,CAAC7J,MAAM,EAAED,CAAC,EAAE,EAAE;MAClC,IAAIwP,GAAG,GAAG1F,IAAI,CAAC9J,CAAC,CAAC;MACjB,IAAI,OAAOwP,GAAG,KAAK,UAAU,EAAE;QAC3B;QACA;QACA;QACA;QACA;QACA1F,IAAI,CAAC9J,CAAC,CAAC,GAAIwP,GAAG,CAACvP,MAAM,KAAK,CAAC,GAAIqP,QAAQ,CAACE,GAAG,CAAC,GAAGD,SAAS,CAACC,GAAG,CAAC;QAC7D;QACA;QACA1F,IAAI,CAAC9J,CAAC,CAAC,CAACyP,QAAQ,GAAG,YAAY;UAC3B,OAAOD,GAAG,CAACC,QAAQ,EAAE;QACzB,CAAC;MACL;IACJ;IACA,OAAO3F,IAAI;EACf;EACA,SAASf,kBAAkB,CAACe,IAAI,EAAE;IAC9B,MAAMwF,QAAQ,GAAG,UAAU7D,EAAE,EAAE;MAC3B,OAAO,YAAY;QACf,OAAOhB,QAAQ,CAACC,GAAG,CAACe,EAAE,EAAE,IAAI,EAAErD,SAAS,CAAC;MAC5C,CAAC;IACL,CAAC;IACD,OAAOiH,eAAe,CAACvF,IAAI,EAAEwF,QAAQ,CAAC;EAC1C;EACA,SAASrG,cAAc,CAACa,IAAI,EAAE;IAC1B,MAAMyF,SAAS,GAAG,UAAU9D,EAAE,EAAE;MAC5B,OAAO,UAAUX,IAAI,EAAE;QACnB,OAAOgE,QAAQ,CAACpE,GAAG,CAACe,EAAE,EAAE,IAAI,EAAE,CAACX,IAAI,CAAC,CAAC;MACzC,CAAC;IACL,CAAC;IACD,MAAMwE,QAAQ,GAAG,UAAU7D,EAAE,EAAE;MAC3B,OAAO,YAAY;QACf,OAAOqD,QAAQ,CAACpE,GAAG,CAACe,EAAE,EAAE,IAAI,CAAC;MACjC,CAAC;IACL,CAAC;IACD,OAAO4D,eAAe,CAACvF,IAAI,EAAEwF,QAAQ,EAAEC,SAAS,CAAC;EACrD;EACA,SAASG,eAAe,CAAC5F,IAAI,EAAE;IAC3B,MAAMyF,SAAS,GAAG,UAAU9D,EAAE,EAAE;MAC5B,OAAO,UAAUX,IAAI,EAAE;QACnB,OAAOiE,SAAS,CAACrE,GAAG,CAACe,EAAE,EAAE,IAAI,EAAE,CAACX,IAAI,CAAC,CAAC;MAC1C,CAAC;IACL,CAAC;IACD,MAAMwE,QAAQ,GAAG,UAAU7D,EAAE,EAAE;MAC3B,OAAO,YAAY;QACf,OAAOsD,SAAS,CAACrE,GAAG,CAACe,EAAE,EAAE,IAAI,CAAC;MAClC,CAAC;IACL,CAAC;IACD,OAAO4D,eAAe,CAACvF,IAAI,EAAEwF,QAAQ,EAAEC,SAAS,CAAC;EACrD;EACAhJ,MAAM,CAAC8G,QAAQ,GAAG9G,MAAM,CAACoJ,KAAK,GAAG,YAAY;IACzC,OAAOX,aAAa,CAAC3B,QAAQ,CAAClF,KAAK,CAAC,IAAI,EAAEY,kBAAkB,CAACX,SAAS,CAAC,CAAC;EAC5E,CAAC;EACD7B,MAAM,CAACkH,SAAS,GAAGlH,MAAM,CAACoJ,KAAK,CAACnC,IAAI,GAAG,YAAY;IAC/C,OAAOwB,aAAa,CAAC3B,QAAQ,CAACG,IAAI,CAACrF,KAAK,CAAC,IAAI,EAAEY,kBAAkB,CAACX,SAAS,CAAC,CAAC;EACjF,CAAC;EACD7B,MAAM,CAAC8G,QAAQ,CAACC,IAAI,GAAG/G,MAAM,CAACoJ,KAAK,CAACrC,IAAI,GAAG,YAAY;IACnD,OAAO0B,aAAa,CAAC3B,QAAQ,CAACC,IAAI,CAACnF,KAAK,CAAC,IAAI,EAAEY,kBAAkB,CAACX,SAAS,CAAC,CAAC;EACjF,CAAC;EACD7B,MAAM,CAACoH,EAAE,GAAGpH,MAAM,CAACqJ,OAAO,GAAGrJ,MAAM,CAACuH,IAAI,GAAG,YAAY;IACnD,OAAOkB,aAAa,CAACrB,EAAE,CAACxF,KAAK,CAAC,IAAI,EAAEc,cAAc,CAACb,SAAS,CAAC,CAAC;EAClE,CAAC;EACD7B,MAAM,CAACsH,GAAG,GAAGtH,MAAM,CAACsJ,QAAQ,GAAG,YAAY;IACvC,OAAOb,aAAa,CAACrB,EAAE,CAACH,IAAI,CAACrF,KAAK,CAAC,IAAI,EAAEc,cAAc,CAACb,SAAS,CAAC,CAAC;EACvE,CAAC;EACD7B,MAAM,CAACoH,EAAE,CAACL,IAAI,GAAG/G,MAAM,CAACuH,IAAI,CAACR,IAAI,GAAG,YAAY;IAC5C,OAAO0B,aAAa,CAACrB,EAAE,CAACL,IAAI,CAACnF,KAAK,CAAC,IAAI,EAAEc,cAAc,CAACb,SAAS,CAAC,CAAC;EACvE,CAAC;EACD7B,MAAM,CAAC0I,KAAK,GAAG1I,MAAM,CAACuJ,aAAa,GAAG,YAAY;IAC9C,OAAOd,aAAa,CAACC,KAAK,CAAC9G,KAAK,CAAC,IAAI,EAAEuH,eAAe,CAACtH,SAAS,CAAC,CAAC;EACtE,CAAC;EACD7B,MAAM,CAAC2I,SAAS,GAAG3I,MAAM,CAACwJ,QAAQ,GAAG,YAAY;IAC7C,OAAOf,aAAa,CAACE,SAAS,CAAC/G,KAAK,CAAC,IAAI,EAAEc,cAAc,CAACb,SAAS,CAAC,CAAC;EACzE,CAAC;EACD7B,MAAM,CAAC4I,MAAM,GAAG5I,MAAM,CAACyJ,UAAU,GAAG,YAAY;IAC5C,OAAOhB,aAAa,CAACG,MAAM,CAAChH,KAAK,CAAC,IAAI,EAAEuH,eAAe,CAACtH,SAAS,CAAC,CAAC;EACvE,CAAC;EACD7B,MAAM,CAAC6I,UAAU,GAAG7I,MAAM,CAAC0J,KAAK,GAAG,YAAY;IAC3C,OAAOjB,aAAa,CAACI,UAAU,CAACjH,KAAK,CAAC,IAAI,EAAEc,cAAc,CAACb,SAAS,CAAC,CAAC;EAC1E,CAAC;EACD,CAAC,CAAC8H,eAAe,EAAEC,WAAW,KAAK;IAC/BtB,KAAK,CAACuB,MAAM,CAACtJ,SAAS,CAACuJ,OAAO,GAAG,UAAU5E,EAAE,EAAE;MAC3CxK,IAAI,CAAC0C,OAAO,CAAC+H,iBAAiB,CAAC,iBAAiB,EAAE,MAAM;QACpDwE,eAAe,CAACpH,IAAI,CAAC,IAAI,EAAE2C,EAAE,CAAC;MAClC,CAAC,CAAC;IACN,CAAC;IACDoD,KAAK,CAACuB,MAAM,CAACtJ,SAAS,CAAC4D,GAAG,GAAG,UAAUe,EAAE,EAAE;MACvC,IAAI,CAAC3D,EAAE,CAAC,MAAM,EAAGwI,CAAC,IAAK;QACnBxB,QAAQ,GAAGrC,QAAQ,CAACrH,IAAI,CAAC,IAAInC,aAAa,EAAE,CAAC;MACjD,CAAC,CAAC;MACF,IAAI,CAAC6E,EAAE,CAAC,MAAM,EAAE,CAACgG,IAAI,EAAEtO,GAAG,KAAK;QAC3B,MAAM2M,aAAa,GAAG2C,QAAQ,IAAIA,QAAQ,CAACpL,GAAG,CAAC,eAAe,CAAC;QAC/D,IAAIyI,aAAa,IAAI3M,GAAG,EAAE;UACtB,IAAI;YACA;YACAA,GAAG,CAAC0M,OAAO,IAAIC,aAAa,CAACvH,2BAA2B,EAAE;UAC9D,CAAC,CACD,OAAO3F,KAAK,EAAE,CACd;QACJ;MACJ,CAAC,CAAC;MACF,OAAOkR,WAAW,CAACrH,IAAI,CAAC,IAAI,EAAE2C,EAAE,CAAC;IACrC,CAAC;EACL,CAAC,EAAEoD,KAAK,CAACuB,MAAM,CAACtJ,SAAS,CAACuJ,OAAO,EAAExB,KAAK,CAACuB,MAAM,CAACtJ,SAAS,CAAC4D,GAAG,CAAC;AAClE,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAU6F,OAAO,EAAE;EAChB,MAAMC,iBAAiB,CAAC;IACpBxR,WAAW,CAACyR,cAAc,EAAEC,YAAY,EAAEtK,UAAU,EAAE;MAClD,IAAI,CAACqK,cAAc,GAAGA,cAAc;MACpC,IAAI,CAACC,YAAY,GAAGA,YAAY;MAChC,IAAI,CAACC,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACC,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,OAAO,GAAG,KAAK;MACpB,IAAI,CAACC,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAACC,aAAa,GAAG,IAAI;MACzB,IAAI,CAAC3K,OAAO,GAAGpF,IAAI,CAAC0C,OAAO;MAC3B,IAAI,CAACsN,6BAA6B,GAAG,CAAC;MACtC,IAAI,CAACC,mCAAmC,GAAG,KAAK;MAChD,IAAI,CAAChQ,IAAI,GAAG,oBAAoB,GAAGkF,UAAU;MAC7C,IAAI,CAAChD,UAAU,GAAG;QAAE,mBAAmB,EAAE;MAAK,CAAC;MAC/C,IAAI,CAAC8N,mCAAmC,GACpCX,OAAO,CAACtP,IAAI,CAACK,UAAU,CAAC,qCAAqC,CAAC,CAAC,KAAK,IAAI;IAChF;IACA6P,iCAAiC,GAAG;MAChC,OAAO,IAAI,CAACF,6BAA6B,GAAG,CAAC;IACjD;IACAG,qBAAqB,GAAG;MACpB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACL,oBAAoB,KAAK,IAAI,EAAE;QACpCjF,YAAY,CAAC,IAAI,CAACiF,oBAAoB,CAAC;QACvC,IAAI,CAACA,oBAAoB,GAAG,IAAI;MACpC;MACA,IAAI,EAAE,IAAI,CAACJ,kBAAkB,IAAI,IAAI,CAACC,kBAAkB,IACnD,IAAI,CAACM,mCAAmC,IAAI,IAAI,CAACC,iCAAiC,EAAG,CAAC,EAAE;QACzF;QACA;QACA,IAAI,CAAC9K,OAAO,CAACqE,GAAG,CAAC,MAAM;UACnB,IAAI,CAACqG,oBAAoB,GAAGlF,UAAU,CAAC,MAAM;YACzC,IAAI,CAAC,IAAI,CAACgF,eAAe,IAAI,EAAE,IAAI,CAACF,kBAAkB,IAAI,IAAI,CAACC,kBAAkB,CAAC,EAAE;cAChF,IAAI,CAACH,cAAc,EAAE;YACzB;UACJ,CAAC,EAAE,CAAC,CAAC;QACT,CAAC,CAAC;MACN;IACJ;IACAY,mBAAmB,GAAG;MAClB,IAAI,CAAC,IAAI,CAACH,mCAAmC,EAAE;QAC3C;MACJ;MACA,MAAMG,mBAAmB,GAAGC,OAAO,CAACrQ,IAAI,CAACK,UAAU,CAAC,qBAAqB,CAAC,CAAC;MAC3E,IAAI+P,mBAAmB,EAAE;QACrBA,mBAAmB,EAAE;MACzB;IACJ;IACAE,qBAAqB,GAAG;MACpB,IAAI,CAAC,IAAI,CAACL,mCAAmC,EAAE;QAC3C;MACJ;MACA,MAAMK,qBAAqB,GAAGD,OAAO,CAACrQ,IAAI,CAACK,UAAU,CAAC,uBAAuB,CAAC,CAAC;MAC/E,IAAIiQ,qBAAqB,EAAE;QACvBA,qBAAqB,EAAE;MAC3B;IACJ;IACAhQ,cAAc,CAAC+D,QAAQ,EAAE3B,OAAO,EAAEqC,MAAM,EAAErE,IAAI,EAAE;MAC5C,IAAIA,IAAI,CAACI,IAAI,KAAK,WAAW,EAAE;QAC3B,IAAI,CAAC+O,OAAO,GAAG,KAAK;MACxB;MACA,IAAInP,IAAI,CAACI,IAAI,KAAK,WAAW,IAAIJ,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACE,IAAI,YAAYyP,OAAO,EAAE;QACxE;QACA,IAAI3P,IAAI,CAACE,IAAI,CAAC2O,iBAAiB,CAACgB,sBAAsB,CAAC,KAAK,IAAI,EAAE;UAC9D;UACA,IAAI,CAACP,6BAA6B,EAAE;QACxC;MACJ;MACA,OAAO3L,QAAQ,CAACpD,YAAY,CAAC8D,MAAM,EAAErE,IAAI,CAAC;IAC9C;IACAiE,YAAY,CAACN,QAAQ,EAAE3B,OAAO,EAAEqC,MAAM,EAAErE,IAAI,EAAE8D,SAAS,EAAEC,SAAS,EAAE;MAChE,IAAI/D,IAAI,CAACI,IAAI,KAAK,WAAW,EAAE;QAC3B,IAAI,CAAC+O,OAAO,GAAG,KAAK;MACxB;MACA,OAAOxL,QAAQ,CAACO,UAAU,CAACG,MAAM,EAAErE,IAAI,EAAE8D,SAAS,EAAEC,SAAS,CAAC;IAClE;IACAI,YAAY,CAACR,QAAQ,EAAE3B,OAAO,EAAEqC,MAAM,EAAErE,IAAI,EAAE;MAC1C,IAAIA,IAAI,CAACI,IAAI,KAAK,WAAW,EAAE;QAC3B,IAAI,CAAC+O,OAAO,GAAG,KAAK;MACxB;MACA,OAAOxL,QAAQ,CAACS,UAAU,CAACC,MAAM,EAAErE,IAAI,CAAC;IAC5C;IACA;IACA;IACA;IACA;IACA;IACA6D,QAAQ,CAAChE,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE4D,QAAQ,EAAEG,SAAS,EAAEC,SAAS,EAAEV,MAAM,EAAE;MAC1F,IAAI,CAAC,IAAI,CAACgM,aAAa,EAAE;QACrB,IAAI,CAACA,aAAa,GAAG1L,QAAQ;MACjC;MACA,IAAI;QACA,IAAI,CAACwL,OAAO,GAAG,IAAI;QACnB,OAAOtP,kBAAkB,CAACmE,MAAM,CAACjE,UAAU,EAAE4D,QAAQ,EAAEG,SAAS,EAAEC,SAAS,EAAEV,MAAM,CAAC;MACxF,CAAC,SACO;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,IAAI,CAAC8L,OAAO,IAAI,IAAI,CAACE,aAAa,KAAK1L,QAAQ,EAAE;UACjD,IAAI,CAAC8L,qBAAqB,EAAE;QAChC;MACJ;IACJ;IACAjP,aAAa,CAACX,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEzC,KAAK,EAAE;MAC9D;MACA,MAAMiJ,MAAM,GAAG1G,kBAAkB,CAACc,WAAW,CAACZ,UAAU,EAAEzC,KAAK,CAAC;MAChE,IAAIiJ,MAAM,EAAE;QACR,IAAI,CAACwI,YAAY,CAACzR,KAAK,CAAC;QACxB,IAAI,CAAC4R,eAAe,GAAG,IAAI;MAC/B;MACA,OAAO,KAAK;IAChB;IACApM,SAAS,CAACa,QAAQ,EAAE3B,OAAO,EAAEqC,MAAM,EAAEC,YAAY,EAAE;MAC/CX,QAAQ,CAACY,OAAO,CAACF,MAAM,EAAEC,YAAY,CAAC;MACtC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAItC,OAAO,KAAKqC,MAAM,EAAE;QACpB;MACJ;MACA,IAAIC,YAAY,CAACwL,MAAM,IAAI,WAAW,EAAE;QACpC,IAAI,CAACd,kBAAkB,GAAG1K,YAAY,CAAC5B,SAAS;QAChD,IAAI,CAAC+M,qBAAqB,EAAE;MAChC,CAAC,MACI,IAAInL,YAAY,CAACwL,MAAM,IAAI,WAAW,EAAE;QACzC,IAAI,CAACb,kBAAkB,GAAG3K,YAAY,CAAC7B,SAAS;QAChD,IAAI,CAACgN,qBAAqB,EAAE;MAChC;IACJ;EACJ;EACAZ,iBAAiB,CAACgB,sBAAsB,GAAGvQ,IAAI,CAACK,UAAU,CAAC,kBAAkB,CAAC;EAC9E;EACA;EACAL,IAAI,CAAC,mBAAmB,CAAC,GAAGuP,iBAAiB;AACjD,CAAC,EAAE,OAAOkB,MAAM,KAAK,WAAW,IAAIA,MAAM,IAAI,OAAOrD,IAAI,KAAK,WAAW,IAAIA,IAAI,IAAI9H,MAAM,CAAC;AAC5FtF,IAAI,CAACqF,YAAY,CAAC,WAAW,EAAE,CAACC,MAAM,EAAEtF,IAAI,EAAEuF,GAAG,KAAK;EAClD;AACJ;AACA;AACA;EACIvF,IAAI,CAACuF,GAAG,CAACW,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,SAASoI,SAAS,CAAC9D,EAAE,EAAE;IACnD;IACA;IACA,IAAIlF,MAAM,CAACU,OAAO,EAAE;MAChB;MACA,OAAO,UAAU6D,IAAI,EAAE;QACnB,IAAI,CAACA,IAAI,EAAE;UACP;UACA;UACAA,IAAI,GAAG,YAAY,CAAE,CAAC;UACtBA,IAAI,CAAC6G,IAAI,GAAG,UAAUrB,CAAC,EAAE;YACrB,MAAMA,CAAC;UACX,CAAC;QACL;QACA3F,aAAa,CAACc,EAAE,EAAE,IAAI,EAAEX,IAAI,EAAGtL,GAAG,IAAK;UACnC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACzB,OAAOsL,IAAI,CAAC6G,IAAI,CAAC,IAAIrS,KAAK,CAACE,GAAG,CAAC,CAAC;UACpC,CAAC,MACI;YACDsL,IAAI,CAAC6G,IAAI,CAACnS,GAAG,CAAC;UAClB;QACJ,CAAC,CAAC;MACN,CAAC;IACL;IACA;IACA;IACA;IACA;IACA,OAAO,YAAY;MACf,OAAO,IAAI8R,OAAO,CAAC,CAACb,cAAc,EAAEC,YAAY,KAAK;QACjD/F,aAAa,CAACc,EAAE,EAAE,IAAI,EAAEgF,cAAc,EAAEC,YAAY,CAAC;MACzD,CAAC,CAAC;IACN,CAAC;EACL,CAAC;EACD,SAAS/F,aAAa,CAACc,EAAE,EAAEe,OAAO,EAAEiE,cAAc,EAAEC,YAAY,EAAE;IAC9D,MAAMjP,WAAW,GAAGR,IAAI,CAAC0C,OAAO;IAChC,MAAM6M,iBAAiB,GAAGvP,IAAI,CAAC,mBAAmB,CAAC;IACnD,IAAIuP,iBAAiB,KAAKnP,SAAS,EAAE;MACjC,MAAM,IAAI/B,KAAK,CAAC,kFAAkF,GAC9F,4EAA4E,CAAC;IACrF;IACA,MAAM2D,aAAa,GAAGhC,IAAI,CAAC,eAAe,CAAC;IAC3C,IAAI,CAACgC,aAAa,EAAE;MAChB,MAAM,IAAI3D,KAAK,CAAC,8EAA8E,GAC1F,uEAAuE,CAAC;IAChF;IACA,MAAM6M,aAAa,GAAGlJ,aAAa,CAACS,GAAG,EAAE;IACzCT,aAAa,CAACY,aAAa,EAAE;IAC7B;IACA;IACA,MAAM6I,SAAS,GAAGzL,IAAI,CAAC0C,OAAO,CAACiO,WAAW,CAAC,eAAe,CAAC;IAC3D,MAAMC,gBAAgB,GAAG1F,aAAa,CAAC7H,WAAW,EAAE;IACpDoI,SAAS,CAACH,MAAM,CAAC7B,GAAG,CAAC,MAAM;MACvB,MAAMoH,YAAY,GAAG,IAAItB,iBAAiB,CAAC,MAAM;QAC7C;QACA,IAAIrE,aAAa,CAAC7H,WAAW,EAAE,IAAIwN,YAAY,EAAE;UAC7C;UACA;UACA;UACA3F,aAAa,CAAC1I,WAAW,CAACoO,gBAAgB,CAAC;QAC/C;QACAC,YAAY,CAACP,qBAAqB,EAAE;QACpC9P,WAAW,CAACiJ,GAAG,CAAC,MAAM;UAClB+F,cAAc,EAAE;QACpB,CAAC,CAAC;MACN,CAAC,EAAGxR,KAAK,IAAK;QACV;QACA,IAAIkN,aAAa,CAAC7H,WAAW,EAAE,IAAIwN,YAAY,EAAE;UAC7C;UACA3F,aAAa,CAAC1I,WAAW,CAACoO,gBAAgB,CAAC;QAC/C;QACAC,YAAY,CAACP,qBAAqB,EAAE;QACpC9P,WAAW,CAACiJ,GAAG,CAAC,MAAM;UAClBgG,YAAY,CAACzR,KAAK,CAAC;QACvB,CAAC,CAAC;MACN,CAAC,EAAE,MAAM,CAAC;MACVkN,aAAa,CAAC1I,WAAW,CAACqO,YAAY,CAAC;MACvCA,YAAY,CAACT,mBAAmB,EAAE;IACtC,CAAC,CAAC;IACF,OAAOpQ,IAAI,CAAC0C,OAAO,CAACoO,UAAU,CAACtG,EAAE,EAAEe,OAAO,CAAC;EAC/C;AACJ,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAUjG,MAAM,EAAE;EACf,MAAMyL,YAAY,GAAGzL,MAAM,CAACnH,IAAI;EAChC;EACA;EACA;EACA;EACA,SAAS6S,QAAQ,GAAG;IAChB,IAAI7J,SAAS,CAACnI,MAAM,KAAK,CAAC,EAAE;MACxB,MAAMyG,CAAC,GAAG,IAAIsL,YAAY,EAAE;MAC5BtL,CAAC,CAACwL,OAAO,CAACD,QAAQ,CAACE,GAAG,EAAE,CAAC;MACzB,OAAOzL,CAAC;IACZ,CAAC,MACI;MACD,MAAMoD,IAAI,GAAGC,KAAK,CAACjD,SAAS,CAACkD,KAAK,CAAClB,IAAI,CAACV,SAAS,CAAC;MAClD,OAAO,IAAI4J,YAAY,CAAC,GAAGlI,IAAI,CAAC;IACpC;EACJ;EACAmI,QAAQ,CAACE,GAAG,GAAG,YAAY;IACvB,MAAMC,qBAAqB,GAAGnR,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;IACvE,IAAI0O,qBAAqB,EAAE;MACvB,OAAOA,qBAAqB,CAACC,iBAAiB,EAAE;IACpD;IACA,OAAOL,YAAY,CAACG,GAAG,CAAChK,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAClD,CAAC;EACD6J,QAAQ,CAACK,GAAG,GAAGN,YAAY,CAACM,GAAG;EAC/BL,QAAQ,CAACM,KAAK,GAAGP,YAAY,CAACO,KAAK;EACnC;EACA,MAAMC,MAAM,GAAG;IACX3G,UAAU,EAAEtF,MAAM,CAACsF,UAAU;IAC7B4G,WAAW,EAAElM,MAAM,CAACkM,WAAW;IAC/B3G,YAAY,EAAEvF,MAAM,CAACuF,YAAY;IACjC4G,aAAa,EAAEnM,MAAM,CAACmM;EAC1B,CAAC;EACD,MAAMC,SAAS,CAAC;IACZ3T,WAAW,GAAG;MACV;MACA,IAAI,CAAC4T,eAAe,GAAG,EAAE;MACzB;MACA,IAAI,CAACC,gBAAgB,GAAG,CAAC;MACzB;MACA,IAAI,CAACC,0BAA0B,GAAGd,YAAY,CAACG,GAAG,EAAE;MACpD;MACA,IAAI,CAACY,kCAAkC,GAAG,EAAE;IAChD;IACAC,kBAAkB,GAAG;MACjB,OAAO,IAAI,CAACH,gBAAgB;IAChC;IACAR,iBAAiB,GAAG;MAChB,OAAO,IAAI,CAACS,0BAA0B,GAAG,IAAI,CAACD,gBAAgB;IAClE;IACAnJ,qBAAqB,CAACuJ,kBAAkB,EAAE;MACtC,IAAI,CAACH,0BAA0B,GAAGG,kBAAkB;IACxD;IACA3E,iBAAiB,GAAG;MAChB,OAAO0D,YAAY,CAACG,GAAG,EAAE;IAC7B;IACAe,gBAAgB,CAACC,EAAE,EAAEC,KAAK,EAAEC,OAAO,EAAE;MACjCA,OAAO,GAAGrR,MAAM,CAACC,MAAM,CAAC;QACpB6H,IAAI,EAAE,EAAE;QACRwJ,UAAU,EAAE,KAAK;QACjBC,uBAAuB,EAAE,KAAK;QAC9BC,EAAE,EAAE,CAAC,CAAC;QACNC,iBAAiB,EAAE;MACvB,CAAC,EAAEJ,OAAO,CAAC;MACX,IAAIK,SAAS,GAAGL,OAAO,CAACG,EAAE,GAAG,CAAC,GAAGb,SAAS,CAACgB,MAAM,EAAE,GAAGN,OAAO,CAACG,EAAE;MAChE,IAAII,OAAO,GAAG,IAAI,CAACf,gBAAgB,GAAGO,KAAK;MAC3C;MACA,IAAIS,QAAQ,GAAG;QACXD,OAAO,EAAEA,OAAO;QAChBJ,EAAE,EAAEE,SAAS;QACbI,IAAI,EAAEX,EAAE;QACRrJ,IAAI,EAAEuJ,OAAO,CAACvJ,IAAI;QAClBsJ,KAAK,EAAEA,KAAK;QACZE,UAAU,EAAED,OAAO,CAACC,UAAU;QAC9BC,uBAAuB,EAAEF,OAAO,CAACE;MACrC,CAAC;MACD,IAAIF,OAAO,CAACI,iBAAiB,EAAE;QAC3B,IAAI,CAACV,kCAAkC,CAAC3S,IAAI,CAACyT,QAAQ,CAAC;MAC1D;MACA,IAAI7T,CAAC,GAAG,CAAC;MACT,OAAOA,CAAC,GAAG,IAAI,CAAC4S,eAAe,CAAC3S,MAAM,EAAED,CAAC,EAAE,EAAE;QACzC,IAAI+T,YAAY,GAAG,IAAI,CAACnB,eAAe,CAAC5S,CAAC,CAAC;QAC1C,IAAI6T,QAAQ,CAACD,OAAO,GAAGG,YAAY,CAACH,OAAO,EAAE;UACzC;QACJ;MACJ;MACA,IAAI,CAAChB,eAAe,CAACjO,MAAM,CAAC3E,CAAC,EAAE,CAAC,EAAE6T,QAAQ,CAAC;MAC3C,OAAOH,SAAS;IACpB;IACAM,6BAA6B,CAACR,EAAE,EAAE;MAC9B,KAAK,IAAIxT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC4S,eAAe,CAAC3S,MAAM,EAAED,CAAC,EAAE,EAAE;QAClD,IAAI,IAAI,CAAC4S,eAAe,CAAC5S,CAAC,CAAC,CAACwT,EAAE,IAAIA,EAAE,EAAE;UAClC,IAAI,CAACZ,eAAe,CAACjO,MAAM,CAAC3E,CAAC,EAAE,CAAC,CAAC;UACjC;QACJ;MACJ;IACJ;IACAiU,SAAS,GAAG;MACR,IAAI,CAACrB,eAAe,GAAG,EAAE;IAC7B;IACAhE,aAAa,GAAG;MACZ,OAAO,IAAI,CAACgE,eAAe,CAAC3S,MAAM;IACtC;IACAyO,UAAU,CAACwF,IAAI,GAAG,CAAC,EAAEC,MAAM,EAAEC,WAAW,EAAE;MACtC,IAAI,IAAI,CAACxB,eAAe,CAAC3S,MAAM,GAAGiU,IAAI,EAAE;QACpC;MACJ;MACA;MACA;MACA,MAAMG,SAAS,GAAG,IAAI,CAACxB,gBAAgB;MACvC,MAAMyB,UAAU,GAAG,IAAI,CAAC1B,eAAe,CAACsB,IAAI,GAAG,CAAC,CAAC;MACjD,IAAI,CAAC7K,IAAI,CAACiL,UAAU,CAACV,OAAO,GAAGS,SAAS,EAAEF,MAAM,EAAEC,WAAW,CAAC;IAClE;IACA/K,IAAI,CAACkL,MAAM,GAAG,CAAC,EAAEJ,MAAM,EAAEC,WAAW,EAAE;MAClC,IAAII,SAAS,GAAG,IAAI,CAAC3B,gBAAgB,GAAG0B,MAAM;MAC9C,IAAIE,eAAe,GAAG,CAAC;MACvBL,WAAW,GAAGpS,MAAM,CAACC,MAAM,CAAC;QAAEyS,iCAAiC,EAAE;MAAK,CAAC,EAAEN,WAAW,CAAC;MACrF;MACA;MACA;MACA,MAAMO,cAAc,GAAGP,WAAW,CAACM,iCAAiC,GAChE,IAAI,CAAC9B,eAAe,GACpB,IAAI,CAACA,eAAe,CAAC5I,KAAK,EAAE;MAChC,IAAI2K,cAAc,CAAC1U,MAAM,KAAK,CAAC,IAAIkU,MAAM,EAAE;QACvCA,MAAM,CAACI,MAAM,CAAC;QACd;MACJ;MACA,OAAOI,cAAc,CAAC1U,MAAM,GAAG,CAAC,EAAE;QAC9B;QACA,IAAI,CAAC8S,kCAAkC,GAAG,EAAE;QAC5C,IAAIpP,OAAO,GAAGgR,cAAc,CAAC,CAAC,CAAC;QAC/B,IAAIH,SAAS,GAAG7Q,OAAO,CAACiQ,OAAO,EAAE;UAC7B;UACA;QACJ,CAAC,MACI;UACD;UACA,IAAIjQ,OAAO,GAAGgR,cAAc,CAACC,KAAK,EAAE;UACpC,IAAI,CAACR,WAAW,CAACM,iCAAiC,EAAE;YAChD,MAAMG,GAAG,GAAG,IAAI,CAACjC,eAAe,CAAC9P,OAAO,CAACa,OAAO,CAAC;YACjD,IAAIkR,GAAG,IAAI,CAAC,EAAE;cACV,IAAI,CAACjC,eAAe,CAACjO,MAAM,CAACkQ,GAAG,EAAE,CAAC,CAAC;YACvC;UACJ;UACAJ,eAAe,GAAG,IAAI,CAAC5B,gBAAgB;UACvC,IAAI,CAACA,gBAAgB,GAAGlP,OAAO,CAACiQ,OAAO;UACvC,IAAIO,MAAM,EAAE;YACRA,MAAM,CAAC,IAAI,CAACtB,gBAAgB,GAAG4B,eAAe,CAAC;UACnD;UACA,IAAIK,MAAM,GAAGnR,OAAO,CAACmQ,IAAI,CAAC3L,KAAK,CAAC5B,MAAM,EAAE5C,OAAO,CAAC4P,uBAAuB,GAAG,CAAC,IAAI,CAACV,gBAAgB,CAAC,GAAGlP,OAAO,CAACmG,IAAI,CAAC;UACjH,IAAI,CAACgL,MAAM,EAAE;YACT;YACA;UACJ;UACA;UACA;UACA,IAAI,CAACV,WAAW,CAACM,iCAAiC,EAAE;YAChD,IAAI,CAAC3B,kCAAkC,CAAC/O,OAAO,CAAC6P,QAAQ,IAAI;cACxD,IAAI7T,CAAC,GAAG,CAAC;cACT,OAAOA,CAAC,GAAG2U,cAAc,CAAC1U,MAAM,EAAED,CAAC,EAAE,EAAE;gBACnC,MAAM+T,YAAY,GAAGY,cAAc,CAAC3U,CAAC,CAAC;gBACtC,IAAI6T,QAAQ,CAACD,OAAO,GAAGG,YAAY,CAACH,OAAO,EAAE;kBACzC;gBACJ;cACJ;cACAe,cAAc,CAAChQ,MAAM,CAAC3E,CAAC,EAAE,CAAC,EAAE6T,QAAQ,CAAC;YACzC,CAAC,CAAC;UACN;QACJ;MACJ;MACAY,eAAe,GAAG,IAAI,CAAC5B,gBAAgB;MACvC,IAAI,CAACA,gBAAgB,GAAG2B,SAAS;MACjC,IAAIL,MAAM,EAAE;QACRA,MAAM,CAAC,IAAI,CAACtB,gBAAgB,GAAG4B,eAAe,CAAC;MACnD;IACJ;IACAhG,sBAAsB,CAAC0F,MAAM,EAAE;MAC3B,IAAI,IAAI,CAACvB,eAAe,CAAC3S,MAAM,KAAK,CAAC,EAAE;QACnC,OAAO,CAAC;MACZ;MACA;MACA;MACA,MAAMoU,SAAS,GAAG,IAAI,CAACxB,gBAAgB;MACvC,MAAMkC,QAAQ,GAAG,IAAI,CAACnC,eAAe,CAAC,IAAI,CAACA,eAAe,CAAC3S,MAAM,GAAG,CAAC,CAAC;MACtE,IAAI,CAACoJ,IAAI,CAAC0L,QAAQ,CAACnB,OAAO,GAAGS,SAAS,EAAEF,MAAM,EAAE;QAAEO,iCAAiC,EAAE;MAAM,CAAC,CAAC;MAC7F,OAAO,IAAI,CAAC7B,gBAAgB,GAAGwB,SAAS;IAC5C;IACA7F,KAAK,CAACwG,KAAK,GAAG,EAAE,EAAEC,aAAa,GAAG,KAAK,EAAEd,MAAM,EAAE;MAC7C,IAAIc,aAAa,EAAE;QACf,OAAO,IAAI,CAACA,aAAa,CAACd,MAAM,CAAC;MACrC,CAAC,MACI;QACD,OAAO,IAAI,CAACe,gBAAgB,CAACF,KAAK,EAAEb,MAAM,CAAC;MAC/C;IACJ;IACAc,aAAa,CAACd,MAAM,EAAE;MAClB,IAAI,IAAI,CAACvB,eAAe,CAAC3S,MAAM,KAAK,CAAC,EAAE;QACnC,OAAO,CAAC;MACZ;MACA;MACA;MACA,MAAMoU,SAAS,GAAG,IAAI,CAACxB,gBAAgB;MACvC,MAAMkC,QAAQ,GAAG,IAAI,CAACnC,eAAe,CAAC,IAAI,CAACA,eAAe,CAAC3S,MAAM,GAAG,CAAC,CAAC;MACtE,IAAI,CAACoJ,IAAI,CAAC0L,QAAQ,CAACnB,OAAO,GAAGS,SAAS,EAAEF,MAAM,CAAC;MAC/C,OAAO,IAAI,CAACtB,gBAAgB,GAAGwB,SAAS;IAC5C;IACAa,gBAAgB,CAACF,KAAK,EAAEb,MAAM,EAAE;MAC5B,MAAME,SAAS,GAAG,IAAI,CAACxB,gBAAgB;MACvC,IAAI4B,eAAe,GAAG,CAAC;MACvB,IAAIhS,KAAK,GAAG,CAAC;MACb,OAAO,IAAI,CAACmQ,eAAe,CAAC3S,MAAM,GAAG,CAAC,EAAE;QACpCwC,KAAK,EAAE;QACP,IAAIA,KAAK,GAAGuS,KAAK,EAAE;UACf,MAAM,IAAI1V,KAAK,CAAC,2CAA2C,GAAG0V,KAAK,GAC/D,+CAA+C,CAAC;QACxD;QACA;QACA;QACA,IAAI,IAAI,CAACpC,eAAe,CAACuC,MAAM,CAACxT,IAAI,IAAI,CAACA,IAAI,CAAC2R,UAAU,IAAI,CAAC3R,IAAI,CAAC4R,uBAAuB,CAAC,CACrFtT,MAAM,KAAK,CAAC,EAAE;UACf;QACJ;QACA,MAAM0D,OAAO,GAAG,IAAI,CAACiP,eAAe,CAACgC,KAAK,EAAE;QAC5CH,eAAe,GAAG,IAAI,CAAC5B,gBAAgB;QACvC,IAAI,CAACA,gBAAgB,GAAGlP,OAAO,CAACiQ,OAAO;QACvC,IAAIO,MAAM,EAAE;UACR;UACAA,MAAM,CAAC,IAAI,CAACtB,gBAAgB,GAAG4B,eAAe,CAAC;QACnD;QACA,MAAMK,MAAM,GAAGnR,OAAO,CAACmQ,IAAI,CAAC3L,KAAK,CAAC5B,MAAM,EAAE5C,OAAO,CAACmG,IAAI,CAAC;QACvD,IAAI,CAACgL,MAAM,EAAE;UACT;UACA;QACJ;MACJ;MACA,OAAO,IAAI,CAACjC,gBAAgB,GAAGwB,SAAS;IAC5C;EACJ;EACA;EACA1B,SAAS,CAACgB,MAAM,GAAG,CAAC;EACpB,MAAMhK,qBAAqB,CAAC;IACxB3K,WAAW,CAACoH,UAAU,EAAEgP,iCAAiC,GAAG,KAAK,EAAEC,gBAAgB,EAAE;MACjF,IAAI,CAACD,iCAAiC,GAAGA,iCAAiC;MAC1E,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;MACxC,IAAI,CAACC,UAAU,GAAG,IAAI3C,SAAS,EAAE;MACjC,IAAI,CAAC4C,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,sBAAsB,GAAGnE,OAAO,CAACrQ,IAAI,CAACK,UAAU,CAAC,uBAAuB,CAAC,CAAC;MAC/E,IAAI,CAACoU,qBAAqB,GAAG,EAAE;MAC/B,IAAI,CAACC,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACxS,UAAU,GAAG;QAAE,uBAAuB,EAAE;MAAK,CAAC;MACnD,IAAI,CAAClC,IAAI,GAAG,wBAAwB,GAAGkF,UAAU;MACjD;MACA;MACA,IAAI,CAAC,IAAI,CAACiP,gBAAgB,EAAE;QACxB,IAAI,CAACA,gBAAgB,GAAG9O,MAAM,CAACtF,IAAI,CAACK,UAAU,CAAC,wBAAwB,CAAC,CAAC;MAC7E;IACJ;IACA,OAAOuU,YAAY,GAAG;MAClB,IAAI5U,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC,IAAI,IAAI,EAAE;QACnD,MAAM,IAAIpE,KAAK,CAAC,wEAAwE,CAAC;MAC7F;IACJ;IACAwW,WAAW,CAACrK,EAAE,EAAEsK,UAAU,EAAE;MACxB,OAAO,CAAC,GAAGjM,IAAI,KAAK;QAChB2B,EAAE,CAACtD,KAAK,CAAC5B,MAAM,EAAEuD,IAAI,CAAC;QACtB,IAAI,IAAI,CAAC0L,UAAU,KAAK,IAAI,EAAE;UAAE;UAC5B,IAAIO,UAAU,CAACC,SAAS,IAAI,IAAI,EAAE;YAC9BD,UAAU,CAACC,SAAS,CAAC7N,KAAK,CAAC5B,MAAM,CAAC;UACtC;UACA;UACA,IAAI,CAACgI,eAAe,EAAE;QAC1B,CAAC,MACI;UAAE;UACH,IAAIwH,UAAU,CAACE,OAAO,IAAI,IAAI,EAAE;YAC5BF,UAAU,CAACE,OAAO,CAAC9N,KAAK,CAAC5B,MAAM,CAAC;UACpC;QACJ;QACA;QACA,OAAO,IAAI,CAACiP,UAAU,KAAK,IAAI;MACnC,CAAC;IACL;IACA,OAAOU,YAAY,CAAC1D,MAAM,EAAEgB,EAAE,EAAE;MAC5B,IAAI2C,KAAK,GAAG3D,MAAM,CAAC1P,OAAO,CAAC0Q,EAAE,CAAC;MAC9B,IAAI2C,KAAK,GAAG,CAAC,CAAC,EAAE;QACZ3D,MAAM,CAAC7N,MAAM,CAACwR,KAAK,EAAE,CAAC,CAAC;MAC3B;IACJ;IACAC,aAAa,CAAC5C,EAAE,EAAE;MACd,OAAO,MAAM;QACT7J,qBAAqB,CAACuM,YAAY,CAAC,IAAI,CAACP,aAAa,EAAEnC,EAAE,CAAC;MAC9D,CAAC;IACL;IACA6C,qBAAqB,CAAC5K,EAAE,EAAE6K,QAAQ,EAAExM,IAAI,EAAE0J,EAAE,EAAE;MAC1C,OAAO,MAAM;QACT;QACA,IAAI,IAAI,CAACkC,qBAAqB,CAAC5S,OAAO,CAAC0Q,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;UAC/C,IAAI,CAAC8B,UAAU,CAACpC,gBAAgB,CAACzH,EAAE,EAAE6K,QAAQ,EAAE;YAAExM,IAAI;YAAEwJ,UAAU,EAAE,IAAI;YAAEE,EAAE;YAAEC,iBAAiB,EAAE;UAAK,CAAC,CAAC;QAC3G;MACJ,CAAC;IACL;IACA8C,qBAAqB,CAAC/C,EAAE,EAAE;MACtB,OAAO,MAAM;QACT7J,qBAAqB,CAACuM,YAAY,CAAC,IAAI,CAACR,qBAAqB,EAAElC,EAAE,CAAC;MACtE,CAAC;IACL;IACAgD,WAAW,CAAC/K,EAAE,EAAE2H,KAAK,EAAEtJ,IAAI,EAAE2M,OAAO,GAAG,IAAI,EAAE;MACzC,IAAIC,aAAa,GAAG,IAAI,CAACN,aAAa,CAACzD,SAAS,CAACgB,MAAM,CAAC;MACxD;MACA,IAAIR,EAAE,GAAG,IAAI,CAAC2C,WAAW,CAACrK,EAAE,EAAE;QAAEuK,SAAS,EAAEU,aAAa;QAAET,OAAO,EAAES;MAAc,CAAC,CAAC;MACnF,IAAIlD,EAAE,GAAG,IAAI,CAAC8B,UAAU,CAACpC,gBAAgB,CAACC,EAAE,EAAEC,KAAK,EAAE;QAAEtJ,IAAI;QAAEyJ,uBAAuB,EAAE,CAACkD;MAAQ,CAAC,CAAC;MACjG,IAAIA,OAAO,EAAE;QACT,IAAI,CAACd,aAAa,CAACvV,IAAI,CAACoT,EAAE,CAAC;MAC/B;MACA,OAAOA,EAAE;IACb;IACAmD,aAAa,CAACnD,EAAE,EAAE;MACd7J,qBAAqB,CAACuM,YAAY,CAAC,IAAI,CAACP,aAAa,EAAEnC,EAAE,CAAC;MAC1D,IAAI,CAAC8B,UAAU,CAACtB,6BAA6B,CAACR,EAAE,CAAC;IACrD;IACAoD,YAAY,CAACnL,EAAE,EAAE6K,QAAQ,EAAExM,IAAI,EAAE;MAC7B,IAAI0J,EAAE,GAAGb,SAAS,CAACgB,MAAM;MACzB,IAAIoC,UAAU,GAAG;QAAEC,SAAS,EAAE,IAAI;QAAEC,OAAO,EAAE,IAAI,CAACM,qBAAqB,CAAC/C,EAAE;MAAE,CAAC;MAC7E,IAAIL,EAAE,GAAG,IAAI,CAAC2C,WAAW,CAACrK,EAAE,EAAEsK,UAAU,CAAC;MACzC;MACAA,UAAU,CAACC,SAAS,GAAG,IAAI,CAACK,qBAAqB,CAAClD,EAAE,EAAEmD,QAAQ,EAAExM,IAAI,EAAE0J,EAAE,CAAC;MACzE;MACA,IAAI,CAAC8B,UAAU,CAACpC,gBAAgB,CAACC,EAAE,EAAEmD,QAAQ,EAAE;QAAExM,IAAI;QAAEwJ,UAAU,EAAE;MAAK,CAAC,CAAC;MAC1E,IAAI,CAACoC,qBAAqB,CAACtV,IAAI,CAACoT,EAAE,CAAC;MACnC,OAAOA,EAAE;IACb;IACAqD,cAAc,CAACrD,EAAE,EAAE;MACf7J,qBAAqB,CAACuM,YAAY,CAAC,IAAI,CAACR,qBAAqB,EAAElC,EAAE,CAAC;MAClE,IAAI,CAAC8B,UAAU,CAACtB,6BAA6B,CAACR,EAAE,CAAC;IACrD;IACAsD,uBAAuB,GAAG;MACtB,IAAI7X,KAAK,GAAG,IAAI,CAACuW,UAAU,IAAI,IAAI,CAACC,sBAAsB,CAAC,CAAC,CAAC;MAC7D,IAAI,CAACA,sBAAsB,CAACxV,MAAM,GAAG,CAAC;MACtC,IAAI,CAACuV,UAAU,GAAG,IAAI;MACtB,MAAMvW,KAAK;IACf;IACA+T,kBAAkB,GAAG;MACjB,OAAO,IAAI,CAACsC,UAAU,CAACtC,kBAAkB,EAAE;IAC/C;IACAX,iBAAiB,GAAG;MAChB,OAAO,IAAI,CAACiD,UAAU,CAACjD,iBAAiB,EAAE;IAC9C;IACA3I,qBAAqB,CAACqN,QAAQ,EAAE;MAC5B,IAAI,CAACzB,UAAU,CAAC5L,qBAAqB,CAACqN,QAAQ,CAAC;IACnD;IACAzI,iBAAiB,GAAG;MAChB,OAAO,IAAI,CAACgH,UAAU,CAAChH,iBAAiB,EAAE;IAC9C;IACA,OAAO0I,SAAS,GAAG;MACf,IAAI,CAAC,CAACzQ,MAAM,CAACtF,IAAI,CAACK,UAAU,CAAC,qBAAqB,CAAC,CAAC,EAAE;QAClD;QACA;QACA;QACA;QACA;QACA;MACJ;MACA,IAAIiF,MAAM,CAAC,MAAM,CAAC,KAAK0L,QAAQ,EAAE;QAC7B;QACA;MACJ;MACA1L,MAAM,CAAC,MAAM,CAAC,GAAG0L,QAAQ;MACzBA,QAAQ,CAACnL,SAAS,GAAGkL,YAAY,CAAClL,SAAS;MAC3C;MACA;MACA;MACA6C,qBAAqB,CAACsN,eAAe,EAAE;IAC3C;IACA,OAAOC,SAAS,GAAG;MACf,IAAI3Q,MAAM,CAAC,MAAM,CAAC,KAAK0L,QAAQ,EAAE;QAC7B1L,MAAM,CAAC,MAAM,CAAC,GAAGyL,YAAY;MACjC;IACJ;IACA,OAAOiF,eAAe,GAAG;MACrB,IAAI1Q,MAAM,CAACsF,UAAU,KAAK2G,MAAM,CAAC3G,UAAU,EAAE;QACzCtF,MAAM,CAACsF,UAAU,GAAG2G,MAAM,CAAC3G,UAAU;QACrCtF,MAAM,CAACuF,YAAY,GAAG0G,MAAM,CAAC1G,YAAY;MAC7C;MACA,IAAIvF,MAAM,CAACkM,WAAW,KAAKD,MAAM,CAACC,WAAW,EAAE;QAC3ClM,MAAM,CAACkM,WAAW,GAAGD,MAAM,CAACC,WAAW;QACvClM,MAAM,CAACmM,aAAa,GAAGF,MAAM,CAACE,aAAa;MAC/C;IACJ;IACAyE,aAAa,GAAG;MACZ,IAAI,CAACvB,eAAe,GAAG,IAAI;MAC3BjM,qBAAqB,CAACqN,SAAS,EAAE;IACrC;IACAI,eAAe,GAAG;MACd,IAAI,CAACxB,eAAe,GAAG,KAAK;MAC5BjM,qBAAqB,CAACuN,SAAS,EAAE;IACrC;IACAxI,UAAU,CAAC2I,KAAK,GAAG,CAAC,EAAElD,MAAM,EAAEC,WAAW,GAAG;MAAEM,iCAAiC,EAAE;IAAK,CAAC,EAAE;MACrF,IAAI2C,KAAK,IAAI,CAAC,EAAE;QACZ;MACJ;MACA1N,qBAAqB,CAACkM,YAAY,EAAE;MACpC,IAAI,CAACtH,eAAe,EAAE;MACtB,IAAI,CAAC+G,UAAU,CAAC5G,UAAU,CAAC2I,KAAK,EAAElD,MAAM,EAAEC,WAAW,CAAC;MACtD,IAAI,IAAI,CAACoB,UAAU,KAAK,IAAI,EAAE;QAC1B,IAAI,CAACsB,uBAAuB,EAAE;MAClC;IACJ;IACAzN,IAAI,CAACkL,MAAM,GAAG,CAAC,EAAEJ,MAAM,EAAEC,WAAW,GAAG;MAAEM,iCAAiC,EAAE;IAAK,CAAC,EAAE;MAChF/K,qBAAqB,CAACkM,YAAY,EAAE;MACpC,IAAI,CAACtH,eAAe,EAAE;MACtB,IAAI,CAAC+G,UAAU,CAACjM,IAAI,CAACkL,MAAM,EAAEJ,MAAM,EAAEC,WAAW,CAAC;MACjD,IAAI,IAAI,CAACoB,UAAU,KAAK,IAAI,EAAE;QAC1B,IAAI,CAACsB,uBAAuB,EAAE;MAClC;IACJ;IACAvI,eAAe,GAAG;MACd5E,qBAAqB,CAACkM,YAAY,EAAE;MACpC,MAAMyB,WAAW,GAAG,MAAM;QACtB,IAAI,IAAI,CAAC9B,UAAU,KAAK,IAAI,IAAI,IAAI,CAACC,sBAAsB,CAACxV,MAAM,EAAE;UAChE;UACA,IAAI,CAAC6W,uBAAuB,EAAE;QAClC;MACJ,CAAC;MACD,OAAO,IAAI,CAACvB,WAAW,CAACtV,MAAM,GAAG,CAAC,EAAE;QAChC,IAAIsX,SAAS,GAAG,IAAI,CAAChC,WAAW,CAACX,KAAK,EAAE;QACxC2C,SAAS,CAACzD,IAAI,CAAC3L,KAAK,CAACoP,SAAS,CAACvR,MAAM,EAAEuR,SAAS,CAACzN,IAAI,CAAC;MAC1D;MACAwN,WAAW,EAAE;IACjB;IACA9I,KAAK,CAACwG,KAAK,EAAEC,aAAa,EAAEd,MAAM,EAAE;MAChCxK,qBAAqB,CAACkM,YAAY,EAAE;MACpC,IAAI,CAACtH,eAAe,EAAE;MACtB,MAAMiJ,OAAO,GAAG,IAAI,CAAClC,UAAU,CAAC9G,KAAK,CAACwG,KAAK,EAAEC,aAAa,EAAEd,MAAM,CAAC;MACnE,IAAI,IAAI,CAACqB,UAAU,KAAK,IAAI,EAAE;QAC1B,IAAI,CAACsB,uBAAuB,EAAE;MAClC;MACA,OAAOU,OAAO;IAClB;IACA/I,sBAAsB,CAAC0F,MAAM,EAAE;MAC3BxK,qBAAqB,CAACkM,YAAY,EAAE;MACpC,IAAI,CAACtH,eAAe,EAAE;MACtB,MAAMiJ,OAAO,GAAG,IAAI,CAAClC,UAAU,CAAC7G,sBAAsB,CAAC0F,MAAM,CAAC;MAC9D,IAAI,IAAI,CAACqB,UAAU,KAAK,IAAI,EAAE;QAC1B,IAAI,CAACsB,uBAAuB,EAAE;MAClC;MACA,OAAOU,OAAO;IAClB;IACA7I,eAAe,GAAG;MACdhF,qBAAqB,CAACkM,YAAY,EAAE;MACpC,IAAI,CAACP,UAAU,CAACrB,SAAS,EAAE;MAC3B,IAAI,CAACyB,qBAAqB,GAAG,EAAE;MAC/B,IAAI,CAACC,aAAa,GAAG,EAAE;IAC3B;IACA/G,aAAa,GAAG;MACZ,OAAO,IAAI,CAAC0G,UAAU,CAAC1G,aAAa,EAAE,GAAG,IAAI,CAAC2G,WAAW,CAACtV,MAAM;IACpE;IACAsB,cAAc,CAAC+D,QAAQ,EAAE3B,OAAO,EAAEqC,MAAM,EAAErE,IAAI,EAAE;MAC5C,QAAQA,IAAI,CAACI,IAAI;QACb,KAAK,WAAW;UACZ,IAAI+H,IAAI,GAAGnI,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACE,IAAI,CAACiI,IAAI;UACtC;UACA;UACA;UACA,IAAI2N,cAAc;UAClB,IAAI3N,IAAI,EAAE;YACN,IAAI4N,aAAa,GAAG/V,IAAI,CAACE,IAAI,CAAC8V,KAAK;YACnC,IAAI,OAAO7N,IAAI,CAAC7J,MAAM,KAAK,QAAQ,IAAI6J,IAAI,CAAC7J,MAAM,GAAGyX,aAAa,GAAG,CAAC,EAAE;cACpED,cAAc,GAAG1N,KAAK,CAACjD,SAAS,CAACkD,KAAK,CAAClB,IAAI,CAACgB,IAAI,EAAE4N,aAAa,GAAG,CAAC,CAAC;YACxE;UACJ;UACA,IAAI,CAACnC,WAAW,CAACnV,IAAI,CAAC;YAClB0T,IAAI,EAAEnS,IAAI,CAACgE,MAAM;YACjBmE,IAAI,EAAE2N,cAAc;YACpBzR,MAAM,EAAErE,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACE,IAAI,CAACmE;UACnC,CAAC,CAAC;UACF;QACJ,KAAK,WAAW;UACZ,QAAQrE,IAAI,CAACqD,MAAM;YACf,KAAK,YAAY;cACbrD,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC2U,WAAW,CAAC7U,IAAI,CAACgE,MAAM,EAAEhE,IAAI,CAACE,IAAI,CAAC,OAAO,CAAC,EAAEkI,KAAK,CAACjD,SAAS,CAACkD,KAAK,CAAClB,IAAI,CAACnH,IAAI,CAACE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;cAC3H;YACJ,KAAK,cAAc;cACfF,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC2U,WAAW,CAAC7U,IAAI,CAACgE,MAAM,EAAE,CAAC,EAAEoE,KAAK,CAACjD,SAAS,CAACkD,KAAK,CAAClB,IAAI,CAACnH,IAAI,CAACE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;cAC1G;YACJ,KAAK,aAAa;cACdF,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC+U,YAAY,CAACjV,IAAI,CAACgE,MAAM,EAAEhE,IAAI,CAACE,IAAI,CAAC,OAAO,CAAC,EAAEkI,KAAK,CAACjD,SAAS,CAACkD,KAAK,CAAClB,IAAI,CAACnH,IAAI,CAACE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;cAC5H;YACJ,KAAK,qBAAqB;cACtB,MAAM,IAAIvC,KAAK,CAAC,+DAA+D,GAC3EqC,IAAI,CAACE,IAAI,CAAC,KAAK,CAAC,CAAC;YACzB,KAAK,uBAAuB;YAC5B,KAAK,6BAA6B;YAClC,KAAK,0BAA0B;cAC3B;cACA;cACAF,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC2U,WAAW,CAAC7U,IAAI,CAACgE,MAAM,EAAE,EAAE,EAAEhE,IAAI,CAACE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAACuT,iCAAiC,CAAC;cACpH;YACJ;cACI;cACA;cACA,MAAMwC,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAAClW,IAAI,CAAC;cACtD,IAAIiW,eAAe,EAAE;gBACjB,MAAM9N,IAAI,GAAGnI,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACE,IAAI,CAAC,MAAM,CAAC;gBAC3C,MAAMuR,KAAK,GAAGtJ,IAAI,IAAIA,IAAI,CAAC7J,MAAM,GAAG,CAAC,GAAG6J,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;gBACnD,IAAIgO,YAAY,GAAGF,eAAe,CAACE,YAAY,GAAGF,eAAe,CAACE,YAAY,GAAGhO,IAAI;gBACrF,IAAI,CAAC,CAAC8N,eAAe,CAACtE,UAAU,EAAE;kBAC9B;kBACA3R,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC+U,YAAY,CAACjV,IAAI,CAACgE,MAAM,EAAEyN,KAAK,EAAE0E,YAAY,CAAC;kBAC3EnW,IAAI,CAACE,IAAI,CAACyR,UAAU,GAAG,IAAI;gBAC/B,CAAC,MACI;kBACD;kBACA3R,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC2U,WAAW,CAAC7U,IAAI,CAACgE,MAAM,EAAEyN,KAAK,EAAE0E,YAAY,CAAC;gBAC9E;gBACA;cACJ;cACA,MAAM,IAAIxY,KAAK,CAAC,kDAAkD,GAAGqC,IAAI,CAACqD,MAAM,CAAC;UAAC;UAE1F;QACJ,KAAK,WAAW;UACZrD,IAAI,GAAG2D,QAAQ,CAACpD,YAAY,CAAC8D,MAAM,EAAErE,IAAI,CAAC;UAC1C;MAAM;MAEd,OAAOA,IAAI;IACf;IACAmE,YAAY,CAACR,QAAQ,EAAE3B,OAAO,EAAEqC,MAAM,EAAErE,IAAI,EAAE;MAC1C,QAAQA,IAAI,CAACqD,MAAM;QACf,KAAK,YAAY;QACjB,KAAK,uBAAuB;QAC5B,KAAK,6BAA6B;QAClC,KAAK,0BAA0B;UAC3B,OAAO,IAAI,CAAC2R,aAAa,CAAChV,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,CAAC;QACpD,KAAK,aAAa;UACd,OAAO,IAAI,CAACgV,cAAc,CAAClV,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD;UACI;UACA;UACA,MAAM+V,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAAClW,IAAI,CAAC;UACtD,IAAIiW,eAAe,EAAE;YACjB,MAAMG,QAAQ,GAAGpW,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC;YACtC,OAAO+V,eAAe,CAACtE,UAAU,GAAG,IAAI,CAACuD,cAAc,CAACkB,QAAQ,CAAC,GAC7D,IAAI,CAACpB,aAAa,CAACoB,QAAQ,CAAC;UACpC;UACA,OAAOzS,QAAQ,CAACS,UAAU,CAACC,MAAM,EAAErE,IAAI,CAAC;MAAC;IAErD;IACA6D,QAAQ,CAACF,QAAQ,EAAE3B,OAAO,EAAEqC,MAAM,EAAEgS,QAAQ,EAAEvS,SAAS,EAAEC,SAAS,EAAEV,MAAM,EAAE;MACxE,IAAI;QACA2E,qBAAqB,CAACqN,SAAS,EAAE;QACjC,OAAO1R,QAAQ,CAACK,MAAM,CAACK,MAAM,EAAEgS,QAAQ,EAAEvS,SAAS,EAAEC,SAAS,EAAEV,MAAM,CAAC;MAC1E,CAAC,SACO;QACJ,IAAI,CAAC,IAAI,CAAC4Q,eAAe,EAAE;UACvBjM,qBAAqB,CAACuN,SAAS,EAAE;QACrC;MACJ;IACJ;IACAW,mBAAmB,CAAClW,IAAI,EAAE;MACtB,IAAI,CAAC,IAAI,CAAC0T,gBAAgB,EAAE;QACxB,OAAO,IAAI;MACf;MACA,KAAK,IAAIrV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACqV,gBAAgB,CAACpV,MAAM,EAAED,CAAC,EAAE,EAAE;QACnD,MAAM4X,eAAe,GAAG,IAAI,CAACvC,gBAAgB,CAACrV,CAAC,CAAC;QAChD,IAAI4X,eAAe,CAAC5S,MAAM,KAAKrD,IAAI,CAACqD,MAAM,EAAE;UACxC,OAAO4S,eAAe;QAC1B;MACJ;MACA,OAAO,IAAI;IACf;IACAzV,aAAa,CAACX,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEzC,KAAK,EAAE;MAC9D,IAAI,CAACuW,UAAU,GAAGvW,KAAK;MACvB,OAAO,KAAK,CAAC,CAAC;IAClB;EACJ;EACA;EACA;EACAgC,IAAI,CAAC,uBAAuB,CAAC,GAAG0I,qBAAqB;AACzD,CAAC,EAAE,OAAO+H,MAAM,KAAK,QAAQ,IAAIA,MAAM,IAAI,OAAOrD,IAAI,KAAK,QAAQ,IAAIA,IAAI,IAAI9H,MAAM,CAAC;AACtFtF,IAAI,CAACqF,YAAY,CAAC,WAAW,EAAE,CAACC,MAAM,EAAEtF,IAAI,EAAEuF,GAAG,KAAK;EAClD,MAAMmD,qBAAqB,GAAG1I,IAAI,IAAIA,IAAI,CAAC,uBAAuB,CAAC;EACnE,SAASgX,gBAAgB,GAAG;IACxB,OAAOhX,IAAI,IAAIA,IAAI,CAAC,eAAe,CAAC;EACxC;EACA,IAAIiX,sBAAsB,GAAG,IAAI;EACjC;AACJ;AACA;AACA;AACA;AACA;EACI,SAASC,kBAAkB,GAAG;IAC1B,IAAID,sBAAsB,EAAE;MACxBA,sBAAsB,CAACd,eAAe,EAAE;IAC5C;IACAc,sBAAsB,GAAG,IAAI;IAC7B;IACAD,gBAAgB,EAAE,IAAIA,gBAAgB,EAAE,CAACpU,aAAa,EAAE,CAACU,aAAa,EAAE;EAC5E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAS4G,SAAS,CAACM,EAAE,EAAE;IACnB;IACA,MAAM2M,WAAW,GAAG,UAAU,GAAGtO,IAAI,EAAE;MACnC,MAAM7G,aAAa,GAAGgV,gBAAgB,EAAE;MACxC,IAAI,CAAChV,aAAa,EAAE;QAChB,MAAM,IAAI3D,KAAK,CAAC,8EAA8E,GAC1F,uEAAuE,CAAC;MAChF;MACA,MAAM6M,aAAa,GAAGlJ,aAAa,CAACY,aAAa,EAAE;MACnD,IAAI5C,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC,EAAE;QAC3C,MAAM,IAAIpE,KAAK,CAAC,qCAAqC,CAAC;MAC1D;MACA,IAAI;QACA;QACA,IAAI,CAAC4Y,sBAAsB,EAAE;UACzB,IAAI/L,aAAa,CAAC7H,WAAW,EAAE,YAAYqF,qBAAqB,EAAE;YAC9D,MAAM,IAAIrK,KAAK,CAAC,qCAAqC,CAAC;UAC1D;UACA4Y,sBAAsB,GAAG,IAAIvO,qBAAqB,EAAE;QACxD;QACA,IAAI0O,GAAG;QACP,MAAMC,iBAAiB,GAAGnM,aAAa,CAAC7H,WAAW,EAAE;QACrD6H,aAAa,CAAC1I,WAAW,CAACyU,sBAAsB,CAAC;QACjDA,sBAAsB,CAACf,aAAa,EAAE;QACtC,IAAI;UACAkB,GAAG,GAAG5M,EAAE,CAACtD,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;UAC1ByE,eAAe,EAAE;QACrB,CAAC,SACO;UACJpC,aAAa,CAAC1I,WAAW,CAAC6U,iBAAiB,CAAC;QAChD;QACA,IAAIJ,sBAAsB,CAACxC,qBAAqB,CAACzV,MAAM,GAAG,CAAC,EAAE;UACzD,MAAM,IAAIX,KAAK,CAAE,GAAE4Y,sBAAsB,CAACxC,qBAAqB,CAACzV,MAAO,GAAE,GACpE,uCAAsC,CAAC;QAChD;QACA,IAAIiY,sBAAsB,CAACvC,aAAa,CAAC1V,MAAM,GAAG,CAAC,EAAE;UACjD,MAAM,IAAIX,KAAK,CAAE,GAAE4Y,sBAAsB,CAACvC,aAAa,CAAC1V,MAAO,+BAA8B,CAAC;QAClG;QACA,OAAOoY,GAAG;MACd,CAAC,SACO;QACJF,kBAAkB,EAAE;MACxB;IACJ,CAAC;IACDC,WAAW,CAAClL,WAAW,GAAG,IAAI;IAC9B,OAAOkL,WAAW;EACtB;EACA,SAASG,qBAAqB,GAAG;IAC7B,IAAIL,sBAAsB,IAAI,IAAI,EAAE;MAChCA,sBAAsB,GAAGjX,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;MAClE,IAAIwU,sBAAsB,IAAI,IAAI,EAAE;QAChC,MAAM,IAAI5Y,KAAK,CAAC,wEAAwE,CAAC;MAC7F;IACJ;IACA,OAAO4Y,sBAAsB;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAS7O,IAAI,CAACkL,MAAM,GAAG,CAAC,EAAEiE,mBAAmB,GAAG,KAAK,EAAE;IACnDD,qBAAqB,EAAE,CAAClP,IAAI,CAACkL,MAAM,EAAE,IAAI,EAAEiE,mBAAmB,CAAC;EACnE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAShK,KAAK,CAACiK,QAAQ,EAAE;IACrB,OAAOF,qBAAqB,EAAE,CAAC/J,KAAK,CAACiK,QAAQ,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;EACI,SAASC,oBAAoB,GAAG;IAC5B,MAAMvT,QAAQ,GAAGoT,qBAAqB,EAAE;IACxCpT,QAAQ,CAACuQ,qBAAqB;IAC9BvQ,QAAQ,CAACuQ,qBAAqB,CAACzV,MAAM,GAAG,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;EACI,SAASsO,eAAe,GAAG;IACvBgK,qBAAqB,EAAE,CAAChK,eAAe,EAAE;EAC7C;EACAtN,IAAI,CAACuF,GAAG,CAACW,MAAM,CAAC,eAAe,CAAC,CAAC,GAC7B;IAAEgR,kBAAkB;IAAE5J,eAAe;IAAEmK,oBAAoB;IAAErP,IAAI;IAAEmF,KAAK;IAAErD;EAAU,CAAC;AAC7F,CAAC,EAAE,IAAI,CAAC;;AAER;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlK,IAAI,CAACqF,YAAY,CAAC,gBAAgB,EAAE,CAACC,MAAM,EAAEtF,IAAI,EAAEuF,GAAG,KAAK;EACvD,MAAMmS,WAAW,GAAGnS,GAAG,CAACW,MAAM,CAAC,OAAO,CAAC;EACvC,MAAMyR,UAAU,GAAG,IAAI;EACvB,MAAMpH,sBAAsB,GAAGhL,GAAG,CAACW,MAAM,CAAC,kBAAkB,CAAC;EAC7D;EACA;EACA;EACA;EACA;EACA;EACA;EACAmK,OAAO,CAAC9K,GAAG,CAACW,MAAM,CAAC,qBAAqB,CAAC,CAAC,GAAG,SAASkK,mBAAmB,GAAG;IACxE,IAAIwH,OAAO,GAAGvH,OAAO,CAACrQ,IAAI,CAACK,UAAU,CAAC,iBAAiB,CAAC,CAAC;IACzD,IAAIuX,OAAO,EAAE;MACT;IACJ;IACAA,OAAO,GAAGvH,OAAO,CAACrQ,IAAI,CAACK,UAAU,CAAC,iBAAiB,CAAC,CAAC,GAAGgQ,OAAO,CAACxK,SAAS,CAACgS,IAAI;IAC9ExH,OAAO,CAACxK,SAAS,CAACgS,IAAI,GAAG,YAAY;MACjC,MAAMC,OAAO,GAAGF,OAAO,CAAC1Q,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC9C,IAAI,IAAI,CAACuQ,WAAW,CAAC,KAAKC,UAAU,EAAE;QAClC;QACA,MAAMI,iBAAiB,GAAG/X,IAAI,CAAC0C,OAAO,CAACD,GAAG,CAAC,mBAAmB,CAAC;QAC/D,IAAIsV,iBAAiB,EAAE;UACnBA,iBAAiB,CAAC/H,6BAA6B,EAAE;UACjD8H,OAAO,CAACvH,sBAAsB,CAAC,GAAG,IAAI;QAC1C;MACJ;MACA,OAAOuH,OAAO;IAClB,CAAC;EACL,CAAC;EACDzH,OAAO,CAAC9K,GAAG,CAACW,MAAM,CAAC,uBAAuB,CAAC,CAAC,GAAG,SAAS8R,qBAAqB,GAAG;IAC5E;IACA,MAAMJ,OAAO,GAAGvH,OAAO,CAACrQ,IAAI,CAACK,UAAU,CAAC,iBAAiB,CAAC,CAAC;IAC3D,IAAIuX,OAAO,EAAE;MACTvH,OAAO,CAACxK,SAAS,CAACgS,IAAI,GAAGD,OAAO;MAChCvH,OAAO,CAACrQ,IAAI,CAACK,UAAU,CAAC,iBAAiB,CAAC,CAAC,GAAGD,SAAS;IAC3D;EACJ,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}