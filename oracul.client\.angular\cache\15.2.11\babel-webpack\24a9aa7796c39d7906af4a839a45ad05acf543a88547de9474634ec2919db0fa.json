{"ast": null, "code": "/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { HttpHeaders, HttpResponse, HttpErrorResponse, HttpEventType, HttpBackend, HttpClientModule } from '@angular/common/http';\nimport * as i0 from '@angular/core';\nimport { Injectable, NgModule } from '@angular/core';\nimport { Observable } from 'rxjs';\n\n/**\n * Controller to be injected into tests, that allows for mocking and flushing\n * of requests.\n *\n * @publicApi\n */\nclass HttpTestingController {}\n\n/**\n * A mock requests that was received and is ready to be answered.\n *\n * This interface allows access to the underlying `HttpRequest`, and allows\n * responding with `HttpEvent`s or `HttpErrorResponse`s.\n *\n * @publicApi\n */\nclass TestRequest {\n  /**\n   * Whether the request was cancelled after it was sent.\n   */\n  get cancelled() {\n    return this._cancelled;\n  }\n  constructor(request, observer) {\n    this.request = request;\n    this.observer = observer;\n    /**\n     * @internal set by `HttpClientTestingBackend`\n     */\n    this._cancelled = false;\n  }\n  /**\n   * Resolve the request by returning a body plus additional HTTP information (such as response\n   * headers) if provided.\n   * If the request specifies an expected body type, the body is converted into the requested type.\n   * Otherwise, the body is converted to `JSON` by default.\n   *\n   * Both successful and unsuccessful responses can be delivered via `flush()`.\n   */\n  flush(body, opts = {}) {\n    if (this.cancelled) {\n      throw new Error(`Cannot flush a cancelled request.`);\n    }\n    const url = this.request.urlWithParams;\n    const headers = opts.headers instanceof HttpHeaders ? opts.headers : new HttpHeaders(opts.headers);\n    body = _maybeConvertBody(this.request.responseType, body);\n    let statusText = opts.statusText;\n    let status = opts.status !== undefined ? opts.status : 200 /* HttpStatusCode.Ok */;\n    if (opts.status === undefined) {\n      if (body === null) {\n        status = 204 /* HttpStatusCode.NoContent */;\n        statusText = statusText || 'No Content';\n      } else {\n        statusText = statusText || 'OK';\n      }\n    }\n    if (statusText === undefined) {\n      throw new Error('statusText is required when setting a custom status.');\n    }\n    if (status >= 200 && status < 300) {\n      this.observer.next(new HttpResponse({\n        body,\n        headers,\n        status,\n        statusText,\n        url\n      }));\n      this.observer.complete();\n    } else {\n      this.observer.error(new HttpErrorResponse({\n        error: body,\n        headers,\n        status,\n        statusText,\n        url\n      }));\n    }\n  }\n  error(error, opts = {}) {\n    if (this.cancelled) {\n      throw new Error(`Cannot return an error for a cancelled request.`);\n    }\n    if (opts.status && opts.status >= 200 && opts.status < 300) {\n      throw new Error(`error() called with a successful status.`);\n    }\n    const headers = opts.headers instanceof HttpHeaders ? opts.headers : new HttpHeaders(opts.headers);\n    this.observer.error(new HttpErrorResponse({\n      error,\n      headers,\n      status: opts.status || 0,\n      statusText: opts.statusText || '',\n      url: this.request.urlWithParams\n    }));\n  }\n  /**\n   * Deliver an arbitrary `HttpEvent` (such as a progress event) on the response stream for this\n   * request.\n   */\n  event(event) {\n    if (this.cancelled) {\n      throw new Error(`Cannot send events to a cancelled request.`);\n    }\n    this.observer.next(event);\n  }\n}\n/**\n * Helper function to convert a response body to an ArrayBuffer.\n */\nfunction _toArrayBufferBody(body) {\n  if (typeof ArrayBuffer === 'undefined') {\n    throw new Error('ArrayBuffer responses are not supported on this platform.');\n  }\n  if (body instanceof ArrayBuffer) {\n    return body;\n  }\n  throw new Error('Automatic conversion to ArrayBuffer is not supported for response type.');\n}\n/**\n * Helper function to convert a response body to a Blob.\n */\nfunction _toBlob(body) {\n  if (typeof Blob === 'undefined') {\n    throw new Error('Blob responses are not supported on this platform.');\n  }\n  if (body instanceof Blob) {\n    return body;\n  }\n  if (ArrayBuffer && body instanceof ArrayBuffer) {\n    return new Blob([body]);\n  }\n  throw new Error('Automatic conversion to Blob is not supported for response type.');\n}\n/**\n * Helper function to convert a response body to JSON data.\n */\nfunction _toJsonBody(body, format = 'JSON') {\n  if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n    throw new Error(`Automatic conversion to ${format} is not supported for ArrayBuffers.`);\n  }\n  if (typeof Blob !== 'undefined' && body instanceof Blob) {\n    throw new Error(`Automatic conversion to ${format} is not supported for Blobs.`);\n  }\n  if (typeof body === 'string' || typeof body === 'number' || typeof body === 'object' || typeof body === 'boolean' || Array.isArray(body)) {\n    return body;\n  }\n  throw new Error(`Automatic conversion to ${format} is not supported for response type.`);\n}\n/**\n * Helper function to convert a response body to a string.\n */\nfunction _toTextBody(body) {\n  if (typeof body === 'string') {\n    return body;\n  }\n  if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n    throw new Error('Automatic conversion to text is not supported for ArrayBuffers.');\n  }\n  if (typeof Blob !== 'undefined' && body instanceof Blob) {\n    throw new Error('Automatic conversion to text is not supported for Blobs.');\n  }\n  return JSON.stringify(_toJsonBody(body, 'text'));\n}\n/**\n * Convert a response body to the requested type.\n */\nfunction _maybeConvertBody(responseType, body) {\n  if (body === null) {\n    return null;\n  }\n  switch (responseType) {\n    case 'arraybuffer':\n      return _toArrayBufferBody(body);\n    case 'blob':\n      return _toBlob(body);\n    case 'json':\n      return _toJsonBody(body);\n    case 'text':\n      return _toTextBody(body);\n    default:\n      throw new Error(`Unsupported responseType: ${responseType}`);\n  }\n}\n\n/**\n * A testing backend for `HttpClient` which both acts as an `HttpBackend`\n * and as the `HttpTestingController`.\n *\n * `HttpClientTestingBackend` works by keeping a list of all open requests.\n * As requests come in, they're added to the list. Users can assert that specific\n * requests were made and then flush them. In the end, a verify() method asserts\n * that no unexpected requests were made.\n *\n *\n */\nclass HttpClientTestingBackend {\n  constructor() {\n    /**\n     * List of pending requests which have not yet been expected.\n     */\n    this.open = [];\n  }\n  /**\n   * Handle an incoming request by queueing it in the list of open requests.\n   */\n  handle(req) {\n    return new Observable(observer => {\n      const testReq = new TestRequest(req, observer);\n      this.open.push(testReq);\n      observer.next({\n        type: HttpEventType.Sent\n      });\n      return () => {\n        testReq._cancelled = true;\n      };\n    });\n  }\n  /**\n   * Helper function to search for requests in the list of open requests.\n   */\n  _match(match) {\n    if (typeof match === 'string') {\n      return this.open.filter(testReq => testReq.request.urlWithParams === match);\n    } else if (typeof match === 'function') {\n      return this.open.filter(testReq => match(testReq.request));\n    } else {\n      return this.open.filter(testReq => (!match.method || testReq.request.method === match.method.toUpperCase()) && (!match.url || testReq.request.urlWithParams === match.url));\n    }\n  }\n  /**\n   * Search for requests in the list of open requests, and return all that match\n   * without asserting anything about the number of matches.\n   */\n  match(match) {\n    const results = this._match(match);\n    results.forEach(result => {\n      const index = this.open.indexOf(result);\n      if (index !== -1) {\n        this.open.splice(index, 1);\n      }\n    });\n    return results;\n  }\n  /**\n   * Expect that a single outstanding request matches the given matcher, and return\n   * it.\n   *\n   * Requests returned through this API will no longer be in the list of open requests,\n   * and thus will not match twice.\n   */\n  expectOne(match, description) {\n    description = description || this.descriptionFromMatcher(match);\n    const matches = this.match(match);\n    if (matches.length > 1) {\n      throw new Error(`Expected one matching request for criteria \"${description}\", found ${matches.length} requests.`);\n    }\n    if (matches.length === 0) {\n      let message = `Expected one matching request for criteria \"${description}\", found none.`;\n      if (this.open.length > 0) {\n        // Show the methods and URLs of open requests in the error, for convenience.\n        const requests = this.open.map(describeRequest).join(', ');\n        message += ` Requests received are: ${requests}.`;\n      }\n      throw new Error(message);\n    }\n    return matches[0];\n  }\n  /**\n   * Expect that no outstanding requests match the given matcher, and throw an error\n   * if any do.\n   */\n  expectNone(match, description) {\n    description = description || this.descriptionFromMatcher(match);\n    const matches = this.match(match);\n    if (matches.length > 0) {\n      throw new Error(`Expected zero matching requests for criteria \"${description}\", found ${matches.length}.`);\n    }\n  }\n  /**\n   * Validate that there are no outstanding requests.\n   */\n  verify(opts = {}) {\n    let open = this.open;\n    // It's possible that some requests may be cancelled, and this is expected.\n    // The user can ask to ignore open requests which have been cancelled.\n    if (opts.ignoreCancelled) {\n      open = open.filter(testReq => !testReq.cancelled);\n    }\n    if (open.length > 0) {\n      // Show the methods and URLs of open requests in the error, for convenience.\n      const requests = open.map(describeRequest).join(', ');\n      throw new Error(`Expected no open requests, found ${open.length}: ${requests}`);\n    }\n  }\n  descriptionFromMatcher(matcher) {\n    if (typeof matcher === 'string') {\n      return `Match URL: ${matcher}`;\n    } else if (typeof matcher === 'object') {\n      const method = matcher.method || '(any)';\n      const url = matcher.url || '(any)';\n      return `Match method: ${method}, URL: ${url}`;\n    } else {\n      return `Match by function: ${matcher.name}`;\n    }\n  }\n}\nHttpClientTestingBackend.ɵfac = function HttpClientTestingBackend_Factory(t) {\n  return new (t || HttpClientTestingBackend)();\n};\nHttpClientTestingBackend.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpClientTestingBackend,\n  factory: HttpClientTestingBackend.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientTestingBackend, [{\n    type: Injectable\n  }], null, null);\n})();\nfunction describeRequest(testRequest) {\n  const url = testRequest.request.urlWithParams;\n  const method = testRequest.request.method;\n  return `${method} ${url}`;\n}\nfunction provideHttpClientTesting() {\n  return [HttpClientTestingBackend, {\n    provide: HttpBackend,\n    useExisting: HttpClientTestingBackend\n  }, {\n    provide: HttpTestingController,\n    useExisting: HttpClientTestingBackend\n  }];\n}\n\n/**\n * Configures `HttpClientTestingBackend` as the `HttpBackend` used by `HttpClient`.\n *\n * Inject `HttpTestingController` to expect and flush requests in your tests.\n *\n * @publicApi\n */\nclass HttpClientTestingModule {}\nHttpClientTestingModule.ɵfac = function HttpClientTestingModule_Factory(t) {\n  return new (t || HttpClientTestingModule)();\n};\nHttpClientTestingModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: HttpClientTestingModule,\n  imports: [HttpClientModule]\n});\nHttpClientTestingModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [provideHttpClientTesting()],\n  imports: [HttpClientModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientTestingModule, [{\n    type: NgModule,\n    args: [{\n      imports: [HttpClientModule],\n      providers: [provideHttpClientTesting()]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { HttpClientTestingModule, HttpTestingController, TestRequest, provideHttpClientTesting };", "map": {"version": 3, "names": ["HttpHeaders", "HttpResponse", "HttpErrorResponse", "HttpEventType", "HttpBackend", "HttpClientModule", "i0", "Injectable", "NgModule", "Observable", "HttpTestingController", "TestRequest", "cancelled", "_cancelled", "constructor", "request", "observer", "flush", "body", "opts", "Error", "url", "urlWithParams", "headers", "_maybeConvertBody", "responseType", "statusText", "status", "undefined", "next", "complete", "error", "event", "_toArrayBufferBody", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_toBlob", "Blob", "_toJsonBody", "format", "Array", "isArray", "_toTextBody", "JSON", "stringify", "HttpClientTestingBackend", "open", "handle", "req", "testReq", "push", "type", "<PERSON><PERSON>", "_match", "match", "filter", "method", "toUpperCase", "results", "for<PERSON>ach", "result", "index", "indexOf", "splice", "expectOne", "description", "descriptionFromMatcher", "matches", "length", "message", "requests", "map", "describeRequest", "join", "expectNone", "verify", "ignoreCancelled", "matcher", "name", "ɵfac", "ɵprov", "testRequest", "provideHttpClientTesting", "provide", "useExisting", "HttpClientTestingModule", "ɵmod", "ɵinj", "args", "imports", "providers"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/common/fesm2020/http/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { HttpHeaders, HttpResponse, HttpErrorResponse, HttpEventType, HttpBackend, HttpClientModule } from '@angular/common/http';\nimport * as i0 from '@angular/core';\nimport { Injectable, NgModule } from '@angular/core';\nimport { Observable } from 'rxjs';\n\n/**\n * Controller to be injected into tests, that allows for mocking and flushing\n * of requests.\n *\n * @publicApi\n */\nclass HttpTestingController {\n}\n\n/**\n * A mock requests that was received and is ready to be answered.\n *\n * This interface allows access to the underlying `HttpRequest`, and allows\n * responding with `HttpEvent`s or `HttpErrorResponse`s.\n *\n * @publicApi\n */\nclass TestRequest {\n    /**\n     * Whether the request was cancelled after it was sent.\n     */\n    get cancelled() {\n        return this._cancelled;\n    }\n    constructor(request, observer) {\n        this.request = request;\n        this.observer = observer;\n        /**\n         * @internal set by `HttpClientTestingBackend`\n         */\n        this._cancelled = false;\n    }\n    /**\n     * Resolve the request by returning a body plus additional HTTP information (such as response\n     * headers) if provided.\n     * If the request specifies an expected body type, the body is converted into the requested type.\n     * Otherwise, the body is converted to `JSON` by default.\n     *\n     * Both successful and unsuccessful responses can be delivered via `flush()`.\n     */\n    flush(body, opts = {}) {\n        if (this.cancelled) {\n            throw new Error(`Cannot flush a cancelled request.`);\n        }\n        const url = this.request.urlWithParams;\n        const headers = (opts.headers instanceof HttpHeaders) ? opts.headers : new HttpHeaders(opts.headers);\n        body = _maybeConvertBody(this.request.responseType, body);\n        let statusText = opts.statusText;\n        let status = opts.status !== undefined ? opts.status : 200 /* HttpStatusCode.Ok */;\n        if (opts.status === undefined) {\n            if (body === null) {\n                status = 204 /* HttpStatusCode.NoContent */;\n                statusText = statusText || 'No Content';\n            }\n            else {\n                statusText = statusText || 'OK';\n            }\n        }\n        if (statusText === undefined) {\n            throw new Error('statusText is required when setting a custom status.');\n        }\n        if (status >= 200 && status < 300) {\n            this.observer.next(new HttpResponse({ body, headers, status, statusText, url }));\n            this.observer.complete();\n        }\n        else {\n            this.observer.error(new HttpErrorResponse({ error: body, headers, status, statusText, url }));\n        }\n    }\n    error(error, opts = {}) {\n        if (this.cancelled) {\n            throw new Error(`Cannot return an error for a cancelled request.`);\n        }\n        if (opts.status && opts.status >= 200 && opts.status < 300) {\n            throw new Error(`error() called with a successful status.`);\n        }\n        const headers = (opts.headers instanceof HttpHeaders) ? opts.headers : new HttpHeaders(opts.headers);\n        this.observer.error(new HttpErrorResponse({\n            error,\n            headers,\n            status: opts.status || 0,\n            statusText: opts.statusText || '',\n            url: this.request.urlWithParams,\n        }));\n    }\n    /**\n     * Deliver an arbitrary `HttpEvent` (such as a progress event) on the response stream for this\n     * request.\n     */\n    event(event) {\n        if (this.cancelled) {\n            throw new Error(`Cannot send events to a cancelled request.`);\n        }\n        this.observer.next(event);\n    }\n}\n/**\n * Helper function to convert a response body to an ArrayBuffer.\n */\nfunction _toArrayBufferBody(body) {\n    if (typeof ArrayBuffer === 'undefined') {\n        throw new Error('ArrayBuffer responses are not supported on this platform.');\n    }\n    if (body instanceof ArrayBuffer) {\n        return body;\n    }\n    throw new Error('Automatic conversion to ArrayBuffer is not supported for response type.');\n}\n/**\n * Helper function to convert a response body to a Blob.\n */\nfunction _toBlob(body) {\n    if (typeof Blob === 'undefined') {\n        throw new Error('Blob responses are not supported on this platform.');\n    }\n    if (body instanceof Blob) {\n        return body;\n    }\n    if (ArrayBuffer && body instanceof ArrayBuffer) {\n        return new Blob([body]);\n    }\n    throw new Error('Automatic conversion to Blob is not supported for response type.');\n}\n/**\n * Helper function to convert a response body to JSON data.\n */\nfunction _toJsonBody(body, format = 'JSON') {\n    if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n        throw new Error(`Automatic conversion to ${format} is not supported for ArrayBuffers.`);\n    }\n    if (typeof Blob !== 'undefined' && body instanceof Blob) {\n        throw new Error(`Automatic conversion to ${format} is not supported for Blobs.`);\n    }\n    if (typeof body === 'string' || typeof body === 'number' || typeof body === 'object' ||\n        typeof body === 'boolean' || Array.isArray(body)) {\n        return body;\n    }\n    throw new Error(`Automatic conversion to ${format} is not supported for response type.`);\n}\n/**\n * Helper function to convert a response body to a string.\n */\nfunction _toTextBody(body) {\n    if (typeof body === 'string') {\n        return body;\n    }\n    if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n        throw new Error('Automatic conversion to text is not supported for ArrayBuffers.');\n    }\n    if (typeof Blob !== 'undefined' && body instanceof Blob) {\n        throw new Error('Automatic conversion to text is not supported for Blobs.');\n    }\n    return JSON.stringify(_toJsonBody(body, 'text'));\n}\n/**\n * Convert a response body to the requested type.\n */\nfunction _maybeConvertBody(responseType, body) {\n    if (body === null) {\n        return null;\n    }\n    switch (responseType) {\n        case 'arraybuffer':\n            return _toArrayBufferBody(body);\n        case 'blob':\n            return _toBlob(body);\n        case 'json':\n            return _toJsonBody(body);\n        case 'text':\n            return _toTextBody(body);\n        default:\n            throw new Error(`Unsupported responseType: ${responseType}`);\n    }\n}\n\n/**\n * A testing backend for `HttpClient` which both acts as an `HttpBackend`\n * and as the `HttpTestingController`.\n *\n * `HttpClientTestingBackend` works by keeping a list of all open requests.\n * As requests come in, they're added to the list. Users can assert that specific\n * requests were made and then flush them. In the end, a verify() method asserts\n * that no unexpected requests were made.\n *\n *\n */\nclass HttpClientTestingBackend {\n    constructor() {\n        /**\n         * List of pending requests which have not yet been expected.\n         */\n        this.open = [];\n    }\n    /**\n     * Handle an incoming request by queueing it in the list of open requests.\n     */\n    handle(req) {\n        return new Observable((observer) => {\n            const testReq = new TestRequest(req, observer);\n            this.open.push(testReq);\n            observer.next({ type: HttpEventType.Sent });\n            return () => {\n                testReq._cancelled = true;\n            };\n        });\n    }\n    /**\n     * Helper function to search for requests in the list of open requests.\n     */\n    _match(match) {\n        if (typeof match === 'string') {\n            return this.open.filter(testReq => testReq.request.urlWithParams === match);\n        }\n        else if (typeof match === 'function') {\n            return this.open.filter(testReq => match(testReq.request));\n        }\n        else {\n            return this.open.filter(testReq => (!match.method || testReq.request.method === match.method.toUpperCase()) &&\n                (!match.url || testReq.request.urlWithParams === match.url));\n        }\n    }\n    /**\n     * Search for requests in the list of open requests, and return all that match\n     * without asserting anything about the number of matches.\n     */\n    match(match) {\n        const results = this._match(match);\n        results.forEach(result => {\n            const index = this.open.indexOf(result);\n            if (index !== -1) {\n                this.open.splice(index, 1);\n            }\n        });\n        return results;\n    }\n    /**\n     * Expect that a single outstanding request matches the given matcher, and return\n     * it.\n     *\n     * Requests returned through this API will no longer be in the list of open requests,\n     * and thus will not match twice.\n     */\n    expectOne(match, description) {\n        description = description || this.descriptionFromMatcher(match);\n        const matches = this.match(match);\n        if (matches.length > 1) {\n            throw new Error(`Expected one matching request for criteria \"${description}\", found ${matches.length} requests.`);\n        }\n        if (matches.length === 0) {\n            let message = `Expected one matching request for criteria \"${description}\", found none.`;\n            if (this.open.length > 0) {\n                // Show the methods and URLs of open requests in the error, for convenience.\n                const requests = this.open.map(describeRequest).join(', ');\n                message += ` Requests received are: ${requests}.`;\n            }\n            throw new Error(message);\n        }\n        return matches[0];\n    }\n    /**\n     * Expect that no outstanding requests match the given matcher, and throw an error\n     * if any do.\n     */\n    expectNone(match, description) {\n        description = description || this.descriptionFromMatcher(match);\n        const matches = this.match(match);\n        if (matches.length > 0) {\n            throw new Error(`Expected zero matching requests for criteria \"${description}\", found ${matches.length}.`);\n        }\n    }\n    /**\n     * Validate that there are no outstanding requests.\n     */\n    verify(opts = {}) {\n        let open = this.open;\n        // It's possible that some requests may be cancelled, and this is expected.\n        // The user can ask to ignore open requests which have been cancelled.\n        if (opts.ignoreCancelled) {\n            open = open.filter(testReq => !testReq.cancelled);\n        }\n        if (open.length > 0) {\n            // Show the methods and URLs of open requests in the error, for convenience.\n            const requests = open.map(describeRequest).join(', ');\n            throw new Error(`Expected no open requests, found ${open.length}: ${requests}`);\n        }\n    }\n    descriptionFromMatcher(matcher) {\n        if (typeof matcher === 'string') {\n            return `Match URL: ${matcher}`;\n        }\n        else if (typeof matcher === 'object') {\n            const method = matcher.method || '(any)';\n            const url = matcher.url || '(any)';\n            return `Match method: ${method}, URL: ${url}`;\n        }\n        else {\n            return `Match by function: ${matcher.name}`;\n        }\n    }\n}\nHttpClientTestingBackend.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientTestingBackend, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nHttpClientTestingBackend.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientTestingBackend });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientTestingBackend, decorators: [{\n            type: Injectable\n        }] });\nfunction describeRequest(testRequest) {\n    const url = testRequest.request.urlWithParams;\n    const method = testRequest.request.method;\n    return `${method} ${url}`;\n}\n\nfunction provideHttpClientTesting() {\n    return [\n        HttpClientTestingBackend,\n        { provide: HttpBackend, useExisting: HttpClientTestingBackend },\n        { provide: HttpTestingController, useExisting: HttpClientTestingBackend },\n    ];\n}\n\n/**\n * Configures `HttpClientTestingBackend` as the `HttpBackend` used by `HttpClient`.\n *\n * Inject `HttpTestingController` to expect and flush requests in your tests.\n *\n * @publicApi\n */\nclass HttpClientTestingModule {\n}\nHttpClientTestingModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nHttpClientTestingModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientTestingModule, imports: [HttpClientModule] });\nHttpClientTestingModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientTestingModule, providers: [\n        provideHttpClientTesting(),\n    ], imports: [HttpClientModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        HttpClientModule,\n                    ],\n                    providers: [\n                        provideHttpClientTesting(),\n                    ],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { HttpClientTestingModule, HttpTestingController, TestRequest, provideHttpClientTesting };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,WAAW,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,WAAW,EAAEC,gBAAgB,QAAQ,sBAAsB;AACjI,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AACpD,SAASC,UAAU,QAAQ,MAAM;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;;AAG5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd;AACJ;AACA;EACI,IAAIC,SAAS,GAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACAC,WAAW,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAC3B,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB;AACR;AACA;IACQ,IAAI,CAACH,UAAU,GAAG,KAAK;EAC3B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACII,KAAK,CAACC,IAAI,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB,IAAI,IAAI,CAACP,SAAS,EAAE;MAChB,MAAM,IAAIQ,KAAK,CAAE,mCAAkC,CAAC;IACxD;IACA,MAAMC,GAAG,GAAG,IAAI,CAACN,OAAO,CAACO,aAAa;IACtC,MAAMC,OAAO,GAAIJ,IAAI,CAACI,OAAO,YAAYvB,WAAW,GAAImB,IAAI,CAACI,OAAO,GAAG,IAAIvB,WAAW,CAACmB,IAAI,CAACI,OAAO,CAAC;IACpGL,IAAI,GAAGM,iBAAiB,CAAC,IAAI,CAACT,OAAO,CAACU,YAAY,EAAEP,IAAI,CAAC;IACzD,IAAIQ,UAAU,GAAGP,IAAI,CAACO,UAAU;IAChC,IAAIC,MAAM,GAAGR,IAAI,CAACQ,MAAM,KAAKC,SAAS,GAAGT,IAAI,CAACQ,MAAM,GAAG,GAAG,CAAC;IAC3D,IAAIR,IAAI,CAACQ,MAAM,KAAKC,SAAS,EAAE;MAC3B,IAAIV,IAAI,KAAK,IAAI,EAAE;QACfS,MAAM,GAAG,GAAG,CAAC;QACbD,UAAU,GAAGA,UAAU,IAAI,YAAY;MAC3C,CAAC,MACI;QACDA,UAAU,GAAGA,UAAU,IAAI,IAAI;MACnC;IACJ;IACA,IAAIA,UAAU,KAAKE,SAAS,EAAE;MAC1B,MAAM,IAAIR,KAAK,CAAC,sDAAsD,CAAC;IAC3E;IACA,IAAIO,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG,EAAE;MAC/B,IAAI,CAACX,QAAQ,CAACa,IAAI,CAAC,IAAI5B,YAAY,CAAC;QAAEiB,IAAI;QAAEK,OAAO;QAAEI,MAAM;QAAED,UAAU;QAAEL;MAAI,CAAC,CAAC,CAAC;MAChF,IAAI,CAACL,QAAQ,CAACc,QAAQ,EAAE;IAC5B,CAAC,MACI;MACD,IAAI,CAACd,QAAQ,CAACe,KAAK,CAAC,IAAI7B,iBAAiB,CAAC;QAAE6B,KAAK,EAAEb,IAAI;QAAEK,OAAO;QAAEI,MAAM;QAAED,UAAU;QAAEL;MAAI,CAAC,CAAC,CAAC;IACjG;EACJ;EACAU,KAAK,CAACA,KAAK,EAAEZ,IAAI,GAAG,CAAC,CAAC,EAAE;IACpB,IAAI,IAAI,CAACP,SAAS,EAAE;MAChB,MAAM,IAAIQ,KAAK,CAAE,iDAAgD,CAAC;IACtE;IACA,IAAID,IAAI,CAACQ,MAAM,IAAIR,IAAI,CAACQ,MAAM,IAAI,GAAG,IAAIR,IAAI,CAACQ,MAAM,GAAG,GAAG,EAAE;MACxD,MAAM,IAAIP,KAAK,CAAE,0CAAyC,CAAC;IAC/D;IACA,MAAMG,OAAO,GAAIJ,IAAI,CAACI,OAAO,YAAYvB,WAAW,GAAImB,IAAI,CAACI,OAAO,GAAG,IAAIvB,WAAW,CAACmB,IAAI,CAACI,OAAO,CAAC;IACpG,IAAI,CAACP,QAAQ,CAACe,KAAK,CAAC,IAAI7B,iBAAiB,CAAC;MACtC6B,KAAK;MACLR,OAAO;MACPI,MAAM,EAAER,IAAI,CAACQ,MAAM,IAAI,CAAC;MACxBD,UAAU,EAAEP,IAAI,CAACO,UAAU,IAAI,EAAE;MACjCL,GAAG,EAAE,IAAI,CAACN,OAAO,CAACO;IACtB,CAAC,CAAC,CAAC;EACP;EACA;AACJ;AACA;AACA;EACIU,KAAK,CAACA,KAAK,EAAE;IACT,IAAI,IAAI,CAACpB,SAAS,EAAE;MAChB,MAAM,IAAIQ,KAAK,CAAE,4CAA2C,CAAC;IACjE;IACA,IAAI,CAACJ,QAAQ,CAACa,IAAI,CAACG,KAAK,CAAC;EAC7B;AACJ;AACA;AACA;AACA;AACA,SAASC,kBAAkB,CAACf,IAAI,EAAE;EAC9B,IAAI,OAAOgB,WAAW,KAAK,WAAW,EAAE;IACpC,MAAM,IAAId,KAAK,CAAC,2DAA2D,CAAC;EAChF;EACA,IAAIF,IAAI,YAAYgB,WAAW,EAAE;IAC7B,OAAOhB,IAAI;EACf;EACA,MAAM,IAAIE,KAAK,CAAC,yEAAyE,CAAC;AAC9F;AACA;AACA;AACA;AACA,SAASe,OAAO,CAACjB,IAAI,EAAE;EACnB,IAAI,OAAOkB,IAAI,KAAK,WAAW,EAAE;IAC7B,MAAM,IAAIhB,KAAK,CAAC,oDAAoD,CAAC;EACzE;EACA,IAAIF,IAAI,YAAYkB,IAAI,EAAE;IACtB,OAAOlB,IAAI;EACf;EACA,IAAIgB,WAAW,IAAIhB,IAAI,YAAYgB,WAAW,EAAE;IAC5C,OAAO,IAAIE,IAAI,CAAC,CAAClB,IAAI,CAAC,CAAC;EAC3B;EACA,MAAM,IAAIE,KAAK,CAAC,kEAAkE,CAAC;AACvF;AACA;AACA;AACA;AACA,SAASiB,WAAW,CAACnB,IAAI,EAAEoB,MAAM,GAAG,MAAM,EAAE;EACxC,IAAI,OAAOJ,WAAW,KAAK,WAAW,IAAIhB,IAAI,YAAYgB,WAAW,EAAE;IACnE,MAAM,IAAId,KAAK,CAAE,2BAA0BkB,MAAO,qCAAoC,CAAC;EAC3F;EACA,IAAI,OAAOF,IAAI,KAAK,WAAW,IAAIlB,IAAI,YAAYkB,IAAI,EAAE;IACrD,MAAM,IAAIhB,KAAK,CAAE,2BAA0BkB,MAAO,8BAA6B,CAAC;EACpF;EACA,IAAI,OAAOpB,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAChF,OAAOA,IAAI,KAAK,SAAS,IAAIqB,KAAK,CAACC,OAAO,CAACtB,IAAI,CAAC,EAAE;IAClD,OAAOA,IAAI;EACf;EACA,MAAM,IAAIE,KAAK,CAAE,2BAA0BkB,MAAO,sCAAqC,CAAC;AAC5F;AACA;AACA;AACA;AACA,SAASG,WAAW,CAACvB,IAAI,EAAE;EACvB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOA,IAAI;EACf;EACA,IAAI,OAAOgB,WAAW,KAAK,WAAW,IAAIhB,IAAI,YAAYgB,WAAW,EAAE;IACnE,MAAM,IAAId,KAAK,CAAC,iEAAiE,CAAC;EACtF;EACA,IAAI,OAAOgB,IAAI,KAAK,WAAW,IAAIlB,IAAI,YAAYkB,IAAI,EAAE;IACrD,MAAM,IAAIhB,KAAK,CAAC,0DAA0D,CAAC;EAC/E;EACA,OAAOsB,IAAI,CAACC,SAAS,CAACN,WAAW,CAACnB,IAAI,EAAE,MAAM,CAAC,CAAC;AACpD;AACA;AACA;AACA;AACA,SAASM,iBAAiB,CAACC,YAAY,EAAEP,IAAI,EAAE;EAC3C,IAAIA,IAAI,KAAK,IAAI,EAAE;IACf,OAAO,IAAI;EACf;EACA,QAAQO,YAAY;IAChB,KAAK,aAAa;MACd,OAAOQ,kBAAkB,CAACf,IAAI,CAAC;IACnC,KAAK,MAAM;MACP,OAAOiB,OAAO,CAACjB,IAAI,CAAC;IACxB,KAAK,MAAM;MACP,OAAOmB,WAAW,CAACnB,IAAI,CAAC;IAC5B,KAAK,MAAM;MACP,OAAOuB,WAAW,CAACvB,IAAI,CAAC;IAC5B;MACI,MAAM,IAAIE,KAAK,CAAE,6BAA4BK,YAAa,EAAC,CAAC;EAAC;AAEzE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmB,wBAAwB,CAAC;EAC3B9B,WAAW,GAAG;IACV;AACR;AACA;IACQ,IAAI,CAAC+B,IAAI,GAAG,EAAE;EAClB;EACA;AACJ;AACA;EACIC,MAAM,CAACC,GAAG,EAAE;IACR,OAAO,IAAItC,UAAU,CAAEO,QAAQ,IAAK;MAChC,MAAMgC,OAAO,GAAG,IAAIrC,WAAW,CAACoC,GAAG,EAAE/B,QAAQ,CAAC;MAC9C,IAAI,CAAC6B,IAAI,CAACI,IAAI,CAACD,OAAO,CAAC;MACvBhC,QAAQ,CAACa,IAAI,CAAC;QAAEqB,IAAI,EAAE/C,aAAa,CAACgD;MAAK,CAAC,CAAC;MAC3C,OAAO,MAAM;QACTH,OAAO,CAACnC,UAAU,GAAG,IAAI;MAC7B,CAAC;IACL,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIuC,MAAM,CAACC,KAAK,EAAE;IACV,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,OAAO,IAAI,CAACR,IAAI,CAACS,MAAM,CAACN,OAAO,IAAIA,OAAO,CAACjC,OAAO,CAACO,aAAa,KAAK+B,KAAK,CAAC;IAC/E,CAAC,MACI,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAClC,OAAO,IAAI,CAACR,IAAI,CAACS,MAAM,CAACN,OAAO,IAAIK,KAAK,CAACL,OAAO,CAACjC,OAAO,CAAC,CAAC;IAC9D,CAAC,MACI;MACD,OAAO,IAAI,CAAC8B,IAAI,CAACS,MAAM,CAACN,OAAO,IAAI,CAAC,CAACK,KAAK,CAACE,MAAM,IAAIP,OAAO,CAACjC,OAAO,CAACwC,MAAM,KAAKF,KAAK,CAACE,MAAM,CAACC,WAAW,EAAE,MACrG,CAACH,KAAK,CAAChC,GAAG,IAAI2B,OAAO,CAACjC,OAAO,CAACO,aAAa,KAAK+B,KAAK,CAAChC,GAAG,CAAC,CAAC;IACpE;EACJ;EACA;AACJ;AACA;AACA;EACIgC,KAAK,CAACA,KAAK,EAAE;IACT,MAAMI,OAAO,GAAG,IAAI,CAACL,MAAM,CAACC,KAAK,CAAC;IAClCI,OAAO,CAACC,OAAO,CAACC,MAAM,IAAI;MACtB,MAAMC,KAAK,GAAG,IAAI,CAACf,IAAI,CAACgB,OAAO,CAACF,MAAM,CAAC;MACvC,IAAIC,KAAK,KAAK,CAAC,CAAC,EAAE;QACd,IAAI,CAACf,IAAI,CAACiB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC9B;IACJ,CAAC,CAAC;IACF,OAAOH,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIM,SAAS,CAACV,KAAK,EAAEW,WAAW,EAAE;IAC1BA,WAAW,GAAGA,WAAW,IAAI,IAAI,CAACC,sBAAsB,CAACZ,KAAK,CAAC;IAC/D,MAAMa,OAAO,GAAG,IAAI,CAACb,KAAK,CAACA,KAAK,CAAC;IACjC,IAAIa,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACpB,MAAM,IAAI/C,KAAK,CAAE,+CAA8C4C,WAAY,YAAWE,OAAO,CAACC,MAAO,YAAW,CAAC;IACrH;IACA,IAAID,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;MACtB,IAAIC,OAAO,GAAI,+CAA8CJ,WAAY,gBAAe;MACxF,IAAI,IAAI,CAACnB,IAAI,CAACsB,MAAM,GAAG,CAAC,EAAE;QACtB;QACA,MAAME,QAAQ,GAAG,IAAI,CAACxB,IAAI,CAACyB,GAAG,CAACC,eAAe,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QAC1DJ,OAAO,IAAK,2BAA0BC,QAAS,GAAE;MACrD;MACA,MAAM,IAAIjD,KAAK,CAACgD,OAAO,CAAC;IAC5B;IACA,OAAOF,OAAO,CAAC,CAAC,CAAC;EACrB;EACA;AACJ;AACA;AACA;EACIO,UAAU,CAACpB,KAAK,EAAEW,WAAW,EAAE;IAC3BA,WAAW,GAAGA,WAAW,IAAI,IAAI,CAACC,sBAAsB,CAACZ,KAAK,CAAC;IAC/D,MAAMa,OAAO,GAAG,IAAI,CAACb,KAAK,CAACA,KAAK,CAAC;IACjC,IAAIa,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACpB,MAAM,IAAI/C,KAAK,CAAE,iDAAgD4C,WAAY,YAAWE,OAAO,CAACC,MAAO,GAAE,CAAC;IAC9G;EACJ;EACA;AACJ;AACA;EACIO,MAAM,CAACvD,IAAI,GAAG,CAAC,CAAC,EAAE;IACd,IAAI0B,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB;IACA;IACA,IAAI1B,IAAI,CAACwD,eAAe,EAAE;MACtB9B,IAAI,GAAGA,IAAI,CAACS,MAAM,CAACN,OAAO,IAAI,CAACA,OAAO,CAACpC,SAAS,CAAC;IACrD;IACA,IAAIiC,IAAI,CAACsB,MAAM,GAAG,CAAC,EAAE;MACjB;MACA,MAAME,QAAQ,GAAGxB,IAAI,CAACyB,GAAG,CAACC,eAAe,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MACrD,MAAM,IAAIpD,KAAK,CAAE,oCAAmCyB,IAAI,CAACsB,MAAO,KAAIE,QAAS,EAAC,CAAC;IACnF;EACJ;EACAJ,sBAAsB,CAACW,OAAO,EAAE;IAC5B,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC7B,OAAQ,cAAaA,OAAQ,EAAC;IAClC,CAAC,MACI,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAClC,MAAMrB,MAAM,GAAGqB,OAAO,CAACrB,MAAM,IAAI,OAAO;MACxC,MAAMlC,GAAG,GAAGuD,OAAO,CAACvD,GAAG,IAAI,OAAO;MAClC,OAAQ,iBAAgBkC,MAAO,UAASlC,GAAI,EAAC;IACjD,CAAC,MACI;MACD,OAAQ,sBAAqBuD,OAAO,CAACC,IAAK,EAAC;IAC/C;EACJ;AACJ;AACAjC,wBAAwB,CAACkC,IAAI;EAAA,iBAAyFlC,wBAAwB;AAAA,CAAoD;AAClMA,wBAAwB,CAACmC,KAAK,kBAD8EzE,EAAE;EAAA,OACYsC,wBAAwB;EAAA,SAAxBA,wBAAwB;AAAA,EAAG;AACrJ;EAAA,mDAF4GtC,EAAE,mBAElBsC,wBAAwB,EAAc,CAAC;IACvHM,IAAI,EAAE3C;EACV,CAAC,CAAC;AAAA;AACV,SAASgE,eAAe,CAACS,WAAW,EAAE;EAClC,MAAM3D,GAAG,GAAG2D,WAAW,CAACjE,OAAO,CAACO,aAAa;EAC7C,MAAMiC,MAAM,GAAGyB,WAAW,CAACjE,OAAO,CAACwC,MAAM;EACzC,OAAQ,GAAEA,MAAO,IAAGlC,GAAI,EAAC;AAC7B;AAEA,SAAS4D,wBAAwB,GAAG;EAChC,OAAO,CACHrC,wBAAwB,EACxB;IAAEsC,OAAO,EAAE9E,WAAW;IAAE+E,WAAW,EAAEvC;EAAyB,CAAC,EAC/D;IAAEsC,OAAO,EAAExE,qBAAqB;IAAEyE,WAAW,EAAEvC;EAAyB,CAAC,CAC5E;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwC,uBAAuB,CAAC;AAE9BA,uBAAuB,CAACN,IAAI;EAAA,iBAAyFM,uBAAuB;AAAA,CAAkD;AAC9LA,uBAAuB,CAACC,IAAI,kBA7BgF/E,EAAE;EAAA,MA6BQ8E,uBAAuB;EAAA,UAAY/E,gBAAgB;AAAA,EAAI;AAC7K+E,uBAAuB,CAACE,IAAI,kBA9BgFhF,EAAE;EAAA,WA8B4C,CAClJ2E,wBAAwB,EAAE,CAC7B;EAAA,UAAY5E,gBAAgB;AAAA,EAAI;AACrC;EAAA,mDAjC4GC,EAAE,mBAiClB8E,uBAAuB,EAAc,CAAC;IACtHlC,IAAI,EAAE1C,QAAQ;IACd+E,IAAI,EAAE,CAAC;MACCC,OAAO,EAAE,CACLnF,gBAAgB,CACnB;MACDoF,SAAS,EAAE,CACPR,wBAAwB,EAAE;IAElC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASG,uBAAuB,EAAE1E,qBAAqB,EAAEC,WAAW,EAAEsE,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}